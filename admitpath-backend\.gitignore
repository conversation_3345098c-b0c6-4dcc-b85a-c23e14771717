# Virtual Environment
myenv/
venv/
ENV/
env/
.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs
*.log
logs/
log/

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# Local development settings
.env.local
.env.development.local
.env.test.local
.env.production.local

# pytest
.pytest_cache/


# Alembic
alembic
alembic/*
alembic.ini
alembic/versions/
alembic/env.py

# Database
*.db
*.sqlite3
*.postgresql

# FastAPI specific
.pytest_cache/
__pycache__/
*.pyc

# System specific
.DS_Store
*.swp
*.swo

# OAuth and Authentication
token.pickle
credentials.json
google_test.html
test.html
*_test.html
*.pickle

# Credentials and Secrets
*credentials*.json
*secret*.json

refactor.py

# Local Docker Testing stuff
docker-compose.dev.yml
nginx.dev.conf
Dockerfile.dev.nginx