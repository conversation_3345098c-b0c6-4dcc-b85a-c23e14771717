# AdmitPath Backend

This repository serves as the Python backend for the core AdmitPath platform.

## Directory Structure

```
admitpath-backend/
├── app/                    # Main application package
│   ├── __init__.py        # Makes app a Python package
│   ├── main.py            # FastAPI application initialization
│   └── routes/            # API routes
│       ├── __init__.py    # Makes routes a Python package
│       └── health.py      # Health check endpoints
├── requirements.txt        # Python dependencies
├── runserver.py           # Server startup script
└── .gitignore            # Git ignore rules
```

## Setup

1. Create a virtual environment:
```bash
python3 -m venv myenv
source myenv/bin/activate  # On Windows use: myenv\Scripts\activate
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Running the Server

To start the development server:
```bash
python runserver.py
```

The server will start at `http://localhost:8000`

## API Endpoints

### Health Check
- `GET /health` - Basic health check endpoint
- `GET /health/detailed` - Detailed health status with version information


### Authentication
- `POST /signup/counselor` - Register a new counselor
  ```json
  {
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "first_name": "<PERSON>",
    "last_name": "Doe"
  }
  ```

- `POST /signin` - Login for registered users
  ```json
  {
    "username": "<EMAIL>",
    "password": "SecurePass123!"
  }
  ```

### Password Reset Flow
- `POST /forgot-password` - Request password reset
  ```json
  {
    "email": "<EMAIL>"
  }
  ```

- `POST /verify-code` - Verify reset code
  ```json
  {
    "email": "<EMAIL>",
    "code": "123456"
  }
  ```

- `POST /reset-password` - Reset password with verification code
  ```json
  {
    "email": "<EMAIL>",
    "code": "123456",
    "new_password": "NewSecure123!"
  }
  ```


## Development

- The application uses FastAPI framework
- Routes are organized in the `app/routes` directory
- New routes should be added as separate modules in the routes directory
- All routes must be registered in `app/main.py`


