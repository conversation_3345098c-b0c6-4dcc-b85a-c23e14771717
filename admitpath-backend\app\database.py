from sqlalchemy import create_engine, text  
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

SQLALCHEMY_DATABASE_URL = os.getenv("DATABASE_URL")

# Fix for Supabase connection string
if SQLALCHEMY_DATABASE_URL and SQLALCHEMY_DATABASE_URL.startswith("postgres://"):
    SQLALCHEMY_DATABASE_URL = SQLALCHEMY_DATABASE_URL.replace("postgres://", "postgresql://", 1)

# Create SQLAlchemy engine with connection pooling optimized for Supabase Pro tier
engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    pool_size=10,       # Maintain 10 steady connections
    max_overflow=8,     # Allow 8 additional connections (total max 18)
    pool_timeout=30,    # Wait 30 seconds for a connection
    pool_recycle=1800,  # Recycle connections after 30 mins to prevent stale connections
    pool_pre_ping=True, # Enable connection health checks
    # Echo SQL statements for debugging - set to False in production
    echo=False
)

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class
Base = declarative_base()

# Dependency to get DB session with proper cleanup
def get_db():
    db = SessionLocal()
    try:
        # Verify connection is active before yielding
        db.execute(text("SELECT 1"))
        yield db
    except Exception as e:
        logger.error(f"Database connection error: {str(e)}")
        raise
    finally:
        # Ensure connection is properly closed
        try:
            db.close()
        except Exception as e:
            logger.error(f"Error closing database connection: {str(e)}")
        
        
# Test connection function
from .utils.logger import logger

def test_db_connection():
    try:
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            logger.info("Database connection successful!")
            return {"status": "success", "message": "Database connection successful!"}
    except Exception as e:
        logger.error("Database connection failed!")
        logger.exception(e)
        return {"status": "error", "message": str(e)}