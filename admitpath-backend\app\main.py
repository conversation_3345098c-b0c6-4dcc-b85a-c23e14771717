from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.routes import (
    health,
    db_test,
    auth,
    counselor_profile,
    counseling_session,
    promo_codes,
    review,
    counselor_dashboard,
    counselor_availability,
    counselor_services,
    counselor_packages,
    student_profile,
    student_dashboard,
    profile_picture,
    user_info,
    student_notes,
    student_bank_details,
    resource,
    student_packages,
    counselor_bank_details,
    contact,
    direct_message,
    websocket_chat,
    stripe_payment,
    newsletter,
    student_session,
    all_counselor_profiles
)
from app.models import User, Counselor, Student
from .database import engine, Base


app = FastAPI(
    title="AdmitPath API",
    description="Backend API for AdmitPath application",
    version="1.0.0",
    redirect_slashes=False,
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Temporarily allow all origins for testing
    allow_credentials=True,
    allow_methods=["*"],  # Allow all methods
    allow_headers=["*"],  # Allow all headers
    expose_headers=["*"]
)

from .database import test_db_connection
from .services.scheduler import start_scheduler, shutdown_scheduler

# Test database connection and start scheduler on startup
@app.on_event("startup")
def startup_event():
    test_db_connection()
    start_scheduler()

# Shutdown scheduler on application shutdown
@app.on_event("shutdown")
def shutdown_event():
    shutdown_scheduler()

# Include routes
app.include_router(health.router, tags=["Health"])
app.include_router(db_test.router, tags=["Database"])
app.include_router(auth.router, prefix="/auth", tags=["Authentication"])
app.include_router(user_info.router, prefix="/user", tags=["User Info"])
app.include_router(profile_picture.router, prefix="/user", tags=["Profile Picture"])
app.include_router(counselor_profile.router, prefix="/counselor/profile", tags=["Counselor Profile"])
app.include_router(counseling_session.router, prefix="/counselor/counseling-sessions", tags=["Counseling Sessions"])
app.include_router(review.router, tags=["Reviews"])
app.include_router(counselor_dashboard.router, prefix="/counselor/dashboard", tags=["Counselor Dashboard"])
app.include_router(counselor_availability.router, prefix="/counselor/availability", tags=["Counselor Availability"])
app.include_router(counselor_services.router, prefix="/counselor/services-offered", tags=["Counselor Services"])
app.include_router(counselor_packages.router, prefix="/counselor/packages", tags=["Counselor Packages"])
app.include_router(counselor_bank_details.router, prefix="/counselor/bank-details", tags=["Counselor Bank Details"])


# Student routes
app.include_router(student_profile.router, prefix="/student/profile", tags=["Student Profile"])
app.include_router(student_dashboard.router, prefix="/student/dashboard", tags=["Student Dashboard"])
app.include_router(student_notes.router, prefix="", tags=["Student Session Notes"])
app.include_router(student_bank_details.router, prefix="/bank-details", tags=["Student Bank Details"])
app.include_router(student_packages.router, prefix="/student/packages", tags=["Student Packages"])
app.include_router(student_session.router, prefix="/student/sessions", tags=["Student Sessions"])
# Resources
app.include_router(resource.router, prefix="/resources", tags=["Resources"])

# Contact form
app.include_router(contact.router, prefix="/contact", tags=["Contact"])

# Messages
app.include_router(direct_message.router, prefix="/messages", tags=["Messages"])
app.include_router(websocket_chat.router, tags=["WebSocket Chat"])

# Payments
app.include_router(stripe_payment.router, prefix="/payments", tags=["Payments"])

# Promo codes
app.include_router(promo_codes.router, prefix="/promo-codes", tags=["Promo Codes"])

# Newsletter
app.include_router(newsletter.router, prefix="/newsletter", tags=["Newsletter"])

# All counselors data with pagination and filters
app.include_router(all_counselor_profiles.router, prefix="/counselor/profiles", tags=["Counselor Profiles With All Data"])

# Create database tables
Base.metadata.create_all(bind=engine)


