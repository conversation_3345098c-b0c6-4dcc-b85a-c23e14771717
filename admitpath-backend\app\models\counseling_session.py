# app/models/counseling_session.py
from sqlalchemy import Column, Integer, String, Float, DateTime, Foreign<PERSON>ey, Text, Boolean, Enum as SQLAlchemyEnum
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum as PyEnum

from ..database import Base

class SessionStatus(PyEnum):
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    UPCOMING = "upcoming"

class CounselingSession(Base):
    __tablename__ = "counseling_sessions"

    id = Column(Integer, primary_key=True, index=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"))
    student_id = Column(Integer, ForeignKey("students.id"))
    package_subscription_id = Column(Integer, ForeignKey("student_package_subscriptions.id"), nullable=True)
    event_name = Column(String)
    description = Column(Text, nullable=True)
    meeting_link = Column(Text)
    zoom_meeting_id = Column(String)
    date = Column(DateTime(timezone=True))
    start_time = Column(DateTime(timezone=True))
    end_time = Column(DateTime(timezone=True))
    status = Column(String, default=SessionStatus.UPCOMING.value)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    counselor = relationship("Counselor", back_populates="sessions")
    student = relationship("Student", back_populates="sessions")
    session_notes = relationship("SessionNotes", back_populates="session", uselist=False)
    reviews = relationship("SessionReview", back_populates="session")
    payment = relationship("Payment", back_populates="session", uselist=False)
    student_session_notes = relationship("StudentSessionNotes", back_populates="session")
    stripe_payment = relationship("StripePayment", back_populates="session")
    package_subscription = relationship("StudentPackageSubscription", back_populates="sessions")
    reminders = relationship("SessionReminder", back_populates="session")

class SessionNotes(Base):
    __tablename__ = "session_notes"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("counseling_sessions.id"))
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship
    session = relationship("CounselingSession", back_populates="session_notes")

class SessionReview(Base):
    __tablename__ = "session_reviews"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("counseling_sessions.id"))
    student_id = Column(Integer, ForeignKey("students.id"))
    counselor_id = Column(Integer, ForeignKey("counselors.id"))
    rating = Column(Integer)  # 1-5 stars
    received_expected_service = Column(Boolean)  # Yes/No question
    comment = Column(Text)
    additional_feedback = Column(Text, nullable=True)  # Optional feedback
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    session = relationship("CounselingSession", back_populates="reviews")
    student = relationship("Student", back_populates="session_reviews")
    counselor = relationship("Counselor", back_populates="reviews")



class StudentSessionNotes(Base):
    __tablename__ = "student_session_notes"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("counseling_sessions.id"))
    student_id = Column(Integer, ForeignKey("students.id"))  # Added to ensure ownership
    student_notes = Column(Text)
    counselor_notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    session = relationship("CounselingSession", back_populates="student_session_notes")
    student = relationship("Student", back_populates="student_session_notes")
