# app/models/counselor_availability.py
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Date, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

class CounselorAvailability(Base):
    __tablename__ = "counselor_availability"
    
    id = Column(Integer, primary_key=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"))
    local_week_start = Column(Date)
    timezone = Column(String, nullable=False)
    available_slots = Column(JSON)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    counselor = relationship("Counselor", back_populates="availability")