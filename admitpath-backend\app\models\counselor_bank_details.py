# app/models/counselor_bank_details.py

from sqlalchemy import Column, Integer, String, DateTime, Foreign<PERSON>ey, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime

from ..database import Base

class CounselorBankAccount(Base):
    __tablename__ = "counselor_bank_accounts"

    id = Column(Integer, primary_key=True, index=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"), nullable=False)
    bank_name = Column(String, nullable=False)
    account_title = Column(String, nullable=False)
    account_number = Column(String, nullable=False)  # or IBAN
    swift_code = Column(String, nullable=False)
    is_primary = Column(Boolean, default=False)  # To mark primary account
    is_active = Column(Boolean, default=True)  # For soft delete
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship with Counselor
    counselor = relationship("Counselor", back_populates="bank_accounts")