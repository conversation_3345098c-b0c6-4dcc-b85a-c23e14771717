# models/counselor_packages.py
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey, Text, JSON
from sqlalchemy.dialects.postgresql import ARRAY, JSONB
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

class CounselorPackage(Base):
    __tablename__ = "counselor_packages"

    id = Column(Integer, primary_key=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"), nullable=False)
    title = Column(String, nullable=False)
    description = Column(Text, nullable=False)
    service_names = Column(JSONB, default=[])
    total_price = Column(Float, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    counselor = relationship("Counselor", back_populates="packages")
    package_items = relationship("PackageItem", back_populates="package", cascade="all, delete-orphan")
    subscriptions = relationship("StudentPackageSubscription", back_populates="package")


class PackageItem(Base):
    __tablename__ = "package_items"

    id = Column(Integer, primary_key=True)
    package_id = Column(Integer, ForeignKey("counselor_packages.id"))
    service_name = Column(String, nullable=False)  # Direct service name instead of foreign key
    hours = Column(Integer)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship
    package = relationship("CounselorPackage", back_populates="package_items")
