# app/models/counselor_profile.py
from sqlalchemy import Column, Integer, String, Boolean, Float, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base
from .user import Counselor
from enum import Enum

class CounselorCategoryType(str, Enum):
    TOP_IVY_LEAGUE = "top-ivy-league"
    RUSSEL_GROUP = "russel-group"  
    LIBERAL_ARTS = "liberal-arts"
    TOP_UCS = "top-ucs"

class FeaturedCounselor(Base):
    __tablename__ = "featured_counselors"
    
    id = Column(Integer, primary_key=True, index=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"), nullable=False)
    category = Column(String, nullable=False)
    
    
class CounselorPersonalInfo(Base):
    __tablename__ = "counselor_personal_info"
    id = Column(Integer, primary_key=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"), unique=True)
    nationality = Column(String, index=True)
    country_of_residence = Column(String)
    gender = Column(String)
    date_of_birth = Column(DateTime)
    bio = Column(Text, nullable=True)  # New optional field
    tagline = Column(String, nullable=True)  # New optional field for tagline
    profile_image_url = Column(String, nullable=True)  # New optional field for profile image

class CounselorEducation(Base):
    __tablename__ = "counselor_education"
    id = Column(Integer, primary_key=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"))
    university_name = Column(String, index=True)
    degree = Column(String)
    major = Column(String)
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    grade = Column(String)
    accomplishments = Column(String)

class CounselorProfessionalExperience(Base):
    __tablename__ = "counselor_professional_experience"
    id = Column(Integer, primary_key=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"))
    # linkedin_profile = Column(String)
    company_name = Column(String)
    role = Column(String)
    start_date = Column(DateTime)
    end_date = Column(DateTime)
    experience_description = Column(String)
    

class CounselorSocialLinks(Base):
    __tablename__ = "counselor_social_links"

    id = Column(Integer, primary_key=True, index=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"))
    linkedin_url = Column(String, nullable=True)
    portfolio_url = Column(String, nullable=True)
    website_url = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship
    counselor = relationship("Counselor", back_populates="social_links")
        
class CounselorCounselingExperience(Base):
    __tablename__ = "counselor_counseling_experience"
    id = Column(Integer, primary_key=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"))
    has_mentored_before = Column(Boolean, nullable=False)  # Yes/No field
    experience_description = Column(String)  # For the "Briefly describe your experience" field  

class CounselorServices(Base):
    __tablename__ = "counselor_services"
    id = Column(Integer, primary_key=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"))
    service_type = Column(String, index=True)  # e.g., "University Shortlisting"

class CounselorCommitment(Base):
    __tablename__ = "counselor_commitment"
    
    id = Column(Integer, primary_key=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"))
    hours_per_week = Column(String)  # For dropdown with options like "10+"
    hourly_rate = Column(Float, index=True)  # Expected price per hour
    
class CounselorDocuments(Base):
    __tablename__ = "counselor_documents"
    id = Column(Integer, primary_key=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"))
    document_name = Column(String)
    document_url = Column(String)