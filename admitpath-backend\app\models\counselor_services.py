from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON>an, DateTime, ForeignKey, Text, CheckConstraint, UniqueConstraint
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

# Standard service types as constants
class ServiceTypes:
    FREE_INTRO_CALL = "Free 15 Minutes Intro Call"
    PERSONAL_STATEMENT = "Personal Statement Guidance"
    ESSAY_REVIEW = "Essay Review"
    UNIVERSITY_SHORTLISTING = "University Shortlisting"
    EXTRA_CURRICULAR = "Extra Curricular Profile Building"
    SUPPLEMENTARY_ESSAY = "Supplementary Essay Guidance"
    FINANCIAL_AID = "Financial Aid Advice"
    INTERVIEW_PREP = "Interview Preparation"
    OTHER = "Other"

    @classmethod
    def list(cls):
        return [
            cls.FREE_INTRO_CALL,
            cls.PERSONAL_STATEMENT,
            cls.ESSAY_REVIEW,
            cls.UNIVERSITY_SHORTLISTING,
            cls.EXTRA_CURRICULAR,
            cls.SUPPLEMENTARY_ESSAY,
            cls.FINANCIAL_AID,
            cls.INTERVIEW_PREP,
            cls.OTHER
        ]


class CounselorServicesOffered(Base):
    __tablename__ = "counselor_services_offered"

    id = Column(Integer, primary_key=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"))
    service_type = Column(String, index=True)
    custom_type = Column(String, nullable=True)
    description = Column(Text)
    price = Column(Float)
    offers_intro_call = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Updated table arguments to include both constraints
    __table_args__ = (
        CheckConstraint(
            "(service_type != 'Other') OR (custom_type IS NOT NULL)",
            name="check_custom_type_for_other"
        ),
        # Add unique constraint for both standard and custom service types
        # This allows multiple "Other" type services as long as custom_type is different
        UniqueConstraint(
            'counselor_id', 'service_type', 'custom_type',
            name='unique_service_per_counselor'
        ),
    )
    
    # No longer need the display_name property since we're using service_type directly

    counselor = relationship("Counselor", back_populates="services_offered")
    payments = relationship("Payment", back_populates="service")
