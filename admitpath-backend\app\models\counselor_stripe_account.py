# from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DateTime, Enum as SQLAlchemyEnum
# from sqlalchemy.orm import relationship
# from datetime import datetime
# from enum import Enum as PyEnum

# from ..database import Base

# class StripeAccountStatus(PyEnum):
#     PENDING = "pending"
#     ACTIVE = "active"
#     RESTRICTED = "restricted"
#     DISABLED = "disabled"

# class CounselorStripeAccount(Base):
#     __tablename__ = "counselor_stripe_accounts"

#     id = Column(Integer, primary_key=True)
#     counselor_id = Column(Integer, ForeignKey("counselors.id"), unique=True)
#     stripe_account_id = Column(String, unique=True)  # Stripe Connect account ID
#     account_status = Column(String, default=StripeAccountStatus.PENDING.value)
#     charges_enabled = Column(Boolean, default=False)
#     payouts_enabled = Column(Boolean, default=False)
#     default_currency = Column(String)
#     country = Column(String)
    
#     # Bank account details (encrypted in production)
#     bank_name = Column(String)
#     account_holder_name = Column(String)
#     account_number_last4 = Column(String)  # Store only last 4 digits
#     routing_number = Column(String)
#     swift_code = Column(String)
    
#     is_verified = Column(Boolean, default=False)
#     verification_status = Column(String)
#     verification_fields_needed = Column(String)  # JSON string of required fields
    
#     created_at = Column(DateTime, default=datetime.utcnow)
#     updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

#     # Relationships
#     counselor = relationship("Counselor", back_populates="stripe_account")

# class StripePayoutLog(Base):
#     __tablename__ = "stripe_payout_logs"

#     id = Column(Integer, primary_key=True)
#     counselor_id = Column(Integer, ForeignKey("counselors.id"))
#     stripe_payout_id = Column(String, unique=True)
#     amount = Column(Integer)  # Amount in cents
#     currency = Column(String)
#     status = Column(String)
#     arrival_date = Column(DateTime)
    
#     created_at = Column(DateTime, default=datetime.utcnow)
#     updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)