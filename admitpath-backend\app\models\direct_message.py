from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from ..database import Base
from datetime import datetime

class DirectMessage(Base):
    __tablename__ = "direct_messages"

    id = Column(Integer, primary_key=True, index=True)
    counselor_id = Column(Integer, ForeignKey("counselors.id"), nullable=False)
    channel_id = Column(String, nullable=True, index=True)  # Add this line
    student_id = Column(Integer, ForeignKey("students.id"), nullable=False)
    sender_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    message = Column(Text, nullable=False)
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow)
    
    # Create a composite index on counselor_id and student_id for faster lookups
    __table_args__ = (
        Index('idx_counselor_student', 'counselor_id', 'student_id'),
        Index('idx_channel', 'channel_id'),  # Add this line
    )
    
    # Relationships
    counselor = relationship("Counselor", backref="messages")
    student = relationship("Student", backref="messages")
    sender = relationship("User", backref="sent_messages")