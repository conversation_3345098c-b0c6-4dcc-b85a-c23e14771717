from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>umn, Integer, String, Float, DateTime, ForeignKey, Enum as SQLAlchemyEnum, Text
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum as PyEnum

from ..database import Base

class PaymentStatus(PyEnum):
    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"

class CounselorPayoutStatus(PyEnum):
    PENDING = "pending"           # Initial state after student payment
    APPROVED = "approved"         # After confirmation, ready to pay counselor
    PAID_OUT = "paid_out"         # Counselor has been paid
    CANCELLED = "cancelled"       # Session was cancelled, no payment due

class ServiceType(PyEnum):
    SESSION = "session"
    PACKAGE = "package"
    SERVICE = "service"

class PromoCodeType(PyEnum):
    AMOUNT = "amount"      # Fixed amount discount
    PERCENTAGE = "percentage"  # Percentage-based discount
    FIXED_PRICE = "fixed_price"  # Sets a fixed price for service

class PromoCodeStatus(PyEnum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"

class Payment(Base):
    __tablename__ = "payments"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("counseling_sessions.id"), nullable=True)
    package_id = Column(Integer, ForeignKey("student_package_subscriptions.id"), nullable=True)
    service_id = Column(Integer, ForeignKey("counselor_services_offered.id"), nullable=True)
    service_type = Column(String, nullable=False)
    promo_code_id = Column(Integer, ForeignKey("promo_codes.id"), nullable=True)
    amount = Column(Float)
    original_amount = Column(Float, nullable=True)  # Original price before discount
    discount_amount = Column(Float, nullable=True)  # Amount of discount applied
    is_discounted = Column(Boolean, default=False)
    status = Column(String, default=PaymentStatus.PENDING.value)
    description = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    stripe_session_id = Column(String, nullable=True)

    # Counselor payout tracking
    counselor_payout_status = Column(String, default=CounselorPayoutStatus.PENDING.value)
    counselor_payout_date = Column(DateTime, nullable=True)
    counselor_payout_amount = Column(Float, nullable=True)
    counselor_payout_id = Column(String, nullable=True)  # Stripe payout ID or internal reference
    counselor_payout_notes = Column(Text, nullable=True)

    # Session creation metadata (stored until payment completes)
    counselor_id = Column(Integer, ForeignKey("counselors.id"), nullable=True)
    student_id = Column(Integer, ForeignKey("students.id"), nullable=True)
    student_email = Column(String, nullable=True)
    event_name = Column(String, nullable=True)
    description_text = Column(Text, nullable=True)
    date = Column(DateTime, nullable=True)
    start_time = Column(DateTime, nullable=True)
    end_time = Column(DateTime, nullable=True)

    # Relationships
    session = relationship("CounselingSession", back_populates="payment")
    package = relationship("StudentPackageSubscription", back_populates="payments")
    service = relationship("CounselorServicesOffered", back_populates="payments")
    promo_code = relationship("PromoCode", back_populates="payments")

class PromoCode(Base):
    __tablename__ = "promo_codes"

    id = Column(Integer, primary_key=True, index=True)

    # Core Identification
    code = Column(String, unique=True, nullable=False, index=True)

    # Discount Specifics
    type = Column(String, nullable=False)  # PromoCodeType
    amount = Column(Float, nullable=True)  # Discount amount or fixed price
    percentage = Column(Float, nullable=True)  # Percentage discount (0-100)

    # Validity Constraints
    valid_from = Column(DateTime(timezone=True), nullable=False, default=datetime.utcnow)
    valid_to = Column(DateTime(timezone=True), nullable=False)

    # Usage Limitations
    max_uses = Column(Integer, default=1)  # Maximum times code can be used
    current_uses = Column(Integer, default=0)  # Tracks current number of uses

    # Scope Restrictions
    counselor_ids = Column(ARRAY(Integer), nullable=True, default=[])

    # Additional Constraints
    min_purchase_amount = Column(Float, nullable=True)  # Minimum purchase required

    # Availability and Status
    status = Column(String, default=PromoCodeStatus.ACTIVE.value)

    # Tracking and Audit
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    payments = relationship("Payment", back_populates="promo_code")