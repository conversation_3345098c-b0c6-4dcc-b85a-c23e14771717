from sqlalchemy import Column, Inte<PERSON>, String, DateTime, <PERSON><PERSON><PERSON>, <PERSON>olean
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum as PyEnum

from ..database import Base

class ReminderStatus(PyEnum):
    PENDING = "pending"
    SENT = "sent"
    FAILED = "failed"
    CANCELLED = "cancelled"

class ReminderType(PyEnum):
    SESSION_24H = "session_24h"
    SESSION_1H = "session_1h"

class SessionReminder(Base):
    __tablename__ = "session_reminders"

    id = Column(Integer, primary_key=True, index=True)
    session_id = Column(Integer, ForeignKey("counseling_sessions.id"))
    recipient_type = Column(String)  # "student" or "counselor"
    recipient_email = Column(String)
    reminder_type = Column(String)  # "24h" or "1h"
    scheduled_time = Column(DateTime(timezone=True))
    status = Column(String, default=ReminderStatus.PENDING.value)
    sent_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship
    session = relationship("CounselingSession", back_populates="reminders")
