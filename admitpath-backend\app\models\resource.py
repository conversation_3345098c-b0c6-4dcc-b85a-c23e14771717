# app/models/resource.py
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

class Resource(Base):
    __tablename__ = "resources"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    file_url = Column(String, nullable=False)  # URL/path to stored document
    file_type = Column(String, nullable=False)  # e.g., pdf, doc, docx
    image_url = Column(String, nullable=True)  # URL/path to thumbnail image
    category = Column(String, nullable=False)  # e.g., policy, training, onboarding
    audience = Column(String, default="public")  # "public", "counselor", "student"
    uploaded_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
