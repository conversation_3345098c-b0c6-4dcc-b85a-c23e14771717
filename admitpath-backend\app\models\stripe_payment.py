# app/models/stripe_payment.py
from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Enum as SQLAlchemyEnum
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum as PyEnum

from ..database import Base

class StripePaymentStatus(PyEnum):
    PENDING = "pending"
    SUCCEEDED = "succeeded"
    FAILED = "failed"
    REFUNDED = "refunded"
    REQUIRES_PAYMENT_METHOD = "requires_payment_method"
    REQUIRES_CONFIRMATION = "requires_confirmation"
    CANCELED = "canceled"

class StripePayment(Base):
    __tablename__ = "stripe_payments"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey("students.id"))
    session_id = Column(Integer, ForeignKey("counseling_sessions.id"))
    amount = Column(Float, nullable=False)  # Amount in USD
    status = Column(String, default=StripePaymentStatus.PENDING.value)
    stripe_payment_intent_id = Column(String, unique=True)
    stripe_client_secret = Column(String)
    payment_metadata = Column(String)  # JSON string for additional data
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    student = relationship("Student", back_populates="stripe_payments")
    session = relationship("CounselingSession", back_populates="stripe_payment")