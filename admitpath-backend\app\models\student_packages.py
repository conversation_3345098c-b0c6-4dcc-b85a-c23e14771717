# models/student_packages.py
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, JSON
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum as PyEnum

from ..database import Base

class PackageSubscriptionStatus(PyEnum):
    PENDING = "pending"
    ACTIVE = "active"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    EXPIRED = "expired"

class StudentPackageSubscription(Base):
    __tablename__ = "student_package_subscriptions"

    id = Column(Integer, primary_key=True, index=True)
    student_id = Column(Integer, ForeignKey("students.id"))
    package_id = Column(Integer, ForeignKey("counselor_packages.id"))
    status = Column(String, default=PackageSubscriptionStatus.PENDING.value)  # Default to PENDING until payment is received
    start_date = Column(DateTime(timezone=True), default=datetime.utcnow)
    end_date = Column(DateTime(timezone=True), nullable=True)
    service_hours = Column(JSON, nullable=False)  # Format: {"service_name": {"total": X, "used": Y}}
    created_at = Column(DateTime(timezone=True), default=datetime.utcnow)
    updated_at = Column(DateTime(timezone=True), default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    student = relationship("Student", back_populates="package_subscriptions")
    package = relationship("CounselorPackage", back_populates="subscriptions")
    sessions = relationship("CounselingSession", back_populates="package_subscription")
    payments = relationship("Payment", back_populates="package")

    def get_hours(self, service_name: str) -> int:
        if service_name not in self.service_hours:
            return 0
        service_data = self.service_hours[service_name]
        return service_data["total"]

    def increment_used_hours(self, service_name: str, hours: float = 1) -> bool:
        """Increment the used hours for a specific service and mark the field as modified.

        Args:
            service_name: The name of the service to increment hours for
            hours: The number of hours to add (can be a float for partial hours)

        Returns:
            bool: True if successful, False if service not found or not enough hours remaining
        """
        if service_name not in self.service_hours:
            return False

        self.service_hours[service_name]["used"] = round(min(self.service_hours[service_name]["used"] + hours, self.get_hours(service_name)), 2)

        try:
            from sqlalchemy.orm.attributes import flag_modified
            flag_modified(self, "service_hours")
        except ImportError:
            pass

        return True
