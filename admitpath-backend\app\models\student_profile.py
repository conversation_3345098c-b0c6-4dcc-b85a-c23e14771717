# app/models/student_profile.py
from sqlalchemy import Column, Inte<PERSON>, String, <PERSON>olean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

class StudentPersonalInfo(Base):
    __tablename__ = "student_personal_info"
    id = Column(Integer, primary_key=True)
    student_id = Column(Integer, ForeignKey("students.id"))
    nationality = Column(String, index=True)
    country_of_residence = Column(String)
    gender = Column(String)
    date_of_birth = Column(DateTime, nullable=True)
    goals = Column(String, nullable=True)  
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship
    student = relationship("Student", back_populates="personal_info")

class StudentEducationalBackground(Base):
    __tablename__ = "student_educational_background"
    id = Column(Integer, primary_key=True)
    student_id = Column(Inte<PERSON>, <PERSON><PERSON><PERSON>("students.id"))
    current_education_level = Column(String)
    institution_name = Column(String)  # Changed from school_name/university_name to be more generic
    institution_type = Column(String)  # "school" or "university"
    start_date = Column(DateTime, nullable=True)  # Added these fields for better tracking
    end_date = Column(DateTime, nullable=True)
    is_current = Column(Boolean, default=False)  # To mark current institution
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship
    student = relationship("Student", back_populates="educational_background") 

class StudentExpectedServices(Base):
    __tablename__ = "student_expected_services"
    id = Column(Integer, primary_key=True)
    student_id = Column(Integer, ForeignKey("students.id"))
    service_type = Column(String, index=True)  # Multiple services can be stored as JSON or separate rows
    # additional_details = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship
    student = relationship("Student", back_populates="expected_services")