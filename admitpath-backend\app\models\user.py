from sqlalchemy import Column, Integer, String, Boolean, Enum, Float, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import datetime
from ..database import Base
from .counselor_availability import CounselorAvailability  # Add this import


class UserType(PyEnum):
    COUNSELOR = "counselor" 
    STUDENT = "student"

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    password_hash = Column(String)
    first_name = Column(String)  
    last_name = Column(String)   
    user_type = Column(String)
    profile_picture_url = Column(String, nullable=True) 
    is_active = Column(Boolean, default=True)
    timezone = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    
    
class Counselor(Base):
    __tablename__ = "counselors"
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    profile_completion_status = Column(String)  # JSON string tracking completion of each section
    is_profile_complete = Column(Boolean, default=False)
    is_verified = Column(Boolean, default=False)
    rank = Column(Integer, nullable=True)  # int4 in Supabase, nullable, lower number = higher rank
    
    # Add relationships
    personal_info = relationship("CounselorPersonalInfo", backref="counselor", uselist=False)
    education = relationship("CounselorEducation", backref="counselor")
    professional_experience = relationship("CounselorProfessionalExperience", backref="counselor")
    services = relationship("CounselorServices", backref="counselor")
    documents = relationship("CounselorDocuments", backref="counselor")
    counseling_experience = relationship("CounselorCounselingExperience", backref="counselor", uselist=False)
    commitment = relationship("CounselorCommitment", backref="counselor", uselist=False)
    sessions = relationship("CounselingSession", back_populates="counselor")
    reviews = relationship("SessionReview", back_populates="counselor")
    availability = relationship("CounselorAvailability", back_populates="counselor")
    services_offered = relationship("CounselorServicesOffered", back_populates="counselor")
    packages = relationship("CounselorPackage", back_populates="counselor")  # Add this line
    social_links = relationship("CounselorSocialLinks", back_populates="counselor", uselist=False)
    # stripe_account = relationship("CounselorStripeAccount", back_populates="counselor", uselist=False)
    bank_accounts = relationship("CounselorBankAccount", back_populates="counselor")
           
class Student(Base):
    __tablename__ = "students"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    profile_completion_status = Column(String)  # JSON string tracking completion of each section
    is_profile_complete = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    personal_info = relationship("StudentPersonalInfo", back_populates="student", uselist=False)
    educational_background = relationship("StudentEducationalBackground", back_populates="student", uselist=False)
    expected_services = relationship("StudentExpectedServices", back_populates="student")
    sessions = relationship("CounselingSession", back_populates="student")
    session_reviews = relationship("SessionReview", back_populates="student")    
    student_session_notes = relationship("StudentSessionNotes", back_populates="student")
    bank_accounts = relationship("StudentBankAccount", back_populates="student")
    package_subscriptions = relationship("StudentPackageSubscription", back_populates="student")
    stripe_payments = relationship("StripePayment", back_populates="student")