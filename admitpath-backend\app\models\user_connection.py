from sqlalchemy import <PERSON>umn, Integer, String, DateTime, <PERSON>olean, ForeignKey, Index
from sqlalchemy.orm import relationship
from datetime import datetime
from ..database import Base

class UserConnection(Base):
    """
    Tracks user connection status for the chat system.
    Used to determine when to send email notifications.
    """
    __tablename__ = "user_connections"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    channel_id = Column(String, nullable=False, index=True)
    last_connected_at = Column(DateTime(timezone=True), default=datetime.now)
    is_connected = Column(Boolean, default=True)
    
    __table_args__ = (
        Index('idx_user_channel', 'user_id', 'channel_id'),
    )
    
    user = relationship("User", backref="connections")
