from fastapi import APIRouter, Depends, Query
from pydantic import BaseModel
from sqlalchemy.orm import Session, joinedload, subqueryload
from typing import Optional, List
from math import ceil
from datetime import datetime
from sqlalchemy import or_

from ..database import get_db
from ..models.user import Counselor, User
from ..models.counselor_profile import *
from ..schemas.counselor_profile import *
from ..models.counselor_services import *
from ..models.counselor_packages import *
from ..routes.counselor_profile import *

router = APIRouter()

class PaginatedCounselorListResponse(BaseModel):
    items: List[PublicCounselorProfileResponse]
    total: int
    page: int
    pages: int
    has_next: bool
    has_previous: bool

@router.get("/public-profile/list", response_model=PaginatedCounselorListResponse)
async def list_public_counselor_profiles(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=50),
    countries: Optional[str] = None,
    universities: Optional[str] = None,
    # min_hourly_rate: Optional[float] = None,
    # max_hourly_rate: Optional[float] = None,
    service_types: Optional[str] = None,
    name: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Get paginated list of all counselor profiles with optional filtering.
    This endpoint is publicly accessible.

    Filter parameters countries, universities, and service_types accept comma-separated values
    to filter for multiple options (e.g., "Singapore,Bangladesh,India").

    The name parameter allows searching for counselors by their name (searches in both first and last name).
    """
    offset = (page - 1) * limit

    query = (
        db.query(Counselor, User)
        .join(User, Counselor.user_id == User.id)
        .options(
            # Eager load Counselor relationships using existing backrefs
            joinedload(Counselor.personal_info),
            joinedload(Counselor.commitment),
            joinedload(Counselor.counseling_experience),
            joinedload(Counselor.social_links),
            subqueryload(Counselor.education),
            subqueryload(Counselor.professional_experience),
            subqueryload(Counselor.services_offered),
            subqueryload(Counselor.packages).joinedload(CounselorPackage.package_items)
        )
        .filter(Counselor.is_verified == True)
    )

    # Handle multiple countries filter
    if countries:
        country_list = [c.strip() for c in countries.split(",")]
        query = query.join(CounselorPersonalInfo).filter(
            CounselorPersonalInfo.country_of_residence.in_(country_list))

    # Handle multiple universities filter
    if universities:
        university_list = [u.strip() for u in universities.split(",")]
        university_filters = []
        for uni in university_list:
            university_filters.append(CounselorEducation.university_name.ilike(f"%{uni}%"))

        query = query.join(CounselorEducation).filter(or_(*university_filters))

    # Pricing filter not needed for now
    # if min_hourly_rate is not None or max_hourly_rate is not None:
    #     query = query.join(CounselorCommitment)

    #     if min_hourly_rate is not None:
    #         query = query.filter(CounselorCommitment.hourly_rate >= min_hourly_rate)

    #     if max_hourly_rate is not None:
    #         query = query.filter(CounselorCommitment.hourly_rate <= max_hourly_rate)

    # Handle multiple service types filter
    if service_types:
        service_type_list = [s.strip() for s in service_types.split(",")]
        query = query.join(CounselorServicesOffered).filter(
            CounselorServicesOffered.service_type.in_(service_type_list))

    # Handle name filter
    if name and name.strip():
        name_search = name.strip().lower()
        query = query.filter(
            or_(
                User.first_name.ilike(f"%{name_search}%"),
                User.last_name.ilike(f"%{name_search}%"),
                # Also search in combined first and last name
                (User.first_name + " " + User.last_name).ilike(f"%{name_search}%")
            )
        )

    # Order by rank (nulls last), then by user_id for consistent ordering
    query = query.distinct().order_by(
        Counselor.rank.nullslast(),
        Counselor.user_id
    )

    total_items = query.count()

    results = query.offset(offset).limit(limit).all()

    items = []
    for counselor, user in results:
        # Get pre-loaded relationships
        personal_info = counselor.personal_info
        education = counselor.education
        professional_exp = counselor.professional_experience
        counseling_exp = counselor.counseling_experience
        services = counselor.services_offered
        packages = counselor.packages
        commitment = counselor.commitment
        social_links = counselor.social_links

        education_data = [{
            "university_name": edu.university_name,
            "degree": edu.degree,
            "major": edu.major,
            "start_date": format_date(edu.start_date),
            "end_date": format_date(edu.end_date),
            "grade": edu.grade,
            "accomplishments": edu.accomplishments
        } for edu in education] if education else []

        # Calculate hourly rate based on services
        hourly_rate = None
        if services:
            # Filter out None values and 0 prices
            service_prices = [float(service.price) for service in services if float(service.price) > 0]
            if service_prices:  # If there are any non-zero prices
                hourly_rate = min(service_prices)  # Get the minimum non-zero price

        # If no services with valid prices, fall back to commitment hourly rate
        if hourly_rate is None or hourly_rate == 0:
            hourly_rate = float(commitment.hourly_rate) if commitment and commitment.hourly_rate else None

        if hourly_rate is not None:
            hourly_rate = round(hourly_rate)

        counselor_data = {
            "counselor_id": counselor.id,
            "user_id": user.id,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "profile_picture_url": user.profile_picture_url,
            "nationality": personal_info.nationality if personal_info else None,
            "country_of_residence": personal_info.country_of_residence if personal_info else None,
            "gender": personal_info.gender if personal_info else None,
            "date_of_birth": format_date(personal_info.date_of_birth) if personal_info else None,
            "bio": personal_info.bio if personal_info else None,
            "tagline": personal_info.tagline if personal_info else None,
            "education": education_data,
            "professional_experience": [{
                "role": exp.role,
                "company_name": exp.company_name,
                "start_date": format_date(exp.start_date),
                "end_date": format_date(exp.end_date),
                "experience_description": exp.experience_description
            } for exp in professional_exp] if professional_exp else [],
            "has_mentored_before": counseling_exp.has_mentored_before if counseling_exp else None,
            "experience_description": counseling_exp.experience_description if counseling_exp else None,
            "services": services,
            "packages": packages,
            "hours_per_week": get_average_hours(commitment.hours_per_week) if commitment and commitment.hours_per_week else None,
            "hourly_rate": hourly_rate,
            "linkedin_url": social_links.linkedin_url if social_links else None,
            "portfolio_url": social_links.portfolio_url if social_links else None,
            "website_url": social_links.website_url if social_links else None,
        }
        items.append(counselor_data)

    total_pages = ceil(total_items / limit) if total_items > 0 else 0

    return {
        "items": items,
        "total": total_items,
        "page": page,
        "pages": total_pages,
        "has_next": page < total_pages,
        "has_previous": page > 1
    }

def format_date(date: Optional[datetime]) -> Optional[str]:
    return date.strftime("%Y-%m-%d") if date else None

def get_average_hours(hours_range: Optional[str]) -> Optional[float]:
    if not hours_range:
        return None
    try:
        low, high = map(int, hours_range.split("-"))
        return (low + high) / 2
    except:
        return None


@router.get("/public-profile/list/featured", response_model=FeaturedCounselorsResponse)
async def list_featured_counselors(
    category: str = Query(..., description="Category category (top-ivy-league, russel-group, liberal-arts, top-ucs)"),
    db: Session = Depends(get_db)
):
    """
    Get list of featured counselors by category category.
    This endpoint is publicly accessible.

    Valid category categorys:
    - top-ivy-league
    - russel-group
    - liberal-arts
    - top-ucs
    """

    # Validate category parameter
    valid_categories = ["top-ivy-league", "russel-group", "liberal-arts", "top-ucs"]
    if category not in valid_categories:
        raise HTTPException(status_code=400, detail=f"Invalid category. Must be one of: {', '.join(valid_categories)}")

    query = (
        db.query(Counselor, User, FeaturedCounselor)
        .join(FeaturedCounselor, Counselor.id == FeaturedCounselor.counselor_id)
        .join(User, Counselor.user_id == User.id)
        .options(
            joinedload(Counselor.personal_info),
            joinedload(Counselor.commitment),
            joinedload(Counselor.services_offered),
            joinedload(Counselor.education)
        )
        .filter(
            FeaturedCounselor.category == category,
            Counselor.is_verified == True
        )
    )

    results = query.all()

    items = []
    for counselor, user, featured in results:
        # Get the needed relationships
        personal_info = counselor.personal_info
        services = counselor.services_offered
        commitment = counselor.commitment

        # Get the latest education (sort by end date descending)
        education = None
        if counselor.education:
            sorted_education = sorted(
                counselor.education,
                key=lambda edu: edu.end_date if edu.end_date else "1900-01-01",
                reverse=True
            )
            if sorted_education:
                education = sorted_education[0]

        # Calculate hourly rate based on services
        hourly_rate = None
        if services:
            # Filter out None values and 0 prices
            service_prices = [float(service.price) for service in services if float(service.price) > 0]
            if service_prices:  # If there are any non-zero prices
                hourly_rate = min(service_prices)  # Get the minimum non-zero price

        # If no services with valid prices, fall back to commitment hourly rate
        if hourly_rate is None or hourly_rate == 0:
            hourly_rate = float(commitment.hourly_rate) if commitment and commitment.hourly_rate else None

        if hourly_rate is not None:
            hourly_rate = round(hourly_rate)

        # Build the education data object if education exists
        education_data = None
        if education:
            education_data = {
                "university_name": education.university_name,
                "degree": education.degree,
                "major": education.major,
                "start_date": format_date(education.start_date),
                "end_date": format_date(education.end_date)
            }

        counselor_data = {
            "user_id": user.id,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "profile_picture_url": user.profile_picture_url,
            "country_of_residence": personal_info.country_of_residence if personal_info else None,
            "tagline": personal_info.tagline if personal_info else None,
            "bio": personal_info.bio if personal_info else None,
            "education": [education_data] if education_data else None,
            "hourly_rate": hourly_rate
        }
        items.append(counselor_data)

    return {
        "category": category,
        "items": items
    }