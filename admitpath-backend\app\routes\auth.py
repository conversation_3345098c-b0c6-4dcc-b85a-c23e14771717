from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.security import OAuth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from typing import Optional
from datetime import timedelta, datetime
import random
import string
import re
import json

from ..database import get_db
from ..models.user import User, Counselor, Student
from ..schemas.user import *
from ..utils.auth import *
from ..utils.email_utils import send_email_ses
from ..utils.email_types import EmailType
from ..utils.logger import logger
from ..utils.google_auth import verify_google_token
from ..utils.linkedin_auth import *

router = APIRouter()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token")

@router.post("/signup/student", response_model=StudentResponse)
async def register_student(
    user_data: StudentSignup,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    # Check if user exists
    if db.query(User).filter(User.email == user_data.email).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Create user with profile_picture_url
    db_user = User(
        email=user_data.email,
        first_name=user_data.first_name,
        last_name=user_data.last_name,
        password_hash=get_password_hash(user_data.password),
        user_type="student",
        profile_picture_url=user_data.profile_picture_url if hasattr(user_data, 'profile_picture_url') else None
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    # Initialize profile completion status
    initial_completion_status = {
        "personal_info": False,
        "educational_background": False,
        "expected_services": False
    }
    
    # Create student profile
    db_student = Student(
        user_id=db_user.id,
        profile_completion_status=json.dumps(initial_completion_status),
        is_profile_complete=False
    )
    db.add(db_student)
    db.commit()
    db.refresh(db_student)
    
    # Schedule admin notification in background
    logger.info(f"Scheduling regular signup student notification for {db_user.email}")
    background_tasks.add_task(
        send_email_ses,
        to_email=["<EMAIL>", "<EMAIL>"],
        email_type=EmailType.NOTIFY_ADMIN_STUDENT_SIGNUP.value,
        user_data={
            "first_name": db_user.first_name,
            "last_name": db_user.last_name,
            "email": db_user.email,
            "signup_time": datetime.utcnow().isoformat(),
            "signup_method": "email"
        }
    )

    # Return user data with student profile info
    return {
        "id": db_user.id,
        "email": db_user.email,
        "first_name": db_user.first_name,
        "last_name": db_user.last_name,
        "profile_picture_url": db_user.profile_picture_url,
        "profile_completion_status": initial_completion_status,
        "is_profile_complete": False
    }
    
@router.post("/signup/counselor", response_model=CounselorResponse)
async def register_counselor(
    user_data: CounselorSignup,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    # Check if user exists
    if db.query(User).filter(User.email == user_data.email).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Create user with profile_picture_url
    db_user = User(
        email=user_data.email,
        first_name=user_data.first_name,
        last_name=user_data.last_name,
        password_hash=get_password_hash(user_data.password),
        user_type="counselor",
        profile_picture_url=user_data.profile_picture_url if hasattr(user_data, 'profile_picture_url') else None
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    # Create counselor profile with default values
    initial_completion_status = {
        "personal_info": False,
        "education": False,
        "professional_experience": False,
        "counseling_experience": False,
        "services": False,
        "documents": False
    }
    
    db_counselor = Counselor(
        user_id=db_user.id,
        profile_completion_status=json.dumps(initial_completion_status),
        is_profile_complete=False,
        is_verified=False
    )
    db.add(db_counselor)
    db.commit()
    db.refresh(db_user)
    
    # Schedule admin notification in background
    logger.info(f"Scheduling regular signup counselor notification for {db_user.email}")
    background_tasks.add_task(
        send_email_ses,
        to_email=["<EMAIL>", "<EMAIL>"],
        email_type=EmailType.NOTIFY_ADMIN_COUNSELOR_SIGNUP.value,
        user_data={
            "first_name": db_user.first_name,
            "last_name": db_user.last_name,
            "email": db_user.email,
            "signup_time": datetime.utcnow().isoformat(),
            "signup_method": "email"
        }
    )

    # Return user data with counselor profile info
    return {
        "id": db_user.id,
        "email": db_user.email,
        "first_name": db_user.first_name,
        "last_name": db_user.last_name,
        "profile_picture_url": db_user.profile_picture_url,
        "profile_completion_status": initial_completion_status,
        "is_profile_complete": False,
        "is_verified": False
    }
    
@router.post("/signin")
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    # Authenticate user
    user = db.query(User).filter(User.email == form_data.username).first()
    if not user or not verify_password(form_data.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create access token
    access_token = create_access_token(
        data={"sub": user.email, "user_type": user.user_type},
        expires_delta=timedelta(hours=ACCESS_TOKEN_EXPIRE_HOURS)
    )
    
    return {
        "access_token": access_token, 
        "token_type": "bearer",
        "user_type": user.user_type
    }

    
# Store verification codes temporarily (in production, use Redis or similar)
verification_codes = {}

@router.post("/forgot-password")
async def request_password_reset(
    request: ForgotPasswordRequest,
    db: Session = Depends(get_db)
):
    user = db.query(User).filter(User.email == request.email).first()
    print("Email given: ", request.email, "User: ", user)
    if not user:
        # For security, don't reveal if email exists or not
        return {"message": "If an account exists with this email, you will receive a verification code"}
    
    # Generate 6-digit verification code
    verification_code = ''.join(random.choices(string.digits, k=6))
    
    # Store code with timestamp (expires in 10 minutes)
    verification_codes[request.email] = {
        'code': verification_code,
        'expires_at': datetime.utcnow() + timedelta(minutes=10)
    }
    
    # Send email with verification code
    try:
        logger.info(f"Sending password reset verification code to {request.email}")
        await send_email_ses(
            to_email=request.email,
            email_type=EmailType.PASSWORD_RESET.value,
            otp=verification_code
        )
        logger.info(f"Successfully sent password reset verification code to {request.email}")
        return {
            "message": "Verification code sent to email",
            "code": verification_code  # Remove in production
        }
    except HTTPException as e:
        # Log the error but don't expose it to the user
        logger.error(f"Failed to send password reset email to {request.email}: {str(e)}")
        return {
            "message": "If an account exists with this email, you will receive a verification code"
        }
        
@router.post("/verify-code")
async def verify_reset_code(request: VerificationRequest):
    stored_data = verification_codes.get(request.email)
    
    if not stored_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No verification code found or code expired"
        )
    
    if datetime.utcnow() > stored_data['expires_at']:
        del verification_codes[request.email]
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Verification code has expired"
        )
    
    if request.code != stored_data['code']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid verification code"
        )
    
    return {"message": "Code verified successfully"}

@router.post("/reset-password")
async def reset_password(
    request: ResetPasswordRequest,
    db: Session = Depends(get_db)
):
    # Verify the code is still valid
    stored_data = verification_codes.get(request.email)
    if not stored_data or stored_data['code'] != request.code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired verification code"
        )
    
    if datetime.utcnow() > stored_data['expires_at']:
        del verification_codes[request.email]
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Verification code has expired"
        )
    
    # Update the password
    user = db.query(User).filter(User.email == request.email).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Password validation is handled by the Pydantic model
    user.password_hash = get_password_hash(request.new_password)
    db.commit()
    
    # Clean up the verification code
    del verification_codes[request.email]
    
    return {"message": "Password reset successfully"}



@router.post("/google", response_model=OAuthResponse)
async def google_auth(
    request: GoogleAuthRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    # Verify Google token and get user info
    user_info = await verify_google_token(request.token)
    
    # Check if user exists
    user = db.query(User).filter(User.email == user_info['email']).first()
    is_new_user = user is None
    
    if user:
        # Login case - user exists
        if request.user_type is not None and user.user_type != request.user_type:
            # If user_type is provided (signup attempt) but doesn't match
            raise HTTPException(
                status_code=400,
                detail=f"Email already registered as a {user.user_type}"
            )
    else:
        # Signup case - new user
        if request.user_type is None:
            # If no user_type provided for new user
            raise HTTPException(
                status_code=400,
                detail="user_type is required for registration"
            )
            
        # Create new user
        user = User(
            email=user_info['email'],
            first_name=user_info['first_name'],
            last_name=user_info['last_name'],
            user_type=request.user_type,
            password_hash=None  # OAuth users don't need passwords
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        
        # Create specific profile based on user type
        if request.user_type == UserType.COUNSELOR:
            initial_completion_status = {
                "personal_info": False,
                "education": False,
                "professional_experience": False,
                "counseling_experience": False,
                "services": False,
                "documents": False
            }
            
            counselor = Counselor(
                user_id=user.id,
                profile_completion_status=json.dumps(initial_completion_status),
                is_profile_complete=False,
                is_verified=False
            )
            db.add(counselor)
            db.commit()
            
            # Send admin notification
            admin_emails = ["<EMAIL>", "<EMAIL>"]
            logger.info(f"Scheduling Google OAuth counselor signup notification for {user.email}")
            background_tasks.add_task(
                send_email_ses,
                to_email=admin_emails,
                email_type=EmailType.NOTIFY_ADMIN_COUNSELOR_SIGNUP.value,
                user_data={
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "email": user.email,
                    "signup_time": datetime.utcnow().isoformat(),
                    "signup_method": "google"
                }
            )
        else:  # STUDENT
            initial_completion_status = {
                "personal_info": False,
                "educational_background": False,
                "expected_services": False
            }
            student = Student(
                user_id=user.id,
                profile_completion_status=json.dumps(initial_completion_status),
                is_profile_complete=False
            )
            db.add(student)
            db.commit()
            
            # Send admin notification
            admin_emails = ["<EMAIL>", "<EMAIL>"]
            logger.info(f"Scheduling Google OAuth student signup notification for {user.email}")
            background_tasks.add_task(
                send_email_ses,
                to_email=admin_emails,
                email_type=EmailType.NOTIFY_ADMIN_STUDENT_SIGNUP.value,
                user_data={
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "email": user.email,
                    "signup_time": datetime.utcnow().isoformat(),
                    "signup_method": "google"
                }
            )
    
    # Create access token
    access_token = create_access_token(
        data={"sub": user.email, "user_type": user.user_type},
        expires_delta=timedelta(hours=ACCESS_TOKEN_EXPIRE_HOURS)
    )
    
    return {
        "access_token": access_token,
        "token_type": "bearer",
        "user_type": user.user_type,
        "is_new_user": is_new_user
    }
    
 


@router.post("/linkedin", response_model=OAuthResponse)
async def linkedin_auth(
    request: LinkedInAuthRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    try:
        # Exchange code for tokens
        tokens = await get_linkedin_tokens(request.code)
        
        if 'id_token' not in tokens:
            raise HTTPException(
                status_code=400,
                detail="No ID token received from LinkedIn"
            )
        
        # Get user info from ID token
        user_info = await get_linkedin_user_info(tokens['id_token'])
        
        if not user_info.get('email_verified'):
            raise HTTPException(
                status_code=400,
                detail="Email not verified with LinkedIn"
            )
            
        # Check if user exists
        user = db.query(User).filter(User.email == user_info['email']).first()
        is_new_user = user is None
        
        if user:
            # Login case - user exists
            if request.user_type is not None and user.user_type != request.user_type:
                # If user_type is provided (signup attempt) but doesn't match
                raise HTTPException(
                    status_code=400,
                    detail=f"Email already registered as a {user.user_type}"
                )
        else:
            # Signup case - new user
            if request.user_type is None:
                raise HTTPException(
                    status_code=400,
                    detail="user_type is required for registration"
                )
                
            # Create new user
            user = User(
                email=user_info['email'],
                first_name=user_info['first_name'],
                last_name=user_info['last_name'],
                user_type=request.user_type,
                password_hash=None  # OAuth users don't need passwords
            )
            db.add(user)
            db.commit()
            db.refresh(user)
            
            # Create specific profile based on user type
            if request.user_type == UserType.COUNSELOR:
                initial_completion_status = {
                    "personal_info": False,
                    "education": False,
                    "professional_experience": False,
                    "counseling_experience": False,
                    "services": False,
                    "documents": False
                }
                
                counselor = Counselor(
                    user_id=user.id,
                    profile_completion_status=json.dumps(initial_completion_status),
                    is_profile_complete=False,
                    is_verified=False
                )
                db.add(counselor)
                db.commit()
                
                # Send admin notification
                admin_emails = ["<EMAIL>", "<EMAIL>"]
                logger.info(f"Scheduling LinkedIn OAuth counselor signup notification for {user.email}")
                background_tasks.add_task(
                    send_email_ses,
                    to_email=admin_emails,
                    email_type=EmailType.NOTIFY_ADMIN_COUNSELOR_SIGNUP.value,
                    user_data={
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "email": user.email,
                        "signup_time": datetime.utcnow().isoformat(),
                        "signup_method": "linkedin"
                    }
                )
            else:  # STUDENT
                initial_completion_status = {
                    "personal_info": False,
                    "educational_background": False,
                    "expected_services": False
                }
                student = Student(
                    user_id=user.id,
                    profile_completion_status=json.dumps(initial_completion_status),
                    is_profile_complete=False
                )
                db.add(student)
                db.commit()
                
                # Send admin notification
                admin_emails = ["<EMAIL>", "<EMAIL>"]
                logger.info(f"Scheduling LinkedIn OAuth student signup notification for {user.email}")
                background_tasks.add_task(
                    send_email_ses,
                    to_email=admin_emails,
                    email_type=EmailType.NOTIFY_ADMIN_STUDENT_SIGNUP.value,
                    user_data={
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "email": user.email,
                        "signup_time": datetime.utcnow().isoformat(),
                        "signup_method": "linkedin"
                    }
                )
        
        # Create access token for our app
        access_token = create_access_token(
            data={"sub": user.email, "user_type": user.user_type},
            expires_delta=timedelta(hours=ACCESS_TOKEN_EXPIRE_HOURS)
        )
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user_type": user.user_type,
            "is_new_user": is_new_user
        }
        
    except Exception as e:
        if isinstance(e, HTTPException):
            raise e
        else:
            raise HTTPException(
                status_code=400,
                detail=str(e)
            )

# Store signup verification codes temporarily (in production, use Redis or similar)
signup_verification_codes = {}

@router.post("/signup/initiate")
async def initiate_signup(
    request: SignupInitRequest,
    db: Session = Depends(get_db)
):
    # Check if email exists
    if db.query(User).filter(User.email == request.email).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    # Generate verification code
    code = ''.join(random.choices(string.digits, k=6))
    signup_verification_codes[request.email] = {
        'code': code,
        'expires_at': datetime.utcnow() + timedelta(minutes=10),
        'signup_data': {
            'email': request.email,
            'first_name': request.first_name,
            'last_name': request.last_name,
            'user_type': request.user_type
        }
    }
    
    # Send verification email
    logger.info(f"Sending signup verification code to {request.email}")
    await send_email_ses(
        to_email=request.email,
        email_type=EmailType.SIGNUP.value,
        otp=code
    )
    logger.info(f"Successfully sent signup verification code to {request.email}")
    
    # Return code in response for testing
    return {
        "message": "Verification code sent to your email",
        "code": code  # Remove this in production
    }

@router.post("/signup/resend")
async def resend_verification_code(
    request: SignupResendRequest,
    db: Session = Depends(get_db)
):
    stored_data = signup_verification_codes.get(request.email)
    if not stored_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No signup session found"
        )
    
    # Generate new verification code
    code = ''.join(random.choices(string.digits, k=6))
    
    # Update stored data with new code and reset expiry
    stored_data['code'] = code
    stored_data['expires_at'] = datetime.utcnow() + timedelta(minutes=10)
    signup_verification_codes[request.email] = stored_data
    
    # Comment out email sending for testing
    await send_email_ses(
        to_email=request.email,
        email_type=EmailType.SIGNUP.value,
        otp=code
    )
    print(f"New verification code for {request.email}: {code}")  # For testing
    
    # Return code in response for testing
    return {
        "message": "New verification code sent",
        "code": code  # Remove this in production
    }

@router.post("/signup/verify")
async def verify_signup_code(
    request: SignupVerifyRequest,
    db: Session = Depends(get_db)
):
    # Verify code
    stored_data = signup_verification_codes.get(request.email)
    if not stored_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No verification code found or code expired"
        )
    
    if datetime.utcnow() > stored_data['expires_at']:
        del signup_verification_codes[request.email]
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Verification code has expired"
        )
    
    if request.code != stored_data['code']:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid verification code"
        )
    
    # Mark as verified in stored data
    stored_data['verified'] = True
    
    return {"message": "Email verified successfully"}

@router.post("/signup/complete")
async def complete_signup(
    request: SignupCompleteRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    # Get stored signup data
    stored_data = signup_verification_codes.get(request.email)
    if not stored_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No signup data found or session expired"
        )
    
    if not stored_data.get('verified'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email not verified"
        )
    
    # Double check email doesn't exist (in case created during verification)
    if db.query(User).filter(User.email == request.email).first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )
    
    signup_data = stored_data['signup_data']
    
    try:
        # Create user based on user type
        if signup_data['user_type'] == UserType.STUDENT:
            user_data = StudentSignup(
                email=request.email,
                password=request.password,
                first_name=signup_data['first_name'],
                last_name=signup_data['last_name']
            )
            response = await register_student(user_data, background_tasks, db)
        else:
            user_data = CounselorSignup(
                email=request.email,
                password=request.password,
                first_name=signup_data['first_name'],
                last_name=signup_data['last_name']
            )
            response = await register_counselor(user_data, background_tasks, db)
        
        # Clean up verification code
        del signup_verification_codes[request.email]
        
        # Generate access token
        access_token = create_access_token(data={"sub": request.email})
        
        # Return response with token
        return {
            **response,
            "access_token": access_token,
            "token_type": "bearer",
            "user_type": signup_data['user_type']
        }
        
    except Exception as e:
        # If anything fails, ensure verification code is cleaned up
        if request.email in signup_verification_codes:
            del signup_verification_codes[request.email]
        raise e