# routes/contact.py
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import Optional
from datetime import datetime

from ..database import get_db
from ..models.contact import ContactInquiry
from ..schemas.contact import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ContactInquiryResponse, ContactInquiryList
from ..utils.auth import get_current_user

router = APIRouter()

@router.post("", response_model=ContactInquiryResponse)
async def create_contact_inquiry(
    inquiry: ContactInquiryCreate,
    db: Session = Depends(get_db)
):
    """
    Create a new contact inquiry from the contact form
    """
    db_inquiry = ContactInquiry(
        email=inquiry.email,
        message=inquiry.message
    )
    
    db.add(db_inquiry)
    db.commit()
    db.refresh(db_inquiry)
    
    return db_inquiry

@router.get("", response_model=ContactInquiryList)
async def get_contact_inquiries(
    is_addressed: Optional[bool] = None,
    skip: int = Query(default=0, ge=0),
    limit: int = Query(default=10, ge=1, le=100),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get list of contact inquiries with optional filtering
    """
    # Build query
    query = db.query(ContactInquiry)
    
    if is_addressed is not None:
        query = query.filter(ContactInquiry.is_addressed == is_addressed)
    
    # Get total count
    total = query.count()
    
    # Get paginated results
    inquiries = query.order_by(desc(ContactInquiry.created_at))\
                    .offset(skip)\
                    .limit(limit)\
                    .all()
    
    return ContactInquiryList(total=total, items=inquiries)

@router.put("/{inquiry_id}/address", response_model=ContactInquiryResponse)
async def mark_inquiry_addressed(
    inquiry_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Mark a contact inquiry as addressed
    """
    inquiry = db.query(ContactInquiry).filter(ContactInquiry.id == inquiry_id).first()
    if not inquiry:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Contact inquiry not found"
        )
    
    inquiry.is_addressed = True
    inquiry.addressed_at = datetime.utcnow()
    
    db.commit()
    db.refresh(inquiry)
    
    return inquiry