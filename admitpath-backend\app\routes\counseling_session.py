# app/routes/counseling_session.py
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import datetime, date, time, timedelta, timezone as tz
from sqlalchemy import or_, and_
from zoneinfo import ZoneInfo

from app.utils.email_types import EmailType
from app.utils.email_utils import send_email_ses
from app.utils.session_timezone import format_session_times

from ..database import get_db
from ..models.counseling_session import *
from ..models.payment import *
from ..models.user import Counselor, Student, User
from ..schemas.counseling_session import *
from ..schemas.payment import *
from ..utils.auth import get_current_user
from ..utils.verify_user import verify_counselor
from ..utils.zoom_utils import zoom_client
from ..utils.logger import setup_logger
from ..services.reminder_service import schedule_session_reminders, cancel_session_reminders

logger = setup_logger(__name__)


router = APIRouter()

@router.get("", response_model=SessionListResponse)
async def get_sessions(
    timezone: str,
    status: Optional[str] = Query(None, enum=["pending", "completed", "cancelled"]),
    payout_status: Optional[str] = Query(None, enum=["pending", "approved", "paid_out", "cancelled"]),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)

    # Timezone setup
    try:
        tz_info = ZoneInfo(timezone)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid timezone: {str(e)}"
        )

    # Build query with joins to get student info and payout status
    query = db.query(
        CounselingSession,
        Payment.counselor_payout_status.label('payout_status'),
        Student.user_id.label('student_user_id'),
        User.first_name.label('student_first_name'),
        User.last_name.label('student_last_name')
    ).join(
        Student, CounselingSession.student_id == Student.id
    ).join(
        User, Student.user_id == User.id
    ).outerjoin(
        Payment, CounselingSession.id == Payment.session_id
    ).filter(
        CounselingSession.counselor_id == counselor.id,
        or_(
            and_(Payment.id.isnot(None), Payment.status == PaymentStatus.COMPLETED.value),
            CounselingSession.package_subscription_id.isnot(None)
        )
    )

    # Apply non-date filters
    if status:
        query = query.filter(CounselingSession.status == status)
    if payout_status:
        query = query.filter(Payment.counselor_payout_status == payout_status)

    # Execute query
    total = query.count()
    results = query.order_by(CounselingSession.date.desc()).all()

    # Process results to include student info, payout status with timezone conversion
    sessions = []
    for session, payout_status, student_user_id, student_first_name, student_last_name in results:
        try:
            # Convert UTC times to local timezone
            start_local = session.start_time.astimezone(tz_info)
            end_local = session.end_time.astimezone(tz_info)
            created_local = session.created_at.astimezone(tz_info)
            updated_local = session.updated_at.astimezone(tz_info)

            date_local = start_local.date()
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Time conversion error: {str(e)}"
            )

        session_dict = session.__dict__.copy()
        session_dict.pop('_sa_instance_state', None)  # Remove SQLAlchemy internal state

        # Create student's full name
        student_name = f"{student_first_name} {student_last_name}"

        # Update with converted times and student info
        session_dict.update({
            'date': date_local,
            'start_time': start_local,
            'end_time': end_local,
            'created_at': created_local,
            'updated_at': updated_local,
            'payout_status': payout_status,
            'student_user_id': student_user_id,
            'student_name': student_name
        })

        sessions.append(session_dict)

    return SessionListResponse(total=total, items=sessions)


@router.get("/{session_id}", response_model=SessionResponse)
async def get_session(
    session_id: int,
    timezone: str,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Timezone setup
    try:
        tz_info = ZoneInfo(timezone)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid timezone: {str(e)}"
        )

    # Get specific session with student info
    session_data = db.query(
        CounselingSession,
        Payment.counselor_payout_status.label('payout_status'),
        Student.user_id.label('student_user_id'),
        User.first_name.label('student_first_name'),
        User.last_name.label('student_last_name')
    ).join(
        Student, CounselingSession.student_id == Student.id
    ).join(
        User, Student.user_id == User.id
    ).outerjoin(
        Payment, CounselingSession.id == Payment.session_id
    ).filter(
        CounselingSession.id == session_id,
        CounselingSession.counselor_id == counselor.id
    ).first()

    if not session_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )

    session, payout_status, student_user_id, student_first_name, student_last_name = session_data

    try:
        # Convert UTC times to local timezone
        start_local = session.start_time.astimezone(tz_info)
        end_local = session.end_time.astimezone(tz_info)
        created_local = session.created_at.astimezone(tz_info)
        updated_local = session.updated_at.astimezone(tz_info)

        date_local = start_local.date()
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Time conversion error: {str(e)}"
        )

    # Create response dictionary with converted times
    session_dict = session.__dict__.copy()
    session_dict.pop('_sa_instance_state', None)  # Remove SQLAlchemy internal state

    # Create student's full name
    student_name = f"{student_first_name} {student_last_name}"

    # Update with converted times and additional data
    session_dict.update({
        'date': date_local,
        'start_time': start_local,
        'end_time': end_local,
        'created_at': created_local,
        'updated_at': updated_local,
        'payout_status': payout_status,
        'student_user_id': student_user_id,
        'student_name': student_name
    })

    return session_dict

@router.put("/{session_id}", response_model=SessionResponse)
async def update_counselor_session(
    session_id: int,
    session_update: SessionUpdate,
    timezone: str,
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Get session with student email
    session_data = db.query(
        CounselingSession,
        User.email.label('student_email'),
        User.first_name.label('student_first_name'),
        User.last_name.label('student_last_name'),
        User.timezone.label('student_timezone')
    ).join(
        Student, CounselingSession.student_id == Student.id
    ).join(
        User, Student.user_id == User.id
    ).filter(
        CounselingSession.id == session_id,
        CounselingSession.counselor_id == counselor.id
    ).first()

    if not session_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )

    db_session, student_email, student_first_name, student_last_name, student_timezone = session_data

    try:
        # Validate timezone
        tz_info = ZoneInfo(timezone)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid timezone: {str(e)}"
        )

    update_dict = session_update.dict(exclude_unset=True)

    try:
        db_session.start_time = update_dict['start_time']
        db_session.date = update_dict['date']
        db_session.end_time = update_dict['end_time']

        if db_session.start_time >= db_session.end_time:
            raise ValueError("End time must be after start time")

        # Check for conflicting sessions
        conflicting_session = db.query(CounselingSession).filter(
            CounselingSession.counselor_id == counselor.id,
            CounselingSession.id != session_id,
            CounselingSession.status.notin_(["cancelled", "completed"]),
            CounselingSession.start_time < db_session.end_time,
            CounselingSession.end_time > db_session.start_time
        ).first()

        if conflicting_session:
            raise ValueError("The selected time slot conflicts with another existing session")

        # Update other fields
        for key, value in update_dict.items():
            if key not in ['start_time', 'end_time', 'date']:
                setattr(db_session, key, value)

        db.commit()
        db.refresh(db_session)

        # Cancel existing reminders and schedule new ones
        await cancel_session_reminders(db, session_id)
        await schedule_session_reminders(
            db=db,
            session_id=db_session.id,
            counselor_email=current_user.email,
            student_email=student_email
        )

        students_local_time_data = format_session_times(db_session, student_timezone)

        session_data = {
            "event_name": db_session.event_name,
            "student_name": f"{student_first_name} {student_last_name}",
            "counselor_name": f"{current_user.first_name} {current_user.last_name}",
            **students_local_time_data,
            "description": db_session.description or "",
            "meeting_link": db_session.meeting_link
        }

        background_tasks.add_task(
            send_email_ses,
            to_email=student_email,
            email_type=EmailType.SESSION_RESCHEDULE.value,
            session_data=session_data
        )

        admin_tz_info = ZoneInfo("Asia/Singapore")

        admin_session_data = {
            "event_name": db_session.event_name,
            "student_name": f"{student_first_name} {student_last_name}",
            "student_email": student_email,
            "counselor_name": f"{current_user.first_name} {current_user.last_name}",
            "date": db_session.date.astimezone(admin_tz_info).strftime("%Y-%m-%d"),
            "start_time": db_session.start_time.astimezone(admin_tz_info).strftime("%H:%M:%S"),
            "end_time": db_session.end_time.astimezone(admin_tz_info).strftime("%H:%M:%S"),
            "meeting_link": db_session.meeting_link,
            "created_at": db_session.created_at.isoformat() + "Z",
            "updated_at": datetime.utcnow().isoformat() + "Z"
        }

        background_tasks.add_task(
            send_email_ses,
            to_email=["<EMAIL>", "<EMAIL>"],
            email_type=EmailType.NOTIFY_ADMIN_SESSION_RESCHEDULED.value,
            session_data=admin_session_data
        )

    except ValueError as ve:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Update failed: {str(e)}"
        )

    # Prepare response
    session_dict = db_session.__dict__.copy()
    session_dict.update({
        'start_time': db_session.start_time,
        'end_time': db_session.end_time,
    })
    session_dict.pop('_sa_instance_state', None)
    return session_dict


@router.delete("/{session_id}")
async def delete_session(
    session_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)


    db_session = db.query(CounselingSession).filter(
        CounselingSession.id == session_id,
        CounselingSession.counselor_id == counselor.id
    ).first()

    if not db_session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )

    # Instead of deleting, mark as cancelled
    db_session.status = "cancelled"
    db.commit()

    # Cancel any pending reminders
    await cancel_session_reminders(db, session_id)

    return {"message": "Session cancelled successfully"}



# session notes endpoints
@router.post("/{session_id}/notes", response_model=SessionNotesResponse)
async def add_session_notes(
    session_id: int,
    notes: SessionNotesCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)


    # Verify session exists and belongs to counselor
    session = db.query(CounselingSession).filter(
        CounselingSession.id == session_id,
        CounselingSession.counselor_id == counselor.id
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )

    # Check if notes already exist
    existing_notes = db.query(SessionNotes).filter(
        SessionNotes.session_id == session_id
    ).first()

    if existing_notes:
        # Update existing notes
        existing_notes.notes = notes.notes
        existing_notes.updated_at = datetime.utcnow()
        db_notes = existing_notes
    else:
        # Create new notes
        db_notes = SessionNotes(
            session_id=session_id,
            notes=notes.notes
        )
        db.add(db_notes)

    db.commit()
    db.refresh(db_notes)

    return db_notes

@router.get("/{session_id}/notes", response_model=SessionNotesResponse)
async def get_session_notes(
    session_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)


    # Verify session exists and belongs to counselor
    session = db.query(CounselingSession).filter(
        CounselingSession.id == session_id,
        CounselingSession.counselor_id == counselor.id
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )

    # Get notes
    notes = db.query(SessionNotes).filter(
        SessionNotes.session_id == session_id
    ).first()

    if not notes:
        # Return empty notes object instead of 404
        return SessionNotes(
            id=0,
            session_id=session_id,
            notes="",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

    return notes



@router.delete("/{session_id}/notes")
async def delete_session_notes(
    session_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)


    # Verify session exists and belongs to counselor
    session = db.query(CounselingSession).filter(
        CounselingSession.id == session_id,
        CounselingSession.counselor_id == counselor.id
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )

    # Find and delete notes
    deleted = db.query(SessionNotes).filter(
        SessionNotes.session_id == session_id
    ).delete()

    if not deleted:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No notes found for this session"
        )

    db.commit()

    return {"message": "Session notes deleted successfully"}

# Background task to clean up abandoned sessions
async def cleanup_abandoned_sessions(db: Session):
    """Background task to clean up sessions without completed payments"""
    try:
        # Define the cutoff time (e.g., 24 hours ago)
        cutoff_time = datetime.utcnow() - timedelta(hours=24)

        # Find sessions with pending payments older than the cutoff time
        abandoned_sessions = db.query(CounselingSession)\
            .join(Payment, CounselingSession.id == Payment.session_id)\
            .filter(
                Payment.status == PaymentStatus.PENDING.value,
                Payment.created_at < cutoff_time
            ).all()

        if not abandoned_sessions:
            logger.info("No abandoned sessions found")
            return

        logger.info(f"Found {len(abandoned_sessions)} abandoned sessions to clean up")

        # Update sessions
        for session in abandoned_sessions:
            session.status = SessionStatus.CANCELLED.value
            # Update associated payment
            payment = db.query(Payment).filter(Payment.session_id == session.id).first()
            if payment:
                payment.status = PaymentStatus.FAILED.value

        db.commit()
        logger.info(f"Successfully cleaned up {len(abandoned_sessions)} abandoned sessions")

    except Exception as e:
        db.rollback()
        logger.error(f"Error cleaning up abandoned sessions: {str(e)}")
        raise

# Endpoint to trigger cleanup (admin only or via scheduled task)
@router.post("/cleanup-abandoned")
async def trigger_cleanup_abandoned_sessions(
    background_tasks: BackgroundTasks,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Check if user is admin
    if current_user.user_type != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admins can trigger this operation"
        )

    # Run cleanup as background task
    background_tasks.add_task(cleanup_abandoned_sessions, db)

    return {"message": "Cleanup process initiated"}


@router.put("/{session_id}/notes", response_model=SessionNotesResponse)
async def update_session_notes(
    session_id: int,
    notes: SessionNotesCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update notes for a specific counseling session
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Verify session exists and belongs to counselor
    session = db.query(CounselingSession).filter(
        CounselingSession.id == session_id,
        CounselingSession.counselor_id == counselor.id
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )

    # Get existing notes
    existing_notes = db.query(SessionNotes).filter(
        SessionNotes.session_id == session_id
    ).first()

    if not existing_notes:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No notes found for this session. Please create notes first."
        )

    try:
        # Update notes
        existing_notes.notes = notes.notes
        existing_notes.updated_at = datetime.utcnow()

        db.commit()
        db.refresh(existing_notes)

        return existing_notes

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update session notes: {str(e)}"
        )