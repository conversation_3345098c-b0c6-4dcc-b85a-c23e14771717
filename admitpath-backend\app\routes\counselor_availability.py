# app/routes/counselor_availability.py
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
from datetime import date, datetime, time, timedelta, timezone as tz
from typing import List
from zoneinfo import ZoneInfo
from collections import defaultdict

from app.models.counseling_session import CounselingSession
from app.models.payment import Payment

from ..database import get_db
from ..models.counselor_availability import CounselorAvailability
from ..schemas.counselor_availability import (
    AvailabilityCreate,
    AvailabilityResponse,
)
from ..utils.auth import get_current_user
from ..models import Counselor
from ..models.user import User
from ..utils.verify_user import verify_counselor

router = APIRouter()

@router.post("", response_model=AvailabilityResponse)
async def create_availability(
    availability: AvailabilityCreate,
    timezone: str,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)

    try:
        local_week_start = availability.week_start

        # Store slots exactly as provided
        db_availability = CounselorAvailability(
            counselor_id=counselor.id,
            local_week_start=local_week_start,
            timezone=timezone,
            available_slots=availability.available_slots
        )
        db.add(db_availability)
        db.commit()
        db.refresh(db_availability)

        return await convert_availability_response(db_availability, timezone)

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

async def convert_availability_response(
    availability: CounselorAvailability,
    timezone: str
) -> AvailabilityResponse:
    """Convert stored availability to response format in requested timezone"""
    try:
        # Original timezone of availability creation
        counselor_tz_info = ZoneInfo(availability.timezone)
        # Timezone of the person fetching
        fetch_tz_info = ZoneInfo(timezone)

        formatted_slots = defaultdict(list)

        for day_str, times in availability.available_slots.items():
            day_offset = int(day_str)
            availability_date = availability.local_week_start + timedelta(days=day_offset)

            for time_str in times:
                # Create datetime in counselor's original timezone
                slot_dt = datetime.combine(
                    availability_date,
                    datetime.strptime(time_str, "%H:%M").time()
                ).replace(tzinfo=counselor_tz_info)

                # Convert to fetching user's timezone
                fetch_slot_time = slot_dt.astimezone(fetch_tz_info)

                # Calculate day offset in fetching user's timezone
                new_day_offset = (fetch_slot_time.date() - availability.local_week_start).days

                # Only include slots within the original week (0-6)
                if 0 <= new_day_offset <= 6:
                    formatted_slots[str(new_day_offset)].append(fetch_slot_time.strftime("%H:%M"))

        # Sort and deduplicate slots for each day
        formatted_slots = {
            day: sorted(list(set(times)))
            for day, times in formatted_slots.items()
        }

        return AvailabilityResponse(
            id=availability.id,
            counselor_id=availability.counselor_id,
            week_start=availability.local_week_start,
            available_slots=formatted_slots,
            created_at=availability.created_at,
            updated_at=availability.updated_at
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Conversion error: {str(e)}")

@router.get("", response_model=List[AvailabilityResponse])
async def get_availability(
    timezone: str,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)

    availabilities = db.query(CounselorAvailability).filter(
        CounselorAvailability.counselor_id == counselor.id
    ).all()

    return [await convert_availability_response(avail, timezone) for avail in availabilities]

@router.put("/{week_id}", response_model=AvailabilityResponse)
async def update_availability(
    week_id: int,
    availability_data: AvailabilityCreate,
    timezone: str,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)
    availability = db.query(CounselorAvailability).filter(
        CounselorAvailability.id == week_id,
        CounselorAvailability.counselor_id == counselor.id
    ).first()

    if not availability:
        raise HTTPException(status_code=404, detail="Availability week not found")

    if availability.local_week_start != availability_data.week_start:
        raise HTTPException(
            status_code=400,
            detail="Cannot change week_start date - create a new entry instead"
        )

    try:
        # Update slots exactly as provided
        availability.available_slots = availability_data.available_slots
        availability.timezone = timezone
        db.commit()

        return await convert_availability_response(availability, timezone)

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

@router.delete("/{week_id}")
async def delete_availability(
    week_id: int,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete an availability week"""
    counselor = await verify_counselor(current_user.id, db)

    availability = db.query(CounselorAvailability).filter(
        CounselorAvailability.id == week_id,
        CounselorAvailability.counselor_id == counselor.id
    ).first()

    if not availability:
        raise HTTPException(status_code=404, detail="Availability week not found")

    try:
        db.delete(availability)
        db.commit()
        return {"message": "Availability week deleted successfully"}
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/public/{counselor_user_id}/dates", response_model=List[date])
async def get_available_dates(
    counselor_user_id: int,
    start_date: date,
    end_date: date,
    timezone: str,
    db: Session = Depends(get_db)
):
    """Get available dates within range in requested timezone"""
    counselor = db.query(Counselor).join(User).filter(
        User.id == counselor_user_id
    ).first()

    if not counselor:
        raise HTTPException(status_code=404, detail="Counselor not found")

    try:
        user_tz_info = ZoneInfo(timezone)

        current_time = datetime.now(user_tz_info)
        # Calculate the cutoff time (24 hours from now)
        cutoff_time = current_time + timedelta(hours=24)
        start_range = datetime.combine(start_date, time.min).replace(tzinfo=user_tz_info)
        end_range = datetime.combine(end_date, time.max).replace(tzinfo=user_tz_info)

        availabilities = db.query(CounselorAvailability).filter(
            CounselorAvailability.counselor_id == counselor.id,
            CounselorAvailability.local_week_start <= end_date,
            CounselorAvailability.local_week_start >= start_date - timedelta(days=6)
        ).all()

        available_dates = set()

        for avail in availabilities:
            avail_week_start = avail.local_week_start
            counselor_tz = ZoneInfo(avail.timezone)

            for day_str, times in avail.available_slots.items():
                day_offset = int(day_str)
                availability_date = avail_week_start + timedelta(days=day_offset)

                for time_str in times:
                    slot_dt = datetime.combine(
                        availability_date,
                        datetime.strptime(time_str, "%H:%M").time()
                    ).replace(tzinfo=counselor_tz)

                    # Convert slot time to user's timezone and current time for comparison
                    user_slot_time = slot_dt.astimezone(user_tz_info)

                    # Check if slot is at least 24 hours in the future and within requested range
                    if (
                        user_slot_time > cutoff_time and
                        start_range.date() <= user_slot_time.date() <= end_range.date()
                    ):
                        available_dates.add(user_slot_time.date())

        return sorted(available_dates)

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@router.get("/public/{counselor_user_id}/slots", response_model=List[str])
async def get_available_slots(
    counselor_user_id: int,
    target_date: date,
    timezone: str,
    db: Session = Depends(get_db)
):
    counselor = db.query(Counselor).join(User).filter(
        User.id == counselor_user_id
    ).first()

    if not counselor:
        raise HTTPException(status_code=404, detail="Counselor not found")

    try:
        user_tz_info = ZoneInfo(timezone)
        current_time = datetime.now(user_tz_info)
        # Calculate the cutoff time (24 hours from now)
        cutoff_time = current_time + timedelta(hours=24)

        # Query sessions with completed payments or part of a package
        existing_sessions = db.query(
            CounselingSession.start_time,
            CounselingSession.end_time
        ).outerjoin(
            Payment,
            CounselingSession.id == Payment.session_id
        ).filter(
            CounselingSession.counselor_id == counselor.id,
            CounselingSession.status != "cancelled",
            CounselingSession.status != "completed",
            # Include sessions with completed payments OR part of a package
            or_(
                and_(Payment.id.isnot(None), Payment.status == "completed"),
                CounselingSession.package_subscription_id.isnot(None)
            )
        ).all()

        all_availabilities = db.query(CounselorAvailability).filter(
            CounselorAvailability.counselor_id == counselor.id
        ).all()

        filtered_availabilities = []
        for availability in all_availabilities:
            # Convert target date to the availability's timezone
            target_date_in_avail_tz = datetime.combine(
                target_date,
                datetime.min.time()
            ).replace(tzinfo=user_tz_info).astimezone(ZoneInfo(availability.timezone))

            week_start = availability.local_week_start
            week_end = week_start + timedelta(days=6)

            if week_start <= target_date_in_avail_tz.date() <= week_end:
                filtered_availabilities.append(availability)

        available_slots = []

        for avail in filtered_availabilities:
            counselor_tz = ZoneInfo(avail.timezone)
            week_start = avail.local_week_start

            for day_str, times in avail.available_slots.items():
                day_offset = int(day_str)
                availability_date = week_start + timedelta(days=day_offset)

                for time_str in times:
                    # Create datetime in counselor's timezone
                    slot_dt = datetime.combine(
                        availability_date,
                        datetime.strptime(time_str, "%H:%M").time()
                    ).replace(tzinfo=counselor_tz)

                    user_slot_time = slot_dt.astimezone(user_tz_info)

                    slot_date_in_user_tz = user_slot_time.date()

                    if slot_date_in_user_tz != target_date:
                        continue

                    # Convert to UTC for session comparison
                    utc_slot_time = user_slot_time.astimezone(tz.utc)

                    # Check if slot is at least 24 hours in the future
                    if user_slot_time <= cutoff_time:
                        continue

                    # Check if slot is conflicting with existing sessions (in UTC)
                    is_blocked = any(
                        (session_start < utc_slot_time + timedelta(hours=1)) and
                        (utc_slot_time < session_end)
                        for session_start, session_end in existing_sessions
                    )

                    if not is_blocked:
                        available_slots.append(user_slot_time.isoformat())

        return sorted(set(available_slots))

    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))