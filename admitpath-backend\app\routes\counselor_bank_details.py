# app/routes/counselor_bank_details.py

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime

from ..database import get_db
from ..models.counselor_bank_details import CounselorBankAccount
from ..models.user import Counselor
from ..utils.auth import get_current_user
from ..utils.verify_user import verify_counselor
from ..schemas.counselor_bank_details import (
    CounselorBankAccountCreate,
    CounselorBankAccountUpdate,
    CounselorBankAccountResponse,
    CounselorBankAccountList
)

router = APIRouter()

@router.post("", response_model=CounselorBankAccountResponse)
async def add_bank_account(
    bank_details: CounselorBankAccountCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)
    
    # If this is marked as primary, unset any existing primary account
    if bank_details.is_primary:
        db.query(CounselorBankAccount).filter(
            CounselorBankAccount.counselor_id == counselor.id,
            CounselorBankAccount.is_primary == True
        ).update({"is_primary": False})
    
    # Create new bank account
    db_bank_account = CounselorBankAccount(
        counselor_id=counselor.id,
        **bank_details.dict()
    )
    
    db.add(db_bank_account)
    db.commit()
    db.refresh(db_bank_account)
    
    return db_bank_account

@router.get("/accounts", response_model=CounselorBankAccountList)
async def get_bank_accounts(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)
    
    # Get all active bank accounts
    accounts = db.query(CounselorBankAccount).filter(
        CounselorBankAccount.counselor_id == counselor.id,
        CounselorBankAccount.is_active == True
    ).all()
    
    return CounselorBankAccountList(total=len(accounts), items=accounts)

@router.get("/{account_id}", response_model=CounselorBankAccountResponse)
async def get_bank_account(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)
    
    # Get specific bank account
    account = db.query(CounselorBankAccount).filter(
        CounselorBankAccount.id == account_id,
        CounselorBankAccount.counselor_id == counselor.id,
        CounselorBankAccount.is_active == True
    ).first()
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Bank account not found"
        )
    
    return account

@router.put("/{account_id}", response_model=CounselorBankAccountResponse)
async def update_bank_account(
    account_id: int,
    bank_details: CounselorBankAccountUpdate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)
    
    # Get existing bank account
    account = db.query(CounselorBankAccount).filter(
        CounselorBankAccount.id == account_id,
        CounselorBankAccount.counselor_id == counselor.id,
        CounselorBankAccount.is_active == True
    ).first()
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Bank account not found"
        )
    
    # If updating primary status to True, unset any existing primary account
    update_data = bank_details.dict(exclude_unset=True)
    if update_data.get('is_primary'):
        db.query(CounselorBankAccount).filter(
            CounselorBankAccount.counselor_id == counselor.id,
            CounselorBankAccount.is_primary == True
        ).update({"is_primary": False})
    
    # Update account
    for key, value in update_data.items():
        setattr(account, key, value)
    
    account.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(account)
    
    return account

@router.delete("/{account_id}")
async def delete_bank_account(
    account_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)
    
    # Get account and soft delete
    account = db.query(CounselorBankAccount).filter(
        CounselorBankAccount.id == account_id,
        CounselorBankAccount.counselor_id == counselor.id,
        CounselorBankAccount.is_active == True
    ).first()
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Bank account not found"
        )
    
    # Soft delete by setting is_active to False
    account.is_active = False
    account.updated_at = datetime.utcnow()
    db.commit()
    
    return {"message": "Bank account deleted successfully"}