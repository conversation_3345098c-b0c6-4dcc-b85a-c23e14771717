
# routes/counselor_dashboard.py
from zoneinfo import ZoneInfo
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import func, extract, case, desc, or_, and_
from datetime import datetime, timezone as tz
from calendar import month_abbr
from typing import Optional, List

from ..database import get_db
from ..models.user import Counselor, User, Student
from ..models.counseling_session import CounselingSession
from ..models.payment import Payment
from ..utils.auth import get_current_user
from ..schemas.counselor_dashboard import *
from ..utils.verify_user import verify_counselor

router = APIRouter()

@router.get("/upcoming-sessions", response_model=UpcomingSessions)
async def get_upcoming_sessions(
    timezone: str,
    limit: int = 5,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)

    try:
        tz_info = ZoneInfo(timezone)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid timezone: {str(e)}"
        )

    now = datetime.utcnow()
    sessions = db.query(CounselingSession, User).join(
        Student, Student.id == CounselingSession.student_id
    ).join(
        User, User.id == Student.user_id
    ).outerjoin(
        Payment, Payment.session_id == CounselingSession.id
    ).filter(
        CounselingSession.counselor_id == counselor.id,
        CounselingSession.date >= now,
        CounselingSession.status != "cancelled",
        # Include sessions with completed payments OR part of a package
        or_(
            and_(Payment.id.isnot(None), Payment.status == "completed"),
            CounselingSession.package_subscription_id.isnot(None)
        )
    ).order_by(
        CounselingSession.date,
        CounselingSession.start_time
    ).limit(limit).all()

    upcoming_sessions = []
    for session, user in sessions:
        try:
            start_local = session.start_time.replace(tzinfo=tz.utc).astimezone(tz_info)
            end_local = session.end_time.replace(tzinfo=tz.utc).astimezone(tz_info)
            date_local = start_local.date()

            upcoming_sessions.append(
                UpcomingSession(
                    id=session.id,
                    event_name=session.event_name,
                    date=date_local,
                    start_time=start_local,
                    end_time=end_local,
                    meeting_link=session.meeting_link,
                    student_name=f"{user.first_name} {user.last_name}"
                )
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Time conversion error: {str(e)}"
            )

    return UpcomingSessions(sessions=upcoming_sessions)


@router.get("/earnings-summary", response_model=EarningsSummary)
async def get_earnings_summary(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)

    # Get current year
    current_year = datetime.utcnow().year

    # Query monthly earnings with correct interpretation of approved/pending
    monthly_results = db.query(
        extract('month', CounselingSession.date).label('month'),

        # Total earnings (all completed payments)
        func.sum(case(
            (Payment.status == 'completed', Payment.amount),
            else_=0
        )).label('total'),

        # Approved earnings (paid out to counselor)
        func.sum(case(
            ((Payment.status == 'completed') & (Payment.counselor_payout_status == 'paid_out'), Payment.amount),
            else_=0
        )).label('approved'),

        # Pending earnings (completed payment but not paid to counselor yet)
        func.sum(case(
            ((Payment.status == 'completed') & (
                (Payment.counselor_payout_status == 'pending') |
                (Payment.counselor_payout_status == 'approved')
            ), Payment.amount),
            else_=0
        )).label('pending')
    ).outerjoin(
        Payment, Payment.session_id == CounselingSession.id
    ).filter(
        CounselingSession.counselor_id == counselor.id,
        extract('year', CounselingSession.date) == current_year,
        # Include sessions with completed payments OR part of a package
        or_(
            and_(Payment.id.isnot(None), Payment.status == 'completed'),
            CounselingSession.package_subscription_id.isnot(None)
        )
    ).group_by(
        extract('month', CounselingSession.date)
    ).all()

    # Process monthly data
    monthly_data = []
    for month_num in range(1, 13):
        month_data = next(
            (r for r in monthly_results if r.month == month_num),
            None
        )

        monthly_data.append(MonthlyEarning(
            month=month_abbr[month_num],
            total=float(month_data.total if month_data else 0),
            approved=float(month_data.approved if month_data else 0),
            pending=float(month_data.pending if month_data else 0)
        ))

    # Calculate totals
    total_approved = sum(month.approved for month in monthly_data)
    total_pending = sum(month.pending for month in monthly_data)
    total_earning = total_approved + total_pending

    return EarningsSummary(
        total_earning=total_earning,
        approved=total_approved,
        pending=total_pending,
        monthly_data=monthly_data
    )


@router.get("/my-students", response_model=StudentListResponse)
async def get_counselor_students(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Current timestamp for upcoming sessions calculation
    now = datetime.utcnow()

    # Get all unique students who have either booked or completed sessions
    students_query = (
        db.query(
            Student.id.label('student_id'),
            User.id.label('user_id'),
            User.first_name,
            User.last_name,
            User.email,
            func.max(CounselingSession.date).label('last_session_date'),
            func.count(CounselingSession.id).label('total_sessions'),
            func.sum(case(
                (CounselingSession.date > now, 1),
                else_=0
            )).label('upcoming_sessions')
        )
        .join(User, User.id == Student.user_id)
        .join(CounselingSession, CounselingSession.student_id == Student.id)
        .filter(
            CounselingSession.counselor_id == counselor.id,
            CounselingSession.status != 'cancelled'
        )
        .group_by(Student.id, User.id, User.first_name, User.last_name, User.email)
        .order_by(desc('last_session_date'))
    )

    # Execute query
    students = students_query.all()

    # Prepare response
    student_list = [
        StudentInfo(
            student_id=student.student_id,
            user_id=student.user_id,
            first_name=student.first_name,
            last_name=student.last_name,
            email=student.email,
            last_session_date=student.last_session_date,
            total_sessions=student.total_sessions,
            upcoming_sessions=student.upcoming_sessions
        )
        for student in students
    ]

    return StudentListResponse(
        total=len(student_list),
        students=student_list
    )


