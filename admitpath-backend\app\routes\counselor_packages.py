# routes/counselor_packages.py
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import and_
from typing import List, Optional
from datetime import datetime, timezone as tz
from zoneinfo import ZoneInfo

from ..database import get_db
from ..models.user import Counselor, Student, User
from ..models.counselor_packages import CounselorPackage, PackageItem
from ..models.student_packages import StudentPackageSubscription, PackageSubscriptionStatus
from ..models.counselor_services import CounselorServicesOffered
from ..models.counseling_session import CounselingSession, SessionStatus
from ..models.payment import Payment, PaymentStatus, ServiceType
from ..schemas.counselor_packages import *
from ..schemas.student_packages import PackageSessionCreate
from ..schemas.counseling_session import SessionResponse as CounselingSessionResponse
from ..utils.auth import get_current_user
from ..utils.verify_user import verify_counselor
from ..utils.zoom_utils import zoom_client
from ..utils.email_types import EmailType
from ..utils.email_utils import send_email_ses
from ..utils.session_timezone import format_session_times
from ..services.reminder_service import schedule_session_reminders

router = APIRouter()

@router.post("", response_model=PackageResponse)
async def create_package(
    package: PackageCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new package with service items"""
    counselor = await verify_counselor(current_user.id, db)

    # Verify all services exist for this counselor
    available_services = {
        service.service_type: service
        for service in db.query(CounselorServicesOffered).filter(
            CounselorServicesOffered.counselor_id == counselor.id
        ).all()
    }

    for item in package.items:
        if item.service_name not in available_services:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Service '{item.service_name}' not found in your services"
            )

    db_package = CounselorPackage(
        counselor_id=counselor.id,
        title=package.title,
        description=package.description,
        total_price=package.total_price,
        is_active=True,
        created_at=datetime.now(tz.utc),
        updated_at=datetime.now(tz.utc)
    )
    db.add(db_package)
    db.flush()

    # Create package items
    for item in package.items:
        package_item = PackageItem(
            package_id=db_package.id,
            service_name=item.service_name,
            hours=item.hours
        )
        db.add(package_item)

    db.commit()
    db.refresh(db_package)
    return db_package


@router.get("", response_model=PackageListResponse)
async def get_counselor_packages(
    is_active: Optional[bool] = None,
    skip: int = 0,
    limit: int = 10,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all packages for the logged-in counselor.
    Optional filtering by active status and pagination support.
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Build base query
    query = db.query(CounselorPackage).filter(
        CounselorPackage.counselor_id == counselor.id
    )

    # Apply active status filter if provided
    if is_active is not None:
        query = query.filter(CounselorPackage.is_active == is_active)

    # Get total count for pagination
    total = query.count()

    # Apply pagination and ordering
    packages = query.order_by(
        CounselorPackage.created_at.desc()
    ).offset(skip).limit(limit).all()

    return {
        "total": total,
        "items": packages
    }


@router.get("/package/{package_id}", response_model=PackageResponse)
async def get_package_by_id(
    package_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific package by ID.
    If user is a counselor, they can only see their own package.
    If user is a student, they can see any active package.
    """
    # Try to get package with its services
    package = db.query(CounselorPackage).filter(
        CounselorPackage.id == package_id
    ).first()

    if not package:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Package not found"
        )

    try:
        # If user is a counselor
        counselor = await verify_counselor(current_user.id, db)
        # Verify package ownership
        if package.counselor_id != counselor.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to view this package"
            )
    except HTTPException as e:
        # If user is not a counselor (presumably a student)
        # Only allow viewing active packages
        if not package.is_active:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Package not found"
            )

    return package

@router.get("/{counselor_user_id}/packages", response_model=PackageListResponse)
async def get_counselor_public_packages(
    counselor_user_id: int,
    skip: int = 0,
    limit: int = 10,
    # current_user = Depends(get_current_user),  # Not needed for public endpoint
    db: Session = Depends(get_db)
):
    """
    Get all active packages for a specific counselor.
    This is a public endpoint that returns only active packages.
    Primarily used for students browsing counselor profiles.
    """
    # First verify the counselor exists
    counselor_data = (
        db.query(Counselor, User)
        .join(User, Counselor.user_id == User.id)
        .filter(User.id == counselor_user_id)
        .first()
    )

    if not counselor_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Counselor not found"
        )

    # Extract the counselor object from the tuple
    counselor, _ = counselor_data

    # Get only active packages
    query = db.query(CounselorPackage).filter(
        CounselorPackage.counselor_id == counselor.id,
        CounselorPackage.is_active == True
    )

    # Get total count
    total = query.count()

    # Apply pagination and get packages
    packages = query.order_by(
        CounselorPackage.created_at.desc()
    ).offset(skip).limit(limit).all()

    return {
        "total": total,
        "items": packages
    }



@router.put("/package/{package_id}", response_model=PackageResponse)
async def update_package(
    package_id: int,
    package: PackageUpdate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update an existing package"""
    counselor = await verify_counselor(current_user.id, db)

    db_package = db.query(CounselorPackage).filter(
        CounselorPackage.id == package_id,
        CounselorPackage.counselor_id == counselor.id
    ).first()

    if not db_package:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Package not found"
        )

    # Update basic fields if provided
    for field in ["title", "description", "total_price", "is_active"]:
        if hasattr(package, field) and getattr(package, field) is not None:
            setattr(db_package, field, getattr(package, field))

    # Update items if provided
    if package.items is not None:
        # Verify all services exist
        available_services = {
            service.service_type: service
            for service in db.query(CounselorServicesOffered).filter(
                CounselorServicesOffered.counselor_id == counselor.id
            ).all()
        }

        for item in package.items:
            if item.service_name not in available_services:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Service '{item.service_name}' not found in your services"
                )

        # Delete existing items
        db.query(PackageItem).filter(PackageItem.package_id == package_id).delete()

        # Create new items
        for item in package.items:
            package_item = PackageItem(
                package_id=db_package.id,
                service_name=item.service_name,
                hours=item.hours
            )
            db.add(package_item)

    db_package.updated_at = datetime.now(tz.utc)
    db.commit()
    db.refresh(db_package)
    return db_package

@router.delete("/package/{package_id}", status_code=status.HTTP_200_OK)
async def delete_package(
    package_id: int,
    permanent: bool = False,  # Query parameter to determine if it's permanent delete
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a package.
    By default performs a soft delete (sets is_active=False).
    Use permanent=True query parameter for permanent deletion.
    Only the counselor who owns the package can delete it.
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Get and verify package belongs to counselor
    package = db.query(CounselorPackage).filter(
        CounselorPackage.id == package_id,
        CounselorPackage.counselor_id == counselor.id
    ).first()

    if not package:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Package not found or you don't have permission to delete it"
        )

    try:
        if permanent:
            # Permanent delete - will also delete related services due to cascade
            db.delete(package)
        else:
            # Soft delete - just mark as inactive
            package.is_active = False

        db.commit()

        return {
            "message": "Package deleted successfully",
            "deletion_type": "permanent" if permanent else "soft"
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )

@router.get("/subscriptions", response_model=List[CounselorPackageSubscriptionResponse])
async def get_package_subscriptions(
    status: Optional[str] = None,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all package subscriptions for the counselor's packages

    Args:
        status: Optional filter for subscription status. Defaults to 'active'.
               Valid values: 'pending', 'active', 'completed', 'cancelled', 'expired'
    """
    counselor = await verify_counselor(current_user.id, db)

    # Default to active status if not specified
    subscription_status = status or PackageSubscriptionStatus.ACTIVE.value

    # Validate status if provided
    if subscription_status not in [s.value for s in PackageSubscriptionStatus]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid status. Valid values: {', '.join([s.value for s in PackageSubscriptionStatus])}"
        )

    # Get subscriptions for counselor's packages filtered by status
    subscriptions = (
        db.query(
            StudentPackageSubscription,
            CounselorPackage,
            User
        )
        .join(
            CounselorPackage,
            StudentPackageSubscription.package_id == CounselorPackage.id
        )
        .join(
            Student,
            StudentPackageSubscription.student_id == Student.id
        )
        .join(
            User,
            Student.user_id == User.id
        )
        .filter(
            CounselorPackage.counselor_id == counselor.id,
            StudentPackageSubscription.status == subscription_status
        )
        .order_by(StudentPackageSubscription.created_at.desc())
        .all()
    )

    result = []
    for subscription, package, student in subscriptions:
        # Get all sessions for this subscription
        sessions = db.query(CounselingSession).filter(
            CounselingSession.package_subscription_id == subscription.id
        ).all()

        result.append({
            "id": subscription.id,
            "package": {
                "id": package.id,
                "title": package.title,
            },
            "student": {
                "id": student.id,
                "name": f"{student.first_name} {student.last_name}",
                "profile_picture": student.profile_picture_url
            },
            "start_date": subscription.start_date,
            "end_date": subscription.end_date,
            "status": subscription.status,
            "service_hours": subscription.service_hours,
            "sessions": [
                {
                    "id": session.id,
                    "date": session.date,
                    "start_time": session.start_time,
                    "end_time": session.end_time,
                    "status": session.status,
                    "event_name": session.event_name
                }
                for session in sessions
            ]
        })

    return result

@router.post("/subscription/{subscription_id}/sessions", response_model=CounselingSessionResponse)
async def create_package_session_by_counselor(
    subscription_id: int,
    session_data: PackageSessionCreate,
    background_tasks: BackgroundTasks,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new session for a package subscription (counselor only)"""
    counselor = await verify_counselor(current_user.id, db)

    subscription = db.query(StudentPackageSubscription).join(
        CounselorPackage,
        StudentPackageSubscription.package_id == CounselorPackage.id
    ).filter(
        StudentPackageSubscription.id == subscription_id,
        CounselorPackage.counselor_id == counselor.id,
        StudentPackageSubscription.status == PackageSubscriptionStatus.ACTIVE.value
    ).first()

    if not subscription:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subscription not found or not active"
        )

    package_items = {
        item.service_name: item.hours
        for item in subscription.package.package_items
    }

    if session_data.service_name not in package_items:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Service '{session_data.service_name}' not found in package"
        )

    session_duration_hours = (session_data.end_time - session_data.start_time).total_seconds() / 3600

    remaining_hours = package_items[session_data.service_name] - subscription.service_hours[session_data.service_name]["used"]

    student = db.query(Student).filter(Student.id == subscription.student_id).first()
    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )

    duration = int((session_data.end_time - session_data.start_time).total_seconds() / 60)
    meeting_details = await zoom_client.create_meeting(
        topic=f"Counseling Session: {session_data.service_name}",
        start_time=session_data.start_time,
        duration=duration,
    )

    db_session = CounselingSession(
        student_id=student.id,
        counselor_id=counselor.id,
        package_subscription_id=subscription.id,
        event_name=session_data.service_name,
        date=session_data.date,
        start_time=session_data.start_time,
        end_time=session_data.end_time,
        status=SessionStatus.UPCOMING.value,
        zoom_meeting_id=meeting_details["id"],
        meeting_link=meeting_details["join_url"],
        created_at=datetime.now(tz.utc),
        updated_at=datetime.now(tz.utc)
    )

    db.add(db_session)
    db.flush()

    # Get student email for reminders
    student_user = db.query(User).join(Student, User.id == Student.user_id).filter(
        Student.id == student.id
    ).first()

    # Schedule reminders
    await schedule_session_reminders(
        db=db,
        session_id=db_session.id,
        counselor_email=current_user.email,
        student_email=student_user.email
    )

    session_duration_hours = (session_data.end_time - session_data.start_time).total_seconds() / 3600
    hours_used = round(session_duration_hours * 2) / 2

    subscription.service_hours[session_data.service_name]["used"] += hours_used

    db_session.description = f"Session for {session_data.service_name} - {hours_used} hours used from package"

    db.commit()
    db.refresh(db_session)

    try:
        student_user = db.query(User).join(Student, Student.user_id == User.id).filter(
            Student.id == student.id
        ).first()

        if student_user:
            package = subscription.package

            student_timezone = student_user.timezone or 'UTC'
            formatted_times = format_session_times(db_session, student_timezone)

            remaining_hours = package_items[session_data.service_name] - subscription.service_hours[session_data.service_name]["used"]

            session_email_data = {
                "event_name": db_session.event_name,
                "package_name": package.title,
                "student_name": f"{student_user.first_name} {student_user.last_name}",
                "counselor_name": f"{current_user.first_name} {current_user.last_name}",
                **formatted_times,
                "description": db_session.description or "",
                "meeting_link": db_session.meeting_link,
                "hours_used": hours_used,
                "hours_remaining": remaining_hours
            }

            background_tasks.add_task(
                send_email_ses,
                to_email=student_user.email,
                email_type=EmailType.PACKAGE_SESSION_CREATION.value,
                session_data=session_email_data
            )

            admin_tz_info = ZoneInfo("Asia/Singapore")
            admin_session_data = {
                **session_email_data,
                "student_email": student_user.email,
                "date": db_session.date.astimezone(admin_tz_info).strftime("%Y-%m-%d"),
                "start_time": db_session.start_time.astimezone(admin_tz_info).strftime("%H:%M UTC"),
                "end_time": db_session.end_time.astimezone(admin_tz_info).strftime("%H:%M UTC"),
            }

            background_tasks.add_task(
                send_email_ses,
                to_email=["<EMAIL>", "<EMAIL>"],
                email_type=EmailType.NOTIFY_ADMIN_SESSION_CREATED.value,
                session_data=admin_session_data
            )
    except Exception as e:
        print(f"Error sending package session email notification: {str(e)}")

    return db_session
