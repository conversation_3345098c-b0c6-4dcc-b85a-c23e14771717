# app/routes/counselor_profile.py
from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile, Form, BackgroundTasks
from sqlalchemy.orm import Session, joinedload
import json
import os
from typing import Dict
from ..database import get_db
from ..models.user import Counselor, User
from ..models.counselor_profile import *
from ..schemas.counselor_profile import *
from ..utils.auth import get_current_user
from ..utils.s3 import upload_to_s3
from ..utils.verify_user import verify_counselor
from ..utils.email_utils import send_email_ses
from ..utils.email_types import EmailType
from ..utils.logger import logger
from ..models.counselor_services import *
from ..models.counselor_packages import *
router = APIRouter() 

async def check_counselor_profile_completion(counselor: Counselor, db: Session):
    """
    Check if all required profile sections are complete and update is_profile_complete
    
    Args:
        counselor: Counselor model instance
        db: Database session
    """
    completion_status = json.loads(counselor.profile_completion_status or '{}')
    
    # Define required sections for counselor profile completion
    # only personal_info, education are required
    required_sections = [
        'personal_info',
        'education',
    ]
    
    # Check if all required sections are complete
    is_complete = all(completion_status.get(section, False) for section in required_sections)
    
    # Update is_profile_complete if needed
    if counselor.is_profile_complete != is_complete:
        counselor.is_profile_complete = is_complete
        db.commit()
        
@router.post("/personal-info")
async def create_personal_info(
    nationality: str = Form(...),
    country_of_residence: str = Form(...),
    gender: str = Form(...),
    date_of_birth: str = Form(...),
    bio: Optional[str] = Form(None),
    tagline: Optional[str] = Form(None),
    profile_image: Optional[UploadFile] = File(None),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Get counselor profile
    counselor = await verify_counselor(current_user.id, db)
    
    # Convert date string to datetime
    try:
        date_of_birth = datetime.fromisoformat(date_of_birth.replace('Z', '+00:00'))
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid date format")
    
    # Upload image if provided
    profile_image_url = None
    if profile_image:
        try:
            profile_image_url = await upload_to_s3(profile_image, folder="counselor-profiles")
            # Update user's profile picture URL as well
            current_user.profile_picture_url = profile_image_url
            db.add(current_user)
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to upload image: {str(e)}")

    # Check if personal info already exists
    existing_info = db.query(CounselorPersonalInfo).filter(
        CounselorPersonalInfo.counselor_id == counselor.id
    ).first()

    if existing_info:
        # Update existing record
        existing_info.nationality = nationality
        existing_info.country_of_residence = country_of_residence
        existing_info.gender = gender
        existing_info.date_of_birth = date_of_birth
        existing_info.bio = bio
        existing_info.tagline = tagline
        if profile_image_url:
            existing_info.profile_image_url = profile_image_url
        personal_info = existing_info
    else:
        # Create new record
        personal_info = CounselorPersonalInfo(
            counselor_id=counselor.id,
            nationality=nationality,
            country_of_residence=country_of_residence,
            gender=gender,
            date_of_birth=date_of_birth,
            bio=bio,
            tagline=tagline,
            profile_image_url=profile_image_url
        )
        db.add(personal_info)
    
    try:
        db.commit()
        db.refresh(personal_info)
        if profile_image_url:
            db.refresh(current_user)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))
    
    # Update profile completion status
    completion_status = json.loads(counselor.profile_completion_status or '{}')
    completion_status['personal_info'] = True
    counselor.profile_completion_status = json.dumps(completion_status)
    db.commit()
    
    return personal_info


@router.put("/personal-info/{personal_info_id}")
async def update_personal_info(
    personal_info_id: int,
    nationality: str = Form(...),
    country_of_residence: str = Form(...),
    gender: str = Form(...),
    date_of_birth: str = Form(...),
    bio: Optional[str] = Form(None),
    tagline: Optional[str] = Form(None),
    profile_image: Optional[UploadFile] = File(None),
    delete_image: Optional[bool] = Form(False),
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    # Get counselor profile
    counselor = await verify_counselor(current_user.id, db)

    # Get existing personal info
    personal_info = (
        db.query(CounselorPersonalInfo)
        .filter(
            CounselorPersonalInfo.id == personal_info_id,
            CounselorPersonalInfo.counselor_id == counselor.id,
        )
        .first()
    )

    if not personal_info:
        raise HTTPException(status_code=404, detail="Personal info not found")

    # Convert date string to datetime
    try:
        date_of_birth = datetime.fromisoformat(date_of_birth.replace("Z", "+00:00"))
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid date format")

    # Handle image logic
    if delete_image:
        personal_info.profile_image_url = None
        current_user.profile_picture_url = None
        db.add(current_user)
    elif profile_image:
        try:
            profile_image_url = await upload_to_s3(
                profile_image, folder="counselor-profiles"
            )
            personal_info.profile_image_url = profile_image_url
            current_user.profile_picture_url = profile_image_url
            db.add(current_user)
        except Exception as e:
            raise HTTPException(
                status_code=500, detail=f"Failed to upload image: {str(e)}"
            )

    # Update fields
    personal_info.nationality = nationality
    personal_info.country_of_residence = country_of_residence
    personal_info.gender = gender
    personal_info.date_of_birth = date_of_birth
    personal_info.bio = bio
    personal_info.tagline = tagline

    try:
        db.commit()
        db.refresh(personal_info)
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=400, detail=str(e))

    return personal_info


@router.get("/personal-info", response_model=PersonalInfoResponse)
async def get_personal_info(
    current_user=Depends(get_current_user), db: Session = Depends(get_db)
):
    """
    Get personal information for the logged-in counselor
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Get personal info with user details
    personal_info = (
        db.query(CounselorPersonalInfo, User.first_name, User.last_name)
        .join(Counselor, CounselorPersonalInfo.counselor_id == Counselor.id)
        .join(User, Counselor.user_id == User.id)
        .filter(CounselorPersonalInfo.counselor_id == counselor.id)
        .first()
    )

    if not personal_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Personal information not found",
        )

    # Combine the results into a single dictionary
    result = {
        "id": personal_info[0].id,
        "counselor_id": personal_info[0].counselor_id,
        "nationality": personal_info[0].nationality,
        "country_of_residence": personal_info[0].country_of_residence,
        "gender": personal_info[0].gender,
        "date_of_birth": personal_info[0].date_of_birth,
        "bio": personal_info[0].bio,  # Added bio field
        "tagline": personal_info[0].tagline,  # Added tagline field
        "first_name": personal_info[1],  # From User table
        "last_name": personal_info[2],  # From User table
        "profile_image_url": personal_info[0].profile_image_url,
    }

    return result


@router.put("/personal-info", response_model=PersonalInfoResponse)
async def update_personal_info(
    personal_info: PersonalInfoCreate,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Update personal information for the logged-in counselor
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Check if personal info exists
    existing_info = (
        db.query(CounselorPersonalInfo)
        .filter(CounselorPersonalInfo.counselor_id == counselor.id)
        .first()
    )

    if not existing_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Personal information not found. Please create personal info first.",
        )

    # Update existing personal info
    update_data = personal_info.dict(exclude_unset=True)  # Only include set values
    for key, value in update_data.items():
        setattr(existing_info, key, value)

    try:
        db.commit()
        db.refresh(existing_info)

        # Get user details to include in response
        user = db.query(User).filter(User.id == counselor.user_id).first()

        # Combine the results into a single dictionary
        result = {
            "id": existing_info.id,
            "counselor_id": existing_info.counselor_id,
            "nationality": existing_info.nationality,
            "country_of_residence": existing_info.country_of_residence,
            "gender": existing_info.gender,
            "date_of_birth": existing_info.date_of_birth,
            "bio": existing_info.bio,
            "tagline": existing_info.tagline,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "profile_image_url": existing_info.profile_image_url,
        }

        return result

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update personal information: {str(e)}",
        )


# store education information for a counselor
@router.post("/education")
async def create_education(
    education: EducationCreate,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    # Get counselor profile
    counselor = await verify_counselor(current_user.id, db)

    # Create education record
    db_education = CounselorEducation(
        counselor_id=counselor.id,
        university_name=education.university_name,
        degree=education.degree,
        major=education.major,
        start_date=education.start_date,
        end_date=education.end_date,
        grade=education.grade,
        accomplishments=education.accomplishments,
    )
    db.add(db_education)

    # Update profile completion status
    completion_status = json.loads(counselor.profile_completion_status or "{}")
    completion_status["education"] = True
    counselor.profile_completion_status = json.dumps(completion_status)

    db.commit()

    return {"message": "Education information added successfully"}


# Get all education records for a counselor
@router.get("/education")
async def get_education(
    current_user=Depends(get_current_user), db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)

    education_records = (
        db.query(CounselorEducation)
        .filter(CounselorEducation.counselor_id == counselor.id)
        .all()
    )

    return education_records


@router.get("/education/{education_id}", response_model=EducationResponse)
async def get_specific_education(
    education_id: int,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Get a specific education record for the logged-in counselor
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Get specific education record
    education_record = (
        db.query(CounselorEducation)
        .filter(
            CounselorEducation.id == education_id,
            CounselorEducation.counselor_id == counselor.id,
        )
        .first()
    )

    if not education_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Education record not found or you don't have permission to view it",
        )

    return education_record


@router.put("/education/{education_id}", response_model=EducationResponse)
async def update_education(
    education_id: int,
    education: EducationCreate,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Update a specific education record for the logged-in counselor
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Get the specific education record and verify ownership
    education_record = (
        db.query(CounselorEducation)
        .filter(
            CounselorEducation.id == education_id,
            CounselorEducation.counselor_id == counselor.id,
        )
        .first()
    )

    if not education_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Education record not found or you don't have permission to edit it",
        )

    # Update education record fields
    for key, value in education.dict(exclude_unset=True).items():
        setattr(education_record, key, value)

    try:
        db.commit()
        db.refresh(education_record)
        return education_record

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update education record: {str(e)}",
        )


# create professional experience record
@router.post("/professional-experience")
async def create_professional_experience(
    experience: ProfessionalExperienceCreate,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    # Get counselor profile
    counselor = await verify_counselor(current_user.id, db)

    # Filter out None values from the experience data
    exp_data = {k: v for k, v in experience.dict().items() if v is not None}

    # Create professional experience record with only provided fields
    db_experience = CounselorProfessionalExperience(
        counselor_id=counselor.id, **exp_data
    )
    db.add(db_experience)

    # Update profile completion status if at least one field is provided
    if exp_data:
        completion_status = json.loads(counselor.profile_completion_status or "{}")
        completion_status["professional_experience"] = True
        counselor.profile_completion_status = json.dumps(completion_status)

    db.commit()

    return {"message": "Professional experience added successfully"}


# Get professional experience
@router.get("/professional-experience")
async def get_professional_experience(
    current_user=Depends(get_current_user), db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)

    experiences = (
        db.query(CounselorProfessionalExperience)
        .filter(CounselorProfessionalExperience.counselor_id == counselor.id)
        .all()
    )

    return experiences


@router.put(
    "/professional-experience/{experience_id}",
    response_model=ProfessionalExperienceResponse,
)
async def update_professional_experience(
    experience_id: int,
    experience: ProfessionalExperienceUpdate,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Update a specific professional experience record for the logged-in counselor
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Get the specific experience record and verify ownership
    experience_record = (
        db.query(CounselorProfessionalExperience)
        .filter(
            CounselorProfessionalExperience.id == experience_id,
            CounselorProfessionalExperience.counselor_id == counselor.id,
        )
        .first()
    )

    if not experience_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Professional experience record not found or you don't have permission to edit it",
        )

    # Update fields
    for key, value in experience.dict(exclude_unset=True).items():
        if value is not None:  # Only update non-None values
            setattr(experience_record, key, value)

    try:
        db.commit()
        db.refresh(experience_record)
        return experience_record

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update professional experience: {str(e)}",
        )


# Create counseling experience record
@router.post("/counseling-experience")
async def create_counseling_experience(
    experience: CounselingExperienceCreate,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    # Get counselor profile
    counselor = await verify_counselor(current_user.id, db)

    # Check if counseling experience already exists
    existing_experience = (
        db.query(CounselorCounselingExperience)
        .filter(CounselorCounselingExperience.counselor_id == counselor.id)
        .first()
    )

    if existing_experience:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Counseling experience already exists.",
        )

    # Create counseling experience record
    db_experience = CounselorCounselingExperience(
        counselor_id=counselor.id,
        has_mentored_before=experience.has_mentored_before,
        experience_description=experience.experience_description,
    )
    db.add(db_experience)

    # Update profile completion status
    completion_status = json.loads(counselor.profile_completion_status or "{}")
    completion_status["counseling_experience"] = True
    counselor.profile_completion_status = json.dumps(completion_status)

    db.commit()

    return {"message": "Counseling experience added successfully"}


@router.get("/counseling-experience")
async def get_counseling_experience(
    current_user=Depends(get_current_user), db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)

    experience = (
        db.query(CounselorCounselingExperience)
        .filter(CounselorCounselingExperience.counselor_id == counselor.id)
        .first()
    )

    if not experience:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No counseling experience found",
        )

    return experience


@router.put("/counseling-experience", response_model=CounselingExperienceResponse)
async def update_counseling_experience(
    experience: CounselingExperienceCreate,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Update counseling experience for the logged-in counselor
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Check if counseling experience exists
    existing_experience = (
        db.query(CounselorCounselingExperience)
        .filter(CounselorCounselingExperience.counselor_id == counselor.id)
        .first()
    )

    if not existing_experience:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No counseling experience found. Please create one first.",
        )

    # Update fields
    for key, value in experience.dict(exclude_unset=True).items():
        setattr(existing_experience, key, value)

    try:
        db.commit()
        db.refresh(existing_experience)
        return existing_experience

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update counseling experience: {str(e)}",
        )


# Create services offered by counselor record
@router.post("/services")
async def create_services(
    services: ServicesCreate,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    # Get counselor profile
    counselor = await verify_counselor(current_user.id, db)

    # Delete existing services for this counselor
    db.query(CounselorServices).filter(
        CounselorServices.counselor_id == counselor.id
    ).delete()

    # Add all services
    for service_type in services.services:
        db_service = CounselorServices(
            counselor_id=counselor.id, service_type=service_type
        )
        db.add(db_service)

    # Update profile completion status
    completion_status = json.loads(counselor.profile_completion_status or "{}")
    completion_status["services"] = True
    counselor.profile_completion_status = json.dumps(completion_status)

    db.commit()

    return {"message": "Services updated successfully"}


@router.get("/services")
async def get_services(
    current_user=Depends(get_current_user), db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)

    services = (
        db.query(CounselorServices)
        .filter(CounselorServices.counselor_id == counselor.id)
        .all()
    )

    return [service.service_type for service in services]


@router.put("/services", response_model=List[str])
async def update_services(
    services: ServicesCreate,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Update services for the logged-in counselor
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    try:
        # Delete existing services
        db.query(CounselorServices).filter(
            CounselorServices.counselor_id == counselor.id
        ).delete()

        # Add new services
        for service_type in services.services:
            db_service = CounselorServices(
                counselor_id=counselor.id, service_type=service_type
            )
            db.add(db_service)

        db.commit()

        # Return updated services
        return services.services

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update services: {str(e)}",
        )


# Create commitment record
@router.post("/commitment")
async def create_commitment(
    commitment: CommitmentCreate,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    # Get counselor profile
    counselor = await verify_counselor(current_user.id, db)

    # Filter out None values
    commitment_data = {k: v for k, v in commitment.dict().items() if v is not None}

    # Check if commitment info already exists
    existing_commitment = (
        db.query(CounselorCommitment)
        .filter(CounselorCommitment.counselor_id == counselor.id)
        .first()
    )

    if existing_commitment:
        # Update only provided fields
        for key, value in commitment_data.items():
            setattr(existing_commitment, key, value)
    else:
        # Create new commitment record with provided fields
        db_commitment = CounselorCommitment(
            counselor_id=counselor.id, **commitment_data
        )
        db.add(db_commitment)

    # Update profile completion status only if any data was provided
    if commitment_data:
        completion_status = json.loads(counselor.profile_completion_status or "{}")
        completion_status["commitment"] = True
        counselor.profile_completion_status = json.dumps(completion_status)

    db.commit()

    return {"message": "Commitment information saved successfully"}


@router.get("/commitment")
async def get_commitment(
    current_user=Depends(get_current_user), db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)

    commitment = (
        db.query(CounselorCommitment)
        .filter(CounselorCommitment.counselor_id == counselor.id)
        .first()
    )

    if not commitment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No commitment information found",
        )

    return commitment


@router.put("/commitment", response_model=CommitmentResponse)
async def update_commitment(
    commitment: CommitmentCreate,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Update commitment information for the logged-in counselor
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Check if commitment exists
    existing_commitment = (
        db.query(CounselorCommitment)
        .filter(CounselorCommitment.counselor_id == counselor.id)
        .first()
    )

    if not existing_commitment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No commitment information found. Please create one first.",
        )

    # Update only provided fields
    commitment_data = commitment.dict(exclude_unset=True)
    if commitment_data:
        for key, value in commitment_data.items():
            if value is not None:  # Only update non-None values
                setattr(existing_commitment, key, value)

    try:
        db.commit()
        db.refresh(existing_commitment)
        return existing_commitment

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update commitment information: {str(e)}",
        )


@router.post("/documents/upload", response_model=DocumentResponse)
async def upload_document(
    file: UploadFile = File(...),
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    counselor = await verify_counselor(current_user.id, db)

    # Validate file size (25MB limit)
    MAX_FILE_SIZE = 25 * 1024 * 1024
    file_content = await file.read()
    if len(file_content) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File size exceeds 25MB limit",
        )

    await file.seek(0)

    # Validate file type
    ALLOWED_TYPES = [".pdf", ".doc", ".docx"]
    file_ext = os.path.splitext(file.filename)[1].lower()
    if file_ext not in ALLOWED_TYPES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type {file_ext} not allowed. Please upload {', '.join(ALLOWED_TYPES)}",
        )

    # Upload to S3
    file_url = await upload_to_s3(file, folder=f"counselor-documents/{counselor.id}")

    # Store document info
    db_document = CounselorDocuments(
        counselor_id=counselor.id, document_name=file.filename, document_url=file_url
    )
    db.add(db_document)
    db.commit()
    db.refresh(db_document)

    return db_document


@router.post("/submit", response_model=dict)
async def submit_counselor_profile(
    background_tasks: BackgroundTasks,
    current_user=Depends(get_current_user), 
    db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)

    # Update profile completion status
    completion_status = json.loads(counselor.profile_completion_status or "{}")
    completion_status["documents"] = True
    counselor.profile_completion_status = json.dumps(completion_status)

    # Check if all required sections are complete
    await check_counselor_profile_completion(counselor, db)

    db.commit()

    # Send completion email in background if profile is complete
    if counselor.is_profile_complete:
        admin_emails = ["<EMAIL>", "<EMAIL>"]
        logger.info(f"Scheduling counselor profile completion email for {current_user.email}")
        background_tasks.add_task(
            send_email_ses,
            to_email=current_user.email,
            email_type=EmailType.SIGNUP_COUNSELOR_COMPLETE.value,
            user_data={
                "first_name": current_user.first_name,
                "last_name": current_user.last_name,
                "email": current_user.email
            },
            bcc_email=admin_emails
        )

    return {"message": "Profile submitted successfully"}


@router.get("/documents", response_model=List[DocumentResponse])
async def get_documents(
    current_user=Depends(get_current_user), db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)

    documents = (
        db.query(CounselorDocuments)
        .filter(CounselorDocuments.counselor_id == counselor.id)
        .all()
    )

    return documents


@router.delete("/documents/{document_id}")
async def delete_document(
    document_id: int,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Delete a specific document for the logged-in counselor
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Check if document exists and belongs to counselor
    document = (
        db.query(CounselorDocuments)
        .filter(
            CounselorDocuments.id == document_id,
            CounselorDocuments.counselor_id == counselor.id,
        )
        .first()
    )

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document not found or you don't have permission to delete it",
        )

    try:
        # Delete document from database
        db.delete(document)

        # Update profile completion status if no documents remain
        remaining_documents = (
            db.query(CounselorDocuments)
            .filter(CounselorDocuments.counselor_id == counselor.id)
            .count()
        )

        if remaining_documents == 0:
            completion_status = json.loads(counselor.profile_completion_status or "{}")
            completion_status["documents"] = False
            counselor.profile_completion_status = json.dumps(completion_status)

        db.commit()

        return {"message": "Document deleted successfully"}

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to delete document: {str(e)}",
        )


@router.get("/social-links", response_model=SocialLinksResponse)
async def get_social_links(
    current_user=Depends(get_current_user), db: Session = Depends(get_db)
):
    """
    Get social links for the logged-in counselor
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Get social links
    social_links = (
        db.query(CounselorSocialLinks)
        .filter(CounselorSocialLinks.counselor_id == counselor.id)
        .first()
    )

    if not social_links:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No social links found for this counselor",
        )

    return social_links


@router.post("/social-links", response_model=SocialLinksResponse)
async def create_social_links(
    links: SocialLinksCreate,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Create social links for the logged-in counselor
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Check if social links already exist
    existing_links = (
        db.query(CounselorSocialLinks)
        .filter(CounselorSocialLinks.counselor_id == counselor.id)
        .first()
    )

    if existing_links:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Social links already exist for this counselor. Use PUT endpoint to update.",
        )

    # Create social links record
    db_social_links = CounselorSocialLinks(
        counselor_id=counselor.id, **links.dict(exclude_unset=True)
    )
    db.add(db_social_links)

    db.commit()
    db.refresh(db_social_links)

    return db_social_links


@router.put("/social-links", response_model=SocialLinksResponse)
async def update_social_links(
    links: SocialLinksCreate,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Update social links for the logged-in counselor.
    Creates new links if they don't exist.
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Get existing links or create new
    social_links = (
        db.query(CounselorSocialLinks)
        .filter(CounselorSocialLinks.counselor_id == counselor.id)
        .first()
    )

    if not social_links:
        # Create new links if they don't exist
        social_links = CounselorSocialLinks(counselor_id=counselor.id)
        db.add(social_links)

        # Update profile completion status
        completion_status = json.loads(counselor.profile_completion_status or "{}")
        completion_status["social_links"] = True
        counselor.profile_completion_status = json.dumps(completion_status)

    # Update fields
    for key, value in links.dict(exclude_unset=True).items():
        setattr(social_links, key, value)

    try:
        db.commit()
        db.refresh(social_links)
        return social_links

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update social links: {str(e)}",
        )


class CounselorUserIDList(BaseModel):
    user_ids: List[int]
    total_count: int


@router.get("/counselor-user-ids", response_model=CounselorUserIDList)
async def get_all_counselor_user_ids(db: Session = Depends(get_db)):
    """
    Returns a list of user IDs for all users who are counselors
    """
    # Query user IDs from the users table where user_type is counselor
    counselor_user_ids = [
        id[0] for id in db.query(User.id).filter(User.user_type == "counselor").all()
    ]

    return CounselorUserIDList(
        user_ids=counselor_user_ids, total_count=len(counselor_user_ids)
    )


@router.delete("/education/{education_id}")
async def delete_education(
    education_id: int,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Delete a specific education record for the logged-in counselor
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Get the specific education record and verify ownership
    education_record = (
        db.query(CounselorEducation)
        .filter(
            CounselorEducation.id == education_id,
            CounselorEducation.counselor_id == counselor.id,
        )
        .first()
    )

    if not education_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Education record not found or you don't have permission to delete it",
        )

    try:
        # Delete the education record
        db.delete(education_record)

        # Check if this was the last education record
        remaining_education = (
            db.query(CounselorEducation)
            .filter(CounselorEducation.counselor_id == counselor.id)
            .count()
        )

        # Update profile completion status if no education records remain
        if remaining_education == 0:
            completion_status = json.loads(counselor.profile_completion_status or "{}")
            completion_status["education"] = False
            counselor.profile_completion_status = json.dumps(completion_status)

        db.commit()
        return {"message": "Education record deleted successfully"}

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to delete education record: {str(e)}",
        )


@router.delete("/professional-experience/{experience_id}")
async def delete_professional_experience(
    experience_id: int,
    current_user=Depends(get_current_user),
    db: Session = Depends(get_db),
):
    """
    Delete a specific professional experience record for the logged-in counselor
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)

    # Get the specific experience record and verify ownership
    experience_record = (
        db.query(CounselorProfessionalExperience)
        .filter(
            CounselorProfessionalExperience.id == experience_id,
            CounselorProfessionalExperience.counselor_id == counselor.id,
        )
        .first()
    )

    if not experience_record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Professional experience record not found or you don't have permission to delete it",
        )

    try:
        # Delete the experience record
        db.delete(experience_record)

        # Check if this was the last professional experience record
        remaining_experience = (
            db.query(CounselorProfessionalExperience)
            .filter(CounselorProfessionalExperience.counselor_id == counselor.id)
            .count()
        )

        # Update profile completion status if no professional experience records remain
        if remaining_experience == 0:
            completion_status = json.loads(counselor.profile_completion_status or "{}")
            completion_status["professional_experience"] = False
            counselor.profile_completion_status = json.dumps(completion_status)

        db.commit()
        return {"message": "Professional experience record deleted successfully"}

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to delete professional experience record: {str(e)}",
        )


from typing import Optional, List
from pydantic import BaseModel
from datetime import datetime


class EducationInfo(BaseModel):
    university_name: str
    degree: str
    major: str
    start_date: str
    end_date: str
    grade: Optional[str]
    accomplishments: Optional[str]


class ProfessionalExperienceInfo(BaseModel):
    role: str
    company_name: Optional[str]
    start_date: Optional[str]
    end_date: Optional[str]
    experience_description: Optional[str]


class ServiceInfo(BaseModel):
    id: int
    service_type: str
    custom_type: Optional[str] = None
    description: str
    price: float
    offers_intro_call: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PackageServiceBasic(BaseModel):
    service_id: int
    hours: int

    class Config:
        from_attributes = True


class PackageInfo(BaseModel):
    id: int
    title: str
    description: str
    total_price: float
    is_active: bool
    # package_services: Optional[List[PackageServiceBasic]]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class PublicCounselorProfileResponse(BaseModel):
    # Basic Info
    counselor_id: int
    user_id: int  # Changed from counselor_id
    first_name: str
    last_name: str
    profile_picture_url: Optional[str] = None  # Add this line

    # Personal Info
    nationality: Optional[str]
    country_of_residence: Optional[str]
    gender: Optional[str]
    date_of_birth: Optional[str]
    bio: Optional[str] = None  # Add this line
    tagline: Optional[str] = None 

    # Education
    education: List[EducationInfo]

    # Professional Experience
    professional_experience: List[ProfessionalExperienceInfo]

    # Counseling Experience
    has_mentored_before: Optional[bool]
    experience_description: Optional[str]

    # Services
    services: List[ServiceInfo]

    # Packages
    packages: List[PackageInfo]  # Add packages field

    # Commitment
    hours_per_week: Optional[float]
    hourly_rate: Optional[float]

    # Social Links
    linkedin_url: Optional[str] = None
    portfolio_url: Optional[str] = None
    website_url: Optional[str] = None

    class Config:
        from_attributes = True

class FeaturedCounselorItem(BaseModel):

    user_id: int
    first_name: str
    last_name: str
    profile_picture_url: Optional[str] = None
    country_of_residence: Optional[str] = None
    tagline: Optional[str] = None
    bio: Optional[str] = None
    education: Optional[List[dict]] = None
    hourly_rate: Optional[float] = None
    
    class Config:
        orm_mode = True

class FeaturedCounselorsResponse(BaseModel):
    category: str
    items: List[FeaturedCounselorItem]
    
    class Config:
        orm_mode = True


def format_date(date: Optional[datetime]) -> Optional[str]:
    """Format datetime object to string or return None"""
    return date.strftime("%Y-%m-%d") if date else None


def get_average_hours(hours_range: str) -> int:
    """Convert hour range string to average number of hours"""
    if not hours_range:
        return None

    if hours_range == "40+":
        return 40

    try:
        # Split range like "6-10" into start and end
        start, end = map(int, hours_range.split("-"))
        return (start + end) // 2
    except:
        return None

# This route isnt working.  Idk when this was added or for what - Ahmad
@router.get(
    "/by-counselor-id/{counselor_id}", response_model=PublicCounselorProfileResponse
)
async def get_counselor_by_id(counselor_id: int, db: Session = Depends(get_db)):
    """
    Get all public profile information for a specific counselor by their counselor ID.
    This endpoint is publicly accessible.
    """
    # Get counselor and basic user info
    counselor_data = (
        db.query(Counselor, User)
        .join(User, Counselor.user_id == User.id)
        .filter(Counselor.id == counselor_id)
        .first()
    )
    
    if not counselor_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Counselor not found"
        )
        
    counselor, user = counselor_data
    user_id = user.id
    counselor_id = counselor.id
    
    # Rest of the function is the same as get_public_counselor_profile
    # Get personal info
    personal_info = (
        db.query(CounselorPersonalInfo)
        .filter(CounselorPersonalInfo.counselor_id == counselor_id)
        .first()
    )

    # Get education records
    education = (
        db.query(CounselorEducation)
        .filter(CounselorEducation.counselor_id == counselor_id)
        .all()
    )

    # Get professional experience
    professional_exp = (
        db.query(CounselorProfessionalExperience)
        .filter(CounselorProfessionalExperience.counselor_id == counselor_id)
        .all()
    )

    # Get counseling experience
    counseling_exp = (
        db.query(CounselorCounselingExperience)
        .filter(CounselorCounselingExperience.counselor_id == counselor_id)
        .first()
    )

    # Get services
    services = (
        db.query(CounselorServicesOffered)
        .filter(CounselorServicesOffered.counselor_id == counselor_id)
        .all()
    )

    # Get commitment info
    commitment = (
        db.query(CounselorCommitment)
        .filter(CounselorCommitment.counselor_id == counselor_id)
        .first()
    )

    # Get social links
    social_links = (
        db.query(CounselorSocialLinks)
        .filter(CounselorSocialLinks.counselor_id == counselor_id)
        .first()
    )

    # Simplified query for packages
    packages = (
        db.query(CounselorPackage)
        .filter(
            CounselorPackage.counselor_id == counselor_id,
            CounselorPackage.is_active == True,
        )
        .options(joinedload(CounselorPackage.package_items))
        .all()
    )

    # Format education data with string dates
    education_data = [
        {
            "university_name": edu.university_name,
            "degree": edu.degree,
            "major": edu.major,
            "start_date": format_date(edu.start_date),
            "end_date": format_date(edu.end_date),
            "grade": edu.grade,
            "accomplishments": edu.accomplishments,
        }
        for edu in education
    ]

    # Format professional experience data with string dates
    professional_exp_data = [
        {
            "role": exp.role,
            "company_name": exp.company_name,
            "start_date": format_date(exp.start_date),
            "end_date": format_date(exp.end_date),
            "experience_description": exp.experience_description,
        }
        for exp in professional_exp
    ]

    # Combine all data
    response_data = {
        "counselor_id": counselor_id,
        "user_id": user_id,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "profile_picture_url": (
            personal_info.profile_image_url
            if personal_info and personal_info.profile_image_url
            else user.profile_picture_url
        ),
        # Personal Info
        "nationality": personal_info.nationality if personal_info else None,
        "country_of_residence": (
            personal_info.country_of_residence if personal_info else None
        ),
        "gender": personal_info.gender if personal_info else None,
        "date_of_birth": (
            format_date(personal_info.date_of_birth) if personal_info else None
        ),
        "bio": personal_info.bio if personal_info else None,
        "tagline": personal_info.tagline if personal_info else None,
        # Education
        "education": education_data,
        # Professional Experience
        "professional_experience": professional_exp_data,
        # Counseling Experience
        "has_mentored_before": (
            counseling_exp.has_mentored_before if counseling_exp else None
        ),
        "experience_description": (
            counseling_exp.experience_description if counseling_exp else None
        ),
        # Services
        "services": services,  # The complete service objects will be automatically serialized
        # Packages
        "packages": packages,
        # Commitment
        "hours_per_week": (
            get_average_hours(commitment.hours_per_week)
            if commitment and commitment.hours_per_week
            else None
        ),
        "hourly_rate": (
            float(commitment.hourly_rate)
            if commitment and commitment.hourly_rate
            else None
        ),
        # Social Links
        "linkedin_url": social_links.linkedin_url if social_links else None,
        "portfolio_url": social_links.portfolio_url if social_links else None,
        "website_url": social_links.website_url if social_links else None,
    }

    return response_data

@router.get(
    "/public-profile/user/{user_id}", response_model=PublicCounselorProfileResponse
)
async def get_public_counselor_profile(user_id: int, db: Session = Depends(get_db)):
    """
    Get all public profile information for a specific counselor by their user ID.
    This endpoint is publicly accessible.
    """
    # Get counselor and basic user info
    counselor_data = (
        db.query(Counselor, User)
        .join(User, Counselor.user_id == User.id)
        .filter(User.id == user_id)
        .first()
    )

    if not counselor_data:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Counselor not found"
        )

    counselor, user = counselor_data
    counselor_id = counselor.id  # Get the counselor_id for subsequent queries

    # Get personal info
    personal_info = (
        db.query(CounselorPersonalInfo)
        .filter(CounselorPersonalInfo.counselor_id == counselor_id)
        .first()
    )

    # Get education records
    education = (
        db.query(CounselorEducation)
        .filter(CounselorEducation.counselor_id == counselor_id)
        .all()
    )

    # Get professional experience
    professional_exp = (
        db.query(CounselorProfessionalExperience)
        .filter(CounselorProfessionalExperience.counselor_id == counselor_id)
        .all()
    )

    # Get counseling experience
    counseling_exp = (
        db.query(CounselorCounselingExperience)
        .filter(CounselorCounselingExperience.counselor_id == counselor_id)
        .first()
    )

    # Get services
    services = (
        db.query(CounselorServicesOffered)
        .filter(CounselorServicesOffered.counselor_id == counselor_id)
        .all()
    )

    # Get commitment info
    commitment = (
        db.query(CounselorCommitment)
        .filter(CounselorCommitment.counselor_id == counselor_id)
        .first()
    )

    # Get social links
    social_links = (
        db.query(CounselorSocialLinks)
        .filter(CounselorSocialLinks.counselor_id == counselor_id)
        .first()
    )

    # Simplified query for packages
    packages = (
        db.query(CounselorPackage)
        .filter(
            CounselorPackage.counselor_id == counselor_id,
            CounselorPackage.is_active == True,
        )
        .options(joinedload(CounselorPackage.package_items))
        .all()
    )

    # Format education data with string dates
    education_data = [
        {
            "university_name": edu.university_name,
            "degree": edu.degree,
            "major": edu.major,
            "start_date": format_date(edu.start_date),
            "end_date": format_date(edu.end_date),
            "grade": edu.grade,
            "accomplishments": edu.accomplishments,
        }
        for edu in education
    ]

    # Format professional experience data with string dates
    professional_exp_data = [
        {
            "role": exp.role,
            "company_name": exp.company_name,
            "start_date": format_date(exp.start_date),
            "end_date": format_date(exp.end_date),
            "experience_description": exp.experience_description,
        }
        for exp in professional_exp
    ]

    hourly_rate = None
    if services:
        # Filter out None values and 0 prices
        service_prices = [float(service.price) for service in services if float(service.price) > 0]
        if service_prices:  # If there are any non-zero prices
            hourly_rate = min(service_prices)  # Get the minimum non-zero price

    # If no services with valid prices, fall back to commitment hourly rate
    if hourly_rate is None or hourly_rate == 0:
        hourly_rate = float(commitment.hourly_rate) if commitment and commitment.hourly_rate else None

    if hourly_rate is not None:
        hourly_rate = round(hourly_rate)

    # Combine all data
    response_data = {
        "counselor_id": counselor_id,
        "user_id": user_id,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "profile_picture_url": (
            personal_info.profile_image_url
            if personal_info and personal_info.profile_image_url
            else user.profile_picture_url
        ),
        # Personal Info
        "nationality": personal_info.nationality if personal_info else None,
        "country_of_residence": (
            personal_info.country_of_residence if personal_info else None
        ),
        "gender": personal_info.gender if personal_info else None,
        "date_of_birth": (
            format_date(personal_info.date_of_birth) if personal_info else None
        ),
        "bio": personal_info.bio if personal_info else None,
        "tagline": personal_info.tagline if personal_info else None,
        # Education
        "education": education_data,
        # Professional Experience
        "professional_experience": professional_exp_data,
        # Counseling Experience
        "has_mentored_before": (
            counseling_exp.has_mentored_before if counseling_exp else None
        ),
        "experience_description": (
            counseling_exp.experience_description if counseling_exp else None
        ),
        # Services
        "services": services,  # The complete service objects will be automatically serialized
        # Packages
        "packages": packages,
        # Commitment
        "hours_per_week": (
            get_average_hours(commitment.hours_per_week)
            if commitment and commitment.hours_per_week
            else None
        ),
        "hourly_rate": hourly_rate,
        # Social Links
        "linkedin_url": social_links.linkedin_url if social_links else None,
        "portfolio_url": social_links.portfolio_url if social_links else None,
        "website_url": social_links.website_url if social_links else None,
    }

    return response_data