from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from sqlalchemy.exc import IntegrityError

from ..database import get_db
from ..models.counselor_services import CounselorServicesOffered, ServiceTypes
from ..schemas.counselor_services import *
from ..utils.auth import get_current_user
from ..utils.verify_user import verify_counselor
from ..models.user import Counselor  # Add this import


router = APIRouter()


@router.post("/", response_model=ServiceOfferedResponse)
async def create_service_offered(
    service: ServiceOfferedCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    counselor = await verify_counselor(current_user.id, db)
    
    # Create new service
    # If service type is "Other", use the custom_type as the service_type
    service_name = service.service_type
    if service.service_type == "Other" and service.custom_type:
        service_name = service.custom_type
        
    db_service = CounselorServicesOffered(
        counselor_id=counselor.id,
        service_type=service_name,
        custom_type=None,  # No longer needed with simplified approach
        description=service.description,
        price=service.price,
        offers_intro_call=service.offers_intro_call
    )
    
    try:
        db.add(db_service)
        db.commit()
        db.refresh(db_service)
        return db_service
        
    except IntegrityError as e:
        db.rollback()
        if "unique_service_per_counselor" in str(e):
            service_name = service.service_type
            if service.service_type == "Other" and service.custom_type:
                service_name = service.custom_type
                
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"A service with name '{service_name}' already exists"
            )
        raise e
    


@router.get("/{counselor_id}", response_model=List[ServiceOfferedResponse])
async def get_counselor_services(
    counselor_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all services offered by a specific counselor.
    Any authenticated user (student or counselor) can access this endpoint.
    Final URL will be: /counselor/services-offered/{counselor_id}
    """
    # Verify counselor exists
    counselor = db.query(Counselor).filter(Counselor.id == counselor_id).first()
    if not counselor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Counselor not found"
        )
        
    # Get their services
    services = db.query(CounselorServicesOffered).filter(
        CounselorServicesOffered.counselor_id == counselor_id
    ).order_by(
        CounselorServicesOffered.service_type.asc(),
        CounselorServicesOffered.created_at.desc()
    ).all()
    
    return services    


@router.put("/{service_id}", response_model=ServiceOfferedResponse)
async def update_service(
    service_id: int,
    service_update: ServiceUpdate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update an existing service.
    Only the counselor who owns the service can update it.
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)
    
    # Get and verify service belongs to counselor
    service = db.query(CounselorServicesOffered).filter(
        CounselorServicesOffered.id == service_id,
        CounselorServicesOffered.counselor_id == counselor.id
    ).first()
    
    if not service:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Service not found or you don't have permission to edit it"
        )
    
    # Check for duplicate service type if service type is being changed
    if (service_update.service_type and 
        service_update.service_type != service.service_type):
        
        existing_service = db.query(CounselorServicesOffered).filter(
            CounselorServicesOffered.counselor_id == counselor.id,
            CounselorServicesOffered.service_type == service_update.service_type,
            CounselorServicesOffered.id != service_id
        ).first()
        
        if existing_service and service_update.service_type != ServiceTypes.OTHER:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"A service of type '{service_update.service_type}' already exists"
            )
    
    try:
        # Update service attributes
        for key, value in service_update.dict(exclude_unset=True).items():
            setattr(service, key, value)
            
        db.commit()
        db.refresh(service)
        
        return service
        
    except IntegrityError as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to update service. This might be due to a duplicate service type."
        )
        
        

@router.get("/service/{service_id}", response_model=ServiceOfferedResponse)
async def get_service_by_id(
    service_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific service by its ID.
    Only the counselor who owns the service can access it.
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)
    
    # Get and verify service belongs to counselor
    service = db.query(CounselorServicesOffered).filter(
        CounselorServicesOffered.id == service_id,
        CounselorServicesOffered.counselor_id == counselor.id
    ).first()
    
    if not service:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Service not found or you don't have permission to view it"
        )
    
    return service        


# routes/counselor_services.py
@router.delete("/{service_id}")
async def delete_service(
    service_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a specific service.
    Only the counselor who owns the service can delete it.
    """
    # Verify counselor
    counselor = await verify_counselor(current_user.id, db)
    
    # Try to delete the service that belongs to this counselor
    result = db.query(CounselorServicesOffered).filter(
        CounselorServicesOffered.id == service_id,
        CounselorServicesOffered.counselor_id == counselor.id
    ).delete()
    
    # If no rows were deleted (result == 0), then either the service
    # doesn't exist or doesn't belong to this counselor
    if not result:
        # Check if service exists at all to give more specific error
        service_exists = db.query(CounselorServicesOffered).filter(
            CounselorServicesOffered.id == service_id
        ).first()
        
        if service_exists:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to delete this service"
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Service not found"
            )
    
    db.commit()
    
    return {
        "message": "Service deleted successfully",
        "service_id": service_id
    }