# import stripe
# from fastapi import APIRouter, Depends, HTTPException, status
# from sqlalchemy.orm import Session
# import os
# from typing import Dict

# from ..database import get_db
# from ..models.user import Counselor
# from ..models.payment import CounselorStripeAccount, StripeAccountStatus
# from ..utils.auth import get_current_user
# from ..utils.verify_user import verify_counselor

# router = APIRouter()

# # Initialize Stripe with your secret key
# stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

# @router.post("/setup-stripe-account", response_model=Dict)
# async def setup_stripe_account(
#     current_user = Depends(get_current_user),
#     db: Session = Depends(get_db)
# ):
#     # Verify the user is a counselor
#     counselor = await verify_counselor(current_user.id, db)
    
#     # Check if counselor already has a Stripe account
#     existing_account = db.query(CounselorStripeAccount).filter(
#         CounselorStripeAccount.counselor_id == counselor.id
#     ).first()
    
#     if existing_account and existing_account.stripe_account_id:
#         # If account exists, return the account link for onboarding continuation
#         try:
#             account_link = stripe.AccountLink.create(
#                 account=existing_account.stripe_account_id,
#                 refresh_url=f"{os.getenv('FRONTEND_URL')}/counselor/payments/refresh",
#                 return_url=f"{os.getenv('FRONTEND_URL')}/counselor/payments/complete",
#                 type="account_onboarding",
#             )
#             return {"account_link": account_link.url}
#         except stripe.error.StripeError as e:
#             raise HTTPException(
#                 status_code=status.HTTP_400_BAD_REQUEST,
#                 detail=f"Error with Stripe: {str(e)}"
#             )
    
#     try:
#         # Create a new Stripe Connect account
#         stripe_account = stripe.Account.create(
#             type="standard",
#             email=current_user.email,
#             business_type="individual",
#             country="US",  # You might want to make this dynamic based on counselor's location
#             capabilities={
#                 "card_payments": {"requested": True},
#                 "transfers": {"requested": True},
#             },
#         )
        
#         # Create local record
#         db_account = CounselorStripeAccount(
#             counselor_id=counselor.id,
#             stripe_account_id=stripe_account.id,
#             account_status=StripeAccountStatus.PENDING.value,
#             country="US",  # Match with Stripe account
#             default_currency="usd"
#         )
#         db.add(db_account)
#         db.commit()
        
#         # Create an account link for onboarding
#         account_link = stripe.AccountLink.create(
#             account=stripe_account.id,
#             refresh_url=f"{os.getenv('FRONTEND_URL')}/counselor/payments/refresh",
#             return_url=f"{os.getenv('FRONTEND_URL')}/counselor/payments/complete",
#             type="account_onboarding",
#         )
        
#         return {"account_link": account_link.url}
        
#     except stripe.error.StripeError as e:
#         # If anything fails, clean up and raise error
#         if 'db_account' in locals():
#             db.delete(db_account)
#             db.commit()
#         raise HTTPException(
#             status_code=status.HTTP_400_BAD_REQUEST,
#             detail=f"Error creating Stripe account: {str(e)}"
#         )