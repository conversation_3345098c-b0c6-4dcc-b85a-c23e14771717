from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import text 
from ..database import get_db, test_db_connection

router = APIRouter()

@router.get("/test-db")
def test_db():
    return test_db_connection()

@router.get("/test-db-session")
def test_db_session(db: Session = Depends(get_db)):
    try:
        # Try to execute a simple query
        db.execute(text("SELECT 1"))
        return {"status": "success", "message": "Database session is working!"}
    except Exception as e:
        return {"status": "error", "message": str(e)}