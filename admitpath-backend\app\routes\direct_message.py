from zoneinfo import ZoneInfo
from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_, func
from datetime import datetime, timezone

from ..database import get_db
from ..models.user import User, Counselor, Student
from ..models.direct_message import DirectMessage
from ..models.user_connection import UserConnection
from ..schemas.direct_message import *
from ..utils.auth import get_current_user
from ..services.websocket_manager import manager

router = APIRouter()

def generate_channel_id(counselor_id: int, student_id: int) -> str:
    """
    Generate a consistent channel ID for a counselor-student pair.
    The smaller ID always comes first to ensure consistency.
    """
    return f"chat_{min(counselor_id, student_id)}_{max(counselor_id, student_id)}"

async def get_counselor_student_ids(user_id: int, recipient_id: int, db: Session) -> tuple[int, int, str]:
    """Helper function to get counselor_id, student_id, and channel_id based on the users involved"""
    sender = db.query(User).filter(User.id == user_id).first()
    recipient = db.query(User).filter(User.id == recipient_id).first()

    if not recipient:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Recipient not found"
        )

    # Verify one is counselor and other is student
    if sender.user_type == "counselor" and recipient.user_type == "student":
        counselor = db.query(Counselor).filter(Counselor.user_id == sender.id).first()
        student = db.query(Student).filter(Student.user_id == recipient.id).first()
    elif sender.user_type == "student" and recipient.user_type == "counselor":
        counselor = db.query(Counselor).filter(Counselor.user_id == recipient.id).first()
        student = db.query(Student).filter(Student.user_id == sender.id).first()
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Direct messages can only be between a counselor and a student"
        )

    if not counselor or not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Counselor or student profile not found"
        )

    channel_id = generate_channel_id(counselor.id, student.id)
    return counselor.id, student.id, channel_id

@router.post("", response_model=MessageResponse)
async def send_message(
    message_data: MessageCreate,
    current_user=Depends(get_current_user),
    db: Session=Depends(get_db),
    background_tasks: BackgroundTasks = None
):
    """
    Send a direct message to another user.
    Only allows messages between counselors and students.

    This REST endpoint is maintained for backward compatibility.
    New clients should use the WebSocket endpoint for real-time messaging.
    """

    counselor_id, student_id, channel_id = await get_counselor_student_ids(
        current_user.id,
        message_data.recipient_id,
        db
    )

    # Create the message
    db_message = DirectMessage(
        channel_id=channel_id,
        counselor_id=counselor_id,
        student_id=student_id,
        sender_id=current_user.id,
        message=message_data.message,
        created_at=message_data.created_at
    )

    db.add(db_message)
    db.commit()
    db.refresh(db_message)

    # Create response with sender's name
    response = MessageResponse(
        id=db_message.id,
        channel_id=channel_id,
        message=db_message.message,
        sender_id=current_user.id,
        sender_name=f"{current_user.first_name} {current_user.last_name}",
        sender_profile_picture_url=current_user.profile_picture_url,
        created_at=db_message.created_at
    )

    # Broadcast the message to all connected clients in this channel
    # Convert to dict for JSON serialization
    message_dict = {
        "id": response.id,
        "channel_id": response.channel_id,
        "message": response.message,
        "sender_id": response.sender_id,
        "sender_name": response.sender_name,
        "sender_profile_picture_url": response.sender_profile_picture_url,
        "created_at": response.created_at.isoformat()
    }

    # Use background task to avoid blocking the response
    if background_tasks:
        background_tasks.add_task(manager.broadcast_to_channel, message_dict, channel_id)

        # Check if recipient is connected, if not send email notification
        recipient = db.query(User).filter(User.id == message_data.recipient_id).first()
        if recipient:
            background_tasks.add_task(
                manager.send_message_notification,
                db=db,
                message=db_message,
                sender=current_user,
                recipient=recipient
            )

    return response

@router.get("/channels", response_model=ChannelListResponse)
async def get_user_channels(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all chat channels for the logged-in user.
    Returns channels sorted by last message timestamp.
    """
    # Determine if user is counselor or student
    counselor = db.query(Counselor).filter(Counselor.user_id == current_user.id).first()
    student = db.query(Student).filter(Student.user_id == current_user.id).first()

    if not (counselor or student):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User profile not found"
        )

    # Query to get unique channels and their latest message timestamps
    if counselor:
        # For counselor: get all their channels with latest message
        subquery = db.query(
            DirectMessage.channel_id,
            DirectMessage.student_id.label('other_id'),
            func.max(DirectMessage.created_at).label('last_message_at')
        ).filter(
            DirectMessage.counselor_id == counselor.id
        ).group_by(
            DirectMessage.channel_id,
            DirectMessage.student_id
        ).subquery()

        channels = db.query(
            DirectMessage.channel_id,
            subquery.c.other_id,
            subquery.c.last_message_at
        ).join(
            subquery,
            DirectMessage.channel_id == subquery.c.channel_id
        ).filter(
            DirectMessage.created_at == subquery.c.last_message_at
        ).order_by(subquery.c.last_message_at.desc()).all()

        other_user_type = "student"
    else:
        # For student: get all their channels with latest message
        subquery = db.query(
            DirectMessage.channel_id,
            DirectMessage.counselor_id.label('other_id'),
            func.max(DirectMessage.created_at).label('last_message_at')
        ).filter(
            DirectMessage.student_id == student.id
        ).group_by(
            DirectMessage.channel_id,
            DirectMessage.counselor_id
        ).subquery()

        channels = db.query(
            DirectMessage.channel_id,
            subquery.c.other_id,
            subquery.c.last_message_at
        ).join(
            subquery,
            DirectMessage.channel_id == subquery.c.channel_id
        ).filter(
            DirectMessage.created_at == subquery.c.last_message_at
        ).order_by(subquery.c.last_message_at.desc()).all()

        other_user_type = "counselor"

    # Get other participants' info
    channel_previews = []
    for channel in channels:
        # Get other participant's user info
        if other_user_type == "student":
            other_user = db.query(User).join(Student).filter(
                Student.id == channel.other_id
            ).first()
        else:
            other_user = db.query(User).join(Counselor).filter(
                Counselor.id == channel.other_id
            ).first()

        if other_user:
            channel_previews.append(
                ChannelPreview(
                    channel_id=channel.channel_id,
                    other_participant=ChannelParticipant(
                        user_id=other_user.id,
                        first_name=other_user.first_name,
                        last_name=other_user.last_name,
                        user_type=other_user_type,
                        profile_picture_url=other_user.profile_picture_url
                    ),
                    last_message_at=channel.last_message_at
                )
            )

    return ChannelListResponse(channels=channel_previews)

@router.get("/channel/{channel_id}", response_model=MessageListResponse)
async def get_channel_messages(
    channel_id: str,
    skip: int=Query(default=0, ge=0),
    limit: int=Query(default=50, ge=1, le=100),
    current_user=Depends(get_current_user),
    db: Session=Depends(get_db)
):
    """
    Get all messages in a specific channel.
    Messages are paginated and ordered by creation time.
    Verifies the user has access to this channel.
    """


    # First verify user has access to this channel
    counselor = db.query(Counselor).filter(Counselor.user_id == current_user.id).first()
    student = db.query(Student).filter(Student.user_id == current_user.id).first()

    # Check if channel exists and user has access
    channel_query = db.query(DirectMessage).filter(
        DirectMessage.channel_id == channel_id
    )

    if counselor:
        channel_query = channel_query.filter(DirectMessage.counselor_id == counselor.id)
    elif student:
        channel_query = channel_query.filter(DirectMessage.student_id == student.id)
    else:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User does not have access to this channel"
        )

    first_message = channel_query.first()
    if not first_message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Channel not found or no access"
        )

    # Update user connection record for this channel
    # This helps track if the user has seen messages in this channel
    user_connection = db.query(UserConnection).filter(
        UserConnection.user_id == current_user.id,
        UserConnection.channel_id == channel_id
    ).first()

    if user_connection:
        user_connection.last_connected_at = datetime.now(timezone.utc)
    else:
        user_connection = UserConnection(
            user_id=current_user.id,
            channel_id=channel_id,
            is_connected=False,  # WebSocket connection will set this to True
            last_connected_at=datetime.now(timezone.utc)
        )
        db.add(user_connection)

    db.commit()

    # Get messages with pagination
    total = channel_query.count()
    messages = channel_query.order_by(
        DirectMessage.created_at.desc()
    ).offset(skip).limit(limit).all()

    has_more = total > (skip + limit)

    # Create response with sender names and local times
    message_responses = []
    for msg in messages:
        sender = db.query(User).filter(User.id == msg.sender_id).first()

        message_responses.append(
            MessageResponse(
                id=msg.id,
                channel_id=channel_id,
                message=msg.message,
                sender_id=msg.sender_id,
                sender_name=f"{sender.first_name} {sender.last_name}",
                sender_profile_picture_url=sender.profile_picture_url,
                created_at=msg.created_at
            )
        )

    return MessageListResponse(
        total=total,
        channel_id=channel_id,
        messages=message_responses,
        has_more=has_more
    )