from fastapi import APIRouter, status
from typing import Dict

router = APIRouter()


@router.get("/", response_model=Dict[str, str])
async def health_check():
    return {"status": "ok"}


@router.get("/health/detailed", response_model=Dict[str, str])
async def detailed_health_check():
    """
    Detailed health check endpoint with more system information
    """
    return {
        "status": "healthy",
        "message": "API is running",
        "version": "1.0.0",
        "environment": "development",
    }
