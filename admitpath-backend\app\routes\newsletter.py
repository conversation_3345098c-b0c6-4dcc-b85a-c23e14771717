# routes/newsletter.py
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import Optional

from ..database import get_db
from ..models.newsletter import NewsletterSubscriber
from ..schemas.newsletter import (
    NewsletterSubscribe, 
    NewsletterSubscriberResponse,
    NewsletterSubscriberList
)
from ..utils.auth import get_current_user

router = APIRouter()

@router.post("/subscribe", response_model=NewsletterSubscriberResponse)
async def subscribe_to_newsletter(
    subscriber: NewsletterSubscribe,
    db: Session = Depends(get_db)
):
    """
    Subscribe to the newsletter with name and email
    """
    # Check if email already exists
    existing_subscriber = db.query(NewsletterSubscriber).filter(
        NewsletterSubscriber.email == subscriber.email
    ).first()
    
    if existing_subscriber:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="This email is already subscribed to the newsletter"
        )
    
    # Create new subscriber
    db_subscriber = NewsletterSubscriber(
        name=subscriber.name,
        email=subscriber.email
    )
    
    db.add(db_subscriber)
    db.commit()
    db.refresh(db_subscriber)
    
    return db_subscriber

@router.get("/subscribers", response_model=NewsletterSubscriberList)
async def get_newsletter_subscribers(
    skip: int = Query(default=0, ge=0),
    limit: int = Query(default=10, ge=1, le=100),
    current_user = Depends(get_current_user),  # Only authenticated users can view subscribers
    db: Session = Depends(get_db)
):
    """
    Get list of newsletter subscribers with pagination
    """
    # Get total count
    total = db.query(NewsletterSubscriber).count()
    
    # Get paginated results
    subscribers = db.query(NewsletterSubscriber)\
        .order_by(desc(NewsletterSubscriber.subscribed_at))\
        .offset(skip)\
        .limit(limit)\
        .all()
    
    return NewsletterSubscriberList(total=total, items=subscribers)