# In routes/user.py
from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile
from sqlalchemy.orm import Session
from typing import Optional
import os

from ..database import get_db
from ..models.user import User
from ..schemas.user import ProfilePictureResponse
from ..utils.auth import get_current_user
from ..utils.s3 import upload_to_s3

router = APIRouter()

@router.post("/profile-picture", response_model=ProfilePictureResponse)
async def upload_profile_picture(
    file: UploadFile = File(...),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Validate file size (e.g., 15MB limit)
    MAX_FILE_SIZE = 15 * 1024 * 1024  # 15MB
    file_content = await file.read()
    if len(file_content) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File size exceeds 15MB limit"
        )
    
    await file.seek(0)

    # Validate file type
    ALLOWED_TYPES = [".jpg", ".jpeg", ".png", ".webp"]
    file_ext = os.path.splitext(file.filename)[1].lower()
    if file_ext not in ALLOWED_TYPES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type {file_ext} not allowed. Please upload {', '.join(ALLOWED_TYPES)}"
        )
    
    await file.seek(0)
    
    try:
        # Upload to S3
        file_url = await upload_to_s3(
            file,
            folder=f"profile-pictures/{current_user.id}"
        )
        
        # Update user's profile picture URL
        current_user.profile_picture_url = file_url
        db.commit()
        
        return {"profile_picture_url": file_url}
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to upload profile picture: {str(e)}"
        )


@router.delete("/profile-picture")
async def delete_profile_picture(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    if not current_user.profile_picture_url:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No profile picture found"
        )
    
    # TODO: Add logic to delete the file from S3
    # For now, just remove the URL from the database
    current_user.profile_picture_url = None
    db.commit()
    
    return {"message": "Profile picture deleted successfully"}