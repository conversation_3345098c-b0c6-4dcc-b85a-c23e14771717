from token import OP
from fastapi import APIRouter, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy import func
from sqlalchemy.orm import Session
from typing import Optional, Dict, Union
from datetime import datetime, timezone

from app.database import get_db
from app.models.payment import PromoCode, PromoCodeStatus, PromoCodeType

router = APIRouter()

def validate_promo_code(db: Session, code: str, requested_counselor_id: int, original_amount: float) -> PromoCode:
    """Shared validation logic without state changes"""
    promo_code = db.query(PromoCode).filter(
        PromoCode.code == code,
        PromoCode.status == PromoCodeStatus.ACTIVE.value
    ).first()

    if not promo_code:
        raise HTTPException(404, "Invalid promo code")

    if promo_code.counselor_ids and requested_counselor_id not in promo_code.counselor_ids:
        raise HTTPException(404, "Invalid promo code")

    current_time = datetime.now(timezone.utc)
    if not (promo_code.valid_from <= current_time <= promo_code.valid_to):
        raise HTTPException(400, "Promo Code no longer valid")

    if promo_code.max_uses and promo_code.current_uses >= promo_code.max_uses:
        raise HTTPException(400, "Promo Code no longer valid")
    
    if promo_code.min_purchase_amount and original_amount < promo_code.min_purchase_amount:
        raise HTTPException(400, f"Minimum purchase: {promo_code.min_purchase_amount}")

    return promo_code

class PromoCodeVerificationResponse(BaseModel):
    type: str
    amount: Optional[float] = None
    percentage: Optional[float] = None

# Updated route with proper response model
@router.get("/verify", response_model=PromoCodeVerificationResponse)
async def verify_promo_code(code: str, requested_counselor_id: int, original_amount: float, db: Session = Depends(get_db)):
    try:
        promo_code = validate_promo_code(db, code, requested_counselor_id, original_amount)
        return {
            "type": promo_code.type,
            "amount": promo_code.amount,
            "percentage": promo_code.percentage,
        }
    except HTTPException as he:
        raise
    except Exception as e:
        raise HTTPException(500, f"Verification failed: {str(e)}")

def apply_promo_code(
    db: Session, 
    promo_code_str: str, 
    original_amount: float,
    requested_counselor_id: int
) -> Dict[str, Union[float, PromoCode]]:
    """
    Apply promo code to calculate discounted amount. Returns:
    - original_amount: Original price
    - discount_amount: Calculated discount
    - final_amount: Price after discount
    - promo_code: The promo code object
    """
    promo_code = validate_promo_code(db, promo_code_str, requested_counselor_id, original_amount) 

    # Calculate discount based on type
    if promo_code.type == PromoCodeType.AMOUNT.value:
        discount = min(promo_code.amount, original_amount)
    elif promo_code.type == PromoCodeType.PERCENTAGE.value:
        discount = original_amount * (promo_code.percentage / 100)
    elif promo_code.type == PromoCodeType.FIXED_PRICE.value:
        discount = original_amount - promo_code.amount
        discount = max(discount, 0)  # Prevent negative discounts
    else:
        discount = 0

    final_amount = original_amount - discount
    final_amount = max(final_amount, 0)  # Ensure non-negative

    # Track usage (will commit with transaction later)
    promo_code.current_uses += 1

    return {
        "original_amount": original_amount,
        "discount_amount": discount,
        "final_amount": final_amount,
        "promo_code": promo_code
    }