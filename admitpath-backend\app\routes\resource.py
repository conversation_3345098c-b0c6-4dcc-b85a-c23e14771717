# app/routes/resource.py
from fastapi import APIRouter, Depends, HTTPException, status, File, UploadFile
from sqlalchemy.orm import Session
from typing import List
import os
import json
from ..database import get_db
from ..models.resource import Resource
from ..schemas.resource import ResourceResponse, ResourceListResponse
from ..utils.auth import get_current_user
from ..utils.s3 import upload_to_s3

router = APIRouter()

@router.post("/upload", response_model=ResourceResponse)
async def upload_resource(
    title: str,
    category: str,
    description: str = None,
    audience: str = "public",
    image_url: str = None,
    file: UploadFile = File(...),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Upload a new resource document"""
    
    # Validate file size (25MB limit)
    MAX_FILE_SIZE = 25 * 1024 * 1024
    file_content = await file.read()
    if len(file_content) > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File size exceeds 25MB limit"
        )
    
    await file.seek(0)

    # Validate file type
    ALLOWED_TYPES = [".pdf", ".doc", ".docx"]
    file_ext = os.path.splitext(file.filename)[1].lower()
    if file_ext not in ALLOWED_TYPES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type {file_ext} not allowed. Please upload {', '.join(ALLOWED_TYPES)}"
        )
    
    # Upload to S3
    file_url = await upload_to_s3(
        file,
        folder=f"resources/{current_user.id}"
    )
    
    # Store resource info
    db_resource = Resource(
        title=title,
        description=description,
        category=category,
        audience=audience,
        file_url=file_url,
        file_type=file_ext,
        image_url=image_url,
        uploaded_by=current_user.id
    )
    db.add(db_resource)
    db.commit()
    db.refresh(db_resource)
    
    return db_resource

@router.get("", response_model=ResourceListResponse)
async def get_resources(
    category: str = None,
    audience: str = None,
    page: int = 1,
    page_size: int = 10,
    db: Session = Depends(get_db)
):
    """Get all resources with optional category and audience filters. This endpoint is publicly accessible."""
    query = db.query(Resource)
    
    # Apply filters if provided
    if category:
        query = query.filter(Resource.category == category)
    
    if audience:
        query = query.filter(Resource.audience == audience)
    
    # Get total count for pagination
    total = query.count()
    
    # Apply pagination
    offset = (page - 1) * page_size
    resources = query.order_by(Resource.created_at.desc()).offset(offset).limit(page_size).all()
    
    # If page > 1 and no results, it means we've reached the end
    if page > 1 and len(resources) == 0:
        return ResourceListResponse(total=total, items=[])
    
    return ResourceListResponse(total=total, items=resources)

@router.get("/{resource_id}", response_model=ResourceResponse)
async def get_resource(
    resource_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific resource by ID"""
    resource = db.query(Resource).filter(Resource.id == resource_id).first()
    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Resource not found"
        )
    return resource

@router.put("/{resource_id}", response_model=ResourceResponse)
async def update_resource(
    resource_id: int,
    title: str = None,
    description: str = None,
    category: str = None,
    audience: str = None,
    image_url: str = None,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update resource metadata (not the file itself)"""
    # Get and verify resource
    resource = db.query(Resource).filter(
        Resource.id == resource_id,
        Resource.uploaded_by == current_user.id
    ).first()
    
    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Resource not found or you don't have permission to update it"
        )
    
    # Update fields if provided
    if title:
        resource.title = title
    if description is not None:
        resource.description = description
    if category:
        resource.category = category
    if audience:
        resource.audience = audience
    if image_url is not None:
        resource.image_url = image_url
    
    db.commit()
    db.refresh(resource)
    
    return resource

@router.get("/by-audience/{audience}", response_model=ResourceListResponse)
async def get_resources_by_audience(
    audience: str,
    category: str = None,
    page: int = 1,
    page_size: int = 10,
    db: Session = Depends(get_db)
):
    """Get resources filtered by audience type (public, counselor, student)"""
    query = db.query(Resource).filter(Resource.audience == audience)
    
    # Apply category filter if provided
    if category:
        query = query.filter(Resource.category == category)
    
    # Get total count for pagination
    total = query.count()
    
    # Apply pagination
    offset = (page - 1) * page_size
    resources = query.order_by(Resource.created_at.desc()).offset(offset).limit(page_size).all()
    
    # If page > 1 and no results, it means we've reached the end
    if page > 1 and len(resources) == 0:
        return ResourceListResponse(total=total, items=[])
    
    return ResourceListResponse(total=total, items=resources)

@router.post("/update-audience")
async def update_existing_resources_audience(
    db: Session = Depends(get_db)
):
    """Set default audience value for existing resources"""
    resources = db.query(Resource).filter(Resource.audience == None).all()
    count = len(resources)
    
    for resource in resources:
        resource.audience = "public"
    
    db.commit()
    
    return {"message": f"Updated {count} resources with default audience value"}

@router.post("/generate-image-urls")
async def generate_image_urls_for_resources(
    db: Session = Depends(get_db)
):
    """Generate image URLs for existing resources based on their titles"""
    resources = db.query(Resource).filter(Resource.image_url == None).all()
    count = len(resources)
    
    base_url = "/images/resources/"
    
    for resource in resources:
        # Generate an image URL from the title
        filename = resource.title.lower().replace(" ", "-").replace("%", "%25") + ".jpg"
        resource.image_url = base_url + filename
    
    db.commit()
    
    return {"message": f"Generated image URLs for {count} resources"}

@router.delete("/{resource_id}")
async def delete_resource(
    resource_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a resource"""
    # Get and verify resource
    resource = db.query(Resource).filter(
        Resource.id == resource_id,
        Resource.uploaded_by == current_user.id
    ).first()
    
    if not resource:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Resource not found or you don't have permission to delete it"
        )
    
    # TODO: Delete file from S3 if needed
    # You might want to add S3 deletion logic here
    
    db.delete(resource)
    db.commit()
    
    return {"message": "Resource deleted successfully"}