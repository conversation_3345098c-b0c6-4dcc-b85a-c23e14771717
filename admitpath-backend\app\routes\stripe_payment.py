# app/routes/stripe_payment.py
from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, status, Header, Request, Query
from sqlalchemy.orm import Session

from app.utils.email_types import EmailType
from app.utils.email_utils import send_email_ses
from app.utils.session_timezone import format_session_times
from ..database import get_db
from sqlalchemy import or_, func, and_
from ..models.stripe_payment import StripePayment, StripePaymentStatus
from ..models.counseling_session import CounselingSession, SessionStatus
from ..models.user import *
from ..models.payment import *
from ..models.counselor_packages import *
from ..schemas.stripe_payment import *
from ..models.counselor_services import *
from ..utils.stripe_utils import *
from ..utils.auth import *
from ..models.student_packages import StudentPackageSubscription, PackageSubscriptionStatus

from datetime import timezone as tz

from zoneinfo import ZoneInfo

router = APIRouter()

stripe.api_key = os.getenv('STRIPE_SECRET_KEY')
endpoint_secret = os.getenv('STRIPE_WEBHOOK_SECRET')


@router.post("/session/reserve-and-checkout", response_model=CheckoutSessionResponse)
async def reserve_and_checkout(
    session_data: SessionReservationCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        # Verify student
        student = db.query(Student).filter(Student.user_id == current_user.id).first()
        if not student:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only students can make payments"
            )

        # Get counselor
        counselor = db.query(Counselor).filter(
            Counselor.id == session_data.counselor_id
        ).first()

        if not counselor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Counselor not found"
            )

        # Check if the time slot is already booked
        existing_session = db.query(CounselingSession).filter(
            CounselingSession.counselor_id == counselor.id,
            CounselingSession.date == session_data.date,
            CounselingSession.start_time <= session_data.end_time,
            CounselingSession.end_time >= session_data.start_time,
            CounselingSession.status != "cancelled"
        ).first()

        if existing_session:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="This time slot is already booked"
            )

        # Create payment record with session details (but no actual session yet)
        payment = Payment(
            service_type=ServiceType.SESSION.value,
            amount=session_data.amount,
            status=PaymentStatus.PENDING.value,
            description=f"Counseling session reservation: {session_data.event_name}",

            # Store session details to create after payment
            counselor_id=counselor.id,
            student_id=student.id,
            student_email=current_user.email,
            event_name=session_data.event_name,
            description_text=session_data.description,
            date=session_data.date,
            start_time=session_data.start_time,
            end_time=session_data.end_time
        )

        db.add(payment)
        db.commit()
        db.refresh(payment)

        # Calculate duration in minutes
        duration = int((session_data.end_time - session_data.start_time).total_seconds() / 60)

        # Create Stripe checkout session
        checkout_session = stripe.checkout.Session.create(
            customer_email=current_user.email,
            payment_method_types=['card'],
            line_items=[{
                'price_data': {
                    'currency': 'usd',
                    'product_data': {
                        'name': f'Counseling Session: {session_data.event_name}',
                        'description': f'Session on {session_data.date.strftime("%Y-%m-%d %H:%M")}',
                    },
                    'unit_amount': int(session_data.amount * 100),
                },
                'quantity': 1,
            }],
            metadata={
                'payment_id': str(payment.id),
                'counselor_id': str(counselor.id),
                'student_id': str(student.id),
                'student_email': current_user.email,
                'event_name': session_data.event_name,
                'session_date': session_data.date.isoformat(),
                'session_start': session_data.start_time.isoformat(),
                'session_end': session_data.end_time.isoformat(),
                'session_duration': str(duration),
                'description': session_data.description
            },
            mode='payment',
            success_url=f'{os.getenv("FRONTEND_URL")}/student/dashboard/sessions/success?payment_id={payment.id}',
            cancel_url=f'{os.getenv("FRONTEND_URL")}/student/dashboard/payments/failed?payment_id={payment.id}'
        )

        # Update payment record with Stripe information
        payment.stripe_session_id = checkout_session.id
        db.commit()

        return CheckoutSessionResponse(
            checkout_url=checkout_session.url,
            session_id=checkout_session.id,
            payment_id=payment.id
        )

    except stripe.error.StripeError as e:
        db.rollback()
        print(f"Stripe error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error creating checkout session: {str(e)}"
        )

@router.post("/session/create-checkout-session", response_model=CheckoutSessionResponse)
async def create_checkout_session(
    session_data: CheckoutSessionCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        # Verify student
        student = db.query(Student).filter(Student.user_id == current_user.id).first()
        if not student:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only students can make payments"
            )

        # Verify counseling session
        counseling_session = db.query(CounselingSession).filter(
            CounselingSession.id == session_data.session_id,
            CounselingSession.student_id == student.id
        ).first()

        if not counseling_session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Session not found or unauthorized"
            )

        # Get existing payment record
        payment = db.query(Payment).filter(
            Payment.session_id == counseling_session.id
        ).first()

        if not payment:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Payment record not found for this session"
            )

        # Create Stripe checkout session
        checkout_session = stripe.checkout.Session.create(
            customer_email=current_user.email,
            payment_method_types=['card'],
            line_items=[{
                'price_data': {
                    'currency': 'usd',
                    'product_data': {
                        'name': f'Counseling Session: {counseling_session.event_name}',
                        'description': f'Session on {counseling_session.date.strftime("%Y-%m-%d %H:%M")}',
                    },
                    'unit_amount': int(payment.amount * 100),
                },
                'quantity': 1,
            }],
            metadata={
                'session_id': str(counseling_session.id),
                'student_id': str(student.id),
                'student_email': current_user.email,
                'counseling_date': counseling_session.date.isoformat()
            },
            mode='payment',
            success_url=f'{os.getenv("FRONTEND_URL")}/student/dashboard/sessions/success?session_id={counseling_session.id}',
            cancel_url=f'{os.getenv("FRONTEND_URL")}/student/dashboard/payments/failed?session_id={counseling_session.id}'
        )

        # Update existing payment record with Stripe information
        payment.stripe_session_id = checkout_session.id
        payment.status = StripePaymentStatus.PENDING.value
        payment.description = f"Payment for session: {counseling_session.event_name} with id: {counseling_session.id}"

        db.commit()
        db.refresh(payment)

        return CheckoutSessionResponse(
            checkout_url=checkout_session.url,
            session_id=checkout_session.id,
            payment_id=payment.id
        )

    except stripe.error.StripeError as e:
        print(f"Stripe error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error creating checkout session: {str(e)}"
        )


@router.post("/package/create-checkout-session", response_model=CheckoutSessionResponse)
async def create_package_checkout_session(
    checkout_data: PackageCheckoutSessionCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    try:
        # Verify student
        student = db.query(Student).filter(Student.user_id == current_user.id).first()
        if not student:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only students can make payments"
            )

        # Get package and verify it's active
        package = db.query(CounselorPackage).filter(
            CounselorPackage.id == checkout_data.package_id,
            CounselorPackage.is_active == True
        ).first()

        if not package:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Package not found or inactive"
            )

        # Find the student's subscription for this package
        subscription = db.query(StudentPackageSubscription).filter(
            StudentPackageSubscription.package_id == package.id,
            StudentPackageSubscription.student_id == student.id,
            StudentPackageSubscription.status == PackageSubscriptionStatus.PENDING.value
        ).order_by(StudentPackageSubscription.created_at.desc()).first()

        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No pending subscription found for this package. Please subscribe first."
            )

        # Find the first session for this subscription
        first_session = db.query(CounselingSession).filter(
            CounselingSession.package_subscription_id == subscription.id
        ).first()

        if not first_session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No session found for this subscription. Please try subscribing again."
            )

        payment = db.query(Payment).filter(
            Payment.package_id == checkout_data.subscription_id,
            Payment.student_id == student.id,
            Payment.status == PaymentStatus.PENDING.value
        ).order_by(Payment.created_at.desc()).first()

        if not payment:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Payment record not found for this package subscription. Please try subscribing again."
            )

        # Create Stripe checkout session
        checkout_session = stripe.checkout.Session.create(
            customer_email=current_user.email,
            payment_method_types=['card'],
            line_items=[{
                'price_data': {
                    'currency': 'usd',
                    'product_data': {
                        'name': f'Package: {package.title}',
                        'description': package.description,
                    },
                    'unit_amount': int(payment.amount * 100),
                },
                'quantity': 1,
            }],
            metadata={
                'package_id': str(package.id),
                'subscription_id': str(subscription.id),
                'student_id': str(student.id),
                'first_session_id': str(first_session.id),
                'selected_service': checkout_data.selected_service
            },
            mode='payment',
            success_url=f'{os.getenv("FRONTEND_URL")}/student/dashboard/packages/success?subscription_id={subscription.id}',
            cancel_url=f'{os.getenv("FRONTEND_URL")}/student/dashboard/payments/failed?subscription_id={subscription.id}'
        )



        # Update existing payment record with Stripe information
        payment.stripe_session_id = checkout_session.id
        payment.status = StripePaymentStatus.PENDING.value
        payment.description = f"Payment for package subscription: {package.title} (Subscription ID: {subscription.id})"
        payment.updated_at = datetime.now(tz.utc)
        db.commit()
        db.refresh(payment)

        return CheckoutSessionResponse(
            checkout_url=checkout_session.url,
            session_id=checkout_session.id,
            payment_id=payment.id
        )

    except stripe.error.StripeError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error creating checkout session: {str(e)}"
        )

@router.post("/webhook")
async def stripe_webhook(
    request: Request,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    # Verify the webhook
    event = await verify_stripe_webhook(request)

    try:
        if event['type'] == 'checkout.session.completed':
            session = event['data']['object']
            metadata = session.get('metadata', {})

            # Find payment record
            payment = db.query(Payment).filter(
                Payment.stripe_session_id == session["id"]
            ).first()

            if not payment:
                print(f"Payment not found for session {session['id']}")
                raise HTTPException(status_code=404, detail="Payment not found")

            if payment.status == PaymentStatus.COMPLETED.value:
                return {"status": "already_processed"}

            # Update payment record
            payment.status = PaymentStatus.COMPLETED.value
            payment.updated_at = datetime.now(tz.utc)
            payment.amount = session.get('amount_total', 0) / 100
            payment.stripe_payment_intent_id = session.get('payment_intent')

            # Handle package payment
            if payment.service_type == ServiceType.PACKAGE.value:
                subscription_id = metadata.get('subscription_id')
                if subscription_id:
                    # Update package subscription status
                    subscription = db.query(StudentPackageSubscription).filter(
                        StudentPackageSubscription.id == subscription_id
                    ).first()

                    if subscription:
                        subscription.status = PackageSubscriptionStatus.ACTIVE.value

                        # Update first session status
                        first_session = db.query(CounselingSession).filter(
                            CounselingSession.package_subscription_id == subscription.id
                        ).first()

                        # Get student and counselor details for email
                        student = db.query(User).join(Student).filter(Student.id == payment.student_id).first()
                        counselor = db.query(User).join(Counselor).filter(Counselor.id == payment.counselor_id).first()
                        package = subscription.package

                        # Format first session times in student's timezone
                        student_timezone = student.timezone or 'UTC'
                        formatted_times = {}
                        if first_session:
                            formatted_times = format_session_times(first_session, student_timezone)

                        # Prepare package data for email
                        package_data = {
                            "package_name": package.title,
                            "student_name": f"{student.first_name} {student.last_name}",
                            "counselor_name": f"{counselor.first_name} {counselor.last_name}",
                            "package_description": package.description,
                            "total_price": package.total_price,
                            "service_hours": subscription.service_hours,
                            "first_session": formatted_times if formatted_times else None
                        }

                        # Send confirmation email to student
                        background_tasks.add_task(
                            send_email_ses,
                            to_email=student.email,
                            email_type=EmailType.PACKAGE_PURCHASE.value,
                            package_data=package_data
                        )

                        # Send notification to counselor
                        background_tasks.add_task(
                            send_email_ses,
                            to_email=counselor.email,
                            email_type=EmailType.PACKAGE_PURCHASE_NOTIFY_COUNSELOR.value,
                            package_data=package_data
                        )

                        # Send notification to admin with UTC times
                        admin_package_data = {
                            **package_data,
                            "first_session_utc": {
                                "date": first_session.date.strftime("%Y-%m-%d") if first_session else None,
                                "start_time": first_session.start_time.strftime("%H:%M UTC") if first_session else None,
                                "end_time": first_session.end_time.strftime("%H:%M UTC") if first_session else None,
                            } if first_session else None
                        }

                        background_tasks.add_task(
                            send_email_ses,
                            to_email=["<EMAIL>", "<EMAIL>"],
                            email_type=EmailType.NOTIFY_ADMIN_PACKAGE_PURCHASE.value,
                            package_data=admin_package_data
                        )

            # Handle individual session payment
            elif payment.service_type == ServiceType.SESSION.value:
                counseling_session = db.query(CounselingSession).get(payment.session_id)
                if counseling_session:
                    # Get student and counselor details
                    student = db.query(User).join(Student).filter(Student.id == counseling_session.student_id).first()
                    counselor = db.query(User).join(Counselor).filter(Counselor.id == counseling_session.counselor_id).first()

                    # Format session times for student's timezone
                    student_timezone = student.timezone or 'UTC'
                    formatted_times = format_session_times(counseling_session, student_timezone)

                    # Prepare session data for email
                    session_data = {
                        "event_name": counseling_session.event_name,
                        "student_name": f"{student.first_name} {student.last_name}",
                        "counselor_name": f"{counselor.first_name} {counselor.last_name}",
                        **formatted_times,
                        "description": counseling_session.description or "",
                        "meeting_link": counseling_session.meeting_link
                    }

                    # Send confirmation email to student with their local timezone
                    background_tasks.add_task(
                        send_email_ses,
                        to_email=student.email,
                        email_type=EmailType.SESSION_CREATION.value,
                        session_data=session_data
                    )

                    # Send notification to counselor
                    counselor_timezone = counselor.timezone or 'UTC'
                    counselor_formatted_times = format_session_times(counseling_session, counselor_timezone)
                    counselor_session_data = {
                        "event_name": counseling_session.event_name,
                        "student_name": f"{student.first_name} {student.last_name}",
                        "counselor_name": f"{counselor.first_name} {counselor.last_name}",
                        **counselor_formatted_times,
                        "description": counseling_session.description or "",
                        "meeting_link": counseling_session.meeting_link
                    }

                    background_tasks.add_task(
                        send_email_ses,
                        to_email=counselor.email,
                        email_type=EmailType.SESSION_CREATION.value,
                        session_data=counselor_session_data
                    )

                    admin_tz_info = ZoneInfo("Asia/Singapore")

                    admin_session_data = {
                        **session_data,
                        "student_email": student.email,
                        "date": counseling_session.date.astimezone(admin_tz_info).strftime("%Y-%m-%d"),
                        "start_time": counseling_session.start_time.astimezone(admin_tz_info).strftime("%H:%M UTC"),
                        "end_time": counseling_session.end_time.astimezone(admin_tz_info).strftime("%H:%M UTC"),
                    }

                    background_tasks.add_task(
                        send_email_ses,
                        to_email=["<EMAIL>", "<EMAIL>"],
                        email_type=EmailType.NOTIFY_ADMIN_SESSION_CREATED.value,
                        session_data=admin_session_data
                    )

            db.commit()
            return {"status": "success", "payment_id": payment.id}

        elif event['type'] in ['checkout.session.expired', 'payment_intent.payment_failed']:
            session = event['data']['object']
            payment = db.query(Payment).filter(
                Payment.stripe_session_id == session.id
            ).first()

            if payment:
                payment.status = PaymentStatus.FAILED.value
                payment.updated_at = datetime.now(tz.utc)
                payment.failure_reason = session.get('last_payment_error', {}).get('message')
                db.commit()

        return {"status": "handled", "event_type": event['type']}

    except HTTPException as he:
        raise he
    except Exception as e:
        db.rollback()
        print(f"Webhook processing failed: {str(e)}")
        return {"status": "error", "message": str(e)}

@router.get("/student/payment-history", response_model=PaymentHistoryPaginatedResponse)
async def get_student_payment_history(
    status: Optional[str] = Query(None, enum=["pending", "completed", "rejected"]),
    service_type: Optional[str] = Query(None, enum=["session", "package", "service"]),
    page: int = Query(1, gt=0),
    page_size: int = Query(10, gt=0, le=100),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student
    student = db.query(Student).filter(Student.user_id == current_user.id).first()
    if not student:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )

    # Build base query
    query = (
        db.query(
            Payment,
            User.first_name.label('counselor_first_name'),
            User.last_name.label('counselor_last_name'),
            User.id.label('counselor_id'),
            CounselingSession.date.label('session_date'),
            CounselorServicesOffered.service_type.label('service_name'),
            CounselorPackage.title.label('package_name')
        )
        .join(
            CounselingSession,
            Payment.session_id == CounselingSession.id,
            isouter=True
        )
        .join(
            CounselorServicesOffered,
            Payment.service_id == CounselorServicesOffered.id,
            isouter=True
        )
        # For package payments, we need to join through StudentPackageSubscription
        .outerjoin(
            StudentPackageSubscription,
            Payment.package_id == StudentPackageSubscription.id
        )
        .outerjoin(
            CounselorPackage,
            StudentPackageSubscription.package_id == CounselorPackage.id
        )
        .join(
            Counselor,
            Payment.counselor_id == Counselor.id,
            isouter=True
        )
        .join(
            User,
            Counselor.user_id == User.id,
            isouter=True
        )
        .filter(Payment.student_id == student.id)
    )

    # Apply filters
    if status:
        query = query.filter(Payment.status == status)

    if service_type:
        # Make sure we're using the correct enum value
        service_type_value = service_type
        if service_type == "package":
            service_type_value = ServiceType.PACKAGE.value
        elif service_type == "session":
            service_type_value = ServiceType.SESSION.value
        elif service_type == "service":
            service_type_value = ServiceType.SERVICE.value

        query = query.filter(Payment.service_type == service_type_value)

    # Get total count for pagination
    total_count = query.count()

    # Apply pagination
    items = query.order_by(Payment.created_at.desc())\
        .offset((page - 1) * page_size)\
        .limit(page_size)\
        .all()

    # Process results
    payment_history = []
    for (
        payment,
        counselor_first_name,
        counselor_last_name,
        counselor_id,
        session_date,
        service_name,
        package_name
    ) in items:
        # Default values
        service_display_name = "Unknown Service"
        payment_date = payment.created_at
        counselor_name = "Unknown Counselor"

        # Set counselor name if available
        if counselor_first_name and counselor_last_name:
            counselor_name = f"{counselor_first_name} {counselor_last_name}"

        # Determine service name and date based on payment type
        if payment.service_type == ServiceType.SESSION.value:
            service_display_name = "Counseling Session"
            if session_date:
                payment_date = session_date
        elif payment.service_type == ServiceType.PACKAGE.value:
            # For package payments, always set a display name even if package_name is None
            if package_name:
                service_display_name = f"Package: {package_name}"
            else:
                # Get package name from the database if not provided in the join
                if payment.package_id:
                    package = db.query(CounselorPackage).join(
                        StudentPackageSubscription,
                        StudentPackageSubscription.package_id == CounselorPackage.id
                    ).filter(StudentPackageSubscription.id == payment.package_id).first()

                    if package:
                        service_display_name = f"Package: {package.title}"
                    else:
                        service_display_name = "Package Purchase"

        payment_history.append({
            "id": payment.id,
            "amount": payment.amount,
            "original_amount": getattr(payment, 'original_amount', payment.amount),
            "discount_amount": getattr(payment, 'discount_amount', 0),
            "is_discounted": getattr(payment, 'is_discounted', False),
            "status": payment.status,
            "created_at": payment.created_at,
            "session_id": payment.session_id,
            "package_id": payment.package_id,
            "service_id": payment.service_id,
            "service_type": payment.service_type,
            "counselor_name": counselor_name,
            "counselor_id": counselor_id,
            "payment_date": payment_date,
            "service_name": service_display_name
        })

    return PaymentHistoryPaginatedResponse(
        items=payment_history,
        total=total_count,
        page=page,
        size=page_size,
        has_more=total_count > (page * page_size)
    )




@router.get("/student/payment-stats", response_model=StudentStatsResponse)
async def get_student_stats(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student
    student = db.query(Student).filter(Student.user_id == current_user.id).first()
    if not student:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied"
        )

    # Calculate total amount spent from completed payments (including all payment types)
    total_amount = db.query(func.coalesce(func.sum(Payment.amount), 0.0))\
        .filter(
            and_(
                Payment.status == PaymentStatus.COMPLETED.value,
                Payment.student_id == student.id
            )
        ).scalar()

    # Count total completed sessions
    total_sessions = db.query(func.count(CounselingSession.id))\
        .outerjoin(Payment, CounselingSession.id == Payment.session_id)\
        .filter(
            and_(
                CounselingSession.student_id == student.id,
                or_(
                    # Include both direct session payments and package sessions
                    and_(
                        Payment.id.isnot(None),
                        Payment.status == PaymentStatus.COMPLETED.value
                    ),
                    CounselingSession.package_subscription_id.isnot(None)
                )
            )
        ).scalar()

    # Calculate total hours spent in sessions
    session_hours = db.query(
        func.coalesce(
            func.sum(
                func.extract('epoch', CounselingSession.end_time - CounselingSession.start_time) / 3600
            ), 0.0
        )
    )\
    .outerjoin(Payment, CounselingSession.id == Payment.session_id)\
    .filter(
        and_(
            CounselingSession.student_id == student.id,
            or_(
                # Include both direct session payments and package sessions
                and_(
                    Payment.id.isnot(None),
                    Payment.status == PaymentStatus.COMPLETED.value
                ),
                CounselingSession.package_subscription_id.isnot(None)
            )
        )
    ).scalar() or 0.0

    # Total hours is the sum of session hours
    total_hours = session_hours

    return StudentStatsResponse(
        total_amount_spent=float(total_amount),
        total_sessions=total_sessions,
        total_hours_spent=round(float(total_hours), 1)  # Round to 1 decimal place
    )
