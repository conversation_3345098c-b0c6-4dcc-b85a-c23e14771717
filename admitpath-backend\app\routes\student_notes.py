# app/routes/student_dashboard.py

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime

from ..database import get_db
from ..models.counseling_session import StudentSessionNotes, CounselingSession
from ..models.user import Student
from ..utils.auth import get_current_user
from ..schemas.student_notes import (
    StudentNotesCreate,
    StudentNotesUpdate,
    StudentNotesResponse,
    StudentNotesList
)

router = APIRouter()

@router.post("/sessions/{session_id}/notes", response_model=StudentNotesResponse)
async def create_session_notes(
    session_id: int,
    notes: StudentNotesCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student exists and get their details
    student = db.query(Student).filter(Student.user_id == current_user.id).first()
    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )
    
    # Verify session exists and belongs to student
    session = db.query(CounselingSession).filter(
        CounselingSession.id == session_id,
        CounselingSession.student_id == student.id
    ).first()
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    
    # Check if notes already exist
    existing_notes = db.query(StudentSessionNotes).filter(
        StudentSessionNotes.session_id == session_id,
        StudentSessionNotes.student_id == student.id
    ).first()
    
    if existing_notes:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Notes already exist for this session. Use PUT to update."
        )
    
    # Create new notes
    db_notes = StudentSessionNotes(
        session_id=session_id,
        student_id=student.id,
        student_notes=notes.student_notes,
        counselor_notes=notes.counselor_notes
    )
    
    db.add(db_notes)
    db.commit()
    db.refresh(db_notes)
    
    return db_notes

@router.get("/sessions/{session_id}/notes", response_model=StudentNotesResponse)
async def get_session_notes(
    session_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student
    student = db.query(Student).filter(Student.user_id == current_user.id).first()
    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )
    
    # Get notes for the session
    notes = db.query(StudentSessionNotes).filter(
        StudentSessionNotes.session_id == session_id,
        StudentSessionNotes.student_id == student.id
    ).first()
    
    if not notes:
        # Return empty notes object instead of 404
        return StudentSessionNotes(
            id=0,
            session_id=session_id,
            student_id=student.id,
            student_notes="",
            counselor_notes="",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
    
    return notes

@router.get("/notes", response_model=StudentNotesList)
async def get_all_session_notes(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student
    student = db.query(Student).filter(Student.user_id == current_user.id).first()
    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )
    
    # Get all notes for the student
    notes = db.query(StudentSessionNotes).filter(
        StudentSessionNotes.student_id == student.id
    ).all()
    
    total = len(notes)
    return StudentNotesList(total=total, items=notes)

@router.put("/sessions/{session_id}/notes", response_model=StudentNotesResponse)
async def update_session_notes(
    session_id: int,
    notes_update: StudentNotesUpdate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student
    student = db.query(Student).filter(Student.user_id == current_user.id).first()
    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )
    
    # Get existing notes
    existing_notes = db.query(StudentSessionNotes).filter(
        StudentSessionNotes.session_id == session_id,
        StudentSessionNotes.student_id == student.id
    ).first()
    
    if not existing_notes:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notes not found for this session"
        )
    
    # Update notes
    update_data = notes_update.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(existing_notes, key, value)
    
    existing_notes.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(existing_notes)
    
    return existing_notes

@router.delete("/sessions/{session_id}/notes")
async def delete_session_notes(
    session_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student
    student = db.query(Student).filter(Student.user_id == current_user.id).first()
    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )
    
    # Find and delete notes
    deleted = db.query(StudentSessionNotes).filter(
        StudentSessionNotes.session_id == session_id,
        StudentSessionNotes.student_id == student.id
    ).delete()
    
    if not deleted:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No notes found for this session"
        )
    
    db.commit()
    
    return {"message": "Session notes deleted successfully"}