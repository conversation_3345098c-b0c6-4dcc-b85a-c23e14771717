# routes/student_packages.py
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import and_
from typing import List, Optional
from datetime import datetime, timedelta, timezone as tz

from app.routes.promo_codes import apply_promo_code

from ..database import get_db
from ..models.student_packages import StudentPackageSubscription, PackageSubscriptionStatus
from ..models.counseling_session import CounselingSession, SessionStatus
from ..models.counselor_packages import CounselorPackage
from ..models.payment import Payment, PaymentStatus, ServiceType
from ..models.user import Student, Counselor, User
from ..utils.auth import get_current_user
from ..utils.zoom_utils import zoom_client
from ..schemas.student_packages import *
from ..schemas.counselor_packages import CounselorPackageSubscriptionResponse
from ..services.reminder_service import schedule_session_reminders

router = APIRouter()

@router.post("/subscribe", response_model=PackageSubscriptionResponse)
async def subscribe_to_package(
    subscription: PackageSubscriptionCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a package subscription with first session"""
    try:
        student = db.query(Student).filter(Student.user_id == current_user.id).first()
        if not student:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only students can subscribe to packages"
            )

        package = db.query(CounselorPackage).filter(
            CounselorPackage.id == subscription.package_id,
            CounselorPackage.is_active == True
        ).first()

        if not package:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Package not found or inactive"
            )

        package_items = {
            item.service_name: item.hours
            for item in package.package_items
        }

        if subscription.selected_service not in package_items:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Service '{subscription.selected_service}' not found in package"
            )

        service_hours = {
            item.service_name: {"total": item.hours, "used": 0}
            for item in package.package_items
        }

        start_date = datetime.now(tz.utc)
        end_date = None

        db_subscription = StudentPackageSubscription(
            student_id=student.id,
            package_id=package.id,
            status=PackageSubscriptionStatus.PENDING.value,
            start_date=start_date,
            end_date=end_date,
            service_hours=service_hours
        )

        db.add(db_subscription)
        db.flush()

        duration = int((subscription.first_session.end_time - subscription.first_session.start_time).total_seconds() / 60)
        meeting_details = await zoom_client.create_meeting(
            topic=f"Counseling Session: {subscription.selected_service}",
            start_time=subscription.first_session.start_time,
            duration=duration,
        )

        db_session = CounselingSession(
            student_id=student.id,
            counselor_id=package.counselor_id,
            package_subscription_id=db_subscription.id,
            event_name=subscription.first_session.event_name,
            description=f"First session for package: {package.title} - Service: {subscription.selected_service}",
            date=subscription.first_session.date,
            start_time=subscription.first_session.start_time,
            end_time=subscription.first_session.end_time,
            status=SessionStatus.UPCOMING.value,
            zoom_meeting_id=meeting_details["id"],
            meeting_link=meeting_details["join_url"],
            created_at=datetime.now(tz.utc),
            updated_at=datetime.now(tz.utc)
        )

        db.add(db_session)
        db.flush()

        # Get counselor and student emails for reminders
        counselor_user = db.query(User).join(Counselor, User.id == Counselor.user_id).filter(
            Counselor.id == package.counselor_id
        ).first()
        student_user = current_user

        # Schedule reminders
        await schedule_session_reminders(
            db=db,
            session_id=db_session.id,
            counselor_email=counselor_user.email,
            student_email=student_user.email
        )

        original_amount = package.total_price
        final_amount = original_amount
        promo_code_id = None
        discount_amount = 0

        if subscription.promo_code:
            promo_result = apply_promo_code(db, subscription.promo_code, original_amount, package.counselor_id)
            final_amount = promo_result["final_amount"]
            promo_code_id = promo_result["promo_code"].id
            discount_amount = promo_result["discount_amount"]
            print(promo_result)

        payment = Payment(
            package_id=db_subscription.id,
            service_type=ServiceType.PACKAGE.value,
            amount=final_amount,
            original_amount=original_amount,
            discount_amount=discount_amount,
            is_discounted=bool(discount_amount),
            status=PaymentStatus.PENDING.value,
            description=f"Package subscription: {package.title} (Subscription ID: {db_subscription.id})",
            student_id=student.id,
            counselor_id=package.counselor_id,
            promo_code_id=promo_code_id,
            created_at=datetime.now(tz.utc),
            updated_at=datetime.now(tz.utc)
        )
        print(final_amount)

        db.add(payment)
        db.commit()
        db.refresh(db_subscription)

        response = {
            "id": db_subscription.id,
            "package_id": db_subscription.package_id,
            "student_id": db_subscription.student_id,
            "status": db_subscription.status,
            "start_date": db_subscription.start_date,
            "end_date": db_subscription.end_date,
            "service_hours": db_subscription.service_hours,
            "first_session_id": db_session.id,
            "created_at": db_subscription.created_at,
            "updated_at": db_subscription.updated_at,
            "original_price": original_amount,
            "final_price": final_amount,
            "discount_amount": discount_amount,
            "is_discounted": bool(discount_amount)
        }

        progress = {}
        for service, hours in db_subscription.service_hours.items():
            total = hours.get("total", 0)
            used = hours.get("used", 0)
            progress[service] = {
                "total": total,
                "used": used,
                "remaining": total - used
            }
        response["progress"] = progress

        return response

    except Exception as e:
        db.rollback()

        if isinstance(e, HTTPException):
            raise

        print(f"Error subscribing to package: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to subscribe to package: {str(e)}"
        )


@router.get("/my-packages", response_model=List[CounselorPackageSubscriptionResponse])
async def get_my_packages(
    status: Optional[str] = None,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed package information for the current student

    Args:
        status: Optional filter for subscription status. Defaults to 'active'.
               Valid values: 'pending', 'active', 'completed', 'cancelled', 'expired'
    """
    student = db.query(Student).filter(Student.user_id == current_user.id).first()
    if not student:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can view packages"
        )

    # Default to active status if not specified
    subscription_status = status or PackageSubscriptionStatus.ACTIVE.value

    # Validate status if provided
    if subscription_status not in [s.value for s in PackageSubscriptionStatus]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid status. Valid values: {', '.join([s.value for s in PackageSubscriptionStatus])}"
        )

    subscriptions = (
        db.query(
            StudentPackageSubscription,
            CounselorPackage,
            User
        )
        .join(
            CounselorPackage,
            StudentPackageSubscription.package_id == CounselorPackage.id
        )
        .join(
            Counselor,
            CounselorPackage.counselor_id == Counselor.id
        )
        .join(
            User,
            Counselor.user_id == User.id
        )
        .filter(
            StudentPackageSubscription.student_id == student.id,
            StudentPackageSubscription.status == subscription_status
        )
        .all()
    )

    result = []
    for subscription, package, counselor in subscriptions:
        sessions = db.query(CounselingSession).filter(
            CounselingSession.package_subscription_id == subscription.id
        ).all()

        student_user = db.query(User).filter(User.id == current_user.id).first()

        result.append({
            "id": subscription.id,
            "package": {
                "id": package.id,
                "title": package.title,
                "description": package.description,
                "counselor_name": f"{counselor.first_name} {counselor.last_name}",
                "counselor_profile_picture": counselor.profile_picture_url
            },
            "student": {
                "id": student.id,
                "name": f"{student_user.first_name} {student_user.last_name}",
                "profile_picture": student_user.profile_picture_url
            },
            "start_date": subscription.start_date,
            "end_date": subscription.end_date,
            "status": subscription.status,
            "service_hours": subscription.service_hours,
            "sessions": [
                {
                    "id": session.id,
                    "date": session.date,
                    "start_time": session.start_time,
                    "end_time": session.end_time,
                    "status": session.status,
                    "event_name": session.event_name
                }
                for session in sessions
            ]
        })

    return result
