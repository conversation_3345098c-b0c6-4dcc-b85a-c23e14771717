# app/routes/student_profile.py
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import json
from ..database import get_db
from ..models.user import Student, User
from ..models.student_profile import *
from ..schemas.student_profile import *
from ..utils.auth import get_current_user
from ..utils.verify_user import verify_student

router = APIRouter()

async def check_profile_completion(student: Student, db: Session):
    """Check if all required profile sections are complete and update is_profile_complete"""
    completion_status = json.loads(student.profile_completion_status or '{}')

    # Check if all required sections are complete
    required_sections = ['personal_info', 'educational_background']
    is_complete = all(completion_status.get(section, False) for section in required_sections)

    # p.s This doesnt matter in the frontend anymore. since its just one column we are tracking it anyway just in case.
    if student.is_profile_complete != is_complete:
        student.is_profile_complete = is_complete
        db.commit()

@router.post("/personal-info")
async def create_personal_info(
    personal_info: PersonalInfoCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Get student profile
    student = await verify_student(current_user.id, db)

    # Check if personal info already exists for this student
    existing_info = db.query(StudentPersonalInfo).filter(
        StudentPersonalInfo.student_id == student.id
    ).first()

    if existing_info:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Personal information already exists for this student"
        )

    # Create personal info excluding first_name and last_name
    personal_info_dict = personal_info.dict(exclude={'first_name', 'last_name'})
    db_personal_info = StudentPersonalInfo(
        student_id=student.id,
        **personal_info_dict
    )
    db.add(db_personal_info)

    # Update user's first and last name if provided
    if personal_info.first_name or personal_info.last_name:
        user = db.query(User).filter(User.id == current_user.id).first()
        if personal_info.first_name:
            user.first_name = personal_info.first_name
        if personal_info.last_name:
            user.last_name = personal_info.last_name

    # Update profile completion status
    completion_status = json.loads(student.profile_completion_status or '{}')
    completion_status['personal_info'] = True
    student.profile_completion_status = json.dumps(completion_status)

    await check_profile_completion(student, db)

    db.commit()

    return {"message": "Personal information added successfully"}

@router.get("/personal-info", response_model=PersonalInfoResponse)
async def get_personal_info(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get personal information for the logged-in student
    """
    # Verify student
    student = await verify_student(current_user.id, db)

    # Get personal info with user details
    personal_info = db.query(
        StudentPersonalInfo,
        User.first_name,
        User.last_name
    ).join(
        Student, StudentPersonalInfo.student_id == Student.id
    ).join(
        User, Student.user_id == User.id
    ).filter(
        StudentPersonalInfo.student_id == student.id
    ).first()

    if not personal_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Personal information not found"
        )

    # Combine the results into a single dictionary
    result = {
        "id": personal_info[0].id,
        "student_id": personal_info[0].student_id,
        "nationality": personal_info[0].nationality,
        "country_of_residence": personal_info[0].country_of_residence,
        "gender": personal_info[0].gender,
        "date_of_birth": personal_info[0].date_of_birth,
        "first_name": personal_info[1],  # From User table
        "last_name": personal_info[2],    # From User table
        "goals": personal_info[0].goals
    }

    return result

@router.put("/personal-info", response_model=PersonalInfoResponse)
async def update_personal_info(
    personal_info: PersonalInfoCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update personal information for the logged-in student
    """
    # Verify student
    student = await verify_student(current_user.id, db)

    # Check if personal info exists
    existing_info = db.query(StudentPersonalInfo).filter(
        StudentPersonalInfo.student_id == student.id
    ).first()

    if not existing_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Personal information not found. Please create personal info first."
        )

    try:
        # Start transaction
        # Update user information (first_name and last_name)
        user = db.query(User).filter(User.id == student.user_id).first()
        user.first_name = personal_info.first_name
        user.last_name = personal_info.last_name

        # Update personal info
        for key, value in personal_info.dict(exclude={'first_name', 'last_name'}).items():
            setattr(existing_info, key, value)

        await check_profile_completion(student, db)

        db.commit()
        db.refresh(existing_info)
        db.refresh(user)

        # Combine the results into a single dictionary
        result = {
            "id": existing_info.id,
            "student_id": existing_info.student_id,
            "nationality": existing_info.nationality,
            "country_of_residence": existing_info.country_of_residence,
            "gender": existing_info.gender,
            "date_of_birth": existing_info.date_of_birth,
            "goals": existing_info.goals,
            "first_name": user.first_name,
            "last_name": user.last_name
        }

        return result

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update personal information: {str(e)}"
        )

@router.post("/educational-background", response_model=EducationResponse)
async def create_educational_background(
    background: EducationCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student
    student = await verify_student(current_user.id, db)

    # Create educational background
    db_background = StudentEducationalBackground(
        student_id=student.id,
        **background.dict()
    )
    db.add(db_background)

    # Update profile completion status if this is their first education entry
    if not db.query(StudentEducationalBackground).filter(
        StudentEducationalBackground.student_id == student.id
    ).first():
        completion_status = json.loads(student.profile_completion_status or '{}')
        completion_status['educational_background'] = True
        student.profile_completion_status = json.dumps(completion_status)

    await check_profile_completion(student, db)

    db.commit()
    db.refresh(db_background)

    return db_background

@router.get("/educational-background", response_model=EducationalBackgroundListResponse)
async def get_educational_backgrounds(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student
    student = await verify_student(current_user.id, db)

    # Get all educational backgrounds
    backgrounds = db.query(StudentEducationalBackground).filter(
        StudentEducationalBackground.student_id == student.id
    ).order_by(StudentEducationalBackground.start_date.desc()).all()

    return {"items": backgrounds}

@router.put("/educational-background/{background_id}", response_model=EducationResponse)
async def update_educational_background(
    background_id: int,
    background: EducationCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student
    student = await verify_student(current_user.id, db)

    # Get existing educational background
    existing_background = db.query(StudentEducationalBackground).filter(
        StudentEducationalBackground.id == background_id,
        StudentEducationalBackground.student_id == student.id
    ).first()

    if not existing_background:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Educational background not found or you don't have permission to edit it"
        )

    # Update fields
    for key, value in background.dict().items():
        setattr(existing_background, key, value)

    try:
        await check_profile_completion(student, db)

        db.commit()
        db.refresh(existing_background)
        return existing_background

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update educational background: {str(e)}"
        )

@router.delete("/educational-background/{background_id}")
async def delete_educational_background(
    background_id: int,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student
    student = await verify_student(current_user.id, db)

    # Get and verify educational background
    background = db.query(StudentEducationalBackground).filter(
        StudentEducationalBackground.id == background_id,
        StudentEducationalBackground.student_id == student.id
    ).first()

    if not background:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Educational background not found or you don't have permission to delete it"
        )

    try:
        db.delete(background)
        db.commit()

        # Update completion status if this was the last education entry
        if not db.query(StudentEducationalBackground).filter(
            StudentEducationalBackground.student_id == student.id
        ).first():
            completion_status = json.loads(student.profile_completion_status or '{}')
            completion_status['educational_background'] = False
            student.profile_completion_status = json.dumps(completion_status)
            await check_profile_completion(student, db)
            db.commit()

        return {"message": "Educational background deleted successfully"}

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to delete educational background: {str(e)}"
        )



@router.post("/expected-services", response_model=ExpectedServicesResponse)
async def create_expected_services(
    services: ExpectedServicesCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student
    student = await verify_student(current_user.id, db)

    # Check if services already exists
    existing_services = db.query(StudentExpectedServices).filter(
        StudentExpectedServices.student_id == student.id
    ).first()

    if existing_services:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Expected services already exist for this student"
        )

    # Create services JSON
    services_data = {
        "selected_services": services.selected_services,
        "additional_details": services.additional_details
    }

    # Create service record
    db_services = StudentExpectedServices(
        student_id=student.id,
        service_type=json.dumps(services_data)
    )
    db.add(db_services)

    # Update profile completion status
    completion_status = json.loads(student.profile_completion_status or '{}')
    completion_status['expected_services'] = True
    student.profile_completion_status = json.dumps(completion_status)

    await check_profile_completion(student, db)

    db.commit()
    db.refresh(db_services)

    return db_services

@router.get("/expected-services")
async def get_expected_services(
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student
    student = await verify_student(current_user.id, db)

    # Get services
    services = db.query(StudentExpectedServices).filter(
        StudentExpectedServices.student_id == student.id
    ).first()

    if not services:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expected services not found"
        )

    # Parse the JSON string to return actual data
    services_data = json.loads(services.service_type)

    return {
        "id": services.id,
        "student_id": services.student_id,
        "selected_services": services_data["selected_services"],
        "additional_details": services_data.get("additional_details"),
        "created_at": services.created_at,
        "updated_at": services.updated_at
    }

@router.put("/expected-services")
async def update_expected_services(
    services: ExpectedServicesCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    # Verify student
    student = await verify_student(current_user.id, db)

    # Get existing services
    existing_services = db.query(StudentExpectedServices).filter(
        StudentExpectedServices.student_id == student.id
    ).first()

    if not existing_services:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Expected services not found. Please create them first."
        )

    # Update services JSON
    services_data = {
        "selected_services": services.selected_services,
        "additional_details": services.additional_details
    }

    # Update the record
    existing_services.service_type = json.dumps(services_data)

    try:
        await check_profile_completion(student, db)

        db.commit()
        db.refresh(existing_services)

        # Parse the JSON string to return actual data
        updated_data = json.loads(existing_services.service_type)

        return {
            "id": existing_services.id,
            "student_id": existing_services.student_id,
            "selected_services": updated_data["selected_services"],
            "additional_details": updated_data.get("additional_details"),
            "created_at": existing_services.created_at,
            "updated_at": existing_services.updated_at
        }

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update expected services: {str(e)}"
        )

@router.get("/by-student-id/{student_id}", response_model=StudentBasicInfoResponse)
async def get_student_by_id(
    student_id: int,
    db: Session = Depends(get_db)
):
    """
    Get basic student information by student ID (not user ID)
    Returns user_id, student_id, first_name, and last_name
    """
    # Query the student and join with User to get the name information
    student_info = db.query(
        Student.id.label('student_id'),
        Student.user_id.label('user_id'),
        User.first_name,
        User.last_name
    ).join(
        User, User.id == Student.user_id
    ).filter(
        Student.id == student_id
    ).first()

    if not student_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student not found"
        )

    return {
        "student_id": student_info.student_id,
        "user_id": student_info.user_id,
        "first_name": student_info.first_name,
        "last_name": student_info.last_name
    }