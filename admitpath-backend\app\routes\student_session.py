import traceback
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from sqlalchemy.orm import Session, joinedload
from typing import Optional
from datetime import datetime, date, time, timedelta, timezone as tz
from sqlalchemy import or_, and_
from zoneinfo import ZoneInfo

from app.routes.promo_codes import apply_promo_code

from ..database import get_db
from ..models.counseling_session import CounselingSession, SessionStatus
from ..models.user import Counselor, Student, User
from ..models.counselor_services import CounselorServicesOffered
from ..schemas.student_session import *
from ..schemas.student_packages import PackageSessionCreate
from ..utils.auth import get_current_user
from ..utils.zoom_utils import *
from ..models.payment import Payment, PaymentStatus, ServiceType
from ..models.counselor_availability import *
from ..models.student_packages import StudentPackageSubscription, PackageSubscriptionStatus
from ..services.reminder_service import schedule_session_reminders

router = APIRouter()

@router.get("/counselor/{counselor_user_id}/services")
async def get_counselor_services(
    counselor_user_id: int,
    db: Session = Depends(get_db)
):
    """Get all services offered by a counselor"""
    # Get counselor by user_id
    counselor = db.query(Counselor).join(
        User, User.id == Counselor.user_id
    ).filter(
        User.id == counselor_user_id
    ).first()

    if not counselor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Counselor not found"
        )

    # Get services
    services = db.query(CounselorServicesOffered).filter(
        CounselorServicesOffered.counselor_id == counselor.id
    ).all()

    return services

@router.post("/counselor/{counselor_user_id}/sessions", response_model=CreateSessionResponse)
async def create_student_session(
    counselor_user_id: int,
    data: StudentSessionWithPaymentCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new session from student side with timezone support"""
    try:
        # Get student
        student_data = db.query(Student).filter(
            Student.user_id == current_user.id
        ).first()

        if not student_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Student profile not found"
            )

        student = student_data

        # Get counselor
        counselor = db.query(Counselor).join(
            User, User.id == Counselor.user_id
        ).filter(
            User.id == counselor_user_id
        ).first()

        if not counselor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Counselor not found"
            )

        # Handling promo code application
        original_amount = data.payment.amount
        final_amount = original_amount
        promo_code_id = None
        discount_amount = 0

        if data.payment.promo_code:
            promo_result = apply_promo_code(db, data.payment.promo_code, original_amount, counselor.id)
            final_amount = promo_result["final_amount"]
            promo_code_id = promo_result["promo_code"].id
            discount_amount = promo_result["discount_amount"]

        # Convert local times to UTC
        try:
            # Localize and convert times
            utc_start = data.session.start_time.replace(tzinfo=tz.utc)
            utc_end = data.session.end_time.replace(tzinfo=tz.utc)

            utc_date = utc_start.date()
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid timezone conversion: {str(e)}"
            )

        # Calculate duration for Zoom meeting
        duration = int((utc_end - utc_start).total_seconds() / 60)

        # Create Zoom meeting
        meeting_details = await zoom_client.create_meeting(
            topic=f"Counseling Session: {data.session.service_type}",
            start_time=utc_start,
            duration=duration,
        )

        # Create session with UTC times
        db_session = CounselingSession(
            counselor_id=counselor.id,
            student_id=student.id,
            event_name=data.session.service_type,
            description=data.session.description,
            date=utc_date,
            start_time=utc_start,
            end_time=utc_end,
            meeting_link=meeting_details['join_url'],
            zoom_meeting_id=meeting_details['id'],
            status=SessionStatus.UPCOMING.value
        )

        db.add(db_session)
        db.commit()
        db.refresh(db_session)

        # Create payment if provided
        payment = Payment(
            session_id=db_session.id,
            service_type=ServiceType.SESSION.value,
            amount=final_amount,
            original_amount=original_amount,
            discount_amount=discount_amount,
            is_discounted=bool(discount_amount),
            status=PaymentStatus.PENDING.value,
            description=f"Counseling session: {db_session.event_name}",
            student_id=student.id,
            counselor_id=counselor.id,
            promo_code_id=promo_code_id,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            counselor_payout_status="pending"
        )
        db.add(payment)
        db.commit()
        db.refresh(db_session)

        # Get counselor and student emails for reminders
        counselor_user = db.query(User).filter(User.id == counselor_user_id).first()
        student_user = current_user

        # Schedule reminders
        await schedule_session_reminders(
            db=db,
            session_id=db_session.id,
            counselor_email=counselor_user.email,
            student_email=student_user.email
        )

        return db_session

    except Exception as e:
        db.rollback()
        print(f"Error creating session: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create session: {str(e)}"
        )

@router.get("/my-sessions", response_model=StudentSessionListResponse)
async def get_student_sessions(
    timezone: str,
    status: Optional[str] = None,
    from_date: Optional[date] = None,
    to_date: Optional[date] = None,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get student sessions with timezone conversion"""
    # Get student profile
    student = db.query(Student).filter(
        Student.user_id == current_user.id
    ).first()

    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student profile not found"
        )

    # Timezone setup
    try:
        tz_info = ZoneInfo(timezone)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid timezone: {str(e)}"
        )

    # Build query with counselor information
    query = db.query(
        CounselingSession,
        User.id.label('counselor_user_id'),
        User.first_name.label('counselor_first_name'),
        User.last_name.label('counselor_last_name'),
        User.profile_picture_url.label('counselor_profile_picture')
    ).join(
        Counselor, CounselingSession.counselor_id == Counselor.id
    ).join(
        User, Counselor.user_id == User.id
    ).outerjoin(
        Payment, CounselingSession.id == Payment.session_id
    ).filter(
        CounselingSession.student_id == student.id,
        or_(
            and_(Payment.id.isnot(None), Payment.status == "completed"),
            CounselingSession.package_subscription_id.isnot(None)
        )
    )

    # Date filtering with timezone conversion
    if from_date or to_date:
        try:
            if from_date:
                local_from = datetime.combine(from_date, time.min).replace(tzinfo=tz_info)
                utc_from = local_from.astimezone(tz.utc)
                query = query.filter(CounselingSession.date >= utc_from)

            if to_date:
                local_to = datetime.combine(to_date, time.max).replace(tzinfo=tz_info)
                utc_to = local_to.astimezone(tz.utc)
                query = query.filter(CounselingSession.date <= utc_to)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid date conversion: {str(e)}"
            )

    if status:
        query = query.filter(CounselingSession.status == status)
    if status == SessionStatus.UPCOMING.value:
        query = query.filter(CounselingSession.end_time > datetime.now())

    # Execute query
    total = query.count()
    results = query.options(joinedload(CounselingSession.payment)).order_by(CounselingSession.date.desc()).all()

    # Convert times to requested timezone
    processed_sessions = []
    for session_data in results:
        session, counselor_user_id, counselor_first_name, counselor_last_name, counselor_profile_picture = session_data
        try:
            # Convert UTC times to local timezone
            start_local = session.start_time.replace(tzinfo=tz.utc).astimezone(tz_info)
            end_local = session.end_time.replace(tzinfo=tz.utc).astimezone(tz_info)
            created_local = session.created_at.replace(tzinfo=tz.utc).astimezone(tz_info)
            updated_local = session.updated_at.replace(tzinfo=tz.utc).astimezone(tz_info)

            date_local = start_local.date()
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Time conversion error: {str(e)}"
            )

        # Create counselor's full name
        counselor_name = f"{counselor_first_name} {counselor_last_name}"

        session_dict = {
            "id": session.id,
            "counselor_id": session.counselor_id,
            "counselor_user_id": counselor_user_id,
            "student_id": session.student_id,
            "event_name": session.event_name,
            "date": date_local,
            "start_time": start_local,
            "end_time": end_local,
            "status": session.status,
            "description": session.description,
            "created_at": created_local,
            "updated_at": updated_local,
            "payment": session.payment,
            "meeting_link": session.meeting_link,
            "counselor_name": counselor_name,
            "counselor_profile_picture": counselor_profile_picture
        }

        processed_sessions.append(session_dict)

    return StudentSessionListResponse(total=total, items=processed_sessions)