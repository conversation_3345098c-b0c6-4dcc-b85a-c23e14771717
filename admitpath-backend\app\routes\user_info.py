from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from ..database import get_db
from ..models.user import User, Student, Counselor
from ..schemas.user_info import UserInfoResponse
from ..utils.auth import get_current_user
from typing import Optional
from zoneinfo import ZoneInfo

router = APIRouter()

@router.get("/me", response_model=UserInfoResponse)
async def get_user_info(
    timezone: Optional[str] = None,  # Add optional timezone parameter
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get information about the currently logged-in user.
    This endpoint works for both students and counselors.
    If a timezone is provided, it will update the user's timezone preference.
    """
    # Get the complete user object with relationships
    user = db.query(User).filter(User.id == current_user.id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Update timezone if provided and different from stored value
    if timezone and (user.timezone is None or user.timezone != timezone):
        try:
            ZoneInfo(timezone)
            user.timezone = timezone
            db.commit()
        except:
            pass
    
    # Get the appropriate profile based on user type
    if user.user_type == "student":
        profile = db.query(Student).filter(Student.user_id == user.id).first()
    else:  # counselor
        profile = db.query(Counselor).filter(Counselor.user_id == user.id).first()
    
    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"{user.user_type.capitalize()} profile not found"
        )
    
    # Use the custom method to create the response
    return UserInfoResponse.from_orm_custom(user, profile)

@router.get("/{user_id}", response_model=UserInfoResponse)
async def get_user_info_by_id(
    user_id: int,
    current_user = Depends(get_current_user),  # Keep authentication to ensure only logged-in users can access
    db: Session = Depends(get_db)
):
    """
    Get information about a specific user by their ID.
    This endpoint works for both students and counselors.
    """
    # Get the complete user object with relationships
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Get the appropriate profile based on user type
    if user.user_type == "student":
        profile = db.query(Student).filter(Student.user_id == user.id).first()
    else:  # counselor
        profile = db.query(Counselor).filter(Counselor.user_id == user.id).first()

    if not profile:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"{user.user_type.capitalize()} profile not found"
        )

    # Use the custom method to create the response
    return UserInfoResponse.from_orm_custom(user, profile)