from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Dict, List, Optional
from datetime import datetime, timezone
import json

from ..database import get_db
from ..models.user import User, Counselor, Student
from ..models.direct_message import DirectMessage
from ..models.user_connection import UserConnection
from ..schemas.direct_message import MessageResponse
from ..utils.auth import get_current_user_ws
from ..services.websocket_manager import manager

router = APIRouter()

@router.websocket("/ws/chat/{channel_id}")
async def websocket_chat_endpoint(
    websocket: WebSocket,
    channel_id: str,
    db: Session = Depends(get_db)
):
    """
    WebSocket endpoint for real-time chat.
    Handles connection, message receiving, and disconnection.
    """
    # Authenticate the user
    try:
        token = websocket.query_params.get("token")
        if not token:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        current_user = await get_current_user_ws(token, db)
        if not current_user:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        # Verify user has access to this channel
        counselor = db.query(Counselor).filter(Counselor.user_id == current_user.id).first()
        student = db.query(Student).filter(Student.user_id == current_user.id).first()

        # Check if channel exists and user has access
        channel_query = db.query(DirectMessage).filter(
            DirectMessage.channel_id == channel_id
        )

        if counselor:
            channel_query = channel_query.filter(DirectMessage.counselor_id == counselor.id)
        elif student:
            channel_query = channel_query.filter(DirectMessage.student_id == student.id)
        else:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        first_message = channel_query.first()
        if not first_message:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        # Accept the connection
        await manager.connect(websocket, channel_id, current_user.id)

        # Update or create user connection record
        user_connection = db.query(UserConnection).filter(
            UserConnection.user_id == current_user.id,
            UserConnection.channel_id == channel_id
        ).first()

        if user_connection:
            user_connection.is_connected = True
            user_connection.last_connected_at = datetime.now(timezone.utc)
        else:
            user_connection = UserConnection(
                user_id=current_user.id,
                channel_id=channel_id,
                is_connected=True,
                last_connected_at=datetime.now(timezone.utc)
            )
            db.add(user_connection)

        db.commit()

        try:
            # Listen for messages
            while True:
                data = await websocket.receive_text()
                message_data = json.loads(data)

                # Process the message
                if message_data.get("type") == "message":
                    # Get the recipient user
                    recipient_id = message_data.get("recipient_id")
                    message_text = message_data.get("message")
                    created_at = datetime.fromisoformat(message_data.get("created_at"))

                    # Determine counselor_id and student_id
                    if counselor:
                        counselor_id = counselor.id
                        student_id = db.query(Student).filter(Student.user_id == recipient_id).first().id
                    else:
                        counselor_id = db.query(Counselor).filter(Counselor.user_id == recipient_id).first().id
                        student_id = student.id

                    # Create the message in the database
                    db_message = DirectMessage(
                        channel_id=channel_id,
                        counselor_id=counselor_id,
                        student_id=student_id,
                        sender_id=current_user.id,
                        message=message_text,
                        created_at=created_at
                    )

                    db.add(db_message)
                    db.commit()
                    db.refresh(db_message)

                    # Create response with sender's name
                    response = {
                        "id": db_message.id,
                        "channel_id": channel_id,
                        "message": db_message.message,
                        "sender_id": current_user.id,
                        "sender_name": f"{current_user.first_name} {current_user.last_name}",
                        "sender_profile_picture_url": current_user.profile_picture_url,
                        "created_at": db_message.created_at.isoformat()
                    }

                    # Broadcast the message to all clients in the channel
                    await manager.broadcast_to_channel(response, channel_id)

                    # Check if recipient is connected, if not send email notification
                    recipient = db.query(User).filter(User.id == recipient_id).first()
                    if recipient:
                        await manager.send_message_notification(
                            db=db,
                            message=db_message,
                            sender=current_user,
                            recipient=recipient
                        )

        except WebSocketDisconnect:
            # Handle disconnection
            manager.disconnect(websocket)

            # Update user connection record
            user_connection = db.query(UserConnection).filter(
                UserConnection.user_id == current_user.id,
                UserConnection.channel_id == channel_id
            ).first()

            if user_connection:
                user_connection.is_connected = False
                db.commit()

    except Exception as e:
        # Handle any other exceptions
        print(f"WebSocket error: {str(e)}")
        await websocket.close(code=status.WS_1011_INTERNAL_ERROR)
