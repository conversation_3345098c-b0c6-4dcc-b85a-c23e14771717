# schemas/contact.py
from pydantic import BaseModel, EmailStr, Field
from datetime import datetime
from typing import Optional

class ContactInquiryCreate(BaseModel):
    email: EmailStr
    message: str = Field(..., min_length=10, max_length=2000)

class ContactInquiryResponse(BaseModel):
    id: int
    email: str
    message: str
    created_at: datetime
    is_addressed: bool
    addressed_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class ContactInquiryList(BaseModel):
    total: int
    items: list[ContactInquiryResponse]
