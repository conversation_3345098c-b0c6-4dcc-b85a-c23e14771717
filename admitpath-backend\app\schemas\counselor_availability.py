from pydantic import BaseModel,field_validator 
from datetime import datetime, date
from typing import List, Dict
import re


class AvailabilityCreate(BaseModel):
    week_start: date  # Local date (will be converted to UTC)
    available_slots: Dict[str, List[str]]  # {"0": ["09:00", "10:00"], ...}

    @field_validator('available_slots')
    @classmethod
    def validate_slots(cls, v):
        if not all(k in map(str, range(7)) for k in v.keys()):
            raise ValueError("Days must be 0-6")
        for day, times in v.items():
            if not isinstance(times, list):
                raise ValueError("Times must be a list")
            for t in times:
                if not re.match(r"^\d{2}:\d{2}$", t):
                    raise ValueError("Invalid time format")
        return v

class AvailabilityResponse(BaseModel):
    id: int
    counselor_id: int
    week_start: date
    available_slots: Dict[str, List[str]]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True