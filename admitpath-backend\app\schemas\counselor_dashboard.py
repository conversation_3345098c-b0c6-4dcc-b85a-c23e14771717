# schemas/counselor_dashboard.py
from pydantic import BaseModel
from typing import List, Dict, Optional
from datetime import datetime

class UpcomingSession(BaseModel):
    id: int
    event_name: str
    date: datetime
    start_time: datetime
    end_time: datetime
    meeting_link: Optional[str]
    student_name: str

class UpcomingSessions(BaseModel):
    sessions: List[UpcomingSession]
    
class MonthlyEarning(BaseModel):
    month: str  # Month name (Jan, Feb, etc.)
    total: float
    approved: float
    pending: float

class EarningsSummary(BaseModel):
    total_earning: float
    approved: float
    pending: float
    monthly_data: List[MonthlyEarning]    
    

class StudentInfo(BaseModel):
    user_id: int
    student_id: int
    first_name: str
    last_name: str
    email: str
    last_session_date: Optional[datetime]
    total_sessions: int
    upcoming_sessions: int
    
    class Config:
        from_attributes = True

class StudentListResponse(BaseModel):
    total: int
    students: List[StudentInfo]    