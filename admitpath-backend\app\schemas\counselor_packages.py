from pydantic import BaseModel, Field
from typing import List, Optional, Dict
from datetime import datetime

class PackageItemBase(BaseModel):
    service_name: str
    hours: int = Field(..., ge=1, description="Hours must be at least 1")

class PackageItemCreate(PackageItemBase):
    pass

class PackageItemResponse(PackageItemBase):
    id: int
    package_id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class PackageBase(BaseModel):
    title: str
    description: str
    total_price: float
    is_active: bool = True

class PackageCreate(PackageBase):
    items: List[PackageItemCreate] = Field(..., min_items=1, description="At least one service item must be provided")

class PackageUpdate(PackageBase):
    title: Optional[str] = None
    description: Optional[str] = None
    total_price: Optional[float] = None
    is_active: Optional[bool] = None
    items: Optional[List[PackageItemCreate]] = Field(None, min_items=1, description="At least one service item must be provided when the field is used")

class PackageResponse(PackageBase):
    id: int
    counselor_id: int
    package_items: List[PackageItemResponse]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class PackageListResponse(BaseModel):
    total: int
    items: List[PackageResponse]

    class Config:
        from_attributes = True

class CounselorPackageSubscriptionResponse(BaseModel):
    id: int
    package: Dict
    student: Dict
    start_date: datetime
    end_date: Optional[datetime] = None
    status: str
    service_hours: Dict
    sessions: List[Dict]

    class Config:
        from_attributes = True
