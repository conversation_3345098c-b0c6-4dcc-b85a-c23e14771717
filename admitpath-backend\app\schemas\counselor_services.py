from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, List
from datetime import datetime

from ..models.counselor_services import ServiceTypes  # Add this import


class ServiceOfferedCreate(BaseModel):
    service_type: str
    custom_type: Optional[str] = None
    description: str = Field(..., min_length=1, max_length=800)
    price: float = Field(...)

    @validator('price')
    def validate_price(cls, v, values):
        if 'service_type' in values and values['service_type'] == 'Free 15 Minutes Intro Call':
            if v != 0:
                raise ValueError("Price must be 0 for Free Intro Call")
        elif v <= 0:
            raise ValueError("Price must be greater than 0 for paid services")
        return v
    offers_intro_call: bool = False

    @validator('service_type')
    def validate_service_type(cls, v):
        # Just make sure it's not empty
        if not v:
            raise ValueError("Service type cannot be empty")
        return v

    @validator('custom_type')
    def validate_custom_type(cls, v, values):
        # Only validate if service_type is "Other"
        if 'service_type' in values and values['service_type'] == ServiceTypes.OTHER:
            if not v:
                raise ValueError("Custom type is required when service type is 'Other'")
        return v

class ServiceOfferedResponse(BaseModel):
    id: int
    service_type: str
    custom_type: Optional[str]
    description: str
    price: float
    offers_intro_call: bool
    created_at: datetime
    updated_at: datetime
    
    # No longer need the display_name property since we're using service_type directly

    class Config:
        from_attributes = True
        
        

class ServiceUpdate(BaseModel):
    service_type: Optional[str] = None
    custom_type: Optional[str] = None
    description: Optional[str] = Field(None, min_length=1, max_length=800)  # Increased limit to 800 characters
    price: Optional[float] = Field(None)

    @validator('price')
    def validate_price(cls, v, values):
        if v is None:
            return v
        if 'service_type' in values and values['service_type'] == 'Free 15 Minutes Intro Call':
            if v != 0:
                raise ValueError("Price must be 0 for Free Intro Call")
        elif v <= 0:
            raise ValueError("Price must be greater than 0 for paid services")
        return v
    offers_intro_call: Optional[bool] = None

    @validator('service_type')
    def validate_service_type(cls, v):
        if v and v not in ServiceTypes.list():
            raise ValueError(f"Invalid service type. Must be one of: {', '.join(ServiceTypes.list())}")
        return v

    @validator('custom_type')
    def validate_custom_type(cls, v, values):
        if 'service_type' in values and values['service_type'] == ServiceTypes.OTHER:
            if not v:
                raise ValueError("Custom type is required when service type is 'Other'")
        elif v is not None and ('service_type' not in values or values['service_type'] != ServiceTypes.OTHER):
            raise ValueError("Custom type should only be provided when service type is 'Other'")
        return v        