from pydantic import BaseModel, Field
from datetime import datetime
from typing import List, Optional

class MessageCreate(BaseModel):
    created_at: datetime
    recipient_id: int
    message: str = Field(..., min_length=1, max_length=5000)

class MessageResponse(BaseModel):
    id: int
    channel_id: str
    message: str
    sender_id: int
    sender_name: str
    sender_profile_picture_url: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True

class MessageListResponse(BaseModel):
    total: int
    channel_id: str  # Add this line
    messages: List[MessageResponse]
    has_more: bool
    
    
    
class ChannelParticipant(BaseModel):
    user_id: int
    first_name: str
    last_name: str
    user_type: str
    profile_picture_url: Optional[str] = None

    class Config:
        from_attributes = True

class ChannelPreview(BaseModel):
    channel_id: str
    other_participant: ChannelParticipant
    last_message_at: datetime

    class Config:
        from_attributes = True

class ChannelListResponse(BaseModel):
    channels: List[ChannelPreview]   