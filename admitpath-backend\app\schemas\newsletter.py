# schemas/newsletter.py
from pydantic import BaseModel, EmailStr, Field
from datetime import datetime
from typing import List

class NewsletterSubscribe(BaseModel):
    name: str = Field(..., min_length=1, max_length=100)
    email: EmailStr

class NewsletterSubscriberResponse(BaseModel):
    id: int
    name: str
    email: str
    subscribed_at: datetime

    class Config:
        from_attributes = True

class NewsletterSubscriberList(BaseModel):
    total: int
    items: List[NewsletterSubscriberResponse]