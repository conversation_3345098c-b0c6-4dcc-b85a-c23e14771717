# app/schemas/resource.py
from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class ResourceCreate(BaseModel):
    title: str
    description: Optional[str] = None
    file_url: str
    file_type: str
    image_url: Optional[str] = None
    category: str
    audience: Optional[str] = "public"  # "public", "counselor", "student"

class ResourceUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    image_url: Optional[str] = None
    category: Optional[str] = None
    audience: Optional[str] = None

class ResourceResponse(BaseModel):
    id: int
    title: str
    description: Optional[str]
    file_url: str
    file_type: str
    image_url: Optional[str] = None
    category: str
    audience: Optional[str] = "public"
    uploaded_by: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class ResourceListResponse(BaseModel):
    total: int
    items: list[ResourceResponse]