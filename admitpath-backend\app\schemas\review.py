# schemas/review.py
from pydantic import BaseModel, Field
from datetime import datetime
from typing import Optional, List

class ReviewCreate(BaseModel):
    rating: int = Field(..., ge=1, le=5)  # Rating between 1-5
    received_expected_service: bool  # Yes/No question
    comment: str = Field(..., min_length=1, max_length=1000)
    additional_feedback: Optional[str] = Field(None, max_length=400)  # Optional feedback
    
        
class CreateReviewResponse(BaseModel):
    id: int
    session_id: int
    student_id: int
    rating: int
    received_expected_service: bool
    comment: str
    additional_feedback: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True

class ReviewResponse(BaseModel):
    id: int
    session_id: int
    student_id: int
    student_name: str  # Added student name
    student_profile_picture: Optional[str]  # Added profile picture
    rating: int
    received_expected_service: bool
    comment: str
    additional_feedback: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True

class CounselorReviewsResponse(BaseModel):
    average_rating: float
    total_reviews: int
    reviews: List[ReviewResponse]
    page: int
    page_size: int
    total_pages: int
    has_next: bool
    has_prev: bool
    
    
class PendingFeedbackSession(BaseModel):
    session_id: int
    event_name: str
    start_time: datetime
    end_time: datetime
    instructor_name: str

    class Config:
        from_attributes = True

class PendingFeedbackResponse(BaseModel):
    total: int
    sessions: List[PendingFeedbackSession]    