# app/schemas/stripe_payment.py
from pydantic import BaseModel, Field, validator
from typing import Optional
from datetime import datetime
from typing import List
from enum import Enum
from datetime import datetime
from app.schemas.counseling_session import SessionCreate

class PaymentIntentCreate(BaseModel):
    session_id: int
    amount: float = Field(..., gt=0)  # Amount must be greater than 0

class PaymentIntentResponse(BaseModel):
    id: int
    client_secret: str
    amount: float
    status: str
    created_at: datetime

    class Config:
        from_attributes = True

class PaymentStatusResponse(BaseModel):
    id: int
    amount: float
    status: str
    stripe_payment_intent_id: str
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True



class PaymentHistoryResponse(BaseModel):
    id: int
    amount: float
    original_amount: Optional[float] = None
    discount_amount: Optional[float] = 0
    is_discounted: Optional[bool] = False
    status: str
    created_at: datetime
    session_id: Optional[int] = None
    package_id: Optional[int] = None
    service_id: Optional[int] = None
    service_type: Optional[str] = None
    counselor_name: str
    counselor_id: Optional[int] = None
    payment_date: datetime
    service_name: str

    class Config:
        from_attributes = True

class PaymentHistoryPaginatedResponse(BaseModel):
    items: List[PaymentHistoryResponse]
    total: int
    page: int
    size: int
    has_more: bool


class SessionPaymentStatusResponse(BaseModel):
    session_id: int
    is_paid: bool
    payment_id: Optional[int] = None
    payment_status: Optional[str] = None
    payment_date: Optional[datetime] = None
    amount: Optional[float] = None

    class Config:
        from_attributes = True


class PaymentConfirmationResponse(BaseModel):
    payment_id: int
    status: str
    session_id: int
    amount: float
    confirmed_at: datetime

    class Config:
        from_attributes = True


class CheckoutSessionCreate(BaseModel):
    session_id: int

class PackageCheckoutSessionCreate(BaseModel):
    package_id: int
    subscription_id: Optional[int] = None  # Optional, can be determined from package_id and student_id
    selected_service: str

    @validator('selected_service')
    def validate_service_name(cls, v):
        if not v:
            raise ValueError("Must specify a service for the first session")
        return v

class CheckoutSessionResponse(BaseModel):
    checkout_url: str
    session_id: str
    payment_id: Optional[int] = None  # Made optional

    class Config:
        from_attributes = True

class PaymentStatusResponse(BaseModel):
    id: int
    amount: float
    status: str
    stripe_payment_intent_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True



class ServiceCheckoutSessionCreate(BaseModel):
    service_id: int

class ServiceType(str, Enum):
    SESSION = "session"
    PACKAGE = "package"

class SessionReservationCreate(BaseModel):
    counselor_id: int
    event_name: str
    description: str
    date: datetime
    start_time: datetime
    end_time: datetime
    amount: float = Field(..., gt=0)
    service_type: Optional[ServiceType] = ServiceType.SESSION

class PaymentHistoryPaginatedResponse(BaseModel):
    items: List[PaymentHistoryResponse]
    total: int
    page: int
    size: int
    has_more: bool

    class Config:
        from_attributes = True


class StudentStatsResponse(BaseModel):
    total_amount_spent: float
    total_sessions: int
    total_hours_spent: float

    class Config:
        from_attributes = True
