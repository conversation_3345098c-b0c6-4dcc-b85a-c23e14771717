# app/schemas/student_bank_details.py

from pydantic import BaseModel, Field, validator
from typing import Optional
from datetime import datetime

class BankAccountCreate(BaseModel):
    bank_name: str = Field(..., min_length=1, description="Name of the bank")
    account_title: str = Field(..., min_length=1, description="Name on the account")
    account_number: str = Field(..., min_length=1, description="Account number or IBAN")
    swift_code: str = Field(..., min_length=8, max_length=11, description="SWIFT/BIC code")
    is_primary: Optional[bool] = Field(False, description="Whether this is the primary account")

    @validator('swift_code')
    def validate_swift_code(cls, v):
        # SWIFT codes are 8 or 11 characters
        if len(v) not in [8, 11]:
            raise ValueError('SWIFT code must be either 8 or 11 characters long')
        return v.upper()  # Convert to uppercase

class BankAccountUpdate(BaseModel):
    bank_name: Optional[str] = Field(None, min_length=1)
    account_title: Optional[str] = Field(None, min_length=1)
    account_number: Optional[str] = Field(None, min_length=1)
    swift_code: Optional[str] = Field(None, min_length=8, max_length=11)
    is_primary: Optional[bool] = None
    is_active: Optional[bool] = None

    @validator('swift_code')
    def validate_swift_code(cls, v):
        if v is not None:
            if len(v) not in [8, 11]:
                raise ValueError('SWIFT code must be either 8 or 11 characters long')
            return v.upper()
        return v

class BankAccountResponse(BaseModel):
    id: int
    student_id: int
    bank_name: str
    account_title: str
    account_number: str
    swift_code: str
    is_primary: bool
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class BankAccountList(BaseModel):
    total: int
    items: list[BankAccountResponse]

    class Config:
        from_attributes = True