# schemas/student_dashboard.py
from pydantic import BaseModel
from datetime import datetime
from typing import List, Optional
from ..schemas.payment import PaymentResponse

class DashboardStats(BaseModel):
    total_sessions: int
    upcoming_sessions: int
    total_counsellors: int

class StudentSessionResponse(BaseModel):
    id: int
    event_name: str
    date: datetime
    start_time: datetime
    end_time: datetime
    student_id: int
    counselor_id: int
    counselor_user_id: int
    meeting_link: Optional[str]
    status: str
    payment: Optional[PaymentResponse] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class StudentSessionListResponse(BaseModel):
    total: int
    items: List[StudentSessionResponse]
    

class CounselorInfo(BaseModel):
    user_id: int
    counselor_id: int
    first_name: str
    last_name: str
    email: str
    profile_picture_url: Optional[str] = None
    last_session_date: Optional[datetime]
    total_sessions: int
    upcoming_sessions: int
    is_verified: bool
    
    class Config:
        from_attributes = True

class CounselorListResponse(BaseModel):
    total: int
    counselors: List[CounselorInfo]    