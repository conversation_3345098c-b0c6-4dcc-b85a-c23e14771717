# app/schemas/student_notes.py

from pydantic import BaseModel, Field, validator
from datetime import datetime
from typing import Optional

class StudentNotesCreate(BaseModel):
    student_notes: str = Field(..., min_length=1, description="Notes content from the student")
    counselor_notes: Optional[str] = Field(None, description="Notes content from the counselor")

class StudentNotesUpdate(BaseModel):
    student_notes: Optional[str] = Field(None, min_length=1, description="Updated notes content from student")
    counselor_notes: Optional[str] = Field(None, description="Updated notes content from counselor")

    class Config:
        # This ensures at least one field is provided during update
        @validator('counselor_notes', 'student_notes')
        def validate_not_all_none(cls, v, values):
            if not v and not any(values.values()):
                raise ValueError('At least one field must be provided for update')
            return v

class StudentNotesResponse(BaseModel):
    id: int
    session_id: int
    student_id: int
    student_notes: str
    counselor_notes: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class StudentNotesList(BaseModel):
    total: int
    items: list[StudentNotesResponse]