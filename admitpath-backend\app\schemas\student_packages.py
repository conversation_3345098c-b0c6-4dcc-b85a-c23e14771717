# schemas/student_packages.py
from pydantic import BaseModel, validator
from typing import Optional, Dict
from datetime import datetime
from .counseling_session import SessionCreate

class PackageSubscriptionCreate(BaseModel):
    package_id: int
    first_session: SessionCreate
    selected_service: str
    promo_code: Optional[str] = None

    @validator('selected_service')
    def validate_service_name(cls, v, values):
        if not v:
            raise ValueError("Must specify a service for the first session")
        return v

class ServiceProgress(BaseModel):
    total: int
    used: int
    remaining: int

class PackageSubscriptionResponse(BaseModel):
    id: int
    package_id: int
    student_id: int
    status: str
    start_date: datetime
    end_date: Optional[datetime]
    service_hours: Dict[str, Dict[str, int]]
    first_session_id: Optional[int]
    created_at: datetime
    updated_at: datetime

    # Additional computed fields for frontend
    progress: Dict[str, ServiceProgress]

    @validator('progress', pre=True, always=True)
    def compute_progress(cls, v, values):
        progress = {}
        service_hours = values.get('service_hours', {})
        for service, hours in service_hours.items():
            total = hours.get('total', 0)
            used = hours.get('used', 0)
            progress[service] = {
                'total': total,
                'used': used,
                'remaining': total - used
            }
        return progress

    class Config:
        orm_mode = True

class PackageSessionCreate(BaseModel):
    service_name: str
    date: datetime
    start_time: datetime
    end_time: datetime
