# app/schemas/student_profile.py
from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List

class StudentBasicInfoResponse(BaseModel):
    user_id: int
    student_id: int
    first_name: str
    last_name: str

    class Config:
        from_attributes = True

class PersonalInfoCreate(BaseModel):
    nationality: str
    country_of_residence: str
    gender: str
    date_of_birth: Optional[datetime] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    goals: Optional[str] = None

class PersonalInfoResponse(BaseModel):
    id: int
    student_id: int
    nationality: str
    country_of_residence: str
    gender: str
    date_of_birth: Optional[datetime] = None  
    first_name: str
    last_name: str
    goals: Optional[str] = None

    class Config:
        from_attributes = True

class EducationCreate(BaseModel):
    current_education_level: str
    institution_name: str
    institution_type: str
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    is_current: bool = False

class EducationResponse(BaseModel):
    id: int
    student_id: int
    current_education_level: str
    institution_name: str
    institution_type: str
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    is_current: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class EducationalBackgroundListResponse(BaseModel):
    items: List[EducationResponse]

class EducationalBackgroundUpdate(BaseModel):
    current_education_level: Optional[str] = None
    school_name: Optional[str] = None
    university_name: Optional[str] = None


class ExpectedServicesCreate(BaseModel):
    selected_services: List[str]
    additional_details: Optional[str] = None

class ExpectedServicesResponse(BaseModel):
    id: int
    student_id: int
    service_type: str  # This will be the JSON string
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True