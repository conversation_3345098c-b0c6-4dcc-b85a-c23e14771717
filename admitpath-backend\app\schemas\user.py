from pydantic import BaseModel, EmailStr, field_validator
from typing import Optional, List, Dict
from enum import Enum
from fastapi import UploadFile
from datetime import datetime
import re
from ..utils.auth import validate_password
import json

# counselor schemas
class CounselorSignup(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str

    @field_validator('password')
    @classmethod
    def validate_password(cls, password: str) -> str:
        return validate_password(password)


class CounselorResponse(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str
    profile_completion_status: Optional[Dict]
    is_profile_complete: bool
    is_verified: bool
    
    class Config:
        from_attributes = True

    @field_validator('profile_completion_status')
    @classmethod
    def parse_json(cls, v):
        if isinstance(v, str):
            return json.loads(v)
        return v
        
# forgot password schemas 
class ForgotPasswordRequest(BaseModel):
    email: EmailStr

class VerificationRequest(BaseModel):
    email: EmailStr
    code: str

class ResetPasswordRequest(BaseModel):
    email: EmailStr
    code: str
    new_password: str

    @field_validator('new_password')
    @classmethod
    def validate_new_password(cls, password: str) -> str:
        return validate_password(password)
    
# Student schemas    
class StudentSignup(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str

    @field_validator('password')
    @classmethod
    def validate_password(cls, password: str) -> str:
        return validate_password(password)
    
class StudentResponse(BaseModel):
    id: int
    email: str
    first_name: str
    last_name: str
    profile_completion_status: dict
    is_profile_complete: bool

    class Config:
        from_attributes = True        
        
class LoginSchema(BaseModel):
    username: str
    password: str        
    
    
# Google auth schemas
class UserType(str, Enum):
    COUNSELOR = "counselor"
    STUDENT = "student"

class GoogleAuthRequest(BaseModel):
    token: str
    user_type: Optional[UserType] = None

class OAuthResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user_type: str
    is_new_user: bool
    
#LinkedIn auth schemas

class LinkedInAuthRequest(BaseModel):
    code: str
    user_type: Optional[UserType] = None
    
    


# class ProfilePictureResponse(BaseModel):
#     profile_picture_url: Optional[str]

#     class Config:
#         from_attributes = True    

class ProfilePictureResponse(BaseModel):
    profile_picture_url: Optional[str] = None

    class Config:
        from_attributes = True

# signup verification schemas
class SignupInitRequest(BaseModel):
    email: EmailStr
    first_name: str
    last_name: str
    user_type: UserType

class SignupVerifyRequest(BaseModel):
    email: str
    code: str

class SignupCompleteRequest(BaseModel):
    email: str
    password: str
    
    @field_validator('password')
    @classmethod
    def validate_password(cls, password: str) -> str:
        return validate_password(password)

class SignupResendRequest(BaseModel):
    email: str