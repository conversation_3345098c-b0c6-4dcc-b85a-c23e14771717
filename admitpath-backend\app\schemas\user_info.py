# schemas/user_info.py
from pydantic import BaseModel
from typing import Optional

class UserInfoResponse(BaseModel):
    user_id: int
    student_id: Optional[int] = None
    counselor_id: Optional[int] = None
    firstName: str
    lastName: str
    email: str
    userType: str
    isProfileComplete: bool
    is_verified: Optional[bool] = None
    profile_picture_url: Optional[str] = None

    class Config:
        from_attributes = True

    @classmethod
    def from_orm_custom(cls, user, profile):
        return cls(
            user_id=user.id,
            student_id=profile.id if user.user_type == "student" else None,
            counselor_id=profile.id if user.user_type == "counselor" else None,
            firstName=user.first_name,
            lastName=user.last_name,
            email=user.email,
            userType=user.user_type,
            isProfileComplete=profile.is_profile_complete if hasattr(profile, 'is_profile_complete') else False,
            is_verified=profile.is_verified if user.user_type == "counselor" else None,
            profile_picture_url=user.profile_picture_url
        )