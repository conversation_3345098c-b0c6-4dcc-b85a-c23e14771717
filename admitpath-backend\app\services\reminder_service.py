from datetime import datetime, timedelta, timezone
from sqlalchemy.orm import Session
from typing import List

from ..models.reminders import Session<PERSON><PERSON><PERSON>, ReminderStatus, ReminderType
from ..models.counseling_session import CounselingSession
from ..models.user import User, Counselor, Student
from ..utils.email_utils import send_email_ses
from ..utils.email_types import EmailType
from ..utils.session_timezone import format_session_times

async def schedule_session_reminders(
    db: Session,
    session_id: int,
    counselor_email: str,
    student_email: str
) -> List[SessionReminder]:
    """
    Schedule 24-hour and 1-hour reminders for a session
    """
    session = db.query(CounselingSession).filter(CounselingSession.id == session_id).first()
    if not session:
        raise ValueError(f"Session with ID {session_id} not found")

    now = datetime.now(timezone.utc)
    session_start = session.start_time

    reminder_24h_time = session_start - timedelta(hours=24)
    reminder_1h_time = session_start - timedelta(hours=1)

    existing_reminders = db.query(SessionReminder).filter(
        SessionReminder.session_id == session_id,
        SessionReminder.status == ReminderStatus.PENDING.value
    ).all()

    existing_reminder_dict = {}
    for reminder in existing_reminders:
        key = f"{reminder.recipient_type}_{reminder.reminder_type}"
        existing_reminder_dict[key] = reminder

    reminders = []

    if reminder_24h_time > now:
        # Create or update 24h reminder for student
        student_24h_key = "student_" + ReminderType.SESSION_24H.value
        if student_24h_key in existing_reminder_dict:
            # Update existing reminder
            student_24h_reminder = existing_reminder_dict[student_24h_key]
            student_24h_reminder.recipient_email = student_email
            student_24h_reminder.scheduled_time = reminder_24h_time
        else:
            # Create new reminder
            student_24h_reminder = SessionReminder(
                session_id=session_id,
                recipient_type="student",
                recipient_email=student_email,
                reminder_type=ReminderType.SESSION_24H.value,
                scheduled_time=reminder_24h_time,
                status=ReminderStatus.PENDING.value
            )
            db.add(student_24h_reminder)
        reminders.append(student_24h_reminder)

        # Create or update 24h reminder for counselor
        counselor_24h_key = "counselor_" + ReminderType.SESSION_24H.value
        if counselor_24h_key in existing_reminder_dict:
            # Update existing reminder
            counselor_24h_reminder = existing_reminder_dict[counselor_24h_key]
            counselor_24h_reminder.recipient_email = counselor_email
            counselor_24h_reminder.scheduled_time = reminder_24h_time
        else:
            # Create new reminder
            counselor_24h_reminder = SessionReminder(
                session_id=session_id,
                recipient_type="counselor",
                recipient_email=counselor_email,
                reminder_type=ReminderType.SESSION_24H.value,
                scheduled_time=reminder_24h_time,
                status=ReminderStatus.PENDING.value
            )
            db.add(counselor_24h_reminder)
        reminders.append(counselor_24h_reminder)

    if reminder_1h_time > now:
        # Create or update 1h reminder for student
        student_1h_key = "student_" + ReminderType.SESSION_1H.value
        if student_1h_key in existing_reminder_dict:
            # Update existing reminder
            student_1h_reminder = existing_reminder_dict[student_1h_key]
            student_1h_reminder.recipient_email = student_email
            student_1h_reminder.scheduled_time = reminder_1h_time
        else:
            # Create new reminder
            student_1h_reminder = SessionReminder(
                session_id=session_id,
                recipient_type="student",
                recipient_email=student_email,
                reminder_type=ReminderType.SESSION_1H.value,
                scheduled_time=reminder_1h_time,
                status=ReminderStatus.PENDING.value
            )
            db.add(student_1h_reminder)
        reminders.append(student_1h_reminder)

        # Create or update 1h reminder for counselor
        counselor_1h_key = "counselor_" + ReminderType.SESSION_1H.value
        if counselor_1h_key in existing_reminder_dict:
            # Update existing reminder
            counselor_1h_reminder = existing_reminder_dict[counselor_1h_key]
            counselor_1h_reminder.recipient_email = counselor_email
            counselor_1h_reminder.scheduled_time = reminder_1h_time
        else:
            # Create new reminder
            counselor_1h_reminder = SessionReminder(
                session_id=session_id,
                recipient_type="counselor",
                recipient_email=counselor_email,
                reminder_type=ReminderType.SESSION_1H.value,
                scheduled_time=reminder_1h_time,
                status=ReminderStatus.PENDING.value
            )
            db.add(counselor_1h_reminder)
        reminders.append(counselor_1h_reminder)

    db.commit()

    for reminder in reminders:
        db.refresh(reminder)

    return reminders

async def cancel_session_reminders(db: Session, session_id: int) -> int:
    """
    Delete all pending reminders for a session
    Returns the number of reminders deleted
    """
    # Find all pending reminders for this session
    reminders = db.query(SessionReminder).filter(
        SessionReminder.session_id == session_id,
        SessionReminder.status == ReminderStatus.PENDING.value
    ).all()

    # Delete each reminder
    count = 0
    for reminder in reminders:
        db.delete(reminder)
        count += 1

    db.commit()
    return count

async def process_due_reminders(db: Session) -> int:
    """
    Process all due reminders
    Returns the number of reminders processed
    """
    now = datetime.now(timezone.utc)

    # Get all pending reminders that are due
    try:
        due_reminders = db.query(SessionReminder).filter(
            SessionReminder.status == ReminderStatus.PENDING.value,
            SessionReminder.scheduled_time <= now
        ).all()
    except Exception as e:
        print(f"Error querying due reminders: {str(e)}")
        return 0

    count = 0
    for reminder in due_reminders:
        try:
            session = db.query(CounselingSession).filter(
                CounselingSession.id == reminder.session_id
            ).first()

            if not session:
                reminder.status = ReminderStatus.FAILED.value
                continue

            if session.status == "cancelled":
                reminder.status = ReminderStatus.CANCELLED.value
                continue

            student = db.query(Student).filter(Student.id == session.student_id).first()
            student_user = db.query(User).filter(User.id == student.user_id).first()

            counselor = db.query(Counselor).filter(Counselor.id == session.counselor_id).first()
            counselor_user = db.query(User).filter(User.id == counselor.user_id).first()

            recipient_timezone = None
            if reminder.recipient_type == "student" and student_user.timezone:
                recipient_timezone = student_user.timezone
            elif reminder.recipient_type == "counselor" and counselor_user.timezone:
                recipient_timezone = counselor_user.timezone

            session_data = {}
            if recipient_timezone:
                session_data = format_session_times(session, recipient_timezone)
            else:
                session_data = {
                    "date": session.date.strftime("%Y-%m-%d"),
                    "start_time": session.start_time.strftime("%H:%M"),
                    "end_time": session.end_time.strftime("%H:%M"),
                }

            session_data.update({
                "event_name": session.event_name,
                "student_name": f"{student_user.first_name} {student_user.last_name}",
                "counselor_name": f"{counselor_user.first_name} {counselor_user.last_name}",
                "description": session.description or "",
                "meeting_link": session.meeting_link
            })

            email_type = None
            if reminder.reminder_type == ReminderType.SESSION_24H.value:
                email_type = EmailType.SESSION_REMINDER_24H.value
            elif reminder.reminder_type == ReminderType.SESSION_1H.value:
                email_type = EmailType.SESSION_REMINDER_1H.value

            await send_email_ses(
                to_email=reminder.recipient_email,
                email_type=email_type,
                session_data=session_data
            )

            # Delete the reminder after sending
            db.delete(reminder)
            count += 1

        except Exception as e:
            print(f"Error processing reminder {reminder.id}: {str(e)}")
            # If the reminder is for a past session, delete it
            session_time = session.start_time if session else None
            if session_time and session_time < now:
                db.delete(reminder)
                print(f"Deleted past reminder {reminder.id} for session at {session_time}")
            else:
                reminder.status = ReminderStatus.FAILED.value

    db.commit()
    return count
