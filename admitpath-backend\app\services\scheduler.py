from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
import logging

from ..database import get_db
from .reminder_service import process_due_reminders

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

scheduler = AsyncIOScheduler()

async def check_and_send_reminders():
    """
    Check for due reminders and send them
    """
    db = None
    try:
        db = next(get_db())

        count = await process_due_reminders(db)

        if count > 0:
            logger.info(f"Processed {count} reminders")

    except Exception as e:
        logger.error(f"Error processing reminders: {str(e)}")
    finally:
        if db is not None:
            db.close()

def start_scheduler():
    """
    Start the scheduler with all jobs
    """
    if scheduler.running:
        return

    scheduler.add_job(
        check_and_send_reminders,
        IntervalTrigger(minutes=1),
        id="check_reminders",
        replace_existing=True
    )

    scheduler.start()
    logger.info("Scheduler started")

def shutdown_scheduler():
    """
    Shutdown the scheduler
    """
    if scheduler.running:
        scheduler.shutdown()
        logger.info("Scheduler shutdown")
