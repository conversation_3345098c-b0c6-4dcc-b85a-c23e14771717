from typing import Dict, List, Set, Optional
import logging
from fastapi import WebSocket
from datetime import datetime, timezone
from sqlalchemy.orm import Session

from ..models.user import User
from ..models.direct_message import DirectMessage
from ..utils.email_utils import send_email_ses
from ..utils.email_types import EmailType

logger = logging.getLogger(__name__)

class ConnectionManager:
    """
    Manages WebSocket connections for the chat system.
    Tracks active connections and handles message broadcasting.
    """
    def __init__(self):
        # Maps channel_id to a set of connected WebSockets
        self.active_connections: Dict[str, List[WebSocket]] = {}

        # Maps user_id to a set of channel_ids they're connected to
        self.user_connections: Dict[int, Set[str]] = {}

        # Maps WebSocket to user_id for easy lookup
        self.socket_to_user: Dict[WebSocket, int] = {}

    async def connect(self, websocket: WebSocket, channel_id: str, user_id: int):
        """
        Connect a user to a specific channel
        """
        await websocket.accept()

        # Initialize channel connections list if it doesn't exist
        if channel_id not in self.active_connections:
            self.active_connections[channel_id] = []

        # Add this connection to the channel
        self.active_connections[channel_id].append(websocket)

        # Initialize user connections set if it doesn't exist
        if user_id not in self.user_connections:
            self.user_connections[user_id] = set()

        # Add this channel to the user's connections
        self.user_connections[user_id].add(channel_id)

        # Map this socket to the user
        self.socket_to_user[websocket] = user_id


    def disconnect(self, websocket: WebSocket):
        """
        Disconnect a WebSocket from all channels
        """
        # Get the user_id for this socket
        user_id = self.socket_to_user.get(websocket)
        if not user_id:
            return

        # Remove this socket from all channels it's connected to
        for channel_id, connections in list(self.active_connections.items()):
            if websocket in connections:
                self.active_connections[channel_id].remove(websocket)

                # If this was the last connection for this channel, remove the channel
                if not self.active_connections[channel_id]:
                    del self.active_connections[channel_id]

        # Remove this channel from the user's connections
        if user_id in self.user_connections:
            # Find channels to remove
            channels_to_remove = set()
            for channel_id in self.user_connections[user_id]:
                if channel_id in self.active_connections:
                    # Check if user has any active connections to this channel
                    user_has_connection = False
                    for conn in self.active_connections[channel_id]:
                        if self.socket_to_user.get(conn) == user_id:
                            user_has_connection = True
                            break

                    if not user_has_connection:
                        channels_to_remove.add(channel_id)
                else:
                    channels_to_remove.add(channel_id)

            # Remove channels
            for channel_id in channels_to_remove:
                self.user_connections[user_id].remove(channel_id)

            # If user has no more connections, remove them from the map
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]

        # Remove this socket from the map
        if websocket in self.socket_to_user:
            del self.socket_to_user[websocket]

    async def broadcast_to_channel(self, message: dict, channel_id: str):
        """
        Broadcast a message to all connections in a channel
        """
        if channel_id in self.active_connections:
            for connection in self.active_connections[channel_id]:
                await connection.send_json(message)

    def is_user_connected_to_channel(self, user_id: int, channel_id: str) -> bool:
        """
        Check if a user is connected to a specific channel
        """
        if user_id not in self.user_connections:
            return False

        return channel_id in self.user_connections[user_id]

    async def send_message_notification(self,
                                       db: Session,
                                       message: DirectMessage,
                                       sender: User,
                                       recipient: User):
        """
        Send an email notification for a new message if the recipient is not connected
        """
        # Check if recipient is connected to the channel
        if self.is_user_connected_to_channel(recipient.id, message.channel_id):
            return

        # Recipient is not connected, send email notification
        try:
            # Prepare email data
            user_data = {
                "recipient_user_type": f"{recipient.user_type}".lower(),
                "first_name": recipient.first_name,
                "sender_name": f"{sender.first_name} {sender.last_name}",
                "message_preview": message.message[:100] + ("..." if len(message.message) > 100 else ""),
                "channel_id": message.channel_id
            }

            # Send email notification
            await send_email_ses(
                to_email=recipient.email,
                email_type=EmailType.CHAT_MESSAGE_NOTIFICATION.value,
                user_data=user_data
            )

        except Exception as e:
            logger.error(f"Failed to send message notification email: {str(e)}")

# Create a global instance of the connection manager
manager = ConnectionManager()
