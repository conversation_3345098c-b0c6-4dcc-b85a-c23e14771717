from enum import Enum

class EmailType(Enum):
    # Authentication related emails
    PASSWORD_RESET = "password_reset"
    SIGNUP = "signup"

    # Profile completion emails
    SIGNUP_STUDENT_COMPLETE = "signup_student_complete"
    SIGNUP_COUNSELOR_COMPLETE = "signup_counselor_complete"

    # Admin notifications
    NOTIFY_ADMIN_STUDENT_SIGNUP = "notify_admin_student_signup"
    NOTIFY_ADMIN_COUNSELOR_SIGNUP = "notify_admin_counselor_signup"
    NOTIFY_ADMIN_SESSION_CREATED = "notify_admin_session_created"
    NOTIFY_ADMIN_SESSION_RESCHEDULED = "notify_admin_session_rescheduled"
    NOTIFY_ADMIN_PACKAGE_PURCHASE = "notify_admin_package_purchase"

    # Session related
    SESSION_CREATION = "session_creation"
    SESSION_RESCHEDULE = "session_reschedule"
    SESSION_REMINDER_24H = "session_reminder_24h"
    SESSION_REMINDER_1H = "session_reminder_1h"

    # Package related
    PACKAGE_PURCHASE = "package_purchase"
    PACKAGE_PURCHASE_NOTIFY_COUNSELOR = "package_purchase_notify_counselor"
    PACKAGE_SESSION_CREATION = "package_session_creation"

    # Chat related
    CHAT_MESSAGE_NOTIFICATION = "chat_message_notification"
