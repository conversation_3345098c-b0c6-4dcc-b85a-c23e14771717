# app/utils/email_utils.py
from typing import Optional, <PERSON><PERSON>
import os
from dotenv import load_dotenv
from .email_types import EmailType

load_dotenv()

def create_email_content(*,
    email_type: str,
    otp: str = None,
    user_data: dict = None,
    session_data: dict = None,
    package_data: dict = None
) -> Tuple[str, str]:
    """
    Create email content based on the email type.

    Args:
        email_type: Type of email from EmailType enum
        otp: Optional OTP code for verification emails
        user_data: Optional user data for notifications
        session_data: Optional session data for session notifications
        package_data: Optional package data for package-related emails
    """
    # Base email style
    style = """
        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
        h2 { color: #333; margin-bottom: 20px; }
        h3 { color: #444; margin-top: 15px; margin-bottom: 10px; }
        .highlight { background-color: #f4f4f4; padding: 15px; margin: 20px 0; text-align: center; }
        .code { color: #0066cc; letter-spacing: 5px; margin: 0; font-size: 24px; font-weight: bold; }
        .footer { margin-top: 30px; font-size: 12px; color: #666; }
        .button { background-color: #0066cc; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 20px 0; }

        /* Service hours table styles */
        .service-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .service-table th { background-color: #0066cc; color: white; text-align: left; padding: 10px; }
        .service-table td { padding: 10px; border-bottom: 1px solid #eee; }
        .service-table tr:last-child td { border-bottom: none; }
        .service-table tr:nth-child(even) { background-color: rgba(0, 102, 204, 0.05); }
        .service-name { font-weight: bold; color: #333; }
        .service-hours { color: #0066cc; text-align: right; font-weight: bold; }

        /* Service progress styles (for session emails) */
        .service-list { list-style-type: none; padding: 0; margin: 15px 0; }
        .service-item { display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #eee; }
        .service-item:last-child { border-bottom: none; }
        .service-progress { width: 100%; background-color: #e0e0e0; height: 8px; border-radius: 4px; margin-top: 5px; }
        .service-progress-bar { background-color: #0066cc; height: 8px; border-radius: 4px; }
    """

    if email_type == EmailType.PASSWORD_RESET.value:
        subject = "Your Password Reset Code - AdmitPath"
        title = "Password Reset Code"
        message = "You have requested to reset your password."
        action_message = "If you did not request this password reset, please ignore this email."
        content = f"""
            <h2>{title}</h2>
            <p>{message}</p>
            <div class="highlight">
                <h1 class="code">{otp}</h1>
            </div>
            <p>This code will expire in 10 minutes.</p>
            <p>{action_message}</p>
        """

    elif email_type == EmailType.SIGNUP.value:
        subject = "Verify Your Email - AdmitPath"
        title = "Email Verification Code"
        message = "Thank you for signing up with AdmitPath. To complete your registration, please verify your email address."
        action_message = "If you did not create an account with AdmitPath, please ignore this email."
        content = f"""
            <h2>{title}</h2>
            <p>{message}</p>
            <div class="highlight">
                <h1 class="code">{otp}</h1>
            </div>
            <p>This code will expire in 10 minutes.</p>
            <p>{action_message}</p>
        """

    elif email_type == EmailType.PACKAGE_PURCHASE.value:
        name = package_data.get('student_name', '')
        counselor_name = package_data.get('counselor_name', '')
        package_name = package_data.get('package_name', '')
        first_session = package_data.get('first_session', {})
        service_hours = package_data.get('service_hours', {})

        # Generate service hours table HTML
        service_hours_html = """
            <h3>Service Hours Commitment:</h3>
            <table class="service-table">
                <thead>
                    <tr>
                        <th>Service</th>
                        <th>Hours</th>
                    </tr>
                </thead>
                <tbody>
        """

        # Calculate total hours across all services
        total_package_hours = 0
        for service_data in service_hours.values():
            total_package_hours += service_data.get('total', 0)

        # Generate table rows for each service
        for service_name, service_data in service_hours.items():
            total = service_data.get('total', 0)

            service_hours_html += f"""
                <tr>
                    <td class="service-name">{service_name}</td>
                    <td class="service-hours">{total} hours</td>
                </tr>
            """

        service_hours_html += """
                </tbody>
            </table>
        """

        subject = f"Package Purchase Confirmation - {package_name}"
        content = f"""
            <h2>Package Purchase Confirmation</h2>
            <p>Dear {name},</p>
            <p>Thank you for purchasing the {package_name} package with {counselor_name}!</p>

            <div class="highlight">
                <h3>Package Details:</h3>
                <p><strong>Package:</strong> {package_name}</p>
                <p><strong>Counselor:</strong> {counselor_name}</p>
                <p><strong>Total Hours:</strong> {total_package_hours} hours</p>
                <p><strong>Description:</strong> {package_data.get('package_description', '')}</p>

                {service_hours_html}
            </div>

            {f'''
            <div class="session-details">
                <h3>Your First Session:</h3>
                <p><strong>Date:</strong> {first_session.get('date', '')}</p>
                <p><strong>Time:</strong> {first_session.get('start_time', '')} - {first_session.get('end_time', '')}</p>
            </div>
            ''' if first_session else ''}

            <p>You can now schedule your counseling sessions through our platform.</p>
            <a href="https://admit-path.com/student/dashboard" class="button">Go to Dashboard</a>
        """

    elif email_type == EmailType.NOTIFY_ADMIN_PACKAGE_PURCHASE.value:
        name = package_data.get('student_name', '')
        counselor_name = package_data.get('counselor_name', '')
        package_name = package_data.get('package_name', '')
        first_session_utc = package_data.get('first_session_utc', {})
        service_hours = package_data.get('service_hours', {})

        # Generate service hours table HTML
        service_hours_html = """
            <h3>Service Hours Commitment:</h3>
            <table class="service-table">
                <thead>
                    <tr>
                        <th>Service</th>
                        <th>Hours</th>
                    </tr>
                </thead>
                <tbody>
        """

        # Calculate total hours across all services
        total_package_hours = 0
        for service_data in service_hours.values():
            total_package_hours += service_data.get('total', 0)

        # Generate table rows for each service
        for service_name, service_data in service_hours.items():
            total = service_data.get('total', 0)

            service_hours_html += f"""
                <tr>
                    <td class="service-name">{service_name}</td>
                    <td class="service-hours">{total} hours</td>
                </tr>
            """

        service_hours_html += """
                </tbody>
            </table>
        """

        subject = f"New Package Purchase - {package_name}"
        content = f"""
            <h2>New Package Purchase</h2>
            <div class="highlight">
                <h3>Package Details:</h3>
                <p><strong>Package:</strong> {package_name}</p>
                <p><strong>Student:</strong> {name}</p>
                <p><strong>Student Email:</strong> {package_data.get('student_email', '')}</p>
                <p><strong>Counselor:</strong> {counselor_name}</p>
                <p><strong>Total Hours:</strong> {total_package_hours} hours</p>
                <p><strong>Price:</strong> ${package_data.get('total_price', '0.00')}</p>

                {service_hours_html}
            </div>

            {f'''
            <div class="session-details">
                <h3>First Session (UTC):</h3>
                <p><strong>Date:</strong> {first_session_utc.get('date', '')}</p>
                <p><strong>Time:</strong> {first_session_utc.get('start_time', '')} - {first_session_utc.get('end_time', '')}</p>
            </div>
            ''' if first_session_utc else ''}
        """

    elif email_type == EmailType.SIGNUP_STUDENT_COMPLETE.value:
        name = user_data.get('first_name', '')
        subject = f"Welcome to AdmitPath, {name}!"
        content = f"""
            <h2>Welcome to AdmitPath!</h2>
            <p>Dear {name},</p>
            <p>Thank you for completing your student profile. You're now ready to start your journey with AdmitPath!</p>
            <p>You can now:</p>
            <ul>
                <li>Browse and connect with our expert counselors</li>
                <li>Schedule counseling sessions</li>
                <li>Access resources and tools to help with your college applications</li>
            </ul>
            <a href="https://admit-path.com/student/dashboard" class="button">Go to Dashboard</a>
        """

    elif email_type == EmailType.SIGNUP_COUNSELOR_COMPLETE.value:
        name = user_data.get('first_name', '')
        subject = f"Profile Submission Received - AdmitPath Counselor"
        content = f"""
            <h2>Thank You for Your Profile Submission</h2>
            <p>Dear {name},</p>
            <p>Thank you for submitting your profile to become a counselor at AdmitPath. Our team will carefully review your application and credentials.</p>
            <p>What happens next:</p>
            <ul>
                <li>Our team will review your profile and credentials</li>
                <li>We'll verify your educational and professional background</li>
                <li>You'll receive a response within 2-3 business days</li>
            </ul>
            <p>If you are approved, you will be able to set your availability and start offering counseling services.</p>
            <p>If you have any questions in the meantime, please don't hesitate to contact us.</p>
        """

    elif email_type in [EmailType.NOTIFY_ADMIN_STUDENT_SIGNUP.value, EmailType.NOTIFY_ADMIN_COUNSELOR_SIGNUP.value]:
        user_type = "Student" if email_type == EmailType.NOTIFY_ADMIN_STUDENT_SIGNUP.value else "Counselor"
        signup_method = user_data.get('signup_method', 'email')
        subject = f"New {user_type} Signup - AdmitPath"
        content = f"""
            <h2>New {user_type} Registration</h2>
            <p>A new {user_type.lower()} has signed up for AdmitPath:</p>
            <div class="highlight">
                <p><strong>Name:</strong> {user_data.get('first_name', '')} {user_data.get('last_name', '')}</p>
                <p><strong>Email:</strong> {user_data.get('email', '')}</p>
                <p><strong>Signup Time:</strong> {user_data.get('signup_time', '')}</p>
                <p><strong>Signup Method:</strong> {signup_method.title()}</p>
            </div>
        """
    elif email_type == EmailType.NOTIFY_ADMIN_SESSION_CREATED.value:
        subject = "New Counseling Session Booked - AdmitPath"
        content = f"""
            <h2>New Session Booking</h2>
            <p>A new counseling session has been scheduled:</p>
            <div class="highlight">
                <p><strong>Event:</strong> {session_data.get('event_name', '')}</p>
                <p><strong>Student:</strong> {session_data.get('student_name', '')}</p>
                <p><strong>Student Email:</strong> {session_data.get('student_email', '')}</p>
                <p><strong>Counselor:</strong> {session_data.get('counselor_name', '')}</p>
                <p><strong>Date:</strong> {session_data.get('date', '')}</p>
                <p><strong>Time:</strong> {session_data.get('start_time', '')} - {session_data.get('end_time', '')}</p>
                <p><strong>Meeting Link:</strong> <a href="{session_data.get('meeting_link', '')}">{session_data.get('meeting_link', '')}</a></p>
            </div>
            <p>Booked at: {session_data.get('created_at', '')} (UTC)</p>
        """

    elif email_type == EmailType.NOTIFY_ADMIN_SESSION_RESCHEDULED.value:
        subject = "Counseling Session Rescheduled - AdmitPath"
        content = f"""
            <h2>Session Rescheduled</h2>
            <p>A counseling session has been rescheduled:</p>
            <div class="highlight">
                <p><strong>Event:</strong> {session_data.get('event_name', '')}</p>
                <p><strong>Student:</strong> {session_data.get('student_name', '')}</p>
                <p><strong>Student Email:</strong> {session_data.get('student_email', '')}</p>
                <p><strong>Counselor:</strong> {session_data.get('counselor_name', '')}</p>
                <p><strong>New Date:</strong> {session_data.get('date', '')}</p>
                <p><strong>New Time:</strong> {session_data.get('start_time', '')} - {session_data.get('end_time', '')}</p>
                <p><strong>Meeting Link:</strong> <a href="{session_data.get('meeting_link', '')}">{session_data.get('meeting_link', '')}</a></p>
            </div>
            <p>Originally booked at: {session_data.get('created_at', '')} (UTC)</p>
            <p>Rescheduled at: {session_data.get('updated_at', '')} (UTC)</p>
        """
    elif email_type == EmailType.SESSION_CREATION.value:
        subject = "New Counseling Session Scheduled - AdmitPath"
        content = f"""
            <h2>Counseling Session Booked Successfully</h2>
            <p>A new counseling session has been scheduled:</p>
            <div class="highlight">
                <p><strong>Event:</strong> {session_data.get('event_name', '')}</p>
                <p><strong>Student:</strong> {session_data.get('student_name', '')}</p>
                <p><strong>Counselor:</strong> {session_data.get('counselor_name', '')}</p>
                <p><strong>Date:</strong> {session_data.get('date', '')}</p>
                <p><strong>Time:</strong> {session_data.get('start_time', '')} - {session_data.get('end_time', '')}</p>
                <p><strong>Description:</strong> {session_data.get('description', '')}</p>
            </div>
            <p>Meeting Link: <a href="{session_data.get('meeting_link', '')}">{session_data.get('meeting_link', '')}</a></p>
            <p>Please make sure to join the session on time.</p>
        """
    elif email_type == EmailType.SESSION_RESCHEDULE.value:
        subject = "Counseling Session Rescheduled - AdmitPath"
        content = f"""
            <h2>Counseling Session Rescheduled</h2>
            <p>Your counseling session has been rescheduled:</p>
            <div class="highlight">
                <p><strong>Event:</strong> {session_data.get('event_name', '')}</p>
                <p><strong>Student:</strong> {session_data.get('student_name', '')}</p>
                <p><strong>Counselor:</strong> {session_data.get('counselor_name', '')}</p>
                <p><strong>New Date:</strong> {session_data.get('date', '')}</p>
                <p><strong>New Time:</strong> {session_data.get('start_time', '')} - {session_data.get('end_time', '')}</p>
                <p><strong>Description:</strong> {session_data.get('description', '')}</p>
            </div>
            <p>Meeting Link: <a href="{session_data.get('meeting_link', '')}">{session_data.get('meeting_link', '')}</a></p>
            <p>Please make sure to join the session on time.</p>
            <p>If you have any questions or need to make further changes, please contact us as soon as possible.</p>
        """

    elif email_type == EmailType.PACKAGE_PURCHASE_NOTIFY_COUNSELOR.value:
        name = package_data.get('counselor_name', '')
        student_name = package_data.get('student_name', '')
        package_name = package_data.get('package_name', '')
        first_session = package_data.get('first_session', {})
        service_hours = package_data.get('service_hours', {})

        # Generate service hours table HTML
        service_hours_html = """
            <h3>Service Hours Commitment:</h3>
            <table class="service-table">
                <thead>
                    <tr>
                        <th>Service</th>
                        <th>Hours</th>
                    </tr>
                </thead>
                <tbody>
        """

        # Calculate total hours across all services
        total_package_hours = 0
        for service_data in service_hours.values():
            total_package_hours += service_data.get('total', 0)

        # Generate table rows for each service
        for service_name, service_data in service_hours.items():
            total = service_data.get('total', 0)

            service_hours_html += f"""
                <tr>
                    <td class="service-name">{service_name}</td>
                    <td class="service-hours">{total} hours</td>
                </tr>
            """

        service_hours_html += """
                </tbody>
            </table>
        """

        subject = f"New Package Purchase - {package_name}"
        content = f"""
            <h2>New Package Purchase</h2>
            <p>Dear {name},</p>
            <p>A student has purchased your package: "{package_name}"</p>

            <div class="highlight">
                <h3>Package Details:</h3>
                <p><strong>Package:</strong> {package_name}</p>
                <p><strong>Student:</strong> {student_name}</p>
                <p><strong>Total Hours:</strong> {total_package_hours} hours</p>
                <p><strong>Description:</strong> {package_data.get('package_description', '')}</p>

                {service_hours_html}
            </div>

            {f'''
            <div class="session-details">
                <h3>First Session:</h3>
                <p><strong>Date:</strong> {first_session.get('date', '')}</p>
                <p><strong>Time:</strong> {first_session.get('start_time', '')} - {first_session.get('end_time', '')}</p>
            </div>
            ''' if first_session else ''}

            <p>Please prepare for the upcoming sessions with this student.</p>
            <a href="https://admit-path.com/counselor/dashboard" class="button">Go to Dashboard</a>
        """

    elif email_type == EmailType.PACKAGE_SESSION_CREATION.value:
        service_name = session_data.get('event_name', '')
        hours_used = session_data.get('hours_used', 0)

        subject = "New Package Session Scheduled - AdmitPath"
        content = f"""
            <h2>Package Session Scheduled</h2>
            <p>Dear {session_data.get('student_name', '')},</p>
            <p>Your counselor has scheduled a new session as part of your package:</p>
            <div class="highlight">
                <p><strong>Package:</strong> {session_data.get('package_name', '')}</p>
                <p><strong>Service:</strong> {service_name}</p>
                <p><strong>Counselor:</strong> {session_data.get('counselor_name', '')}</p>
                <p><strong>Date:</strong> {session_data.get('date', '')}</p>
                <p><strong>Time:</strong> {session_data.get('start_time', '')} - {session_data.get('end_time', '')}</p>
                <p><strong>Duration:</strong> {hours_used} hours</p>
            </div>
            <p>Meeting Link: <a href="{session_data.get('meeting_link', '')}">{session_data.get('meeting_link', '')}</a></p>
            <p>Please make sure to join the session on time.</p>
            <a href="https://admit-path.com/student/dashboard/packages" class="button">View Your Packages</a>
        """

    elif email_type == EmailType.SESSION_REMINDER_24H.value:
        subject = "Reminder: Upcoming Session in 24 Hours - AdmitPath"
        content = f"""
            <h2>Upcoming Session in 24 Hours</h2>
            <p>Dear {session_data.get('student_name' if session_data.get('recipient_type') == 'student' else 'counselor_name', '')},</p>
            <p>This is a friendly reminder that you have a counseling session scheduled in 24 hours:</p>
            <div class="highlight">
                <p><strong>Event:</strong> {session_data.get('event_name', '')}</p>
                <p><strong>{'Counselor' if session_data.get('recipient_type') == 'student' else 'Student'}:</strong> {session_data.get('counselor_name' if session_data.get('recipient_type') == 'student' else 'student_name', '')}</p>
                <p><strong>Date:</strong> {session_data.get('date', '')}</p>
                <p><strong>Time:</strong> {session_data.get('start_time', '')} - {session_data.get('end_time', '')}</p>
                <p><strong>Description:</strong> {session_data.get('description', '')}</p>
            </div>
            <p>Meeting Link: <a href="{session_data.get('meeting_link', '')}">{session_data.get('meeting_link', '')}</a></p>
            <p>Please make sure to join the session on time. If you need to reschedule, please do so at least 24 hours in advance.</p>
            <a href="https://admit-path.com/{'student' if session_data.get('recipient_type') == 'student' else 'counselor'}/dashboard" class="button">Go to Dashboard</a>
        """

    elif email_type == EmailType.SESSION_REMINDER_1H.value:
        subject = "Reminder: Get Ready for Your Session in 1 Hour - AdmitPath"
        content = f"""
            <h2>Get Ready for Your Session in 1 Hour</h2>
            <p>Dear {session_data.get('student_name' if session_data.get('recipient_type') == 'student' else 'counselor_name', '')},</p>
            <p>Your counseling session is starting in 1 hour:</p>
            <div class="highlight">
                <p><strong>Event:</strong> {session_data.get('event_name', '')}</p>
                <p><strong>{'Counselor' if session_data.get('recipient_type') == 'student' else 'Student'}:</strong> {session_data.get('counselor_name' if session_data.get('recipient_type') == 'student' else 'student_name', '')}</p>
                <p><strong>Time:</strong> {session_data.get('start_time', '')} - {session_data.get('end_time', '')}</p>
            </div>
            <p>Meeting Link: <a href="{session_data.get('meeting_link', '')}">{session_data.get('meeting_link', '')}</a></p>
            <p>Please make sure to join the session on time.</p>
        """

    elif email_type == EmailType.CHAT_MESSAGE_NOTIFICATION.value:
        subject = f"New Message from {user_data.get('sender_name')} - AdmitPath"
        content = f"""
            <h2>New Message Received</h2>
            <p>Dear {user_data.get('first_name', '')},</p>
            <p>You have received a new message from <strong>{user_data.get('sender_name', '')}</strong>:</p>
            <div class="highlight">
                <p>{user_data.get('message_preview', '')}</p>
            </div>
            <p>Log in to your AdmitPath account to view and respond to this message.</p>
            <a href="https://admit-path.com/{user_data.get('recipient_user_type', 'counselor')}/dashboard/messages" class="button">Go to Messages</a>
        """

    # Wrap content in base HTML template with styles
    html_content = f"""
    <html>
        <head>
            <style>
                {style}
            </style>
        </head>
        <body>
            {content}
            <p class="footer">This is an automated message, please do not reply to this email.</p>
        </body>
    </html>
    """

    return subject, html_content

from .logger import logger

async def send_email_ses(*,
    to_email: str | list[str],
    email_type: str,
    otp: str = None,
    user_data: dict = None,
    session_data: dict = None,
    package_data: dict = None,
    bcc_email: str | list[str] = None
) -> bool:
    """
    Send an email using AWS SES.

    Args:
        to_email: Recipient email address or list of addresses
        email_type: Type of email from EmailType enum
        otp: Optional OTP code for verification emails
        user_data: Optional user data for notifications
        session_data: Optional session data for session notifications
        package_data: Optional package data for package-related emails
        bcc_email: Optional BCC email address or list of addresses

    Returns:
        bool: True if email sent successfully
    """
    import boto3
    from botocore.exceptions import ClientError
    import os
    from dotenv import load_dotenv

    # Load environment variables
    load_dotenv()

    # Create SES client
    ses = boto3.client(
        'ses',
        aws_access_key_id=os.getenv('AWS_SES_ACCESS_KEY_ID'),
        aws_secret_access_key=os.getenv('AWS_SES_SECRET_ACCESS_KEY'),
        region_name=os.getenv('AWS_SES_REGION')
    )

    # Convert single email to list
    if isinstance(to_email, str):
        to_email = [to_email]

    # Convert BCC email to list if provided
    bcc_addresses = []
    if bcc_email:
        if isinstance(bcc_email, str):
            bcc_addresses = [bcc_email]
        else:
            bcc_addresses = bcc_email

    # Create email content
    subject, html_content = create_email_content(
        email_type=email_type,
        otp=otp,
        user_data=user_data,
        session_data=session_data,
        package_data=package_data
    )

    try:
        logger.info(f"Sending {email_type} email to {to_email}")
        if bcc_addresses:
            logger.info(f"BCCing to {bcc_addresses}")

        response = ses.send_email(
            Source=os.getenv('AWS_SES_SENDER_EMAIL'),
            Destination={
                'ToAddresses': to_email,
                'BccAddresses': bcc_addresses
            },
            Message={
                'Subject': {
                    'Data': subject
                },
                'Body': {
                    'Html': {
                        'Data': html_content
                    }
                }
            }
        )
        logger.info(f"Successfully sent {email_type} email to {to_email}")
        return True
    except ClientError as e:
        error_msg = str(e)
        logger.error(f"Failed to send {email_type} email to {to_email}: {error_msg}")
        return False

# Alias for backward compatibility
async def send_otp_email_ses(to_email: str, otp: str, email_type: str = EmailType.PASSWORD_RESET.value) -> bool:
    """
    Backward compatibility wrapper for send_email_ses.
    """
    return await send_email_ses(
        to_email=to_email,
        email_type=email_type,
        otp=otp
    )
