# app/utils/google_auth.py
from google.oauth2 import id_token
from google.auth.transport import requests
import requests
from fastapi import HTTPException
import os
from dotenv import load_dotenv
from typing import Dict

load_dotenv()

GOOGLE_CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
GOOGLE_CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")

async def verify_google_token(token: str) -> Dict:
    """
    Verify Google OAuth token and return user info using Google's userinfo endpoint.
    
    Args:
        token: Google OAuth access token
        
    Returns:
        dict: User information from Google
        
    Raises:
        HTTPException: If token is invalid or verification fails
    """
    try:
        # Use the access token to get user info from Google's userinfo endpoint
        response = requests.get(
            'https://www.googleapis.com/oauth2/v3/userinfo',
            headers={'Authorization': f'Bearer {token}'}
        )
        response.raise_for_status()
        
        user_info = response.json()
        
        if not user_info.get('email'):
            raise ValueError('Email not found in user info')
            
        # Return user info in the format your app expects
        return {
            'email': user_info['email'],
            'first_name': user_info.get('given_name', ''),
            'last_name': user_info.get('family_name', ''),
            'picture': user_info.get('picture', ''),
            'email_verified': user_info.get('email_verified', False)
        }
        
    except requests.RequestException as e:
        print(f"Google API error: {str(e)}")
        if hasattr(e, 'response'):
            print(f"Response: {e.response.text}")
        raise HTTPException(
            status_code=400,
            detail=f"Failed to verify Google token: {str(e)}"
        )
    except Exception as e:
        print(f"Verification error: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail=f"Invalid token: {str(e)}"
        )