# app/utils/linkedin_auth.py
import os
import requests
import jwt
from fastapi import HTTPException
from typing import Dict
from dotenv import load_dotenv

load_dotenv()

LINKEDIN_CLIENT_ID = os.getenv("LINKEDIN_CLIENT_ID")
LINKEDIN_CLIENT_SECRET = os.getenv("LINKEDIN_CLIENT_SECRET")
LINKEDIN_REDIRECT_URI = os.getenv("LINKEDIN_REDIRECT_URI", "http://localhost:3001/test.html")

async def get_linkedin_tokens(code: str) -> Dict:
    """
    Exchange authorization code for access and id tokens
    """
    try:
        token_url = "https://www.linkedin.com/oauth/v2/accessToken"
        data = {
            "grant_type": "authorization_code",
            "code": code,
            "client_id": LINKEDIN_CLIENT_ID,
            "client_secret": LINKEDIN_CLIENT_SECRET,
            "redirect_uri": LINKEDIN_REDIRECT_URI
        }
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        response = requests.post(token_url, data=data, headers=headers)
        response.raise_for_status()
        
        return response.json()
        
    except requests.exceptions.RequestException as e:
        raise HTTPException(
            status_code=400,
            detail=f"Failed to get LinkedIn tokens: {str(e)}"
        )

async def get_linkedin_user_info(id_token: str) -> Dict:
    """
    Extract user information from ID token
    """
    try:
        # Decode the ID token (without verification since LinkedIn signs it)
        user_info = jwt.decode(id_token, options={"verify_signature": False})
        
        return {
            "email": user_info.get("email"),
            "first_name": user_info.get("given_name"),
            "last_name": user_info.get("family_name"),
            "linkedin_id": user_info.get("sub"),
            "email_verified": user_info.get("email_verified", False)
        }
        
    except jwt.InvalidTokenError as e:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid ID token: {str(e)}"
        )