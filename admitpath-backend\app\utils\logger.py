import logging
import sys

def setup_logger(name: str = None) -> logging.Logger:
    """
    Set up and return a configured logger instance.
    Args:
        name: The name for the logger. If None, returns the root logger.
    Returns:
        A configured logger instance
    """
    logger = logging.getLogger(name)
    
    # Only add handlers if they haven't been added yet
    if not logger.handlers:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # Set level
        logger.setLevel(logging.INFO)
    
    return logger

# Create a default logger instance
logger = setup_logger('admitpath')
