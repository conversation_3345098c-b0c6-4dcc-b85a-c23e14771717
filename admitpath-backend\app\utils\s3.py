import boto3
from botocore.exceptions import Client<PERSON>rror
from fastapi import UploadFile, HTTPException
import os
from typing import Optional
import re


def validate_s3_config():
    required_vars = ['AWS_ACCESS_KEY_ID', 'AWS_SECRET_ACCESS_KEY', 'AWS_BUCKET_NAME']
    missing = [var for var in required_vars if not os.getenv(var)]
    if missing:
        raise ValueError(f"Missing required environment variables: {', '.join(missing)}")


async def upload_to_s3(file: UploadFile, folder: Optional[str] = None) -> str:
    """
    Upload a file to S3 and return the URL.
    
    Args:
        file: The UploadFile object to upload
        folder: Optional folder path within the bucket
        
    Returns:
        str: The URL of the uploaded file

    Raises:
        HTTPException: If upload fails
    """
    try:
        validate_s3_config()

        # Initialize S3 client
        s3_client = boto3.client(
            's3',
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            region_name=os.getenv('AWS_REGION', 'us-east-1')
        )
        bucket_name = os.getenv('AWS_BUCKET_NAME')

        # Read file content
        file_content = await file.read()

        # Sanitize filename - replace spaces with underscores and remove special characters
        sanitized_filename = re.sub(r'[^\w\-.]', '_', file.filename)

        # Create the S3 key (path)
        s3_key = f"{folder}/{sanitized_filename}" if folder else sanitized_filename

        # Ensure the content type is set
        content_type = file.content_type
        if not content_type:
            if sanitized_filename.lower().endswith(('.jpg', '.jpeg')):
                content_type = 'image/jpeg'
            elif sanitized_filename.lower().endswith('.png'):
                content_type = 'image/png'
            else:
                content_type = 'application/octet-stream'

        # Upload to S3
        s3_client.put_object(
            Bucket=bucket_name,
            Key=s3_key,
            Body=file_content,
            ContentType=content_type
        )

        # Generate URL
        url = f"https://{bucket_name}.s3.amazonaws.com/{s3_key}"

        # Reset file cursor for potential future reads
        await file.seek(0)

        return url

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to upload file: {str(e)}")