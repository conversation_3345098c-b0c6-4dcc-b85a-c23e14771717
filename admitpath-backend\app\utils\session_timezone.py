from zoneinfo import ZoneInfo
from datetime import timezone as tz

def format_session_times(db_session, recipient_tz):
    """Helper to format times for a specific timezone

    Properly handles session times that already have timezone information.
    If times already have tzinfo, we use them as is.
    If times don't have tzinfo, we assume they're in UTC.
    """
    start_time = db_session.start_time
    end_time = db_session.end_time

    # If times don't have timezone info, assume they're UTC
    if start_time.tzinfo is None:
        start_time = start_time.replace(tzinfo=tz.utc)
    if end_time.tzinfo is None:
        end_time = end_time.replace(tzinfo=tz.utc)

    # Set default timezone and suffix
    tz_info = tz.utc
    time_suffix = " (UTC)"

    # Try to use recipient's timezone if provided
    if recipient_tz:
        try:
            tz_info = ZoneInfo(recipient_tz)
            time_suffix = ""
        except Exception:
            pass

    # Convert to recipient's timezone
    local_start = start_time.astimezone(tz_info)
    local_end = end_time.astimezone(tz_info)

    return {
        "date": local_start.strftime("%A, %B %d, %Y"),
        "start_time": f"{local_start.strftime('%I:%M %p')}{time_suffix}",
        "end_time": f"{local_end.strftime('%I:%M %p')}{time_suffix}"
    }