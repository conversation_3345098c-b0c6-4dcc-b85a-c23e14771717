# app/utils/stripe_utils.py
import stripe
from fastapi import HTT<PERSON>Ex<PERSON>, Request
import os
from dotenv import load_dotenv
import json
from sqlalchemy.orm import Session
from ..models.stripe_payment import StripePayment, StripePaymentStatus
from urllib.parse import urlparse


load_dotenv()

# Initialize Stripe with secret key
stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

async def create_payment_intent(amount: float, metadata: dict = None) -> dict:
    """
    Create a Stripe PaymentIntent
    
    Args:
        amount: Amount in USD
        metadata: Additional metadata for the payment intent
        
    Returns:
        dict: Payment intent details including client_secret
        
    Raises:
        HTTPException: If payment intent creation fails
    """
    try:
        # Convert amount to cents (<PERSON><PERSON> uses smallest currency unit)
        amount_cents = int(amount * 100)
        
        intent = stripe.PaymentIntent.create(
            amount=amount_cents,
            currency='usd',
            metadata=metadata,
            automatic_payment_methods={
                'enabled': True,
            }
        )
        
        return {
            'client_secret': intent.client_secret,
            'id': intent.id
        }
        
    except stripe.error.StripeError as e:
        raise HTTPException(
            status_code=400,
            detail=f"Failed to create payment intent: {str(e)}"
        )
        
        

# async def verify_stripe_webhook(request: Request, stripe_signature: str) -> stripe.Event:
#     """
#     Verify Stripe webhook signature and return the event
#     """
#     webhook_secret = os.getenv("STRIPE_WEBHOOK_SECRET")
#     payload = await request.body()


#     print("STRIPE SIG YES", stripe_signature, "PPAYLOADD -- ", payload, "and payload, ", payload.decode("utf-8"))
    
#     try:
#         event = stripe.Webhook.construct_event(
#             payload,
#             stripe_signature,
#             webhook_secret,
#         )
        
#         return event
#     except ValueError as e:
#         print("this error e", e)
#         raise HTTPException(status_code=400, detail="Invalid payload")
#     except stripe.error.SignatureVerificationError as e:
#         print("this error stripe e", e)
#         raise HTTPException(status_code=400, detail="Invalid signature")

# Using this different method of verifying the webhook for now
async def verify_stripe_webhook(request: Request) -> stripe.Event:
    """
    Temporary Stripe webhook verification by checking request origin and extracting event data.
    """
    user_agent = request.headers.get("User-Agent", "").lower()
    print(user_agent)
    # Define allowed Stripe User-Agent patterns
    STRIPE_USER_AGENTS = ["stripe", "stripe-cli"]

    # Ensure request is from Stripe
    if not any(agent in user_agent for agent in STRIPE_USER_AGENTS):
        raise HTTPException(status_code=403, detail="Unauthorized webhook request")

    # Read raw body from request
    try:
        payload = await request.body()
        event = json.loads(payload)
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON payload")

    return event
