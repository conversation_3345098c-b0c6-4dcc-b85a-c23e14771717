# app/utils/verify_user.py
from fastapi import HTTPException, status
from sqlalchemy.orm import Session
from ..models.user import Counselor
from ..models.user import Student

async def verify_counselor(user_id: int, db: Session) -> Counselor:
    counselor = db.query(Counselor).filter(Counselor.user_id == user_id).first()
    if not counselor:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Counselor profile not found"
        )
    return counselor


async def verify_student(user_id: int, db: Session) -> Student:
    student = db.query(Student).filter(Student.user_id == user_id).first()
    if not student:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Student profile not found"
        )
    return student