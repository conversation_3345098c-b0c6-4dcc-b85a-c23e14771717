# app/utils/zoom_utils.py
import os
import json
import requests
from typing import Dict, Optional
from datetime import datetime, timedelta
from fastapi import HTTPException
from dotenv import load_dotenv
import base64

load_dotenv()

# Configuration
ZOOM_CLIENT_ID = os.getenv("ZOOM_CLIENT_ID")
ZOOM_CLIENT_SECRET = os.getenv("ZOOM_CLIENT_SECRET")
ZOOM_ACCOUNT_ID = os.getenv("ZOOM_ACCOUNT_ID")

# Constants
ZOOM_BASE_URL = "https://api.zoom.us/v2"
ZOOM_AUTH_URL = "https://zoom.us/oauth/token"

class ZoomAPI:
    def __init__(self):
        self.access_token = None
        self.token_expiry = None
    
    async def get_access_token(self) -> str:
        """
        Get a valid access token, refresh if necessary
        """
        # Check if we have a valid token
        if self.access_token and self.token_expiry and datetime.utcnow() < self.token_expiry:
            return self.access_token
            
        try:
            # Prepare the authentication
            auth_header = f"Basic {base64.b64encode(f'{ZOOM_CLIENT_ID}:{ZOOM_CLIENT_SECRET}'.encode()).decode()}"
            
            headers = {
                "Authorization": auth_header,
                "Content-Type": "application/x-www-form-urlencoded"
            }
            
            data = {
                "grant_type": "account_credentials",
                "account_id": ZOOM_ACCOUNT_ID
            }
            
            # Make the request to get the token
            response = requests.post(ZOOM_AUTH_URL, headers=headers, data=data)
            response.raise_for_status()
            
            token_data = response.json()
            
            # Store the token and calculate expiry
            self.access_token = token_data["access_token"]
            self.token_expiry = datetime.utcnow() + timedelta(seconds=token_data["expires_in"] - 300)  # Subtract 5 minutes for safety
            
            return self.access_token
            
        except requests.RequestException as e:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get Zoom access token: {str(e)}"
            )

    async def create_meeting(self, topic: str, start_time: datetime, duration: int, user_id: str = "me") -> Dict:
        """
        Create a Zoom meeting
        
        Args:
            topic: Meeting topic/title
            start_time: Meeting start time (datetime)
            duration: Meeting duration in minutes
            user_id: Zoom user ID (default "me" for authenticated user)
            
        Returns:
            Dict containing meeting details including join URL
        """
        try:
            token = await self.get_access_token()
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            # Format the meeting data according to Zoom API requirements
            meeting_data = {
                "topic": topic,
                "type": 2,  # Scheduled meeting
                "start_time": start_time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                "duration": duration,
                "timezone": "UTC",
                "settings": {
                    "host_video": True,
                    "participant_video": True,
                    "join_before_host": True,  # Allow joining before host
                    "mute_upon_entry": True,
                    "auto_recording": "none",
                    "waiting_room": False,  # Disable waiting room
                    "who_can_share": "all",  # 'all' or 'host'
                    "allow_multiple_participants_to_share_simultaneously": True,
                    "enforce_login": False,
                    "auto_recording": "cloud",
                }
            }
            
            response = requests.post(
                f"{ZOOM_BASE_URL}/users/{user_id}/meetings",
                headers=headers,
                json=meeting_data
            )
            response.raise_for_status()
            
            return response.json()
            
        except requests.RequestException as e:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to create Zoom meeting: {str(e)}"
            )
    
    async def get_meeting(self, meeting_id: str) -> Dict:
        """
        Get meeting details by ID
        """
        try:
            token = await self.get_access_token()
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                f"{ZOOM_BASE_URL}/meetings/{meeting_id}",
                headers=headers
            )
            response.raise_for_status()
            
            return response.json()
            
        except requests.RequestException as e:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get Zoom meeting details: {str(e)}"
            )
    
    async def update_meeting(self, meeting_id: str, updated_data: Dict) -> Dict:
        """
        Update an existing meeting
        """
        try:
            token = await self.get_access_token()
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            response = requests.patch(
                f"{ZOOM_BASE_URL}/meetings/{meeting_id}",
                headers=headers,
                json=updated_data
            )
            response.raise_for_status()
            
            return response.json()
            
        except requests.RequestException as e:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to update Zoom meeting: {str(e)}"
            )
    
    async def delete_meeting(self, meeting_id: str) -> bool:
        """
        Delete/Cancel a meeting
        """
        try:
            token = await self.get_access_token()
            
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            response = requests.delete(
                f"{ZOOM_BASE_URL}/meetings/{meeting_id}",
                headers=headers
            )
            response.raise_for_status()
            
            return True
            
        except requests.RequestException as e:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to delete Zoom meeting: {str(e)}"
            )

# Create a singleton instance
zoom_client = ZoomAPI()