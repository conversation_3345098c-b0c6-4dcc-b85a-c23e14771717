version: "3.8"

services:
  nginx:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    # restart: always
    networks:
      - app-network

  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    expose:
      - "80"
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - JWT_ALGORITHM=${JWT_ALGORITHM}
      - GMAIL_CREDENTIALS_FILE=${GMAIL_CREDENTIALS_FILE}
      - GMAIL_TOKEN_FILE=${GMAIL_TOKEN_FILE}
      - SENDER_EMAIL=${SENDER_EMAIL}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - LINKEDIN_CLIENT_ID=${LINKEDIN_CLIENT_ID}
      - LINKEDIN_CLIENT_SECRET=${LINKEDIN_CLIENT_SECRET}
      - LINKEDIN_REDIRECT_URI=${LINKEDIN_REDIRECT_URI}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
      - AWS_BUCKET_NAME=${AWS_BUCKET_NAME}
    volumes:
      - ./:/app
      - ./credentials.json:/app/credentials.json
      - ./token.pickle:/app/token.pickle
    restart: unless-stopped
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  app-network:
    driver: bridge
