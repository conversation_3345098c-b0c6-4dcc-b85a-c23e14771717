{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/common/inputField/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"container\": \"index-module__8yU72G__container\",\n  \"error\": \"index-module__8yU72G__error\",\n  \"errorMessage\": \"index-module__8yU72G__errorMessage\",\n  \"input\": \"index-module__8yU72G__input\",\n  \"label\": \"index-module__8yU72G__label\",\n  \"required\": \"index-module__8yU72G__required\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/inputField/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { FC, InputHTMLAttributes } from \"react\";\r\nimport styles from \"./index.module.css\";\r\n\r\ninterface InputFieldProps extends InputHTMLAttributes<HTMLInputElement> {\r\n  label: string;\r\n  error?: string;\r\n  required?: boolean;\r\n}\r\n\r\nexport const InputField: FC<InputFieldProps> = ({\r\n  label,\r\n  required = false,\r\n  error,\r\n  ...inputProps\r\n}) => {\r\n  return (\r\n    <div className={styles.container}>\r\n      <label\r\n        htmlFor={inputProps.id || inputProps.name}\r\n        className={styles.label}\r\n      >\r\n        {label} {required && <span className={styles.required}>*</span>}\r\n      </label>\r\n\r\n      <input\r\n        {...inputProps}\r\n        className={`${styles.input} ${error ? styles.error : \"\"}`}\r\n        aria-required={required}\r\n        aria-invalid={!!error}\r\n        aria-describedby={error ? `${inputProps.id}-error` : undefined}\r\n      />\r\n\r\n      {error && (\r\n        <span\r\n          id={`${inputProps.id}-error`}\r\n          className={styles.errorMessage}\r\n          role=\"alert\"\r\n        >\r\n          {error}\r\n        </span>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWO,MAAM,aAAkC,CAAC,EAC9C,KAAK,EACL,WAAW,KAAK,EAChB,KAAK,EACL,GAAG,YACJ;IACC,qBACE,8OAAC;QAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,SAAS;;0BAC9B,8OAAC;gBACC,SAAS,WAAW,EAAE,IAAI,WAAW,IAAI;gBACzC,WAAW,iKAAA,CAAA,UAAM,CAAC,KAAK;;oBAEtB;oBAAM;oBAAE,0BAAY,8OAAC;wBAAK,WAAW,iKAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;;;;;;;;0BAGzD,8OAAC;gBACE,GAAG,UAAU;gBACd,WAAW,GAAG,iKAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,iKAAA,CAAA,UAAM,CAAC,KAAK,GAAG,IAAI;gBACzD,iBAAe;gBACf,gBAAc,CAAC,CAAC;gBAChB,oBAAkB,QAAQ,GAAG,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG;;;;;;YAGtD,uBACC,8OAAC;gBACC,IAAI,GAAG,WAAW,EAAE,CAAC,MAAM,CAAC;gBAC5B,WAAW,iKAAA,CAAA,UAAM,CAAC,YAAY;gBAC9B,MAAK;0BAEJ;;;;;;;;;;;;AAKX"}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/common/button/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"buttonBase\": \"index-module__Mmy9Oa__buttonBase\",\n  \"buttonContent\": \"index-module__Mmy9Oa__buttonContent\",\n  \"buttonPrimary\": \"index-module__Mmy9Oa__buttonPrimary\",\n  \"buttonSecondary\": \"index-module__Mmy9Oa__buttonSecondary\",\n  \"icon\": \"index-module__Mmy9Oa__icon\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/button/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport styles from \"./index.module.css\";\r\n\r\ninterface ButtonProps {\r\n  variant: \"primary\" | \"secondary\";\r\n  children: React.ReactNode;\r\n  onClick?: (e: any) => void;\r\n  type?: \"button\" | \"submit\" | \"reset\";\r\n  disabled?: boolean;\r\n  icon?: React.ReactNode;\r\n}\r\n\r\nexport const Button: React.FC<ButtonProps> = ({\r\n  variant,\r\n  children,\r\n  onClick,\r\n  type = \"button\",\r\n  disabled = false,\r\n  icon,\r\n}) => {\r\n  const buttonClasses = `${styles.buttonBase} ${\r\n    variant === \"primary\" ? styles.buttonPrimary : styles.buttonSecondary\r\n  }`;\r\n\r\n  return (\r\n    <button\r\n      type={type}\r\n      onClick={onClick}\r\n      className={buttonClasses}\r\n      disabled={disabled}\r\n    >\r\n      <span className={styles.buttonContent}>\r\n        {children}\r\n        {icon && <span className={styles.icon}>{icon}</span>}\r\n      </span>\r\n    </button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAcO,MAAM,SAAgC,CAAC,EAC5C,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,QAAQ,EACf,WAAW,KAAK,EAChB,IAAI,EACL;IACC,MAAM,gBAAgB,GAAG,6JAAA,CAAA,UAAM,CAAC,UAAU,CAAC,CAAC,EAC1C,YAAY,YAAY,6JAAA,CAAA,UAAM,CAAC,aAAa,GAAG,6JAAA,CAAA,UAAM,CAAC,eAAe,EACrE;IAEF,qBACE,8OAAC;QACC,MAAM;QACN,SAAS;QACT,WAAW;QACX,UAAU;kBAEV,cAAA,8OAAC;YAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,aAAa;;gBAClC;gBACA,sBAAQ,8OAAC;oBAAK,WAAW,6JAAA,CAAA,UAAM,CAAC,IAAI;8BAAG;;;;;;;;;;;;;;;;;AAIhD"}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/auth/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"divider\": \"index-module__2JOImG__divider\",\n  \"emailField\": \"index-module__2JOImG__emailField\",\n  \"form\": \"index-module__2JOImG__form\",\n  \"formFooter\": \"index-module__2JOImG__formFooter\",\n  \"formSection\": \"index-module__2JOImG__formSection\",\n  \"formWrapper\": \"index-module__2JOImG__formWrapper\",\n  \"header\": \"index-module__2JOImG__header\",\n  \"heroImage\": \"index-module__2JOImG__heroImage\",\n  \"highlight\": \"index-module__2JOImG__highlight\",\n  \"imageSection\": \"index-module__2JOImG__imageSection\",\n  \"layout\": \"index-module__2JOImG__layout\",\n  \"link\": \"index-module__2JOImG__link\",\n  \"loginLink\": \"index-module__2JOImG__loginLink\",\n  \"logo\": \"index-module__2JOImG__logo\",\n  \"mainContent\": \"index-module__2JOImG__mainContent\",\n  \"nameFields\": \"index-module__2JOImG__nameFields\",\n  \"socialButtons\": \"index-module__2JOImG__socialButtons\",\n  \"terms\": \"index-module__2JOImG__terms\",\n  \"title\": \"index-module__2JOImG__title\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/auth/signup/form.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { FC, useState } from \"react\";\r\nimport { InputField } from \"@components/common/inputField\";\r\nimport { Button } from \"@components/common/button\";\r\nimport { FaArrowRight } from \"react-icons/fa\";\r\nimport styles from \"../index.module.css\";\r\nimport { FormErrors } from \"../../../types/auth\";\r\nimport { useAuth } from \"@/app/hooks/useAuth\";\r\n\r\nexport interface FormProps {\r\n  handleSubmit: (data: {\r\n    firstName: string;\r\n    lastName: string;\r\n    email: string;\r\n  }) => Promise<void>;\r\n  isLogin: boolean;\r\n  isLoading: boolean;\r\n}\r\n\r\nexport const Form: FC<FormProps> = ({ handleSubmit, isLogin, isLoading }) => {\r\n  const [formData, setFormData] = useState({\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n    email: \"\",\r\n  });\r\n  const [errors, setErrors] = useState<FormErrors>({});\r\n\r\n  const onSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // Reset previous errors\r\n    setErrors({});\r\n\r\n    // Validate required fields\r\n    const newErrors: FormErrors = {};\r\n    if (!formData.firstName.trim()) {\r\n      newErrors.firstName = \"First name is required\";\r\n    }\r\n    if (!formData.lastName.trim()) {\r\n      newErrors.lastName = \"Last name is required\";\r\n    }\r\n    if (!formData.email.trim()) {\r\n      newErrors.email = \"Email is required\";\r\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\r\n      newErrors.email = \"Please enter a valid email\";\r\n    }\r\n\r\n    // If there are any errors, display them and stop submission\r\n    if (Object.keys(newErrors).length > 0) {\r\n      setErrors(newErrors);\r\n      return;\r\n    }\r\n\r\n    // If validation passes, submit the form\r\n    await handleSubmit(formData);\r\n  };\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData((prev) => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-6\">\r\n      <form onSubmit={onSubmit} className={styles.form}>\r\n        <div className={styles.nameFields}>\r\n          <InputField\r\n            label=\"First name\"\r\n            name=\"firstName\"\r\n            placeholder=\"First name\"\r\n            required\r\n            value={formData.firstName}\r\n            onChange={handleInputChange}\r\n            error={errors.firstName}\r\n          />\r\n          <InputField\r\n            label=\"Last name\"\r\n            name=\"lastName\"\r\n            placeholder=\"Last name\"\r\n            required\r\n            value={formData.lastName}\r\n            onChange={handleInputChange}\r\n            error={errors.lastName}\r\n          />\r\n        </div>\r\n        <div className={styles.emailField}>\r\n          <InputField\r\n            label=\"Email\"\r\n            type=\"email\"\r\n            name=\"email\"\r\n            placeholder=\"Email\"\r\n            required\r\n            value={formData.email}\r\n            onChange={handleInputChange}\r\n            error={errors.email}\r\n          />\r\n        </div>\r\n        <div className={styles.formFooter}>\r\n          <div className={styles.loginLink}>\r\n            <span>Already have an account?</span>\r\n            <a href=\"/auth/login\">Login</a>\r\n          </div>\r\n          <Button\r\n            variant=\"primary\"\r\n            type=\"submit\"\r\n            icon={<FaArrowRight />}\r\n            disabled={isLoading}\r\n          >\r\n            {isLoading ? \"Creating...\" : \"Create\"}\r\n          </Button>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AADA;AALA;;;;;;;AAoBO,MAAM,OAAsB,CAAC,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,OAAO;IACT;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAElD,MAAM,WAAW,OAAO;QACtB,EAAE,cAAc;QAEhB,wBAAwB;QACxB,UAAU,CAAC;QAEX,2BAA2B;QAC3B,MAAM,YAAwB,CAAC;QAC/B,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC9B,UAAU,SAAS,GAAG;QACxB;QACA,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI,IAAI;YAC7B,UAAU,QAAQ,GAAG;QACvB;QACA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC/C,UAAU,KAAK,GAAG;QACpB;QAEA,4DAA4D;QAC5D,IAAI,OAAO,IAAI,CAAC,WAAW,MAAM,GAAG,GAAG;YACrC,UAAU;YACV;QACF;QAEA,wCAAwC;QACxC,MAAM,aAAa;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACnD;IAGA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAK,UAAU;YAAU,WAAW,iJAAA,CAAA,UAAM,CAAC,IAAI;;8BAC9C,8OAAC;oBAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,UAAU;;sCAC/B,8OAAC,mJAAA,CAAA,aAAU;4BACT,OAAM;4BACN,MAAK;4BACL,aAAY;4BACZ,QAAQ;4BACR,OAAO,SAAS,SAAS;4BACzB,UAAU;4BACV,OAAO,OAAO,SAAS;;;;;;sCAEzB,8OAAC,mJAAA,CAAA,aAAU;4BACT,OAAM;4BACN,MAAK;4BACL,aAAY;4BACZ,QAAQ;4BACR,OAAO,SAAS,QAAQ;4BACxB,UAAU;4BACV,OAAO,OAAO,QAAQ;;;;;;;;;;;;8BAG1B,8OAAC;oBAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,UAAU;8BAC/B,cAAA,8OAAC,mJAAA,CAAA,aAAU;wBACT,OAAM;wBACN,MAAK;wBACL,MAAK;wBACL,aAAY;wBACZ,QAAQ;wBACR,OAAO,SAAS,KAAK;wBACrB,UAAU;wBACV,OAAO,OAAO,KAAK;;;;;;;;;;;8BAGvB,8OAAC;oBAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,UAAU;;sCAC/B,8OAAC;4BAAI,WAAW,iJAAA,CAAA,UAAM,CAAC,SAAS;;8CAC9B,8OAAC;8CAAK;;;;;;8CACN,8OAAC;oCAAE,MAAK;8CAAc;;;;;;;;;;;;sCAExB,8OAAC,+IAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,oBAAM,8OAAC,8IAAA,CAAA,eAAY;;;;;4BACnB,UAAU;sCAET,YAAY,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAMzC"}}, {"offset": {"line": 346, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/auth/signup/header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { FC } from \"react\";\r\n\r\ninterface SignupHeaderProps {\r\n  userType: \"student\" | \"counselor\";\r\n}\r\n\r\nexport const SignupHeader: FC<SignupHeaderProps> = ({ userType }) => {\r\n  return (\r\n    <div>\r\n      <h1 className=\"text-2xl sm:text-4xl font-medium mb-2\">\r\n        {userType === \"student\" ? (\r\n          <>\r\n            Create your <span className=\"text-[#9C0E22]\">Student</span> Profile\r\n          </>\r\n        ) : (\r\n          <>\r\n            Join Our <span className=\"text-[#9C0E22]\">Counselor</span> Network\r\n          </>\r\n        )}\r\n      </h1>\r\n      <p className=\"text-gray-600 text-lg\">\r\n        {userType === \"student\"\r\n          ? \"Get personalized guidance from top counselors\"\r\n          : \"Share your expertise and grow your counseling practice\"}\r\n      </p>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAQO,MAAM,eAAsC,CAAC,EAAE,QAAQ,EAAE;IAC9D,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BACX,aAAa,0BACZ;;wBAAE;sCACY,8OAAC;4BAAK,WAAU;sCAAiB;;;;;;wBAAc;;iDAG7D;;wBAAE;sCACS,8OAAC;4BAAK,WAAU;sCAAiB;;;;;;wBAAgB;;;;;;;;0BAIhE,8OAAC;gBAAE,WAAU;0BACV,aAAa,YACV,kDACA;;;;;;;;;;;;AAIZ"}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 416, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/constants/socialButtons.ts"], "sourcesContent": ["export const socialButtons = [\r\n  {\r\n    label: \"Google\",\r\n    altText: \"Google sign in\",\r\n    provider: \"google\",\r\n  },\r\n  {\r\n    label: \"LinkedIn\",\r\n    altText: \"LinkedIn sign in\",\r\n    provider: \"linkedin\",\r\n  },\r\n  // Apple sign in temporarily disabled\r\n  // {\r\n  //   label: \"Apple\",\r\n  //   altText: \"Apple sign in\",\r\n  //   provider: \"apple\",\r\n  // },\r\n] as const;\r\n\r\nexport enum SOCIAL_PROVIDERS {\r\n  GOOGLE = \"google\",\r\n  LINKEDIN = \"linkedin\",\r\n  // APPLE = \"apple\",\r\n}\r\n"], "names": [], "mappings": ";;;;AAAO,MAAM,gBAAgB;IAC3B;QACE,OAAO;QACP,SAAS;QACT,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;IACZ;CAOD;AAEM,IAAA,AAAK,0CAAA;;;WAAA"}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/lib/apiClient.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport axios from \"axios\";\r\n\r\nconst apiClient = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || \"http://localhost:8000\",\r\n  headers: {\r\n    \"Content-Type\": \"application/json\",\r\n    Accept: \"application/json\",\r\n  },\r\n  // withCredentials: true,\r\n});\r\n\r\n// Request interceptor to handle dynamic headers or logging\r\napiClient.interceptors.request.use(\r\n  (config) => {\r\n    const token = localStorage.getItem(\"access_token\");\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor to handle common errors globally\r\napiClient.interceptors.response.use(\r\n  (response) => {\r\n    return response;\r\n  },\r\n  (error) => {\r\n    // Handle specific error responses (e.g., 401 Unauthorized)\r\n    if (error.response) {\r\n      const { status } = error.response;\r\n\r\n      if (status === 401) {\r\n        if (\r\n          window.location.pathname.startsWith(\"/student\") ||\r\n          window.location.pathname.startsWith(\"/counselor\") ||\r\n          localStorage.getItem(\"access_token\")\r\n        ) {\r\n          console.error(\"Unauthorized. Redirecting to login...\");\r\n          localStorage.removeItem(\"access_token\");\r\n          window.location.href = \"/auth/login\";\r\n        }\r\n      } else if (status >= 500) {\r\n        console.error(\r\n          \"Server error:\",\r\n          error.response.data.message || \"Internal Server Error\"\r\n        );\r\n      }\r\n    } else {\r\n      console.error(\"Network error:\", error.message);\r\n    }\r\n\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default apiClient;\r\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIA,MAAM,YAAY,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC7B,SAAS,6DAAwC;IACjD,SAAS;QACP,gBAAgB;QAChB,QAAQ;IACV;AAEF;AAEA,2DAA2D;AAC3D,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,wDAAwD;AACxD,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,2DAA2D;IAC3D,IAAI,MAAM,QAAQ,EAAE;QAClB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM,QAAQ;QAEjC,IAAI,WAAW,KAAK;YAClB,IACE,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,eACpC,OAAO,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,iBACpC,aAAa,OAAO,CAAC,iBACrB;gBACA,QAAQ,KAAK,CAAC;gBACd,aAAa,UAAU,CAAC;gBACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF,OAAO,IAAI,UAAU,KAAK;YACxB,QAAQ,KAAK,CACX,iBACA,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI;QAEnC;IACF,OAAO;QACL,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;IAC/C;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;uCAGa"}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/utils/auth.ts"], "sourcesContent": ["import { toast } from 'react-toastify';\r\nimport apiClient from '@lib/apiClient';\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_API_BASE_URL;\r\n\r\ninterface AuthError extends Error {\r\n  isAuthError?: boolean;\r\n}\r\n\r\nexport const handleGoogleLogin = async (token: string, userType?: string) => {\r\n  try {\r\n    const response = await apiClient.post('/auth/google', \r\n      userType \r\n        ? { token, user_type: userType }  // Signup case\r\n        : { token }  // Login case\r\n    );\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    if (!error.isAuthError) {\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.error('Google auth error:', error);\r\n      }\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const handleLinkedInLogin = async (code: string, userType?: string) => {\r\n  try {\r\n    const response = await apiClient.post('/auth/linkedin',\r\n      userType \r\n        ? { code, user_type: userType }  // Signup case\r\n        : { code }  // Login case\r\n    );\r\n\r\n    return response.data;\r\n  } catch (error: any) {\r\n    if (!error.isAuthError) {\r\n      if (process.env.NODE_ENV === 'development') {\r\n        console.error('LinkedIn auth error:', error);\r\n      }\r\n    }\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const initiateLinkedInLogin = (userType?: string) => {\r\n  const LINKEDIN_CLIENT_ID = process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID;\r\n  const LINKEDIN_REDIRECT_URI = process.env.NEXT_PUBLIC_LINKEDIN_REDIRECT_URI;\r\n  const scope = 'openid profile email';\r\n  \r\n  // Only include state (userType) for signup flow\r\n  const stateParam = userType ? `&state=${userType}` : '';\r\n  const url = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${LINKEDIN_CLIENT_ID}&redirect_uri=${LINKEDIN_REDIRECT_URI}${stateParam}&scope=${scope}`;\r\n  \r\n  window.location.href = url;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;;AAEA,MAAM;AAMC,MAAM,oBAAoB,OAAO,OAAe;IACrD,IAAI;QACF,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBACpC,WACI;YAAE;YAAO,WAAW;QAAS,EAAG,cAAc;WAC9C;YAAE;QAAM,EAAG,aAAa;;QAG9B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,IAAI,CAAC,MAAM,WAAW,EAAE;YACtB,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,sBAAsB;YACtC;QACF;QACA,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB,OAAO,MAAc;IACtD,IAAI;QACF,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,kBACpC,WACI;YAAE;YAAM,WAAW;QAAS,EAAG,cAAc;WAC7C;YAAE;QAAK,EAAG,aAAa;;QAG7B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAY;QACnB,IAAI,CAAC,MAAM,WAAW,EAAE;YACtB,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACF;QACA,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,CAAC;IACpC,MAAM;IACN,MAAM;IACN,MAAM,QAAQ;IAEd,gDAAgD;IAChD,MAAM,aAAa,WAAW,CAAC,OAAO,EAAE,UAAU,GAAG;IACrD,MAAM,MAAM,CAAC,6EAA6E,EAAE,mBAAmB,cAAc,EAAE,wBAAwB,WAAW,OAAO,EAAE,OAAO;IAElL,OAAO,QAAQ,CAAC,IAAI,GAAG;AACzB"}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 645, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/useAuth.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\nimport apiClient from \"@lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport {\r\n  handleGoogleLogin,\r\n  handleLinkedInLogin,\r\n  initiateLinkedInLogin,\r\n} from \"@/app/utils/auth\";\r\n\r\ninterface User {\r\n  avatar?: string;\r\n  firstName?: string;\r\n  email?: string;\r\n}\r\n\r\ninterface AuthState {\r\n  token: string | null;\r\n  user: User | null;\r\n  userType: string | null;\r\n  loading: boolean;\r\n  login: (username: string, password: string) => Promise<void>;\r\n  signup: (data: SignupData) => Promise<void>;\r\n  verifyEmail: (email: string, code: string) => Promise<void>;\r\n  forgotPassword: (email: string) => Promise<void>;\r\n  resetPassword: (\r\n    email: string,\r\n    code: string,\r\n    newPassword: string\r\n  ) => Promise<void>;\r\n  logout: () => void;\r\n  signupData: Partial<SignupData> | null;\r\n  initiateSignup: (\r\n    data: { first_name: string; last_name: string; email: string },\r\n    userType: \"student\" | \"counselor\"\r\n  ) => Promise<void>;\r\n  resetSignupData: () => void;\r\n  completeSignup: (password: string) => Promise<string>;\r\n  resendVerificationCode: (email: string) => Promise<void>;\r\n  verifySignupCode: (email: string, code: string) => Promise<void>;\r\n  googleAuth: (\r\n    token: string,\r\n    userType: \"student\" | \"counselor\" | undefined\r\n  ) => Promise<void>;\r\n  linkedinAuth: (\r\n    code: string,\r\n    userType: \"student\" | \"counselor\" | undefined\r\n  ) => Promise<void>;\r\n  initiateLinkedInAuth: (userType: \"student\" | \"counselor\" | undefined) => void;\r\n  isAuthenticated: boolean;\r\n  auth: () => boolean;\r\n}\r\n\r\ninterface SignupData {\r\n  email: string;\r\n  password: string;\r\n  first_name: string;\r\n  last_name: string;\r\n  userType: \"student\" | \"counselor\";\r\n}\r\n\r\nexport const useAuth = create<AuthState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      token: null as string | null,\r\n      user: null as User | null,\r\n      userType: null as string | null,\r\n      loading: false,\r\n      signupData: null,\r\n      isAuthenticated: false,\r\n\r\n      auth: () => {\r\n        const token = localStorage.getItem(\"access_token\");\r\n        const isAuthenticated = !!token;\r\n        set({ isAuthenticated });\r\n        return isAuthenticated;\r\n      },\r\n\r\n      login: async (username, password) => {\r\n        try {\r\n          set({ loading: true });\r\n          const formData = new URLSearchParams();\r\n          formData.append(\"username\", username);\r\n          formData.append(\"password\", password);\r\n\r\n          const response = await apiClient.post(\"/auth/signin\", formData, {\r\n            headers: {\r\n              \"Content-Type\": \"application/x-www-form-urlencoded\",\r\n            },\r\n          });\r\n\r\n          const { access_token, token_type, user_type } = response.data;\r\n          localStorage.setItem(\"access_token\", access_token);\r\n          set({\r\n            token: access_token,\r\n            userType: user_type,\r\n            isAuthenticated: true,\r\n          });\r\n\r\n          toast.success(\"Logged in successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(error.response?.data?.detail || \"Login failed\");\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      signup: async (data) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\r\n            `/auth/signup/${data.userType}`,\r\n            {\r\n              email: data.email,\r\n              password: data.password,\r\n              first_name: data.first_name,\r\n              last_name: data.last_name,\r\n            }\r\n          );\r\n\r\n          if (response.data.id) {\r\n            localStorage.setItem(\"email\", data.email);\r\n            toast.success(\"Verification code sent to your email!\");\r\n          }\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Signup failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      verifyEmail: async (email, code) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/verify-code\", { email, code });\r\n          toast.success(\"Code verified successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Verification failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      forgotPassword: async (email) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/forgot-password\", { email });\r\n          toast.success(\"Reset code sent to your email!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg ||\r\n              \"Failed to send reset code\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resetPassword: async (email, code, newPassword) => {\r\n        try {\r\n          set({ loading: true });\r\n          await apiClient.post(\"/auth/reset-password\", {\r\n            email,\r\n            code,\r\n            new_password: newPassword,\r\n          });\r\n          toast.success(\"Password reset successfully!\");\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail?.[0]?.msg || \"Password reset failed\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      logout: () => {\r\n        localStorage.removeItem(\"access_token\");\r\n        set({ token: null, userType: null, isAuthenticated: false });\r\n      },\r\n\r\n      initiateSignup: async (data, userType = \"student\") => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/initiate\", {\r\n            ...data,\r\n            user_type: userType,\r\n          });\r\n          set({ signupData: { ...data, userType } });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Failed to initiate signup\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resetSignupData: () => {\r\n        set({ signupData: null });\r\n      },\r\n\r\n      completeSignup: async (password: string) => {\r\n        const { signupData } = get();\r\n        if (!signupData?.email) {\r\n          toast.error(\"Missing signup data\");\r\n          return;\r\n        }\r\n\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/complete\", {\r\n            email: signupData.email,\r\n            password,\r\n          });\r\n\r\n          const { access_token, token_type, user_type } = response.data;\r\n\r\n          // Store token and update auth state\r\n          localStorage.setItem(\"access_token\", access_token);\r\n          set({\r\n            token: access_token,\r\n            userType: user_type,\r\n            isAuthenticated: true,\r\n            signupData: null,\r\n          });\r\n\r\n          toast.success(\"Account created successfully!\");\r\n\r\n          // Return user type so pages can redirect appropriately\r\n          return user_type;\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Failed to complete signup\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      resendVerificationCode: async (email: string) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/resend\", {\r\n            email,\r\n          });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(error.response?.data?.detail || \"Failed to resend code\");\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      verifySignupCode: async (email: string, code: string) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await apiClient.post(\"/auth/signup/verify\", {\r\n            email,\r\n            code,\r\n          });\r\n          toast.success(response.data.message);\r\n        } catch (error: any) {\r\n          toast.error(\r\n            error.response?.data?.detail || \"Invalid verification code\"\r\n          );\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      googleAuth: async (\r\n        token: string,\r\n        userType: \"student\" | \"counselor\" | undefined\r\n      ) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await handleGoogleLogin(token, userType);\r\n\r\n          if (response.access_token) {\r\n            localStorage.setItem(\"access_token\", response.access_token);\r\n            set({\r\n              token: response.access_token,\r\n              userType: response.user_type,\r\n              isAuthenticated: true,\r\n            });\r\n\r\n            // Get user info to check profile completion status\r\n            const userInfoResponse = await apiClient.get(\r\n              `/user/me?timezone=${\r\n                Intl.DateTimeFormat().resolvedOptions().timeZone\r\n              }`\r\n            );\r\n            const userInfo = userInfoResponse.data;\r\n\r\n            // Success message based on whether it's a new account or not\r\n            if (response.is_new_user) {\r\n              toast.success(\"Account created with Google successfully!\");\r\n            } else {\r\n              toast.success(\"Logged in with Google successfully!\");\r\n            }\r\n\r\n            // Redirect based on user type and profile completion\r\n            if (userInfo.userType === \"counselor\") {\r\n              if (userInfo.is_verified) {\r\n                window.location.href = \"/counselor/dashboard\";\r\n              } else if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/counselor/profile-complete\";\r\n              } else {\r\n                window.location.href = \"/counselor/onboarding\";\r\n              }\r\n            } else {\r\n              window.location.href = \"/explore\";\r\n            }\r\n          }\r\n        } catch (error: any) {\r\n          // Let the error propagate to the component\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      linkedinAuth: async (\r\n        code: string,\r\n        userType: \"student\" | \"counselor\" | undefined\r\n      ) => {\r\n        try {\r\n          set({ loading: true });\r\n          const response = await handleLinkedInLogin(code, userType);\r\n\r\n          if (response.access_token) {\r\n            localStorage.setItem(\"access_token\", response.access_token);\r\n            set({\r\n              token: response.access_token,\r\n              userType: response.user_type,\r\n              isAuthenticated: true,\r\n            });\r\n\r\n            // Get user info to check profile completion status\r\n            const userInfoResponse = await apiClient.get(\r\n              `/user/me?timezone=${\r\n                Intl.DateTimeFormat().resolvedOptions().timeZone\r\n              }`\r\n            );\r\n            const userInfo = userInfoResponse.data;\r\n\r\n            // Success message based on whether it's a new account or not\r\n            if (response.is_new_user) {\r\n              toast.success(\"Account created with LinkedIn successfully!\");\r\n            } else {\r\n              toast.success(\"Logged in with LinkedIn successfully!\");\r\n            }\r\n\r\n            // Redirect based on user type and profile completion\r\n            if (userInfo.userType === \"counselor\") {\r\n              if (userInfo.is_verified) {\r\n                window.location.href = \"/counselor/dashboard\";\r\n              } else if (userInfo.isProfileComplete) {\r\n                window.location.href = \"/counselor/profile-complete\";\r\n              } else {\r\n                window.location.href = \"/counselor/onboarding\";\r\n              }\r\n            } else {\r\n              window.location.href = \"/explore\";\r\n            }\r\n          }\r\n        } catch (error: any) {\r\n          // Let the error propagate to the component\r\n          throw error;\r\n        } finally {\r\n          set({ loading: false });\r\n        }\r\n      },\r\n\r\n      initiateLinkedInAuth: (userType: \"student\" | \"counselor\" | undefined) => {\r\n        initiateLinkedInLogin(userType);\r\n      },\r\n    }),\r\n    {\r\n      name: \"auth-storage\",\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": ";;;AAIA;AACA;AACA;AAJA;AACA;AAHA;;;;;;AA+DO,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC1B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,OAAO;QACP,MAAM;QACN,UAAU;QACV,SAAS;QACT,YAAY;QACZ,iBAAiB;QAEjB,MAAM;YACJ,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,kBAAkB,CAAC,CAAC;YAC1B,IAAI;gBAAE;YAAgB;YACtB,OAAO;QACT;QAEA,OAAO,OAAO,UAAU;YACtB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,YAAY;gBAC5B,SAAS,MAAM,CAAC,YAAY;gBAE5B,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,gBAAgB,UAAU;oBAC9D,SAAS;wBACP,gBAAgB;oBAClB;gBACF;gBAEA,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI;gBAC7D,aAAa,OAAO,CAAC,gBAAgB;gBACrC,IAAI;oBACF,OAAO;oBACP,UAAU;oBACV,iBAAiB;gBACnB;gBAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,QAAQ,OAAO;YACb,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,CAAC,aAAa,EAAE,KAAK,QAAQ,EAAE,EAC/B;oBACE,OAAO,KAAK,KAAK;oBACjB,UAAU,KAAK,QAAQ;oBACvB,YAAY,KAAK,UAAU;oBAC3B,WAAW,KAAK,SAAS;gBAC3B;gBAGF,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;oBACpB,aAAa,OAAO,CAAC,SAAS,KAAK,KAAK;oBACxC,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB;YACF,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,aAAa,OAAO,OAAO;YACzB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,qBAAqB;oBAAE;oBAAO;gBAAK;gBACxD,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,gBAAgB,OAAO;YACrB,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAAE;gBAAM;gBACtD,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OACjC;gBAEJ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,eAAe,OAAO,OAAO,MAAM;YACjC,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,wBAAwB;oBAC3C;oBACA;oBACA,cAAc;gBAChB;gBACA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,OAAO;gBAE5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,QAAQ;YACN,aAAa,UAAU,CAAC;YACxB,IAAI;gBAAE,OAAO;gBAAM,UAAU;gBAAM,iBAAiB;YAAM;QAC5D;QAEA,gBAAgB,OAAO,MAAM,WAAW,SAAS;YAC/C,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAC7D,GAAG,IAAI;oBACP,WAAW;gBACb;gBACA,IAAI;oBAAE,YAAY;wBAAE,GAAG,IAAI;wBAAE;oBAAS;gBAAE;gBACxC,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,iBAAiB;YACf,IAAI;gBAAE,YAAY;YAAK;QACzB;QAEA,gBAAgB,OAAO;YACrB,MAAM,EAAE,UAAU,EAAE,GAAG;YACvB,IAAI,CAAC,YAAY,OAAO;gBACtB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ;YACF;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,yBAAyB;oBAC7D,OAAO,WAAW,KAAK;oBACvB;gBACF;gBAEA,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,SAAS,IAAI;gBAE7D,oCAAoC;gBACpC,aAAa,OAAO,CAAC,gBAAgB;gBACrC,IAAI;oBACF,OAAO;oBACP,UAAU;oBACV,iBAAiB;oBACjB,YAAY;gBACd;gBAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAEd,uDAAuD;gBACvD,OAAO;YACT,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,wBAAwB,OAAO;YAC7B,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;oBAC3D;gBACF;gBACA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAC5C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,kBAAkB,OAAO,OAAe;YACtC,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,uBAAuB;oBAC3D;oBACA;gBACF;gBACA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS,IAAI,CAAC,OAAO;YACrC,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAElC,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,OACV,OACA;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;gBAEhD,IAAI,SAAS,YAAY,EAAE;oBACzB,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;oBAC1D,IAAI;wBACF,OAAO,SAAS,YAAY;wBAC5B,UAAU,SAAS,SAAS;wBAC5B,iBAAiB;oBACnB;oBAEA,mDAAmD;oBACnD,MAAM,mBAAmB,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAC1C,CAAC,kBAAkB,EACjB,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;oBAEJ,MAAM,WAAW,iBAAiB,IAAI;oBAEtC,6DAA6D;oBAC7D,IAAI,SAAS,WAAW,EAAE;wBACxB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO;wBACL,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;oBAEA,qDAAqD;oBACrD,IAAI,SAAS,QAAQ,KAAK,aAAa;wBACrC,IAAI,SAAS,WAAW,EAAE;4BACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO,IAAI,SAAS,iBAAiB,EAAE;4BACrC,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF,OAAO;wBACL,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,2CAA2C;gBAC3C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,cAAc,OACZ,MACA;YAEA,IAAI;gBACF,IAAI;oBAAE,SAAS;gBAAK;gBACpB,MAAM,WAAW,MAAM,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM;gBAEjD,IAAI,SAAS,YAAY,EAAE;oBACzB,aAAa,OAAO,CAAC,gBAAgB,SAAS,YAAY;oBAC1D,IAAI;wBACF,OAAO,SAAS,YAAY;wBAC5B,UAAU,SAAS,SAAS;wBAC5B,iBAAiB;oBACnB;oBAEA,mDAAmD;oBACnD,MAAM,mBAAmB,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAC1C,CAAC,kBAAkB,EACjB,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ,EAChD;oBAEJ,MAAM,WAAW,iBAAiB,IAAI;oBAEtC,6DAA6D;oBAC7D,IAAI,SAAS,WAAW,EAAE;wBACxB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB,OAAO;wBACL,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBAChB;oBAEA,qDAAqD;oBACrD,IAAI,SAAS,QAAQ,KAAK,aAAa;wBACrC,IAAI,SAAS,WAAW,EAAE;4BACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO,IAAI,SAAS,iBAAiB,EAAE;4BACrC,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB,OAAO;4BACL,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACzB;oBACF,OAAO;wBACL,OAAO,QAAQ,CAAC,IAAI,GAAG;oBACzB;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,2CAA2C;gBAC3C,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,sBAAsB,CAAC;YACrB,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAAE;QACxB;IACF,CAAC,GACD;IACE,MAAM;AACR"}}, {"offset": {"line": 989, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 995, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/socialButtons/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { FC } from \"react\";\r\nimport { FaA<PERSON>le, FaLinkedin } from \"react-icons/fa\";\r\nimport { SOCIAL_PROVIDERS } from \"@constants/socialButtons\";\r\nimport { useAuth } from \"@/app/hooks/useAuth\";\r\nimport { useState } from \"react\";\r\nimport { toast } from \"react-toastify\";\r\n\r\ninterface SocialButtonProps {\r\n  label: string;\r\n  altText: string;\r\n  type: \"google\" | \"linkedin\" | \"apple\";\r\n  userType?: \"student\" | \"counselor\";\r\n  isLogin?: boolean;\r\n}\r\n\r\nexport const SocialButton: FC<SocialButtonProps> = ({\r\n  label,\r\n  altText,\r\n  type,\r\n  userType,\r\n  isLogin = false,\r\n}) => {\r\n  const { googleAuth, initiateLinkedInAuth } = useAuth();\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const handleClick = async () => {\r\n    setLoading(true);\r\n    try {\r\n      switch (type) {\r\n        case SOCIAL_PROVIDERS.GOOGLE:\r\n          const { google } = window as any;\r\n          const auth2 = google.accounts.oauth2;\r\n\r\n          const client = auth2.initTokenClient({\r\n            client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,\r\n            scope: \"openid email profile\",\r\n            callback: async (response: any) => {\r\n              try {\r\n                // Only pass userType for signup\r\n                await googleAuth(\r\n                  response.access_token,\r\n                  isLogin ? undefined : userType\r\n                );\r\n              } catch (error: any) {\r\n                if (process.env.NODE_ENV === \"development\") {\r\n                  console.error(\"Google auth error:\", error);\r\n                }\r\n\r\n                if (\r\n                  error.response?.data?.detail.includes(\"already registered\")\r\n                ) {\r\n                  toast.error(error.response?.data?.detail, {\r\n                    toastId: \"auth-error\",\r\n                  });\r\n                } else if (\r\n                  error.response?.data?.detail ===\r\n                  \"user_type is required for registration\"\r\n                ) {\r\n                  toast.error(\r\n                    \"Please use the signup page to register as either a student or counselor.\",\r\n                    { toastId: \"auth-error\" }\r\n                  );\r\n                } else {\r\n                  toast.error(\r\n                    \"Failed to sign in with Google. Please try again.\",\r\n                    { toastId: \"auth-error\" }\r\n                  );\r\n                }\r\n              } finally {\r\n                setLoading(false);\r\n              }\r\n            },\r\n          });\r\n\r\n          client.requestAccessToken();\r\n          break;\r\n\r\n        case SOCIAL_PROVIDERS.LINKEDIN:\r\n          try {\r\n            // Only pass userType for signup\r\n            initiateLinkedInAuth(isLogin ? undefined : userType);\r\n          } catch (error: any) {\r\n            if (process.env.NODE_ENV === \"development\") {\r\n              console.error(\"LinkedIn auth error:\", error);\r\n            }\r\n\r\n            if (error.response?.data?.detail.includes(\"already registered\")) {\r\n              toast.error(error.response?.data?.detail, {\r\n                toastId: \"auth-error\",\r\n              });\r\n            } else if (\r\n              error.response?.data?.detail ===\r\n              \"user_type is required for registration\"\r\n            ) {\r\n              toast.error(\r\n                \"Please use the signup page to register as either a student or counselor.\",\r\n                { toastId: \"auth-error\" }\r\n              );\r\n            } else {\r\n              toast.error(\r\n                \"Failed to sign in with LinkedIn. Please try again.\",\r\n                { toastId: \"auth-error\" }\r\n              );\r\n            }\r\n          }\r\n          setLoading(false);\r\n          break;\r\n\r\n        // case SOCIAL_PROVIDERS.APPLE:\r\n        //   if (process.env.NODE_ENV === \"development\") {\r\n        //     console.log(\"Apple Sign In not implemented yet\");\r\n        //   }\r\n        //   toast.info(\"Apple Sign In coming soon!\", { toastId: \"apple-signin\" });\r\n        //   setLoading(false);\r\n        //   break;\r\n      }\r\n    } catch (error) {\r\n      if (process.env.NODE_ENV === \"development\") {\r\n        console.error(\"Social auth error:\", error);\r\n      }\r\n      toast.error(\"Something went wrong. Please try again.\", {\r\n        toastId: \"auth-error\",\r\n      });\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getIcon = (value: string) => {\r\n    switch (value) {\r\n      case SOCIAL_PROVIDERS.LINKEDIN:\r\n        return (\r\n          <FaLinkedin className=\"w-5 h-5 text-blue-600\" aria-hidden=\"true\" />\r\n        );\r\n      // case SOCIAL_PROVIDERS.APPLE:\r\n      //   return <FaApple className=\"w-5 h-5 text-gray-800\" aria-hidden=\"true\" />;\r\n      case SOCIAL_PROVIDERS.GOOGLE:\r\n        return (\r\n          <span className=\"w-5 h-5\">\r\n            <img src=\"/svgs/googleIcon.svg\" alt=\"Icon\" />\r\n          </span>\r\n        );\r\n      default:\r\n        return null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <button\r\n      type=\"button\"\r\n      className=\"flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\r\n      aria-label={altText}\r\n      onClick={handleClick}\r\n      disabled={loading}\r\n    >\r\n      <div className=\"flex items-center gap-2\">\r\n        {getIcon(type)}\r\n        <span>{loading ? \"Loading...\" : label}</span>\r\n      </div>\r\n    </button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AACA;AAJA;AAHA;;;;;;;AAiBO,MAAM,eAAsC,CAAC,EAClD,KAAK,EACL,OAAO,EACP,IAAI,EACJ,QAAQ,EACR,UAAU,KAAK,EAChB;IACC,MAAM,EAAE,UAAU,EAAE,oBAAoB,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,cAAc;QAClB,WAAW;QACX,IAAI;YACF,OAAQ;gBACN,KAAK,iIAAA,CAAA,mBAAgB,CAAC,MAAM;oBAC1B,MAAM,EAAE,MAAM,EAAE,GAAG;oBACnB,MAAM,QAAQ,OAAO,QAAQ,CAAC,MAAM;oBAEpC,MAAM,SAAS,MAAM,eAAe,CAAC;wBACnC,SAAS;wBACT,OAAO;wBACP,UAAU,OAAO;4BACf,IAAI;gCACF,gCAAgC;gCAChC,MAAM,WACJ,SAAS,YAAY,EACrB,UAAU,YAAY;4BAE1B,EAAE,OAAO,OAAY;gCACnB,wCAA4C;oCAC1C,QAAQ,KAAK,CAAC,sBAAsB;gCACtC;gCAEA,IACE,MAAM,QAAQ,EAAE,MAAM,OAAO,SAAS,uBACtC;oCACA,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,QAAQ;wCACxC,SAAS;oCACX;gCACF,OAAO,IACL,MAAM,QAAQ,EAAE,MAAM,WACtB,0CACA;oCACA,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,4EACA;wCAAE,SAAS;oCAAa;gCAE5B,OAAO;oCACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,oDACA;wCAAE,SAAS;oCAAa;gCAE5B;4BACF,SAAU;gCACR,WAAW;4BACb;wBACF;oBACF;oBAEA,OAAO,kBAAkB;oBACzB;gBAEF,KAAK,iIAAA,CAAA,mBAAgB,CAAC,QAAQ;oBAC5B,IAAI;wBACF,gCAAgC;wBAChC,qBAAqB,UAAU,YAAY;oBAC7C,EAAE,OAAO,OAAY;wBACnB,wCAA4C;4BAC1C,QAAQ,KAAK,CAAC,wBAAwB;wBACxC;wBAEA,IAAI,MAAM,QAAQ,EAAE,MAAM,OAAO,SAAS,uBAAuB;4BAC/D,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,QAAQ;gCACxC,SAAS;4BACX;wBACF,OAAO,IACL,MAAM,QAAQ,EAAE,MAAM,WACtB,0CACA;4BACA,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,4EACA;gCAAE,SAAS;4BAAa;wBAE5B,OAAO;4BACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,sDACA;gCAAE,SAAS;4BAAa;wBAE5B;oBACF;oBACA,WAAW;oBACX;YASJ;QACF,EAAE,OAAO,OAAO;YACd,wCAA4C;gBAC1C,QAAQ,KAAK,CAAC,sBAAsB;YACtC;YACA,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,2CAA2C;gBACrD,SAAS;YACX;YACA,WAAW;QACb;IACF;IAEA,MAAM,UAAU,CAAC;QACf,OAAQ;YACN,KAAK,iIAAA,CAAA,mBAAgB,CAAC,QAAQ;gBAC5B,qBACE,8OAAC,8IAAA,CAAA,aAAU;oBAAC,WAAU;oBAAwB,eAAY;;;;;;YAE9D,+BAA+B;YAC/B,6EAA6E;YAC7E,KAAK,iIAAA,CAAA,mBAAgB,CAAC,MAAM;gBAC1B,qBACE,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,KAAI;wBAAuB,KAAI;;;;;;;;;;;YAG1C;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,WAAU;QACV,cAAY;QACZ,SAAS;QACT,UAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;;gBACZ,QAAQ;8BACT,8OAAC;8BAAM,UAAU,eAAe;;;;;;;;;;;;;;;;;AAIxC"}}, {"offset": {"line": 1149, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1155, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/auth/layout.tsx"], "sourcesContent": ["import type { FC, ReactNode } from \"react\";\r\nimport { SocialButton } from \"@components/common/socialButtons\";\r\nimport { socialButtons } from \"@constants/socialButtons\";\r\nimport Image from \"next/image\";\r\nimport Link from \"next/link\";\r\n\r\ninterface LayoutProps {\r\n    header: ReactNode;\r\n    formContent: ReactNode;\r\n    isLogin: boolean;\r\n    userType?: \"student\" | \"counselor\";\r\n}\r\n\r\nexport const Layout: FC<LayoutProps> = ({\r\n    header,\r\n    formContent,\r\n    isLogin,\r\n    userType = \"student\",\r\n}) => {\r\n    return (\r\n        <div className=\"min-h-screen flex flex-col lg:flex-row overflow-hidden\">\r\n            <div className=\"w-full lg:w-1/2 flex flex-col bg-stone-100 min-h-screen overflow-y-auto\">\r\n                <header className=\"p-4 sm:py-6 sm:px-8 flex items-center\">\r\n                    {/* Logo */}\r\n                    <Link href={\"/\"}>\r\n                        <Image\r\n                            src=\"/icons/logo.png\"\r\n                            alt=\"AdmitPath Logo\"\r\n                            width={100}\r\n                            height={100}\r\n                            className=\"w-[4.5rem]\"\r\n                            priority\r\n                        />\r\n                    </Link>\r\n                </header>\r\n\r\n                <div className=\"flex-1 flex justify-center items-center py-8 px-4\">\r\n                    <div className=\"w-full max-w-lg bg-white rounded-xl p-6 sm:p-8 mx-auto shadow-sm\">\r\n                        {header}\r\n                        <div className=\"flex flex-col sm:flex-row gap-3 w-full my-6\">\r\n                            {socialButtons.map((button) => (\r\n                                <SocialButton\r\n                                    key={button.provider}\r\n                                    type={button.provider}\r\n                                    label={button.label}\r\n                                    altText={button.altText}\r\n                                    userType={userType}\r\n                                    isLogin={isLogin}\r\n                                />\r\n                            ))}\r\n                        </div>\r\n\r\n                        <div className=\"flex items-center gap-4 my-6\">\r\n                            <hr className=\"flex-1 border-gray-200\" />\r\n                            <span className=\"text-gray-500 text-xs sm:text-sm whitespace-nowrap\">\r\n                                or continue with email\r\n                            </span>\r\n                            <hr className=\"flex-1 border-gray-200\" />\r\n                        </div>\r\n\r\n                        {formContent}\r\n\r\n                        {!isLogin && (\r\n                            <div className=\"mt-6 text-center\">\r\n                                {userType === \"student\" ? (\r\n                                    <Link\r\n                                        href=\"/auth/signup/counselor\"\r\n                                        className=\"text-blue-600 hover:text-blue-700 font-medium\"\r\n                                    >\r\n                                        Sign up as a Counselor\r\n                                    </Link>\r\n                                ) : (\r\n                                    <Link\r\n                                        href=\"/auth/signup\"\r\n                                        className=\"text-blue-600 hover:text-blue-700 font-medium\"\r\n                                    >\r\n                                        Sign up as a Student\r\n                                    </Link>\r\n                                )}\r\n                            </div>\r\n                        )}\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <div className=\"hidden lg:block lg:w-1/2 relative\">\r\n                <div className=\"absolute inset-0 bg-black\">\r\n                    <img\r\n                        src=\"/svgs/authCover.svg\"\r\n                        alt=\"Counselor profile\"\r\n                        className=\"w-full h-full object-cover\"\r\n                    />\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AASO,MAAM,SAA0B,CAAC,EACpC,MAAM,EACN,WAAW,EACX,OAAO,EACP,WAAW,SAAS,EACvB;IACG,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAO,WAAU;kCAEd,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAM;sCACR,cAAA,8OAAC,6HAAA,CAAA,UAAK;gCACF,KAAI;gCACJ,KAAI;gCACJ,OAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,QAAQ;;;;;;;;;;;;;;;;kCAKpB,8OAAC;wBAAI,WAAU;kCACX,cAAA,8OAAC;4BAAI,WAAU;;gCACV;8CACD,8OAAC;oCAAI,WAAU;8CACV,iIAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,uBAChB,8OAAC,sJAAA,CAAA,eAAY;4CAET,MAAM,OAAO,QAAQ;4CACrB,OAAO,OAAO,KAAK;4CACnB,SAAS,OAAO,OAAO;4CACvB,UAAU;4CACV,SAAS;2CALJ,OAAO,QAAQ;;;;;;;;;;8CAUhC,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAG,WAAU;;;;;;sDACd,8OAAC;4CAAK,WAAU;sDAAqD;;;;;;sDAGrE,8OAAC;4CAAG,WAAU;;;;;;;;;;;;gCAGjB;gCAEA,CAAC,yBACE,8OAAC;oCAAI,WAAU;8CACV,aAAa,0BACV,8OAAC,4JAAA,CAAA,UAAI;wCACD,MAAK;wCACL,WAAU;kDACb;;;;;6DAID,8OAAC,4JAAA,CAAA,UAAI;wCACD,MAAK;wCACL,WAAU;kDACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUzB,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC;wBACG,KAAI;wBACJ,KAAI;wBACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMlC"}}, {"offset": {"line": 1328, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1334, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/auth/signup/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { FC } from \"react\";\r\nimport { Form, FormProps } from \"./form\";\r\nimport { SignupHeader } from \"./header\";\r\nimport { Layout } from \"../layout\";\r\n\r\ninterface SignupFormProps extends FormProps {\r\n  userType?: \"student\" | \"counselor\";\r\n}\r\n\r\nexport const SignupForm: FC<SignupFormProps> = ({\r\n  handleSubmit,\r\n  isLoading,\r\n  isLogin,\r\n  userType = \"student\",\r\n}) => {\r\n  const formContent = (\r\n    <Form handleSubmit={handleSubmit} isLoading={isLoading} isLogin={isLogin} />\r\n  );\r\n\r\n  return (\r\n    <Layout\r\n      header={<SignupHeader userType={userType} />}\r\n      formContent={formContent}\r\n      isLogin={false}\r\n      userType={userType}\r\n    />\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAWO,MAAM,aAAkC,CAAC,EAC9C,YAAY,EACZ,SAAS,EACT,OAAO,EACP,WAAW,SAAS,EACrB;IACC,MAAM,4BACJ,8OAAC,4IAAA,CAAA,OAAI;QAAC,cAAc;QAAc,WAAW;QAAW,SAAS;;;;;;IAGnE,qBACE,8OAAC,oIAAA,CAAA,SAAM;QACL,sBAAQ,8OAAC,8IAAA,CAAA,eAAY;YAAC,UAAU;;;;;;QAChC,aAAa;QACb,SAAS;QACT,UAAU;;;;;;AAGhB"}}, {"offset": {"line": 1373, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1403, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/auth/signup/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { SignupForm } from \"@components/auth/signup\";\r\nimport { useAuth } from \"@hooks/useAuth\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\ntype SignupData = {\r\n  firstName: string;\r\n  lastName: string;\r\n  email: string;\r\n};\r\n\r\nexport default function SignupPage() {\r\n  const router = useRouter();\r\n  const { initiateSignup, loading } = useAuth();\r\n  const userType = \"student\";\r\n\r\n  const handleInitialSignup = async (data: SignupData) => {\r\n    try {\r\n      await initiateSignup(\r\n        {\r\n          first_name: data.firstName,\r\n          last_name: data.lastName,\r\n          email: data.email,\r\n        },\r\n        userType\r\n      );\r\n\r\n      // Store email for verification\r\n      localStorage.setItem(\"email\", data.email);\r\n      router.replace(\"/auth/verify\");\r\n    } catch (error) {\r\n      // Error handled by hook\r\n    }\r\n  };\r\n\r\n  return (\r\n    <SignupForm\r\n      handleSubmit={handleInitialSignup}\r\n      isLoading={loading}\r\n      isLogin={false}\r\n      userType={userType}\r\n    />\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAYe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAC1C,MAAM,WAAW;IAEjB,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,eACJ;gBACE,YAAY,KAAK,SAAS;gBAC1B,WAAW,KAAK,QAAQ;gBACxB,OAAO,KAAK,KAAK;YACnB,GACA;YAGF,+BAA+B;YAC/B,aAAa,OAAO,CAAC,SAAS,KAAK,KAAK;YACxC,OAAO,OAAO,CAAC;QACjB,EAAE,OAAO,OAAO;QACd,wBAAwB;QAC1B;IACF;IAEA,qBACE,8OAAC,6IAAA,CAAA,aAAU;QACT,cAAc;QACd,WAAW;QACX,SAAS;QACT,UAAU;;;;;;AAGhB"}}, {"offset": {"line": 1444, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}