{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/create/index.tsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const OnboardingPage = registerClientReference(\n    function() { throw new Error(\"Attempted to call OnboardingPage() from the server but OnboardingPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/counselor/profile/create/index.tsx <module evaluation>\",\n    \"OnboardingPage\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,mFACA"}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/create/index.tsx/proxy.js"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const OnboardingPage = registerClientReference(\n    function() { throw new Error(\"Attempted to call OnboardingPage() from the server but OnboardingPage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/app/components/counselor/profile/create/index.tsx\",\n    \"OnboardingPage\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+DACA"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/counselor/onboarding/page.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\r\nimport { OnboardingPage } from \"@components/counselor/profile/create\";\r\n\r\nexport const metadata: Metadata = {\r\n  title: \"Create Counselor Profile\",\r\n  description: \"Create Counselor Profile\",\r\n};\r\n\r\nexport default function CounselorProfileCreationsPage() {\r\n  return <OnboardingPage />;\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS;IACtB,qBAAO,8OAAC,6JAAA,CAAA,iBAAc;;;;;AACxB"}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind'\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR'\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: any\ndeclare const __next_app_load_chunk__: any\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base'\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;IA+BES,WAAWC;AAFb,OAAO,MAAMJ,eAAe;AAH5B,8BAA8B;AAzB9B,SAASN,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;AAYpI,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;AAgBtB,cAAc,qCAAoC;IAIhDS,YAAY;;;;;;;;;;;;QACVC,MAAMZ,UAAUa,QAAQ,yCAAA;QACxBC,EAAAA,IAAM;QACNC,KAAAA;IAAAA;IAAU;gBACV,IAAA;YAAA;YAAA,yBAA2C;wBAC3CC,IAAAA;oBAAAA,OAAY;oBAAA;gCACZC,IAAAA;4BAAAA,KAAU;4BAAA;iCACVC,UAAU,EAAE;kCACd,QAAA,CAAA;gCAAA,QAAA;oCAAA,IAAA;oCAAA;iCAAA;;2BACAC,UAAU;;yBACRC,YAAYnB;0BACd,QAAA,CAAA;wBAAA,UAAA;4BAAA,IAAA;4BAAA;yBAAA;oBACF;iBAAA,CAAE", "ignoreList": [0]}}, {"offset": {"line": 206, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}