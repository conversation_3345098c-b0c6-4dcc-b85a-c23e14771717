{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/counselor/onboarding/layout.tsx"], "sourcesContent": ["import React, { ReactNode } from \"react\";\r\n\r\ninterface LayoutProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const metadata = {\r\n  title: \"Setup Counselor Profile\",\r\n  description:\r\n    \"Onboarding page for setting up counselor profile. This is the first step to getting started with our service.\",\r\n};\r\n\r\nexport default function Layout({ children }: LayoutProps) {\r\n  return <div>{children}</div>;\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAMO,MAAM,WAAW;IACtB,OAAO;IACP,aACE;AACJ;AAEe,SAAS,OAAO,EAAE,QAAQ,EAAe;IACtD,qBAAO,8OAAC;kBAAK;;;;;;AACf"}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,0HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}