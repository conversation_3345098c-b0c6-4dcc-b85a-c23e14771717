{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/loadingSpinner/index.tsx"], "sourcesContent": ["export default function LoadingSpinner() {\r\n  return (\r\n    <div role=\"status\" className=\"absolute left-1/2 -translate-x-1/2 pt-4\">\r\n      <svg\r\n        aria-hidden=\"true\"\r\n        className=\"w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600\"\r\n        viewBox=\"0 0 100 101\"\r\n        fill=\"none\"\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n      >\r\n        <path\r\n          d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\r\n          fill=\"currentColor\"\r\n        />\r\n        <path\r\n          d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\r\n          fill=\"currentFill\"\r\n        />\r\n      </svg>\r\n      <span className=\"sr-only\">Loading...</span>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,MAAK;QAAS,WAAU;;0BAC3B,8OAAC;gBACC,eAAY;gBACZ,WAAU;gBACV,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,8OAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;0BAGT,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC"}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/common/inputField/index.module.css [app-client] (css module)"], "sourcesContent": ["__turbopack_export_value__({\n  \"container\": \"index-module__8yU72G__container\",\n  \"error\": \"index-module__8yU72G__error\",\n  \"errorMessage\": \"index-module__8yU72G__errorMessage\",\n  \"input\": \"index-module__8yU72G__input\",\n  \"label\": \"index-module__8yU72G__label\",\n  \"required\": \"index-module__8yU72G__required\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/inputField/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { FC, InputHTMLAttributes } from \"react\";\r\nimport styles from \"./index.module.css\";\r\n\r\ninterface InputFieldProps extends InputHTMLAttributes<HTMLInputElement> {\r\n  label: string;\r\n  error?: string;\r\n  required?: boolean;\r\n}\r\n\r\nexport const InputField: FC<InputFieldProps> = ({\r\n  label,\r\n  required = false,\r\n  error,\r\n  ...inputProps\r\n}) => {\r\n  return (\r\n    <div className={styles.container}>\r\n      <label\r\n        htmlFor={inputProps.id || inputProps.name}\r\n        className={styles.label}\r\n      >\r\n        {label} {required && <span className={styles.required}>*</span>}\r\n      </label>\r\n\r\n      <input\r\n        {...inputProps}\r\n        className={`${styles.input} ${error ? styles.error : \"\"}`}\r\n        aria-required={required}\r\n        aria-invalid={!!error}\r\n        aria-describedby={error ? `${inputProps.id}-error` : undefined}\r\n      />\r\n\r\n      {error && (\r\n        <span\r\n          id={`${inputProps.id}-error`}\r\n          className={styles.errorMessage}\r\n          role=\"alert\"\r\n        >\r\n          {error}\r\n        </span>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWO,MAAM,aAAkC,CAAC,EAC9C,KAAK,EACL,WAAW,KAAK,EAChB,KAAK,EACL,GAAG,YACJ;IACC,qBACE,8OAAC;QAAI,WAAW,iKAAA,CAAA,UAAM,CAAC,SAAS;;0BAC9B,8OAAC;gBACC,SAAS,WAAW,EAAE,IAAI,WAAW,IAAI;gBACzC,WAAW,iKAAA,CAAA,UAAM,CAAC,KAAK;;oBAEtB;oBAAM;oBAAE,0BAAY,8OAAC;wBAAK,WAAW,iKAAA,CAAA,UAAM,CAAC,QAAQ;kCAAE;;;;;;;;;;;;0BAGzD,8OAAC;gBACE,GAAG,UAAU;gBACd,WAAW,GAAG,iKAAA,CAAA,UAAM,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,iKAAA,CAAA,UAAM,CAAC,KAAK,GAAG,IAAI;gBACzD,iBAAe;gBACf,gBAAc,CAAC,CAAC;gBAChB,oBAAkB,QAAQ,GAAG,WAAW,EAAE,CAAC,MAAM,CAAC,GAAG;;;;;;YAGtD,uBACC,8OAAC;gBACC,IAAI,GAAG,WAAW,EAAE,CAAC,MAAM,CAAC;gBAC5B,WAAW,iKAAA,CAAA,UAAM,CAAC,YAAY;gBAC9B,MAAK;0BAEJ;;;;;;;;;;;;AAKX"}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/lib/countries.ts"], "sourcesContent": ["export const countries = [\r\n  { name: \"Afghanistan\", code: \"AF\" },\r\n  { name: \"Åland Islands\", code: \"AX\" },\r\n  { name: \"Albania\", code: \"AL\" },\r\n  { name: \"Algeria\", code: \"DZ\" },\r\n  { name: \"American Samoa\", code: \"AS\" },\r\n  { name: \"Andorra\", code: \"AD\" },\r\n  { name: \"Angola\", code: \"AO\" },\r\n  { name: \"Ang<PERSON><PERSON>\", code: \"AI\" },\r\n  { name: \"Antarctica\", code: \"AQ\" },\r\n  { name: \"Antigua and Barbuda\", code: \"AG\" },\r\n  { name: \"Argentina\", code: \"AR\" },\r\n  { name: \"Armenia\", code: \"AM\" },\r\n  { name: \"Aruba\", code: \"AW\" },\r\n  { name: \"Australia\", code: \"AU\" },\r\n  { name: \"Austria\", code: \"AT\" },\r\n  { name: \"Azerbaijan\", code: \"AZ\" },\r\n  { name: \"Bahamas\", code: \"BS\" },\r\n  { name: \"Bahrain\", code: \"BH\" },\r\n  { name: \"Bangladesh\", code: \"BD\" },\r\n  { name: \"Barbados\", code: \"BB\" },\r\n  { name: \"Belarus\", code: \"BY\" },\r\n  { name: \"Belgium\", code: \"BE\" },\r\n  { name: \"Belize\", code: \"BZ\" },\r\n  { name: \"Benin\", code: \"BJ\" },\r\n  { name: \"Bermuda\", code: \"BM\" },\r\n  { name: \"Bhutan\", code: \"BT\" },\r\n  { name: \"Bolivia\", code: \"BO\" },\r\n  { name: \"Bosnia and Herzegovina\", code: \"BA\" },\r\n  { name: \"Botswana\", code: \"BW\" },\r\n  { name: \"Bouvet Island\", code: \"BV\" },\r\n  { name: \"Brazil\", code: \"BR\" },\r\n  { name: \"British Indian Ocean Territory\", code: \"IO\" },\r\n  { name: \"Brunei Darussalam\", code: \"BN\" },\r\n  { name: \"Bulgaria\", code: \"BG\" },\r\n  { name: \"Burkina Faso\", code: \"BF\" },\r\n  { name: \"Burundi\", code: \"BI\" },\r\n  { name: \"Cambodia\", code: \"KH\" },\r\n  { name: \"Cameroon\", code: \"CM\" },\r\n  { name: \"Canada\", code: \"CA\" },\r\n  { name: \"Cape Verde\", code: \"CV\" },\r\n  { name: \"Cayman Islands\", code: \"KY\" },\r\n  { name: \"Central African Republic\", code: \"CF\" },\r\n  { name: \"Chad\", code: \"TD\" },\r\n  { name: \"Chile\", code: \"CL\" },\r\n  { name: \"China\", code: \"CN\" },\r\n  { name: \"Christmas Island\", code: \"CX\" },\r\n  { name: \"Cocos (Keeling) Islands\", code: \"CC\" },\r\n  { name: \"Colombia\", code: \"CO\" },\r\n  { name: \"Comoros\", code: \"KM\" },\r\n  { name: \"Congo\", code: \"CG\" },\r\n  { name: \"Congo, The Democratic Republic of The\", code: \"CD\" },\r\n  { name: \"Cook Islands\", code: \"CK\" },\r\n  { name: \"Costa Rica\", code: \"CR\" },\r\n  { name: \"Cote D'ivoire\", code: \"CI\" },\r\n  { name: \"Croatia\", code: \"HR\" },\r\n  { name: \"Cuba\", code: \"CU\" },\r\n  { name: \"Cyprus\", code: \"CY\" },\r\n  { name: \"Czech Republic\", code: \"CZ\" },\r\n  { name: \"Denmark\", code: \"DK\" },\r\n  { name: \"Djibouti\", code: \"DJ\" },\r\n  { name: \"Dominica\", code: \"DM\" },\r\n  { name: \"Dominican Republic\", code: \"DO\" },\r\n  { name: \"Ecuador\", code: \"EC\" },\r\n  { name: \"Egypt\", code: \"EG\" },\r\n  { name: \"El Salvador\", code: \"SV\" },\r\n  { name: \"Equatorial Guinea\", code: \"GQ\" },\r\n  { name: \"Eritrea\", code: \"ER\" },\r\n  { name: \"Estonia\", code: \"EE\" },\r\n  { name: \"Ethiopia\", code: \"ET\" },\r\n  { name: \"Falkland Islands (Malvinas)\", code: \"FK\" },\r\n  { name: \"Faroe Islands\", code: \"FO\" },\r\n  { name: \"Fiji\", code: \"FJ\" },\r\n  { name: \"Finland\", code: \"FI\" },\r\n  { name: \"France\", code: \"FR\" },\r\n  { name: \"French Guiana\", code: \"GF\" },\r\n  { name: \"French Polynesia\", code: \"PF\" },\r\n  { name: \"French Southern Territories\", code: \"TF\" },\r\n  { name: \"Gabon\", code: \"GA\" },\r\n  { name: \"Gambia\", code: \"GM\" },\r\n  { name: \"Georgia\", code: \"GE\" },\r\n  { name: \"Germany\", code: \"DE\" },\r\n  { name: \"Ghana\", code: \"GH\" },\r\n  { name: \"Gibraltar\", code: \"GI\" },\r\n  { name: \"Greece\", code: \"GR\" },\r\n  { name: \"Greenland\", code: \"GL\" },\r\n  { name: \"Grenada\", code: \"GD\" },\r\n  { name: \"Guadeloupe\", code: \"GP\" },\r\n  { name: \"Guam\", code: \"GU\" },\r\n  { name: \"Guatemala\", code: \"GT\" },\r\n  { name: \"Guernsey\", code: \"GG\" },\r\n  { name: \"Guinea\", code: \"GN\" },\r\n  { name: \"Guinea-bissau\", code: \"GW\" },\r\n  { name: \"Guyana\", code: \"GY\" },\r\n  { name: \"Haiti\", code: \"HT\" },\r\n  { name: \"Heard Island and Mcdonald Islands\", code: \"HM\" },\r\n  { name: \"Holy See (Vatican City State)\", code: \"VA\" },\r\n  { name: \"Honduras\", code: \"HN\" },\r\n  { name: \"Hong Kong\", code: \"HK\" },\r\n  { name: \"Hungary\", code: \"HU\" },\r\n  { name: \"Iceland\", code: \"IS\" },\r\n  { name: \"India\", code: \"IN\" },\r\n  { name: \"Indonesia\", code: \"ID\" },\r\n  { name: \"Iran, Islamic Republic of\", code: \"IR\" },\r\n  { name: \"Iraq\", code: \"IQ\" },\r\n  { name: \"Ireland\", code: \"IE\" },\r\n  { name: \"Isle of Man\", code: \"IM\" },\r\n  { name: \"Israel\", code: \"IL\" },\r\n  { name: \"Italy\", code: \"IT\" },\r\n  { name: \"Jamaica\", code: \"JM\" },\r\n  { name: \"Japan\", code: \"JP\" },\r\n  { name: \"Jersey\", code: \"JE\" },\r\n  { name: \"Jordan\", code: \"JO\" },\r\n  { name: \"Kazakhstan\", code: \"KZ\" },\r\n  { name: \"Kenya\", code: \"KE\" },\r\n  { name: \"Kiribati\", code: \"KI\" },\r\n  { name: \"Korea, Democratic People's Republic of\", code: \"KP\" },\r\n  { name: \"Korea, Republic of\", code: \"KR\" },\r\n  { name: \"Kuwait\", code: \"KW\" },\r\n  { name: \"Kyrgyzstan\", code: \"KG\" },\r\n  { name: \"Lao People's Democratic Republic\", code: \"LA\" },\r\n  { name: \"Latvia\", code: \"LV\" },\r\n  { name: \"Lebanon\", code: \"LB\" },\r\n  { name: \"Lesotho\", code: \"LS\" },\r\n  { name: \"Liberia\", code: \"LR\" },\r\n  { name: \"Libyan Arab Jamahiriya\", code: \"LY\" },\r\n  { name: \"Liechtenstein\", code: \"LI\" },\r\n  { name: \"Lithuania\", code: \"LT\" },\r\n  { name: \"Luxembourg\", code: \"LU\" },\r\n  { name: \"Macao\", code: \"MO\" },\r\n  { name: \"Macedonia, The Former Yugoslav Republic of\", code: \"MK\" },\r\n  { name: \"Madagascar\", code: \"MG\" },\r\n  { name: \"Malawi\", code: \"MW\" },\r\n  { name: \"Malaysia\", code: \"MY\" },\r\n  { name: \"Maldives\", code: \"MV\" },\r\n  { name: \"Mali\", code: \"ML\" },\r\n  { name: \"Malta\", code: \"MT\" },\r\n  { name: \"Marshall Islands\", code: \"MH\" },\r\n  { name: \"Martinique\", code: \"MQ\" },\r\n  { name: \"Mauritania\", code: \"MR\" },\r\n  { name: \"Mauritius\", code: \"MU\" },\r\n  { name: \"Mayotte\", code: \"YT\" },\r\n  { name: \"Mexico\", code: \"MX\" },\r\n  { name: \"Micronesia, Federated States of\", code: \"FM\" },\r\n  { name: \"Moldova, Republic of\", code: \"MD\" },\r\n  { name: \"Monaco\", code: \"MC\" },\r\n  { name: \"Mongolia\", code: \"MN\" },\r\n  { name: \"Montenegro\", code: \"ME\" },\r\n  { name: \"Montserrat\", code: \"MS\" },\r\n  { name: \"Morocco\", code: \"MA\" },\r\n  { name: \"Mozambique\", code: \"MZ\" },\r\n  { name: \"Myanmar\", code: \"MM\" },\r\n  { name: \"Namibia\", code: \"NA\" },\r\n  { name: \"Nauru\", code: \"NR\" },\r\n  { name: \"Nepal\", code: \"NP\" },\r\n  { name: \"Netherlands\", code: \"NL\" },\r\n  { name: \"Netherlands Antilles\", code: \"AN\" },\r\n  { name: \"New Caledonia\", code: \"NC\" },\r\n  { name: \"New Zealand\", code: \"NZ\" },\r\n  { name: \"Nicaragua\", code: \"NI\" },\r\n  { name: \"Niger\", code: \"NE\" },\r\n  { name: \"Nigeria\", code: \"NG\" },\r\n  { name: \"Niue\", code: \"NU\" },\r\n  { name: \"Norfolk Island\", code: \"NF\" },\r\n  { name: \"Northern Mariana Islands\", code: \"MP\" },\r\n  { name: \"Norway\", code: \"NO\" },\r\n  { name: \"Oman\", code: \"OM\" },\r\n  { name: \"Pakistan\", code: \"PK\" },\r\n  { name: \"Palau\", code: \"PW\" },\r\n  { name: \"Palestinian Territory, Occupied\", code: \"PS\" },\r\n  { name: \"Panama\", code: \"PA\" },\r\n  { name: \"Papua New Guinea\", code: \"PG\" },\r\n  { name: \"Paraguay\", code: \"PY\" },\r\n  { name: \"Peru\", code: \"PE\" },\r\n  { name: \"Philippines\", code: \"PH\" },\r\n  { name: \"Pitcairn\", code: \"PN\" },\r\n  { name: \"Poland\", code: \"PL\" },\r\n  { name: \"Portugal\", code: \"PT\" },\r\n  { name: \"Puerto Rico\", code: \"PR\" },\r\n  { name: \"Qatar\", code: \"QA\" },\r\n  { name: \"Reunion\", code: \"RE\" },\r\n  { name: \"Romania\", code: \"RO\" },\r\n  { name: \"Russian Federation\", code: \"RU\" },\r\n  { name: \"Rwanda\", code: \"RW\" },\r\n  { name: \"Saint Helena\", code: \"SH\" },\r\n  { name: \"Saint Kitts and Nevis\", code: \"KN\" },\r\n  { name: \"Saint Lucia\", code: \"LC\" },\r\n  { name: \"Saint Pierre and Miquelon\", code: \"PM\" },\r\n  { name: \"Saint Vincent and The Grenadines\", code: \"VC\" },\r\n  { name: \"Samoa\", code: \"WS\" },\r\n  { name: \"San Marino\", code: \"SM\" },\r\n  { name: \"Sao Tome and Principe\", code: \"ST\" },\r\n  { name: \"Saudi Arabia\", code: \"SA\" },\r\n  { name: \"Senegal\", code: \"SN\" },\r\n  { name: \"Serbia\", code: \"RS\" },\r\n  { name: \"Seychelles\", code: \"SC\" },\r\n  { name: \"Sierra Leone\", code: \"SL\" },\r\n  { name: \"Singapore\", code: \"SG\" },\r\n  { name: \"Slovakia\", code: \"SK\" },\r\n  { name: \"Slovenia\", code: \"SI\" },\r\n  { name: \"Solomon Islands\", code: \"SB\" },\r\n  { name: \"Somalia\", code: \"SO\" },\r\n  { name: \"South Africa\", code: \"ZA\" },\r\n  { name: \"South Georgia and The South Sandwich Islands\", code: \"GS\" },\r\n  { name: \"Spain\", code: \"ES\" },\r\n  { name: \"Sri Lanka\", code: \"LK\" },\r\n  { name: \"Sudan\", code: \"SD\" },\r\n  { name: \"Suriname\", code: \"SR\" },\r\n  { name: \"Svalbard and Jan Mayen\", code: \"SJ\" },\r\n  { name: \"Swaziland\", code: \"SZ\" },\r\n  { name: \"Sweden\", code: \"SE\" },\r\n  { name: \"Switzerland\", code: \"CH\" },\r\n  { name: \"Syrian Arab Republic\", code: \"SY\" },\r\n  { name: \"Taiwan\", code: \"TW\" },\r\n  { name: \"Tajikistan\", code: \"TJ\" },\r\n  { name: \"Tanzania, United Republic of\", code: \"TZ\" },\r\n  { name: \"Thailand\", code: \"TH\" },\r\n  { name: \"Timor-leste\", code: \"TL\" },\r\n  { name: \"Togo\", code: \"TG\" },\r\n  { name: \"Tokelau\", code: \"TK\" },\r\n  { name: \"Tonga\", code: \"TO\" },\r\n  { name: \"Trinidad and Tobago\", code: \"TT\" },\r\n  { name: \"Tunisia\", code: \"TN\" },\r\n  { name: \"Turkey\", code: \"TR\" },\r\n  { name: \"Turkmenistan\", code: \"TM\" },\r\n  { name: \"Turks and Caicos Islands\", code: \"TC\" },\r\n  { name: \"Tuvalu\", code: \"TV\" },\r\n  { name: \"Uganda\", code: \"UG\" },\r\n  { name: \"Ukraine\", code: \"UA\" },\r\n  { name: \"United Arab Emirates\", code: \"AE\" },\r\n  { name: \"United Kingdom\", code: \"GB\" },\r\n  { name: \"United States\", code: \"US\" },\r\n  { name: \"United States Minor Outlying Islands\", code: \"UM\" },\r\n  { name: \"Uruguay\", code: \"UY\" },\r\n  { name: \"Uzbekistan\", code: \"UZ\" },\r\n  { name: \"Vanuatu\", code: \"VU\" },\r\n  { name: \"Venezuela\", code: \"VE\" },\r\n  { name: \"Viet Nam\", code: \"VN\" },\r\n  { name: \"Virgin Islands, British\", code: \"VG\" },\r\n  { name: \"Virgin Islands, U.S.\", code: \"VI\" },\r\n  { name: \"Wallis and Futuna\", code: \"WF\" },\r\n  { name: \"Western Sahara\", code: \"EH\" },\r\n  { name: \"Yemen\", code: \"YE\" },\r\n  { name: \"Zambia\", code: \"ZM\" },\r\n  { name: \"Zimbabwe\", code: \"ZW\" },\r\n];\r\n"], "names": [], "mappings": ";;;AAAO,MAAM,YAAY;IACvB;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAuB,MAAM;IAAK;IAC1C;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAA0B,MAAM;IAAK;IAC7C;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAkC,MAAM;IAAK;IACrD;QAAE,MAAM;QAAqB,MAAM;IAAK;IACxC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAA4B,MAAM;IAAK;IAC/C;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAA2B,MAAM;IAAK;IAC9C;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAyC,MAAM;IAAK;IAC5D;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAsB,MAAM;IAAK;IACzC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAqB,MAAM;IAAK;IACxC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAA+B,MAAM;IAAK;IAClD;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAA+B,MAAM;IAAK;IAClD;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAqC,MAAM;IAAK;IACxD;QAAE,MAAM;QAAiC,MAAM;IAAK;IACpD;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAA6B,MAAM;IAAK;IAChD;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAA0C,MAAM;IAAK;IAC7D;QAAE,MAAM;QAAsB,MAAM;IAAK;IACzC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAoC,MAAM;IAAK;IACvD;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAA0B,MAAM;IAAK;IAC7C;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAA8C,MAAM;IAAK;IACjE;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAmC,MAAM;IAAK;IACtD;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAA4B,MAAM;IAAK;IAC/C;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAmC,MAAM;IAAK;IACtD;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAoB,MAAM;IAAK;IACvC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAsB,MAAM;IAAK;IACzC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAyB,MAAM;IAAK;IAC5C;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAA6B,MAAM;IAAK;IAChD;QAAE,MAAM;QAAoC,MAAM;IAAK;IACvD;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAyB,MAAM;IAAK;IAC5C;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAmB,MAAM;IAAK;IACtC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAAgD,MAAM;IAAK;IACnE;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAA0B,MAAM;IAAK;IAC7C;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAgC,MAAM;IAAK;IACnD;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAAe,MAAM;IAAK;IAClC;QAAE,MAAM;QAAQ,MAAM;IAAK;IAC3B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAuB,MAAM;IAAK;IAC1C;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAgB,MAAM;IAAK;IACnC;QAAE,MAAM;QAA4B,MAAM;IAAK;IAC/C;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAAiB,MAAM;IAAK;IACpC;QAAE,MAAM;QAAwC,MAAM;IAAK;IAC3D;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAc,MAAM;IAAK;IACjC;QAAE,MAAM;QAAW,MAAM;IAAK;IAC9B;QAAE,MAAM;QAAa,MAAM;IAAK;IAChC;QAAE,MAAM;QAAY,MAAM;IAAK;IAC/B;QAAE,MAAM;QAA2B,MAAM;IAAK;IAC9C;QAAE,MAAM;QAAwB,MAAM;IAAK;IAC3C;QAAE,MAAM;QAAqB,MAAM;IAAK;IACxC;QAAE,MAAM;QAAkB,MAAM;IAAK;IACrC;QAAE,MAAM;QAAS,MAAM;IAAK;IAC5B;QAAE,MAAM;QAAU,MAAM;IAAK;IAC7B;QAAE,MAAM;QAAY,MAAM;IAAK;CAChC"}}, {"offset": {"line": 1127, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1133, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/mainBtn/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { IconProp } from \"@fortawesome/fontawesome-svg-core\";\r\n\r\ninterface ButtonProps {\r\n  type?: \"submit\" | \"reset\" | \"button\";\r\n  children: React.ReactNode;\r\n  onClick?: (\r\n    e: React.MouseEvent<HTMLButtonElement> | React.FormEvent<Element>\r\n  ) => void;\r\n  style?: React.CSSProperties;\r\n  className?: string;\r\n  variant?: \"primary\" | \"secondary\" | \"neutral\";\r\n  icon?: IconProp;\r\n  disabled?: boolean;\r\n}\r\n\r\nexport const MainButton: React.FC<ButtonProps> = ({\r\n  type,\r\n  children,\r\n  onClick,\r\n  style,\r\n  className = \"\",\r\n  variant = \"neutral\",\r\n  icon,\r\n  disabled = false,\r\n}) => {\r\n  // Default styles for button variants\r\n  const variantStyles = {\r\n    primary: \"bg-mainClr text-white hover:bg-[#152070] focus:ring-blue-300\",\r\n    secondary: \"bg-[#EB5757] text-white hover:bg-[#EB6767]\",\r\n    neutral: \"bg-neutral1 text-neutral10 hover:bg-neutral2 focus:ring-neutral3\",\r\n  };\r\n\r\n  const disabledStyles = \"opacity-50 cursor-not-allowed\";\r\n\r\n  return (\r\n    <button\r\n      type={type}\r\n      className={`py-3 px-6 font-medium rounded-xl text-sm flex items-center justify-center gap-2 \r\n        ${variantStyles[variant]} ${\r\n        disabled ? disabledStyles : \"\"\r\n      } ${className}`}\r\n      style={style}\r\n      onClick={!disabled ? onClick : undefined}\r\n      disabled={disabled}\r\n    >\r\n      {children}\r\n      {icon && <FontAwesomeIcon icon={icon} />}\r\n    </button>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAmBO,MAAM,aAAoC,CAAC,EAChD,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,KAAK,EACL,YAAY,EAAE,EACd,UAAU,SAAS,EACnB,IAAI,EACJ,WAAW,KAAK,EACjB;IACC,qCAAqC;IACrC,MAAM,gBAAgB;QACpB,SAAS;QACT,WAAW;QACX,SAAS;IACX;IAEA,MAAM,iBAAiB;IAEvB,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAC;QACV,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC,EAC1B,WAAW,iBAAiB,GAC7B,CAAC,EAAE,WAAW;QACf,OAAO;QACP,SAAS,CAAC,WAAW,UAAU;QAC/B,UAAU;;YAET;YACA,sBAAQ,8OAAC,oKAAA,CAAA,kBAAe;gBAAC,MAAM;;;;;;;;;;;;AAGtC"}}, {"offset": {"line": 1172, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1178, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/navigationBtns/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport { faArrowRight } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { MainButton } from \"../mainBtn\";\r\n\r\ninterface NavigationButtonsProps {\r\n  currentIndex: number;\r\n  onNext: () => void;\r\n  onBack: () => void;\r\n  disableBack?: boolean;\r\n  nextLabel?: string;\r\n}\r\n\r\nexport const NavigationButtons: React.FC<NavigationButtonsProps> = ({\r\n  currentIndex,\r\n  onNext,\r\n  onBack,\r\n  disableBack = false,\r\n  nextLabel,\r\n}) => (\r\n  <div className=\"flex justify-end gap-3 mt-8\">\r\n    {nextLabel === \"Next\" && (\r\n      <button\r\n        type=\"button\"\r\n        className=\"border py-1.5 px-6 rounded-lg hover:bg-[#f5eaea]\"\r\n        onClick={onBack}\r\n        disabled={disableBack}\r\n      >\r\n        Back\r\n      </button>\r\n    )}\r\n\r\n    {/* <button\r\n      type=\"submit\"\r\n      className=\"bg-mainClr text-white py-1.5 px-6 rounded-lg hover:bg-[#2d2f3b]\"\r\n    >\r\n     \r\n    </button> */}\r\n    <MainButton variant=\"primary\">\r\n      {\" \"}\r\n      {nextLabel}{\" \"}\r\n      {nextLabel === \"Next\" && (\r\n        <span>\r\n          <FontAwesomeIcon icon={faArrowRight} />\r\n        </span>\r\n      )}\r\n    </MainButton>\r\n  </div>\r\n);\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AADA;AAHA;;;;;AAcO,MAAM,oBAAsD,CAAC,EAClE,YAAY,EACZ,MAAM,EACN,MAAM,EACN,cAAc,KAAK,EACnB,SAAS,EACV,iBACC,8OAAC;QAAI,WAAU;;YACZ,cAAc,wBACb,8OAAC;gBACC,MAAK;gBACL,WAAU;gBACV,SAAS;gBACT,UAAU;0BACX;;;;;;0BAWH,8OAAC,gJAAA,CAAA,aAAU;gBAAC,SAAQ;;oBACjB;oBACA;oBAAW;oBACX,cAAc,wBACb,8OAAC;kCACC,cAAA,8OAAC,oKAAA,CAAA,kBAAe;4BAAC,MAAM,wKAAA,CAAA,eAAY"}}, {"offset": {"line": 1235, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1241, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/useFormState.ts"], "sourcesContent": ["import { useState } from \"react\";\r\n\r\nfunction useFormState<T extends Record<string, any>>(initialState: T) {\r\n  const [formData, setFormData] = useState(initialState);\r\n\r\n  const handleChange = (\r\n    e: React.ChangeEvent<\r\n      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement\r\n    >\r\n  ) => {\r\n    const { name, type } = e.target;\r\n\r\n    // Narrow down the type to HTMLInputElement for checkboxes and radio buttons\r\n    if (\r\n      e.target instanceof HTMLInputElement &&\r\n      (type === \"checkbox\" || type === \"radio\")\r\n    ) {\r\n      const { checked } = e.target;\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        [name]: checked, // Use `checked` for checkboxes and radio buttons\r\n      }));\r\n    } else {\r\n      const { value } = e.target;\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        [name]: value, // Use `value` for other input types\r\n      }));\r\n    }\r\n  };\r\n\r\n  return { formData, setFormData, handleChange };\r\n}\r\n\r\nexport default useFormState;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,aAA4C,YAAe;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,eAAe,CACnB;QAIA,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QAE/B,4EAA4E;QAC5E,IACE,EAAE,MAAM,YAAY,oBACpB,CAAC,SAAS,cAAc,SAAS,OAAO,GACxC;YACA,MAAM,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM;YAC5B,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH,OAAO;YACL,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;YAC1B,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,OAAO;QAAE;QAAU;QAAa;IAAa;AAC/C;uCAEe"}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1278, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/utils/time-helpers.ts"], "sourcesContent": ["// Convert date to ISO format\r\nexport const formatDateISO = (date: string) => {\r\n  // Create a date object from the date string\r\n  const dateObj = new Date(date);\r\n\r\n  // Set the time to midnight (00:00:00) to get just the date\r\n  dateObj.setHours(0, 0, 0, 0);\r\n\r\n  // Return the ISO string\r\n  return dateObj.toISOString();\r\n};\r\n\r\n// Convert to long date string\r\nexport function formatToLongDateString(dateString: string) {\r\n  const date = new Date(dateString);\r\n\r\n  return date.toLocaleDateString(\"en-US\", {\r\n    year: \"numeric\",\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n  });\r\n}\r\n\r\n// Convert date to 'yyyy-mm-dd' format in LOCAL TIMEZONE\r\nexport const formatNormalDate = (date: string) => {\r\n  const d = new Date(date);\r\n  const year = d.getFullYear();\r\n  const month = String(d.getMonth() + 1).padStart(2, \"0\"); // Months are 0-based\r\n  const day = String(d.getDate()).padStart(2, \"0\");\r\n  return `${year}-${month}-${day}`;\r\n};\r\n\r\n// Convert ISO time to \"HH:mm\" in LOCAL TIMEZONE\r\nexport function formatTimeForInput(isoTime: string) {\r\n  const date = new Date(isoTime);\r\n  const hours = String(date.getHours()).padStart(2, \"0\"); // Local hours\r\n  const minutes = String(date.getMinutes()).padStart(2, \"0\"); // Local minutes\r\n  return `${hours}:${minutes}`;\r\n}\r\n\r\n// Convert \"HH:mm\" from time input back to ISO format\r\nexport function formatTimeToISO(date: string, time: string) {\r\n  // Create a date object from the date and time strings\r\n  const [hours, minutes] = time.split(':').map(Number);\r\n  const dateObj = new Date(date);\r\n\r\n  // Set the hours and minutes\r\n  dateObj.setHours(hours, minutes, 0, 0);\r\n\r\n  // Return the ISO string\r\n  return dateObj.toISOString();\r\n}\r\n\r\nexport function dateNowWithTZOffset() {\r\n  const now = new Date();\r\n\r\n  const year = now.getFullYear();\r\n  const month = String(now.getMonth() + 1).padStart(2, \"0\");\r\n  const day = String(now.getDate()).padStart(2, \"0\");\r\n  const hours = String(now.getHours()).padStart(2, \"0\");\r\n  const minutes = String(now.getMinutes()).padStart(2, \"0\");\r\n  const seconds = String(now.getSeconds()).padStart(2, \"0\");\r\n\r\n  const microseconds = String(Math.floor(Math.random() * 1000000)).padStart(\r\n    6,\r\n    \"0\"\r\n  );\r\n\r\n  const tzOffset = now.getTimezoneOffset();\r\n  const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60));\r\n  const tzOffsetMinutes = Math.abs(tzOffset % 60);\r\n  const tzOffsetSign = tzOffset <= 0 ? \"+\" : \"-\";\r\n\r\n  const tzFormatted = `${tzOffsetSign}${String(tzOffsetHours).padStart(\r\n    2,\r\n    \"0\"\r\n  )}:${String(tzOffsetMinutes).padStart(2, \"0\")}`;\r\n\r\n  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${microseconds}${tzFormatted}`;\r\n}\r\n"], "names": [], "mappings": "AAAA,6BAA6B;;;;;;;;;AACtB,MAAM,gBAAgB,CAAC;IAC5B,4CAA4C;IAC5C,MAAM,UAAU,IAAI,KAAK;IAEzB,2DAA2D;IAC3D,QAAQ,QAAQ,CAAC,GAAG,GAAG,GAAG;IAE1B,wBAAwB;IACxB,OAAO,QAAQ,WAAW;AAC5B;AAGO,SAAS,uBAAuB,UAAkB;IACvD,MAAM,OAAO,IAAI,KAAK;IAEtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,IAAI,IAAI,KAAK;IACnB,MAAM,OAAO,EAAE,WAAW;IAC1B,MAAM,QAAQ,OAAO,EAAE,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG,MAAM,qBAAqB;IAC9E,MAAM,MAAM,OAAO,EAAE,OAAO,IAAI,QAAQ,CAAC,GAAG;IAC5C,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;AAClC;AAGO,SAAS,mBAAmB,OAAe;IAChD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,QAAQ,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,GAAG,MAAM,cAAc;IACtE,MAAM,UAAU,OAAO,KAAK,UAAU,IAAI,QAAQ,CAAC,GAAG,MAAM,gBAAgB;IAC5E,OAAO,GAAG,MAAM,CAAC,EAAE,SAAS;AAC9B;AAGO,SAAS,gBAAgB,IAAY,EAAE,IAAY;IACxD,sDAAsD;IACtD,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;IAC7C,MAAM,UAAU,IAAI,KAAK;IAEzB,4BAA4B;IAC5B,QAAQ,QAAQ,CAAC,OAAO,SAAS,GAAG;IAEpC,wBAAwB;IACxB,OAAO,QAAQ,WAAW;AAC5B;AAEO,SAAS;IACd,MAAM,MAAM,IAAI;IAEhB,MAAM,OAAO,IAAI,WAAW;IAC5B,MAAM,QAAQ,OAAO,IAAI,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IACrD,MAAM,MAAM,OAAO,IAAI,OAAO,IAAI,QAAQ,CAAC,GAAG;IAC9C,MAAM,QAAQ,OAAO,IAAI,QAAQ,IAAI,QAAQ,CAAC,GAAG;IACjD,MAAM,UAAU,OAAO,IAAI,UAAU,IAAI,QAAQ,CAAC,GAAG;IACrD,MAAM,UAAU,OAAO,IAAI,UAAU,IAAI,QAAQ,CAAC,GAAG;IAErD,MAAM,eAAe,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,QAAQ,CACvE,GACA;IAGF,MAAM,WAAW,IAAI,iBAAiB;IACtC,MAAM,gBAAgB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,WAAW;IACrD,MAAM,kBAAkB,KAAK,GAAG,CAAC,WAAW;IAC5C,MAAM,eAAe,YAAY,IAAI,MAAM;IAE3C,MAAM,cAAc,GAAG,eAAe,OAAO,eAAe,QAAQ,CAClE,GACA,KACA,CAAC,EAAE,OAAO,iBAAiB,QAAQ,CAAC,GAAG,MAAM;IAE/C,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,eAAe,aAAa;AAC/F"}}, {"offset": {"line": 1341, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1347, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1363, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/select.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as SelectPrimitive from \"@radix-ui/react-select\";\r\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Select = SelectPrimitive.Root;\r\n\r\nconst SelectGroup = SelectPrimitive.Group;\r\n\r\nconst SelectValue = SelectPrimitive.Value;\r\n\r\nconst SelectTrigger = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <SelectPrimitive.Icon asChild>\r\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\r\n    </SelectPrimitive.Icon>\r\n  </SelectPrimitive.Trigger>\r\n));\r\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;\r\n\r\nconst SelectScrollUpButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollUpButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronUp className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollUpButton>\r\n));\r\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;\r\n\r\nconst SelectScrollDownButton = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.ScrollDownButton\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default items-center justify-center py-1\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ChevronDown className=\"h-4 w-4\" />\r\n  </SelectPrimitive.ScrollDownButton>\r\n));\r\nSelectScrollDownButton.displayName =\r\n  SelectPrimitive.ScrollDownButton.displayName;\r\n\r\nconst SelectContent = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\r\n>(({ className, children, position = \"popper\", ...props }, ref) => (\r\n  <SelectPrimitive.Portal>\r\n    <SelectPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        position === \"popper\" &&\r\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\r\n        className\r\n      )}\r\n      position={position}\r\n      {...props}\r\n    >\r\n      <SelectScrollUpButton />\r\n      <SelectPrimitive.Viewport\r\n        className={cn(\r\n          \"p-1\",\r\n          position === \"popper\" &&\r\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\r\n        )}\r\n      >\r\n        {children}\r\n      </SelectPrimitive.Viewport>\r\n      <SelectScrollDownButton />\r\n    </SelectPrimitive.Content>\r\n  </SelectPrimitive.Portal>\r\n));\r\nSelectContent.displayName = SelectPrimitive.Content.displayName;\r\n\r\nconst SelectLabel = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\"px-2 py-1.5 text-sm font-semibold\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectLabel.displayName = SelectPrimitive.Label.displayName;\r\n\r\nconst SelectItem = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\r\n>(({ className, children, ...props }, ref) => (\r\n  <SelectPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute right-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <SelectPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </SelectPrimitive.ItemIndicator>\r\n    </span>\r\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n  </SelectPrimitive.Item>\r\n));\r\nSelectItem.displayName = SelectPrimitive.Item.displayName;\r\n\r\nconst SelectSeparator = React.forwardRef<\r\n  React.ElementRef<typeof SelectPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <SelectPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n));\r\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName;\r\n\r\nexport {\r\n  Select,\r\n  SelectGroup,\r\n  SelectValue,\r\n  SelectTrigger,\r\n  SelectContent,\r\n  SelectLabel,\r\n  SelectItem,\r\n  SelectSeparator,\r\n  SelectScrollUpButton,\r\n  SelectScrollDownButton,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAAA;AAAA;AAJA;;;;;;AAQA,MAAM,SAAS,mKAAgB,IAAI;AAEnC,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,cAAc,mKAAgB,KAAK;AAEzC,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,mKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,2TACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,mKAAgB,IAAI;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,qCAAuB,sMAAM,UAAU,CAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,cAAc;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,mKAAgB,cAAc,CAAC,WAAW;AAE7E,MAAM,uCAAyB,sMAAM,UAAU,CAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,gBAAgB;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,mKAAgB,gBAAgB,CAAC,WAAW;AAE9C,MAAM,8BAAgB,sMAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,mKAAgB,MAAM;kBACrB,cAAA,8OAAC,mKAAgB,OAAO;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,mKAAgB,QAAQ;oBACvB,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,mKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,4BAAc,sMAAM,UAAU,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,2BAAa,sMAAM,UAAU,CAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,mKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,mKAAgB,aAAa;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,8OAAC,mKAAgB,QAAQ;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,mKAAgB,IAAI,CAAC,WAAW;AAEzD,MAAM,gCAAkB,sMAAM,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,mKAAgB,SAAS;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,iIAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,mKAAgB,SAAS,CAAC,WAAW"}}, {"offset": {"line": 1549, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/create/PersonalInfo.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\n\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport LoadingSpinner from \"@components/common/loadingSpinner\";\r\nimport { InputField } from \"@components/common/inputField\";\r\nimport { countries } from \"@/lib/countries\";\r\nimport { NavigationButtons } from \"@components/common/navigationBtns\";\r\nimport useFormState from \"@hooks/useFormState\";\r\nimport { toast } from \"react-toastify\";\r\nimport { formatNormalDate, formatDateISO } from \"@/app/utils/time-helpers\";\r\nimport { ProfileCreateProps } from \"@/app/types/counselor/profile\";\r\nimport { useProfile } from \"@/app/hooks/counselor/useProfile\";\r\nimport { Dialog } from \"@headlessui/react\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@components/ui/select\";\r\n\r\nexport default function PersonalInfo({\r\n  selectedMenuIndex = 0, // Default to 0\r\n  onMenuSelect = () => {}, // Default to a no-op function\r\n  onSectionComplete = () => {},\r\n  isCompleted = false,\r\n  edit = false,\r\n}: ProfileCreateProps & {\r\n  onSectionComplete?: () => void;\r\n  isCompleted?: boolean;\r\n}) {\r\n  const { getUserInfo, userInfo } = useProfile();\r\n\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [personalInfoId, setPersonalInfoId] = useState<string | null>(null);\r\n  const [isDirty, setIsDirty] = useState(false);\r\n  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);\r\n\r\n  const { formData, setFormData } = useFormState({\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n    nationality: \"\",\r\n    countryOfResidence: \"\",\r\n    gender: \"\",\r\n    dateOfBirth: \"\",\r\n    bio: \"\",\r\n    tagline: \"\",\r\n  });\r\n\r\n  const [profileImage, setProfileImage] = useState<File | null>(null);\r\n  const [previewUrl, setPreviewUrl] = useState<string>(\"\");\r\n\r\n  const apiURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/counselor/profile/personal-info`;\r\n\r\n  useEffect(() => {\r\n    getUserInfo();\r\n  }, []);\r\n\r\n  // Fetch existing personal info\r\n  useEffect(() => {\r\n    const fetchPersonalInfo = async () => {\r\n      setIsLoading(true);\r\n\r\n      try {\r\n        const response = await apiClient.get(apiURL);\r\n\r\n        if (response.status === 200) {\r\n          const data = response.data;\r\n\r\n          setFormData({\r\n            firstName: formData.firstName || \"\",\r\n            lastName: formData.lastName || \"\",\r\n            nationality: data.nationality || \"\",\r\n            countryOfResidence: data.country_of_residence || \"\",\r\n            gender: data.gender || \"\",\r\n            dateOfBirth: formatNormalDate(data.date_of_birth) || \"\",\r\n            bio: data.bio || \"\",\r\n            tagline: data.tagline || \"\",\r\n          });\r\n          setPersonalInfoId(data.id);\r\n          if (data.profile_image_url) {\r\n            setPreviewUrl(data.profile_image_url);\r\n          }\r\n        }\r\n      } catch (error: any) {\r\n        console.log(error?.response?.data?.detail);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchPersonalInfo();\r\n  }, [setFormData]);\r\n\r\n  // Handle image selection\r\n  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0];\r\n    if (file) {\r\n      setIsDirty(true);\r\n      setProfileImage(file);\r\n      // Create a preview URL\r\n      const url = URL.createObjectURL(file);\r\n      setPreviewUrl(url);\r\n    }\r\n  };\r\n\r\n  // Update handleChange to track form changes\r\n  const handleFormChange = (name: string, value: string) => {\r\n    setIsDirty(true);\r\n    setFormData((prev) => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n  // Validation function\r\n  const validateForm = () => {\r\n    if (\r\n      !formData.nationality ||\r\n      !formData.countryOfResidence ||\r\n      !formData.gender ||\r\n      !formData.dateOfBirth\r\n    ) {\r\n      toast.error(\"All fields are required.\");\r\n      return false;\r\n    }\r\n\r\n    // Check if the date of birth is greater than today\r\n    const enteredDate = new Date(formData.dateOfBirth);\r\n    const today = new Date();\r\n\r\n    if (enteredDate > today) {\r\n      toast.error(\"Date of birth cannot be in the future.\");\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    setHasAttemptedSubmit(true);\r\n\r\n    if (!validateForm()) return;\r\n\r\n    // If form hasn't changed and section is completed, just navigate\r\n    if (!isDirty && isCompleted && !edit) {\r\n      onMenuSelect(selectedMenuIndex + 1);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      // Create FormData object\r\n      const formDataToSend = new FormData();\r\n      formDataToSend.append(\"nationality\", formData.nationality);\r\n      formDataToSend.append(\r\n        \"country_of_residence\",\r\n        formData.countryOfResidence\r\n      );\r\n      formDataToSend.append(\"gender\", formData.gender);\r\n      formDataToSend.append(\r\n        \"date_of_birth\",\r\n        formatDateISO(formData.dateOfBirth)\r\n      );\r\n      if (formData.bio) formDataToSend.append(\"bio\", formData.bio);\r\n      if (formData.tagline) formDataToSend.append(\"tagline\", formData.tagline);\r\n      if (profileImage) formDataToSend.append(\"profile_image\", profileImage);\r\n\r\n      let response;\r\n      if (personalInfoId) {\r\n        // Update existing record\r\n        response = await apiClient.put(\r\n          `${apiURL}/${personalInfoId}`,\r\n          formDataToSend,\r\n          {\r\n            headers: {\r\n              \"Content-Type\": \"multipart/form-data\",\r\n            },\r\n          }\r\n        );\r\n      } else {\r\n        // Create new record\r\n        response = await apiClient.post(apiURL, formDataToSend, {\r\n          headers: {\r\n            \"Content-Type\": \"multipart/form-data\",\r\n          },\r\n        });\r\n      }\r\n\r\n      if (response.status === 200 || response.status === 201) {\r\n        toast.success(\"Personal information saved successfully!\");\r\n        setIsDirty(false);\r\n        if (!edit) {\r\n          onSectionComplete();\r\n          onMenuSelect(selectedMenuIndex + 1);\r\n        }\r\n      }\r\n    } catch (error: any) {\r\n      console.error(\"Error:\", error);\r\n      toast.error(\r\n        error?.response?.data?.detail ||\r\n          \"An error occurred while saving personal information.\"\r\n      );\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const [isModalOpen, setIsModalOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState(\"\");\r\n  const [selectedField, setSelectedField] = useState<\r\n    \"nationality\" | \"countryOfResidence\" | null\r\n  >(null);\r\n\r\n  const handleOpenModal = (field: \"nationality\" | \"countryOfResidence\") => {\r\n    setSelectedField(field);\r\n    setSearchTerm(\"\");\r\n    setIsModalOpen(true);\r\n  };\r\n\r\n  const handleCountrySelect = (code: string) => {\r\n    if (selectedField) {\r\n      handleFormChange(selectedField, code);\r\n      setIsModalOpen(false);\r\n    }\r\n  };\r\n\r\n  const getFieldLabel = (field: string) => {\r\n    const country = countries.find((c) => c.code === field);\r\n    return country ? country.name : \"Please select\";\r\n  };\r\n\r\n  const filteredCountries = countries.filter((country) =>\r\n    country.name.toLowerCase().includes(searchTerm.toLowerCase())\r\n  );\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {isLoading ? (\r\n        <LoadingSpinner />\r\n      ) : (\r\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n          {/* Profile Image Upload */}\r\n          <div className=\"flex flex-col items-center space-y-4 p-6 bg-white rounded-lg shadow-sm\">\r\n            <h3 className=\"text-lg font-semibold text-gray-900\">\r\n              Profile Picture\r\n            </h3>\r\n\r\n            <div className=\"relative\">\r\n              {previewUrl ? (\r\n                <div className=\"group relative w-32 h-32 rounded-full overflow-hidden border-4 border-blue-100\">\r\n                  <img\r\n                    src={previewUrl}\r\n                    alt=\"Profile preview\"\r\n                    className=\"w-full h-full object-cover\"\r\n                  />\r\n                  <div className=\"absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300\">\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={async () => {\r\n                        if (personalInfoId) {\r\n                          setIsLoading(true);\r\n                          try {\r\n                            const formDataToSend = new FormData();\r\n                            formDataToSend.append(\r\n                              \"nationality\",\r\n                              formData.nationality\r\n                            );\r\n                            formDataToSend.append(\r\n                              \"country_of_residence\",\r\n                              formData.countryOfResidence\r\n                            );\r\n                            formDataToSend.append(\"gender\", formData.gender);\r\n                            formDataToSend.append(\r\n                              \"date_of_birth\",\r\n                              formatDateISO(formData.dateOfBirth)\r\n                            );\r\n                            if (formData.bio)\r\n                              formDataToSend.append(\"bio\", formData.bio);\r\n                            if (formData.tagline)\r\n                              formDataToSend.append(\r\n                                \"tagline\",\r\n                                formData.tagline\r\n                              );\r\n                            formDataToSend.append(\"delete_image\", \"true\");\r\n\r\n                            const response = await apiClient.put(\r\n                              `${apiURL}/${personalInfoId}`,\r\n                              formDataToSend,\r\n                              {\r\n                                headers: {\r\n                                  \"Content-Type\": \"multipart/form-data\",\r\n                                },\r\n                              }\r\n                            );\r\n\r\n                            if (response.status === 200) {\r\n                              setPreviewUrl(\"\");\r\n                              setProfileImage(null);\r\n                              setIsDirty(true);\r\n                              toast.success(\r\n                                \"Profile picture removed successfully!\"\r\n                              );\r\n                            }\r\n                          } catch (error: any) {\r\n                            toast.error(\r\n                              error?.response?.data?.detail ||\r\n                                \"Failed to remove profile picture\"\r\n                            );\r\n                          } finally {\r\n                            setIsLoading(false);\r\n                          }\r\n                        } else {\r\n                          setPreviewUrl(\"\");\r\n                          setProfileImage(null);\r\n                          setIsDirty(true);\r\n                        }\r\n                      }}\r\n                      className=\"opacity-0 group-hover:opacity-100 text-white hover:scale-110 transition-all duration-300\"\r\n                    >\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        className=\"h-8 w-8\"\r\n                        fill=\"none\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        stroke=\"currentColor\"\r\n                      >\r\n                        <path\r\n                          strokeLinecap=\"round\"\r\n                          strokeLinejoin=\"round\"\r\n                          strokeWidth={2}\r\n                          d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\r\n                        />\r\n                      </svg>\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              ) : (\r\n                <div className=\"w-32 h-32 rounded-full border-4 border-dashed border-gray-200 flex items-center justify-center bg-gray-50\">\r\n                  <svg\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    className=\"h-12 w-12 text-gray-400\"\r\n                    fill=\"none\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    stroke=\"currentColor\"\r\n                  >\r\n                    <path\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                      strokeWidth={2}\r\n                      d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\r\n                    />\r\n                  </svg>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <div className=\"flex flex-col items-center space-y-2\">\r\n              <label\r\n                htmlFor=\"profile-image\"\r\n                className=\"px-4 py-2 bg-blue-50 text-blue-700 rounded-full cursor-pointer hover:bg-blue-100 transition-colors font-medium text-sm\"\r\n              >\r\n                {previewUrl ? \"Change Photo\" : \"Upload Photo\"}\r\n                <input\r\n                  id=\"profile-image\"\r\n                  type=\"file\"\r\n                  accept=\"image/*\"\r\n                  onChange={handleImageChange}\r\n                  className=\"hidden\"\r\n                />\r\n              </label>\r\n              <p className=\"text-sm text-gray-500\">JPG or PNG</p>\r\n            </div>\r\n          </div>\r\n\r\n          <h3 className=\"font-black mb-4\">Personal Information</h3>\r\n\r\n          {/* First Name and Last Name Fields */}\r\n          <div className=\"grid lg:grid-cols-2 gap-4 mt-4\">\r\n            <InputField\r\n              label=\"First Name\"\r\n              type=\"text\"\r\n              placeholder=\"First Name\"\r\n              name=\"firstName\"\r\n              value={formData.firstName || userInfo?.firstName}\r\n              onChange={(e) => handleFormChange(e.target.name, e.target.value)}\r\n              disabled\r\n              style={{ backgroundColor: \"#d5d5d540\" }}\r\n              required={true}\r\n            />\r\n            <InputField\r\n              label=\"Last Name\"\r\n              type=\"text\"\r\n              placeholder=\"Last Name\"\r\n              name=\"lastName\"\r\n              value={formData.lastName || userInfo?.lastName}\r\n              onChange={(e) => handleFormChange(e.target.name, e.target.value)}\r\n              disabled\r\n              style={{ backgroundColor: \"#d5d5d540\" }}\r\n              required={true}\r\n            />\r\n          </div>\r\n\r\n          {/* Nationality and Country of Residence */}\r\n          <div className=\"grid lg:grid-cols-2 gap-4 mt-4\">\r\n            <div>\r\n              <label className=\"block mb-2 text-sm font-medium text-gray-900\">\r\n                Nationality<span className=\"text-red-500\">*</span>\r\n              </label>\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => handleOpenModal(\"nationality\")}\r\n                className=\"w-full p-3 text-left border rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white\"\r\n              >\r\n                {getFieldLabel(formData.nationality)}\r\n              </button>\r\n              {hasAttemptedSubmit && !formData.nationality && (\r\n                <p className=\"mt-1 text-sm text-red-600\">\r\n                  Nationality is required\r\n                </p>\r\n              )}\r\n            </div>\r\n\r\n            <div>\r\n              <label className=\"block mb-2 text-sm font-medium text-gray-900\">\r\n                Country of Residence<span className=\"text-red-500\">*</span>\r\n              </label>\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => handleOpenModal(\"countryOfResidence\")}\r\n                className=\"w-full p-3 text-left border rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white\"\r\n              >\r\n                {getFieldLabel(formData.countryOfResidence)}\r\n              </button>\r\n              {hasAttemptedSubmit && !formData.countryOfResidence && (\r\n                <p className=\"mt-1 text-sm text-red-600\">\r\n                  Country of residence is required\r\n                </p>\r\n              )}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Gender and Date of Birth */}\r\n          <div className=\"grid lg:grid-cols-2 gap-4 mt-4\">\r\n            <div>\r\n              <label className=\"block mb-2 text-sm font-medium text-gray-900\">\r\n                Gender<span className=\"text-red-500\">*</span>\r\n              </label>\r\n              <Select\r\n                onValueChange={(value) => handleFormChange(\"gender\", value)}\r\n                value={formData.gender}\r\n              >\r\n                <SelectTrigger\r\n                  className={`h-[42px] shadow-none bg-white ${\r\n                    hasAttemptedSubmit && !formData.gender\r\n                      ? \"border-red-500\"\r\n                      : \"\"\r\n                  }`}\r\n                >\r\n                  <SelectValue placeholder=\"Select gender\" />\r\n                </SelectTrigger>\r\n                <SelectContent className=\"bg-white\">\r\n                  <SelectItem\r\n                    className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                    value=\"male\"\r\n                  >\r\n                    Male\r\n                  </SelectItem>\r\n                  <SelectItem\r\n                    className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                    value=\"female\"\r\n                  >\r\n                    Female\r\n                  </SelectItem>\r\n                  <SelectItem\r\n                    className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                    value=\"other\"\r\n                  >\r\n                    Other\r\n                  </SelectItem>\r\n                </SelectContent>\r\n              </Select>\r\n              {hasAttemptedSubmit && !formData.gender && (\r\n                <p className=\"mt-1 text-sm text-red-500\">Gender is required</p>\r\n              )}\r\n            </div>\r\n            <div>\r\n              <label className=\"block mb-2 text-sm font-medium text-gray-900\">\r\n                Date of Birth<span className=\"text-red-500\">*</span>\r\n              </label>\r\n              <input\r\n                type=\"date\"\r\n                name=\"dateOfBirth\"\r\n                value={formData.dateOfBirth}\r\n                onChange={(e) =>\r\n                  handleFormChange(e.target.name, e.target.value)\r\n                }\r\n                required={true}\r\n                className=\"w-full h-[42px] p-3 border rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white\"\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Tagline */}\r\n          <div className=\"mt-4\">\r\n            <label className=\"block mb-2 text-sm font-medium text-gray-900\">\r\n              Tagline (Optional)\r\n            </label>\r\n            <textarea\r\n              name=\"tagline\"\r\n              value={formData.tagline}\r\n              onChange={(e) => handleFormChange(e.target.name, e.target.value)}\r\n              className=\"w-full p-3 border rounded-lg focus:ring-blue-500 focus:border-blue-500\"\r\n              rows={4}\r\n              placeholder=\"Your tagline\"\r\n              maxLength={100}\r\n            />\r\n            <p className=\"mt-1 text-sm text-gray-500\">\r\n              {formData.tagline?.length || 0}/100 characters\r\n            </p>\r\n          </div>\r\n          {/* Bio */}\r\n          <div className=\"mt-4\">\r\n            <label className=\"block mb-2 text-sm font-medium text-gray-900\">\r\n              Bio (Optional)\r\n            </label>\r\n            <textarea\r\n              name=\"bio\"\r\n              value={formData.bio}\r\n              onChange={(e) => handleFormChange(e.target.name, e.target.value)}\r\n              className=\"w-full p-3 border rounded-lg focus:ring-blue-500 focus:border-blue-500\"\r\n              rows={4}\r\n              placeholder=\"Tell us about yourself...\"\r\n              maxLength={1000}\r\n            />\r\n            <p className=\"mt-1 text-sm text-gray-500\">\r\n              {formData.bio?.length || 0}/1000 characters\r\n            </p>\r\n          </div>\r\n\r\n          <NavigationButtons\r\n            currentIndex={selectedMenuIndex || 0} // Fallback to 0 if undefined\r\n            onBack={() =>\r\n              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex - 1 : 0)\r\n            }\r\n            onNext={() =>\r\n              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex + 1 : 1)\r\n            }\r\n            disableBack={!selectedMenuIndex || selectedMenuIndex < 1}\r\n            nextLabel={edit ? \"Save Changes\" : \"Next\"}\r\n          />\r\n        </form>\r\n      )}\r\n\r\n      {/* Country Selection Modal */}\r\n      <Dialog\r\n        open={isModalOpen}\r\n        onClose={() => setIsModalOpen(false)}\r\n        className=\"relative z-50\"\r\n      >\r\n        <div className=\"fixed inset-0 bg-black/30\" aria-hidden=\"true\" />\r\n        <div className=\"fixed inset-0 flex items-center justify-center p-4\">\r\n          <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n            <Dialog.Title className=\"text-lg font-medium leading-6 text-gray-900 mb-4\">\r\n              Select{\" \"}\r\n              {selectedField === \"nationality\"\r\n                ? \"Nationality\"\r\n                : \"Country of Residence\"}\r\n            </Dialog.Title>\r\n\r\n            {/* Search Input */}\r\n            <div className=\"mb-4\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search countries...\"\r\n                value={searchTerm}\r\n                onChange={(e) => setSearchTerm(e.target.value)}\r\n                autoFocus={true}\r\n                className=\"w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500\"\r\n              />\r\n            </div>\r\n\r\n            {/* Countries List */}\r\n            <div className=\"mt-2 max-h-[60vh] overflow-y-auto\">\r\n              {filteredCountries.map((country) => (\r\n                <button\r\n                  key={country.code}\r\n                  type=\"button\"\r\n                  className=\"w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 rounded-lg\"\r\n                  onClick={() => handleCountrySelect(country.code)}\r\n                >\r\n                  {country.name}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </Dialog.Panel>\r\n        </div>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AADA;AAdA;;;;;;;;;;;;;;AAuBe,SAAS,aAAa,EACnC,oBAAoB,CAAC,EACrB,eAAe,KAAO,CAAC,EACvB,oBAAoB,KAAO,CAAC,EAC5B,cAAc,KAAK,EACnB,OAAO,KAAK,EAIb;IACC,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,aAAU,AAAD;IAE3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD,EAAE;QAC7C,WAAW;QACX,UAAU;QACV,aAAa;QACb,oBAAoB;QACpB,QAAQ;QACR,aAAa;QACb,KAAK;QACL,SAAS;IACX;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,MAAM,SAAS,6DAAwC,gCAAgC,CAAC;IAExF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,aAAa;YAEb,IAAI;gBACF,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBAErC,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC3B,MAAM,OAAO,SAAS,IAAI;oBAE1B,YAAY;wBACV,WAAW,SAAS,SAAS,IAAI;wBACjC,UAAU,SAAS,QAAQ,IAAI;wBAC/B,aAAa,KAAK,WAAW,IAAI;wBACjC,oBAAoB,KAAK,oBAAoB,IAAI;wBACjD,QAAQ,KAAK,MAAM,IAAI;wBACvB,aAAa,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,aAAa,KAAK;wBACrD,KAAK,KAAK,GAAG,IAAI;wBACjB,SAAS,KAAK,OAAO,IAAI;oBAC3B;oBACA,kBAAkB,KAAK,EAAE;oBACzB,IAAI,KAAK,iBAAiB,EAAE;wBAC1B,cAAc,KAAK,iBAAiB;oBACtC;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,GAAG,CAAC,OAAO,UAAU,MAAM;YACrC,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;KAAY;IAEhB,yBAAyB;IACzB,MAAM,oBAAoB,CAAC;QACzB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,WAAW;YACX,gBAAgB;YAChB,uBAAuB;YACvB,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,cAAc;QAChB;IACF;IAEA,4CAA4C;IAC5C,MAAM,mBAAmB,CAAC,MAAc;QACtC,WAAW;QACX,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACnD;IAEA,sBAAsB;IACtB,MAAM,eAAe;QACnB,IACE,CAAC,SAAS,WAAW,IACrB,CAAC,SAAS,kBAAkB,IAC5B,CAAC,SAAS,MAAM,IAChB,CAAC,SAAS,WAAW,EACrB;YACA,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;QAEA,mDAAmD;QACnD,MAAM,cAAc,IAAI,KAAK,SAAS,WAAW;QACjD,MAAM,QAAQ,IAAI;QAElB,IAAI,cAAc,OAAO;YACvB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,sBAAsB;QAEtB,IAAI,CAAC,gBAAgB;QAErB,iEAAiE;QACjE,IAAI,CAAC,WAAW,eAAe,CAAC,MAAM;YACpC,aAAa,oBAAoB;YACjC;QACF;QAEA,aAAa;QAEb,IAAI;YACF,yBAAyB;YACzB,MAAM,iBAAiB,IAAI;YAC3B,eAAe,MAAM,CAAC,eAAe,SAAS,WAAW;YACzD,eAAe,MAAM,CACnB,wBACA,SAAS,kBAAkB;YAE7B,eAAe,MAAM,CAAC,UAAU,SAAS,MAAM;YAC/C,eAAe,MAAM,CACnB,iBACA,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,WAAW;YAEpC,IAAI,SAAS,GAAG,EAAE,eAAe,MAAM,CAAC,OAAO,SAAS,GAAG;YAC3D,IAAI,SAAS,OAAO,EAAE,eAAe,MAAM,CAAC,WAAW,SAAS,OAAO;YACvE,IAAI,cAAc,eAAe,MAAM,CAAC,iBAAiB;YAEzD,IAAI;YACJ,IAAI,gBAAgB;gBAClB,yBAAyB;gBACzB,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAC5B,GAAG,OAAO,CAAC,EAAE,gBAAgB,EAC7B,gBACA;oBACE,SAAS;wBACP,gBAAgB;oBAClB;gBACF;YAEJ,OAAO;gBACL,oBAAoB;gBACpB,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,QAAQ,gBAAgB;oBACtD,SAAS;wBACP,gBAAgB;oBAClB;gBACF;YACF;YAEA,IAAI,SAAS,MAAM,KAAK,OAAO,SAAS,MAAM,KAAK,KAAK;gBACtD,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,WAAW;gBACX,IAAI,CAAC,MAAM;oBACT;oBACA,aAAa,oBAAoB;gBACnC;YACF;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,UAAU;YACxB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,OAAO,UAAU,MAAM,UACrB;QAEN,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAE/C;IAEF,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QACjB,cAAc;QACd,eAAe;IACjB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,eAAe;YACjB,iBAAiB,eAAe;YAChC,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,UAAU,gHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK;QACjD,OAAO,UAAU,QAAQ,IAAI,GAAG;IAClC;IAEA,MAAM,oBAAoB,gHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,UAC1C,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG5D,qBACE,8OAAC;QAAI,WAAU;;YACZ,0BACC,8OAAC,uJAAA,CAAA,UAAc;;;;qCAEf,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;0CACZ,2BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,KAAK;4CACL,KAAI;4CACJ,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAK;gDACL,SAAS;oDACP,IAAI,gBAAgB;wDAClB,aAAa;wDACb,IAAI;4DACF,MAAM,iBAAiB,IAAI;4DAC3B,eAAe,MAAM,CACnB,eACA,SAAS,WAAW;4DAEtB,eAAe,MAAM,CACnB,wBACA,SAAS,kBAAkB;4DAE7B,eAAe,MAAM,CAAC,UAAU,SAAS,MAAM;4DAC/C,eAAe,MAAM,CACnB,iBACA,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,WAAW;4DAEpC,IAAI,SAAS,GAAG,EACd,eAAe,MAAM,CAAC,OAAO,SAAS,GAAG;4DAC3C,IAAI,SAAS,OAAO,EAClB,eAAe,MAAM,CACnB,WACA,SAAS,OAAO;4DAEpB,eAAe,MAAM,CAAC,gBAAgB;4DAEtC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,GAAG,OAAO,CAAC,EAAE,gBAAgB,EAC7B,gBACA;gEACE,SAAS;oEACP,gBAAgB;gEAClB;4DACF;4DAGF,IAAI,SAAS,MAAM,KAAK,KAAK;gEAC3B,cAAc;gEACd,gBAAgB;gEAChB,WAAW;gEACX,mJAAA,CAAA,QAAK,CAAC,OAAO,CACX;4DAEJ;wDACF,EAAE,OAAO,OAAY;4DACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,OAAO,UAAU,MAAM,UACrB;wDAEN,SAAU;4DACR,aAAa;wDACf;oDACF,OAAO;wDACL,cAAc;wDACd,gBAAgB;wDAChB,WAAW;oDACb;gDACF;gDACA,WAAU;0DAEV,cAAA,8OAAC;oDACC,OAAM;oDACN,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,QAAO;8DAEP,cAAA,8OAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;yDAOZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAM;wCACN,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,QAAO;kDAEP,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;0CAOZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAQ;wCACR,WAAU;;4CAET,aAAa,iBAAiB;0DAC/B,8OAAC;gDACC,IAAG;gDACH,MAAK;gDACL,QAAO;gDACP,UAAU;gDACV,WAAU;;;;;;;;;;;;kDAGd,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAIzC,8OAAC;wBAAG,WAAU;kCAAkB;;;;;;kCAGhC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mJAAA,CAAA,aAAU;gCACT,OAAM;gCACN,MAAK;gCACL,aAAY;gCACZ,MAAK;gCACL,OAAO,SAAS,SAAS,IAAI,UAAU;gCACvC,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK;gCAC/D,QAAQ;gCACR,OAAO;oCAAE,iBAAiB;gCAAY;gCACtC,UAAU;;;;;;0CAEZ,8OAAC,mJAAA,CAAA,aAAU;gCACT,OAAM;gCACN,MAAK;gCACL,aAAY;gCACZ,MAAK;gCACL,OAAO,SAAS,QAAQ,IAAI,UAAU;gCACtC,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK;gCAC/D,QAAQ;gCACR,OAAO;oCAAE,iBAAiB;gCAAY;gCACtC,UAAU;;;;;;;;;;;;kCAKd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;;4CAA+C;0DACnD,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAE5C,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDAET,cAAc,SAAS,WAAW;;;;;;oCAEpC,sBAAsB,CAAC,SAAS,WAAW,kBAC1C,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;;;;;;;0CAM7C,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;;4CAA+C;0DAC1C,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAErD,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,gBAAgB;wCAC/B,WAAU;kDAET,cAAc,SAAS,kBAAkB;;;;;;oCAE3C,sBAAsB,CAAC,SAAS,kBAAkB,kBACjD,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;;;;;;;;;;;;;kCAQ/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;;4CAA+C;0DACxD,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAEvC,8OAAC,kIAAA,CAAA,SAAM;wCACL,eAAe,CAAC,QAAU,iBAAiB,UAAU;wCACrD,OAAO,SAAS,MAAM;;0DAEtB,8OAAC,kIAAA,CAAA,gBAAa;gDACZ,WAAW,CAAC,8BAA8B,EACxC,sBAAsB,CAAC,SAAS,MAAM,GAClC,mBACA,IACJ;0DAEF,cAAA,8OAAC,kIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;;kEACvB,8OAAC,kIAAA,CAAA,aAAU;wDACT,WAAU;wDACV,OAAM;kEACP;;;;;;kEAGD,8OAAC,kIAAA,CAAA,aAAU;wDACT,WAAU;wDACV,OAAM;kEACP;;;;;;kEAGD,8OAAC,kIAAA,CAAA,aAAU;wDACT,WAAU;wDACV,OAAM;kEACP;;;;;;;;;;;;;;;;;;oCAKJ,sBAAsB,CAAC,SAAS,MAAM,kBACrC,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;;;;;;;0CAG7C,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;;4CAA+C;0DACjD,8OAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAE9C,8OAAC;wCACC,MAAK;wCACL,MAAK;wCACL,OAAO,SAAS,WAAW;wCAC3B,UAAU,CAAC,IACT,iBAAiB,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK;wCAEhD,UAAU;wCACV,WAAU;;;;;;;;;;;;;;;;;;kCAMhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,OAAO;gCACvB,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK;gCAC/D,WAAU;gCACV,MAAM;gCACN,aAAY;gCACZ,WAAW;;;;;;0CAEb,8OAAC;gCAAE,WAAU;;oCACV,SAAS,OAAO,EAAE,UAAU;oCAAE;;;;;;;;;;;;;kCAInC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,8OAAC;gCACC,MAAK;gCACL,OAAO,SAAS,GAAG;gCACnB,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,MAAM,CAAC,KAAK;gCAC/D,WAAU;gCACV,MAAM;gCACN,aAAY;gCACZ,WAAW;;;;;;0CAEb,8OAAC;gCAAE,WAAU;;oCACV,SAAS,GAAG,EAAE,UAAU;oCAAE;;;;;;;;;;;;;kCAI/B,8OAAC,uJAAA,CAAA,oBAAiB;wBAChB,cAAc,qBAAqB;wBACnC,QAAQ,IACN,eAAe,oBAAoB,oBAAoB,IAAI;wBAE7D,QAAQ,IACN,eAAe,oBAAoB,oBAAoB,IAAI;wBAE7D,aAAa,CAAC,qBAAqB,oBAAoB;wBACvD,WAAW,OAAO,iBAAiB;;;;;;;;;;;;0BAMzC,8OAAC,+KAAA,CAAA,SAAM;gBACL,MAAM;gBACN,SAAS,IAAM,eAAe;gBAC9B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;wBAA4B,eAAY;;;;;;kCACvD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;4BAAC,WAAU;;8CACtB,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;;wCAAmD;wCAClE;wCACN,kBAAkB,gBACf,gBACA;;;;;;;8CAIN,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAW;wCACX,WAAU;;;;;;;;;;;8CAKd,8OAAC;oCAAI,WAAU;8CACZ,kBAAkB,GAAG,CAAC,CAAC,wBACtB,8OAAC;4CAEC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,oBAAoB,QAAQ,IAAI;sDAE9C,QAAQ,IAAI;2CALR,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAcnC"}}, {"offset": {"line": 2411, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2417, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/TeaxAreaLimit/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\ninterface TextAreaLimitProps {\r\n  label: string;\r\n  name: string;\r\n  placeholder: string;\r\n  value: string | undefined;\r\n  onChange: (value: string) => void;\r\n  rows?: number;\r\n  charLimit?: number; // Default to 400 if not provided\r\n  disabled?: boolean;\r\n  maxLength?: number;\r\n  required?: boolean;\r\n}\r\n\r\nconst TextAreaLimit: React.FC<TextAreaLimitProps> = ({\r\n  label,\r\n  name,\r\n  placeholder,\r\n  value,\r\n  onChange,\r\n  rows = 4,\r\n  charLimit = 400,\r\n  disabled,\r\n  maxLength = 0,\r\n  required = false,\r\n}) => {\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\r\n    const inputValue = e.target.value;\r\n\r\n    if (inputValue.length <= charLimit) {\r\n      onChange(inputValue);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div>\r\n      <label className=\"font-medium mb-2 text-sm flex justify-between\">\r\n        {label}{\" \"}\r\n        <span>\r\n          {value?.length}/{charLimit}\r\n        </span>\r\n      </label>\r\n      <textarea\r\n        name={name}\r\n        placeholder={placeholder}\r\n        value={value}\r\n        onChange={handleInputChange}\r\n        className=\"w-full border border-gray-300 rounded-md py-2 px-3\"\r\n        rows={rows}\r\n        disabled={disabled}\r\n      ></textarea>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TextAreaLimit;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAeA,MAAM,gBAA8C,CAAC,EACnD,KAAK,EACL,IAAI,EACJ,WAAW,EACX,KAAK,EACL,QAAQ,EACR,OAAO,CAAC,EACR,YAAY,GAAG,EACf,QAAQ,EACR,YAAY,CAAC,EACb,WAAW,KAAK,EACjB;IACC,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa,EAAE,MAAM,CAAC,KAAK;QAEjC,IAAI,WAAW,MAAM,IAAI,WAAW;YAClC,SAAS;QACX;IACF;IAEA,qBACE,8OAAC;;0BACC,8OAAC;gBAAM,WAAU;;oBACd;oBAAO;kCACR,8OAAC;;4BACE,OAAO;4BAAO;4BAAE;;;;;;;;;;;;;0BAGrB,8OAAC;gBACC,MAAM;gBACN,aAAa;gBACb,OAAO;gBACP,UAAU;gBACV,WAAU;gBACV,MAAM;gBACN,UAAU;;;;;;;;;;;;AAIlB;uCAEe"}}, {"offset": {"line": 2475, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2481, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/create/EducationalBackground.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect, useMemo } from \"react\";\r\nimport { Dialog } from \"@headlessui/react\";\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from \"@components/ui/select\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { InputField } from \"@components/common/inputField\";\r\nimport { NavigationButtons } from \"@components/common/navigationBtns\";\r\nimport { toast } from \"react-toastify\";\r\nimport { formatNormalDate, formatDateISO } from \"@/app/utils/time-helpers\";\r\nimport LoadingSpinner from \"@components/common/loadingSpinner\";\r\nimport TextAreaLimit from \"@components/common/TeaxAreaLimit\";\r\nimport { ProfileCreateProps } from \"@/app/types/counselor/profile\";\r\nimport { MainButton } from \"@/app/components/common/mainBtn\";\r\nimport Dropdown from \"@components/common/dropdown\";\r\n\r\ninterface EducationEntry {\r\n  id?: string;\r\n  university_name: string;\r\n  degree: string;\r\n  major: string;\r\n  start_date: string;\r\n  end_date: string;\r\n  grade: string;\r\n  accomplishments: string;\r\n  education_level: string;\r\n  specific_degree: string;\r\n}\r\n\r\ninterface University {\r\n  name: string;\r\n  country: string;\r\n}\r\n\r\nconst searchUniversities = async (query: string): Promise<University[]> => {\r\n  if (!query || query.length < 2) return [];\r\n\r\n  try {\r\n    const response = await fetch(\r\n      `http://universities.hipolabs.com/search?name=${encodeURIComponent(\r\n        query\r\n      )}`\r\n    );\r\n    const data = await response.json();\r\n    return data.map((uni: any) => ({\r\n      name: uni.name,\r\n      country: uni.country,\r\n    }));\r\n  } catch (error) {\r\n    console.error(\"Error fetching universities:\", error);\r\n    return [];\r\n  }\r\n};\r\n\r\nconst initialEntry: EducationEntry = {\r\n  university_name: \"\",\r\n  degree: \"\",\r\n  major: \"\",\r\n  start_date: \"\",\r\n  end_date: \"\",\r\n  grade: \"\",\r\n  accomplishments: \"\",\r\n  education_level: \"\",\r\n  specific_degree: \"\",\r\n};\r\n\r\ntype EducationLevelOption = {\r\n  value: string;\r\n  label: string;\r\n};\r\n\r\nexport default function EducationalInfo({\r\n  selectedMenuIndex = 0,\r\n  onMenuSelect = () => {},\r\n  onSectionComplete = () => {},\r\n  isCompleted = false,\r\n  edit = false,\r\n}: ProfileCreateProps & {\r\n  onSectionComplete?: () => void;\r\n  isCompleted?: boolean;\r\n}) {\r\n  const [educationEntries, setEducationEntries] = useState<EducationEntry[]>([\r\n    initialEntry,\r\n  ]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isDirty, setIsDirty] = useState(false);\r\n\r\n  const apiURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/counselor/profile/education`;\r\n\r\n  const educationLevels: EducationLevelOption[] = [\r\n    { value: \"\", label: \"Select Education Level\" },\r\n    { value: \"high_school\", label: \"High School\" },\r\n    { value: \"associate\", label: \"Associate Degree\" },\r\n    { value: \"bachelors\", label: \"Bachelor's Degree\" },\r\n    { value: \"masters\", label: \"Master's Degree\" },\r\n    { value: \"doctorate\", label: \"Doctorate/PhD\" },\r\n    { value: \"certification\", label: \"Professional Certification\" },\r\n    { value: \"other\", label: \"Other\" },\r\n  ];\r\n\r\n  useEffect(() => {\r\n    const fetchEducationData = async () => {\r\n      setIsLoading(true);\r\n\r\n      try {\r\n        const response = await apiClient.get(apiURL);\r\n\r\n        if (response.status === 200 && response.data) {\r\n          const formattedData = response.data.map((entry: EducationEntry) => ({\r\n            ...entry,\r\n            start_date: formatNormalDate(entry.start_date),\r\n            end_date: formatNormalDate(entry.end_date),\r\n            education_level: entry.degree,\r\n            specific_degree: !educationLevels.find(\r\n              (level) => level.value === entry.degree\r\n            )\r\n              ? entry.degree\r\n              : \"\",\r\n          }));\r\n          setEducationEntries(\r\n            formattedData.length > 0 ? formattedData : [initialEntry]\r\n          );\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching education data:\", error);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n    fetchEducationData();\r\n  }, []);\r\n\r\n  const handleFieldChange = (\r\n    index: number,\r\n    fieldName: keyof EducationEntry,\r\n    value: string\r\n  ) => {\r\n    setIsDirty(true);\r\n    setEducationEntries((prevEntries) => {\r\n      return prevEntries.map((entry, i) => {\r\n        if (i !== index) return entry;\r\n\r\n        const updatedEntry = { ...entry };\r\n\r\n        if (fieldName === \"education_level\") {\r\n          updatedEntry.education_level = value;\r\n          updatedEntry.degree =\r\n            value === \"other\" ? entry.specific_degree : value;\r\n          updatedEntry.specific_degree =\r\n            value === \"other\" ? entry.specific_degree : \"\";\r\n        } else if (fieldName === \"specific_degree\") {\r\n          updatedEntry.specific_degree = value;\r\n          updatedEntry.degree =\r\n            entry.education_level === \"other\" ? value : entry.degree;\r\n        } else {\r\n          updatedEntry[fieldName] = value;\r\n        }\r\n\r\n        return updatedEntry;\r\n      });\r\n    });\r\n  };\r\n\r\n  const handleAddMoreEducation = () => {\r\n    setIsDirty(true);\r\n    const allValid = educationEntries.every((entry) => validateEntry(entry));\r\n\r\n    if (allValid) {\r\n      setEducationEntries((prevEntries) => [\r\n        ...prevEntries,\r\n        { ...initialEntry },\r\n      ]);\r\n    } else {\r\n      toast.error(\r\n        \"Please fill in all the required fields in previous entries before adding more.\"\r\n      );\r\n    }\r\n  };\r\n\r\n  const handleRemoveEducation = async (index: number) => {\r\n    setIsDirty(true);\r\n    const experienceToRemove = educationEntries[index];\r\n\r\n    if (!experienceToRemove.id) {\r\n      setEducationEntries((prevEntries) =>\r\n        prevEntries.filter((_, i) => i !== index)\r\n      );\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const deleteUrl = `${apiURL}/${experienceToRemove.id}`;\r\n      const response = await apiClient.delete(deleteUrl);\r\n\r\n      if (response.status === 200) {\r\n        toast.success(\"Education removed successfully.\");\r\n        setEducationEntries((prevEntries) =>\r\n          prevEntries.filter((_, i) => i !== index)\r\n        );\r\n      } else {\r\n        throw new Error(\r\n          response.data?.message || \"Failed to delete experience.\"\r\n        );\r\n      }\r\n    } catch (error) {\r\n      const errorMessage =\r\n        error instanceof Error\r\n          ? error.message\r\n          : \"Something went wrong while removing experience.\";\r\n      toast.error(errorMessage);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const validateEntry = (entry: EducationEntry): boolean => {\r\n    return Boolean(\r\n      entry.university_name &&\r\n        entry.education_level &&\r\n        entry.major &&\r\n        entry.start_date &&\r\n        entry.end_date\r\n    );\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // If form hasn't changed and section is completed, just navigate\r\n    if (!isDirty && isCompleted && !edit) {\r\n      onMenuSelect(selectedMenuIndex + 1);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      for (const entry of educationEntries) {\r\n        if (!validateEntry(entry)) {\r\n          throw new Error(\r\n            \"All fields (institution name, education level, major, start date, and end date) are required.\"\r\n          );\r\n        }\r\n\r\n        if (new Date(entry.start_date) > new Date(entry.end_date)) {\r\n          throw new Error(\"Start date cannot be after end date.\");\r\n        }\r\n\r\n        const payload = {\r\n          ...entry,\r\n          start_date: formatDateISO(entry.start_date),\r\n          end_date: formatDateISO(entry.end_date),\r\n          degree:\r\n            entry.education_level === \"other\"\r\n              ? entry.specific_degree\r\n              : entry.education_level,\r\n        };\r\n\r\n        const { education_level, specific_degree, ...finalPayload } = payload;\r\n\r\n        const url = entry.id ? `${apiURL}/${entry.id}` : apiURL;\r\n        const method = entry.id ? \"put\" : \"post\";\r\n        const response = await apiClient[method](url, finalPayload);\r\n\r\n        if (response.status !== 200) {\r\n          throw new Error(response.data?.message || \"Something went wrong\");\r\n        }\r\n      }\r\n\r\n      setIsDirty(false);\r\n      if (!edit) {\r\n        onSectionComplete();\r\n        onMenuSelect(selectedMenuIndex + 1);\r\n      }\r\n    } catch (error) {\r\n      const errorMessage =\r\n        error instanceof Error ? error.message : \"Failed to submit form\";\r\n      toast.error(errorMessage);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const [isInstitutionModalOpen, setIsInstitutionModalOpen] = useState(false);\r\n  const [institutionSearchTerm, setInstitutionSearchTerm] = useState(\"\");\r\n  const [selectedEntryIndex, setSelectedEntryIndex] = useState<number | null>(\r\n    null\r\n  );\r\n  const [searchResults, setSearchResults] = useState<University[]>([]);\r\n  const [isSearching, setIsSearching] = useState(false);\r\n\r\n  const handleOpenInstitutionModal = (index: number) => {\r\n    setSelectedEntryIndex(index);\r\n    setInstitutionSearchTerm(\"\");\r\n    setIsInstitutionModalOpen(true);\r\n  };\r\n\r\n  const handleInstitutionSelect = (institutionName: string) => {\r\n    if (selectedEntryIndex !== null) {\r\n      handleFieldChange(selectedEntryIndex, \"university_name\", institutionName);\r\n      setIsInstitutionModalOpen(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const delayDebounce = setTimeout(async () => {\r\n      if (institutionSearchTerm.length >= 2) {\r\n        setIsSearching(true);\r\n        const results = await searchUniversities(institutionSearchTerm);\r\n        setSearchResults(results);\r\n        setIsSearching(false);\r\n      } else {\r\n        setSearchResults([]);\r\n      }\r\n    }, 300);\r\n\r\n    return () => clearTimeout(delayDebounce);\r\n  }, [institutionSearchTerm]);\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {isLoading ? (\r\n        <LoadingSpinner />\r\n      ) : (\r\n        <form onSubmit={handleSubmit}>\r\n          {educationEntries.map((entry, index) => (\r\n            <div key={index} className=\"mb-4 rounded-md relative\">\r\n              <h3 className=\"font-black mb-4 mt-12\">\r\n                {index === 0 ? \"Educational Background\" : \"Add More Education\"}\r\n              </h3>\r\n\r\n              <div className=\"mt-4\">\r\n                <label className=\"block mb-2 text-sm font-medium text-gray-900\">\r\n                  Education Level<span className=\"text-red-500\">*</span>\r\n                </label>\r\n                <Select\r\n                  onValueChange={(value) =>\r\n                    handleFieldChange(index, \"education_level\", value)\r\n                  }\r\n                  value={entry.education_level}\r\n                >\r\n                  <SelectTrigger className=\"h-[42px] shadow-none bg-white\">\r\n                    <SelectValue placeholder=\"Select Education Level\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent className=\"bg-white\">\r\n                    {educationLevels.map(\r\n                      (level) =>\r\n                        level.value && (\r\n                          <SelectItem\r\n                            key={level.value}\r\n                            className=\"cursor-pointer hover:bg-gray-100 transition-all rounded-lg\"\r\n                            value={level.value}\r\n                          >\r\n                            {level.label}\r\n                          </SelectItem>\r\n                        )\r\n                    )}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n\r\n              {entry.education_level === \"other\" && (\r\n                <div className=\"mt-4\">\r\n                  <InputField\r\n                    label=\"Specify Degree/Certification\"\r\n                    type=\"text\"\r\n                    placeholder=\"Enter your specific degree or certification\"\r\n                    name=\"specific_degree\"\r\n                    required={true}\r\n                    value={entry.specific_degree}\r\n                    onChange={(e) =>\r\n                      handleFieldChange(\r\n                        index,\r\n                        \"specific_degree\",\r\n                        e.target.value\r\n                      )\r\n                    }\r\n                  />\r\n                </div>\r\n              )}\r\n\r\n              <div className=\"mt-4\">\r\n                <label className=\"block mb-2 text-sm font-medium text-gray-900\">\r\n                  Institution Name<span className=\"text-red-500\">*</span>\r\n                </label>\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={() => handleOpenInstitutionModal(index)}\r\n                  className=\"w-full p-3 text-left border rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white\"\r\n                >\r\n                  {entry.university_name || \"Select Institution\"}\r\n                </button>\r\n              </div>\r\n\r\n              <div className=\"grid lg:grid-cols-2 gap-4 mt-4\">\r\n                <InputField\r\n                  label=\"Major/Field of Study\"\r\n                  type=\"text\"\r\n                  placeholder=\"Your field of study\"\r\n                  name=\"major\"\r\n                  required={true}\r\n                  value={entry.major}\r\n                  onChange={(e) =>\r\n                    handleFieldChange(index, \"major\", e.target.value)\r\n                  }\r\n                />\r\n                <InputField\r\n                  label=\"GPA/Grade\"\r\n                  type=\"text\"\r\n                  placeholder=\"e.g. 3.7/4.0\"\r\n                  name=\"grade\"\r\n                  value={entry.grade}\r\n                  onChange={(e) =>\r\n                    handleFieldChange(index, \"grade\", e.target.value)\r\n                  }\r\n                />\r\n              </div>\r\n\r\n              <div className=\"grid lg:grid-cols-2 gap-4 mt-4\">\r\n                <InputField\r\n                  label=\"Start Date\"\r\n                  type=\"date\"\r\n                  name=\"start_date\"\r\n                  required={true}\r\n                  value={entry.start_date}\r\n                  onChange={(e) =>\r\n                    handleFieldChange(index, \"start_date\", e.target.value)\r\n                  }\r\n                />\r\n                <InputField\r\n                  label=\"End Date\"\r\n                  type=\"date\"\r\n                  name=\"end_date\"\r\n                  required={true}\r\n                  value={entry.end_date}\r\n                  onChange={(e) =>\r\n                    handleFieldChange(index, \"end_date\", e.target.value)\r\n                  }\r\n                />\r\n              </div>\r\n\r\n              <div className=\"mt-4\">\r\n                <TextAreaLimit\r\n                  label=\"Accomplishments (Optional)\"\r\n                  name={`accomplishments-${index}`}\r\n                  placeholder=\"Share your relevant accomplishments, awards, or notable achievements\"\r\n                  value={entry.accomplishments}\r\n                  onChange={(value) =>\r\n                    handleFieldChange(index, \"accomplishments\", value)\r\n                  }\r\n                />\r\n              </div>\r\n\r\n              {index > 0 && (\r\n                <MainButton\r\n                  variant=\"secondary\"\r\n                  type=\"button\"\r\n                  onClick={() => handleRemoveEducation(index)}\r\n                >\r\n                  Remove Education\r\n                </MainButton>\r\n              )}\r\n            </div>\r\n          ))}\r\n\r\n          <MainButton\r\n            variant=\"primary\"\r\n            type=\"button\"\r\n            onClick={handleAddMoreEducation}\r\n          >\r\n            Add More Education +\r\n          </MainButton>\r\n\r\n          <NavigationButtons\r\n            currentIndex={selectedMenuIndex}\r\n            onBack={() =>\r\n              onMenuSelect(selectedMenuIndex ? selectedMenuIndex - 1 : 0)\r\n            }\r\n            onNext={() =>\r\n              onMenuSelect(selectedMenuIndex ? selectedMenuIndex + 1 : 1)\r\n            }\r\n            disableBack={!selectedMenuIndex || selectedMenuIndex < 1}\r\n            nextLabel={edit ? \"Save Changes\" : \"Next\"}\r\n          />\r\n        </form>\r\n      )}\r\n\r\n      {/* Institution Selection Modal */}\r\n      <Dialog\r\n        open={isInstitutionModalOpen}\r\n        onClose={() => setIsInstitutionModalOpen(false)}\r\n        className=\"relative z-50\"\r\n      >\r\n        <div className=\"fixed inset-0 bg-black/30\" aria-hidden=\"true\" />\r\n        <div className=\"fixed inset-0 flex items-center justify-center p-4\">\r\n          <Dialog.Panel className=\"w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all\">\r\n            <Dialog.Title className=\"text-lg font-medium leading-6 text-gray-900 mb-4\">\r\n              Select Institution\r\n            </Dialog.Title>\r\n\r\n            {/* Search Input */}\r\n            <div className=\"mb-4\">\r\n              <input\r\n                type=\"text\"\r\n                placeholder=\"Search institutions or enter new one...\"\r\n                value={institutionSearchTerm}\r\n                onChange={(e) => setInstitutionSearchTerm(e.target.value)}\r\n                className=\"w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500\"\r\n                autoFocus\r\n              />\r\n            </div>\r\n\r\n            {/* Institutions List */}\r\n            <div className=\"mt-2 max-h-[60vh] overflow-y-auto\">\r\n              {isSearching ? (\r\n                <div className=\"text-center py-4 text-gray-500\">\r\n                  Searching...\r\n                </div>\r\n              ) : institutionSearchTerm.length < 2 ? (\r\n                <div className=\"text-center py-4 text-gray-500\">\r\n                  Type at least 2 characters to search\r\n                </div>\r\n              ) : searchResults.length > 0 ? (\r\n                searchResults.map((institution) => (\r\n                  <button\r\n                    key={`${institution.name}-${institution.country}`}\r\n                    type=\"button\"\r\n                    className=\"w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 rounded-lg\"\r\n                    onClick={() => handleInstitutionSelect(institution.name)}\r\n                  >\r\n                    <div>{institution.name}</div>\r\n                    <div className=\"text-sm text-gray-500\">\r\n                      {institution.country}\r\n                    </div>\r\n                  </button>\r\n                ))\r\n              ) : (\r\n                <button\r\n                  type=\"button\"\r\n                  className=\"w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 rounded-lg text-blue-600\"\r\n                  onClick={() => handleInstitutionSelect(institutionSearchTerm)}\r\n                >\r\n                  Add \"{institutionSearchTerm}\" as new institution\r\n                </button>\r\n              )}\r\n            </div>\r\n          </Dialog.Panel>\r\n        </div>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAhBA;AAHA;;;;;;;;;;;;;AAwCA,MAAM,qBAAqB,OAAO;IAChC,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG,OAAO,EAAE;IAEzC,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,CAAC,6CAA6C,EAAE,mBAC9C,QACC;QAEL,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,GAAG,CAAC,CAAC,MAAa,CAAC;gBAC7B,MAAM,IAAI,IAAI;gBACd,SAAS,IAAI,OAAO;YACtB,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,EAAE;IACX;AACF;AAEA,MAAM,eAA+B;IACnC,iBAAiB;IACjB,QAAQ;IACR,OAAO;IACP,YAAY;IACZ,UAAU;IACV,OAAO;IACP,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;AACnB;AAOe,SAAS,gBAAgB,EACtC,oBAAoB,CAAC,EACrB,eAAe,KAAO,CAAC,EACvB,oBAAoB,KAAO,CAAC,EAC5B,cAAc,KAAK,EACnB,OAAO,KAAK,EAIb;IACC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzE;KACD;IACD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,SAAS,6DAAwC,4BAA4B,CAAC;IAEpF,MAAM,kBAA0C;QAC9C;YAAE,OAAO;YAAI,OAAO;QAAyB;QAC7C;YAAE,OAAO;YAAe,OAAO;QAAc;QAC7C;YAAE,OAAO;YAAa,OAAO;QAAmB;QAChD;YAAE,OAAO;YAAa,OAAO;QAAoB;QACjD;YAAE,OAAO;YAAW,OAAO;QAAkB;QAC7C;YAAE,OAAO;YAAa,OAAO;QAAgB;QAC7C;YAAE,OAAO;YAAiB,OAAO;QAA6B;QAC9D;YAAE,OAAO;YAAS,OAAO;QAAQ;KAClC;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB;YACzB,aAAa;YAEb,IAAI;gBACF,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBAErC,IAAI,SAAS,MAAM,KAAK,OAAO,SAAS,IAAI,EAAE;oBAC5C,MAAM,gBAAgB,SAAS,IAAI,CAAC,GAAG,CAAC,CAAC,QAA0B,CAAC;4BAClE,GAAG,KAAK;4BACR,YAAY,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,UAAU;4BAC7C,UAAU,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,QAAQ;4BACzC,iBAAiB,MAAM,MAAM;4BAC7B,iBAAiB,CAAC,gBAAgB,IAAI,CACpC,CAAC,QAAU,MAAM,KAAK,KAAK,MAAM,MAAM,IAErC,MAAM,MAAM,GACZ;wBACN,CAAC;oBACD,oBACE,cAAc,MAAM,GAAG,IAAI,gBAAgB;wBAAC;qBAAa;gBAE7D;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;YAClD,SAAU;gBACR,aAAa;YACf;QACF;QACA;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CACxB,OACA,WACA;QAEA,WAAW;QACX,oBAAoB,CAAC;YACnB,OAAO,YAAY,GAAG,CAAC,CAAC,OAAO;gBAC7B,IAAI,MAAM,OAAO,OAAO;gBAExB,MAAM,eAAe;oBAAE,GAAG,KAAK;gBAAC;gBAEhC,IAAI,cAAc,mBAAmB;oBACnC,aAAa,eAAe,GAAG;oBAC/B,aAAa,MAAM,GACjB,UAAU,UAAU,MAAM,eAAe,GAAG;oBAC9C,aAAa,eAAe,GAC1B,UAAU,UAAU,MAAM,eAAe,GAAG;gBAChD,OAAO,IAAI,cAAc,mBAAmB;oBAC1C,aAAa,eAAe,GAAG;oBAC/B,aAAa,MAAM,GACjB,MAAM,eAAe,KAAK,UAAU,QAAQ,MAAM,MAAM;gBAC5D,OAAO;oBACL,YAAY,CAAC,UAAU,GAAG;gBAC5B;gBAEA,OAAO;YACT;QACF;IACF;IAEA,MAAM,yBAAyB;QAC7B,WAAW;QACX,MAAM,WAAW,iBAAiB,KAAK,CAAC,CAAC,QAAU,cAAc;QAEjE,IAAI,UAAU;YACZ,oBAAoB,CAAC,cAAgB;uBAChC;oBACH;wBAAE,GAAG,YAAY;oBAAC;iBACnB;QACH,OAAO;YACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT;QAEJ;IACF;IAEA,MAAM,wBAAwB,OAAO;QACnC,WAAW;QACX,MAAM,qBAAqB,gBAAgB,CAAC,MAAM;QAElD,IAAI,CAAC,mBAAmB,EAAE,EAAE;YAC1B,oBAAoB,CAAC,cACnB,YAAY,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAErC;QACF;QAEA,IAAI;YACF,aAAa;YACb,MAAM,YAAY,GAAG,OAAO,CAAC,EAAE,mBAAmB,EAAE,EAAE;YACtD,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,MAAM,CAAC;YAExC,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,oBAAoB,CAAC,cACnB,YAAY,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAEvC,OAAO;gBACL,MAAM,IAAI,MACR,SAAS,IAAI,EAAE,WAAW;YAE9B;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QACb,MAAM,OAAO,GACb;YACN,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAO,QACL,MAAM,eAAe,IACnB,MAAM,eAAe,IACrB,MAAM,KAAK,IACX,MAAM,UAAU,IAChB,MAAM,QAAQ;IAEpB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,iEAAiE;QACjE,IAAI,CAAC,WAAW,eAAe,CAAC,MAAM;YACpC,aAAa,oBAAoB;YACjC;QACF;QAEA,aAAa;QAEb,IAAI;YACF,KAAK,MAAM,SAAS,iBAAkB;gBACpC,IAAI,CAAC,cAAc,QAAQ;oBACzB,MAAM,IAAI,MACR;gBAEJ;gBAEA,IAAI,IAAI,KAAK,MAAM,UAAU,IAAI,IAAI,KAAK,MAAM,QAAQ,GAAG;oBACzD,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,UAAU;oBACd,GAAG,KAAK;oBACR,YAAY,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,UAAU;oBAC1C,UAAU,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,QAAQ;oBACtC,QACE,MAAM,eAAe,KAAK,UACtB,MAAM,eAAe,GACrB,MAAM,eAAe;gBAC7B;gBAEA,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,cAAc,GAAG;gBAE9D,MAAM,MAAM,MAAM,EAAE,GAAG,GAAG,OAAO,CAAC,EAAE,MAAM,EAAE,EAAE,GAAG;gBACjD,MAAM,SAAS,MAAM,EAAE,GAAG,QAAQ;gBAClC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,OAAO,CAAC,KAAK;gBAE9C,IAAI,SAAS,MAAM,KAAK,KAAK;oBAC3B,MAAM,IAAI,MAAM,SAAS,IAAI,EAAE,WAAW;gBAC5C;YACF;YAEA,WAAW;YACX,IAAI,CAAC,MAAM;gBACT;gBACA,aAAa,oBAAoB;YACnC;QACF,EAAE,OAAO,OAAO;YACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAC3C,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACzD;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,6BAA6B,CAAC;QAClC,sBAAsB;QACtB,yBAAyB;QACzB,0BAA0B;IAC5B;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,uBAAuB,MAAM;YAC/B,kBAAkB,oBAAoB,mBAAmB;YACzD,0BAA0B;QAC5B;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,WAAW;YAC/B,IAAI,sBAAsB,MAAM,IAAI,GAAG;gBACrC,eAAe;gBACf,MAAM,UAAU,MAAM,mBAAmB;gBACzC,iBAAiB;gBACjB,eAAe;YACjB,OAAO;gBACL,iBAAiB,EAAE;YACrB;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAsB;IAE1B,qBACE,8OAAC;QAAI,WAAU;;YACZ,0BACC,8OAAC,uJAAA,CAAA,UAAc;;;;qCAEf,8OAAC;gBAAK,UAAU;;oBACb,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAG,WAAU;8CACX,UAAU,IAAI,2BAA2B;;;;;;8CAG5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;gDAA+C;8DAC/C,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEhD,8OAAC,kIAAA,CAAA,SAAM;4CACL,eAAe,CAAC,QACd,kBAAkB,OAAO,mBAAmB;4CAE9C,OAAO,MAAM,eAAe;;8DAE5B,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACtB,gBAAgB,GAAG,CAClB,CAAC,QACC,MAAM,KAAK,kBACT,8OAAC,kIAAA,CAAA,aAAU;4DAET,WAAU;4DACV,OAAO,MAAM,KAAK;sEAEjB,MAAM,KAAK;2DAJP,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;gCAY7B,MAAM,eAAe,KAAK,yBACzB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,mJAAA,CAAA,aAAU;wCACT,OAAM;wCACN,MAAK;wCACL,aAAY;wCACZ,MAAK;wCACL,UAAU;wCACV,OAAO,MAAM,eAAe;wCAC5B,UAAU,CAAC,IACT,kBACE,OACA,mBACA,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;8CAOxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;gDAA+C;8DAC9C,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEjD,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,2BAA2B;4CAC1C,WAAU;sDAET,MAAM,eAAe,IAAI;;;;;;;;;;;;8CAI9B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,mJAAA,CAAA,aAAU;4CACT,OAAM;4CACN,MAAK;4CACL,aAAY;4CACZ,MAAK;4CACL,UAAU;4CACV,OAAO,MAAM,KAAK;4CAClB,UAAU,CAAC,IACT,kBAAkB,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;sDAGpD,8OAAC,mJAAA,CAAA,aAAU;4CACT,OAAM;4CACN,MAAK;4CACL,aAAY;4CACZ,MAAK;4CACL,OAAO,MAAM,KAAK;4CAClB,UAAU,CAAC,IACT,kBAAkB,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAKtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,mJAAA,CAAA,aAAU;4CACT,OAAM;4CACN,MAAK;4CACL,MAAK;4CACL,UAAU;4CACV,OAAO,MAAM,UAAU;4CACvB,UAAU,CAAC,IACT,kBAAkB,OAAO,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;sDAGzD,8OAAC,mJAAA,CAAA,aAAU;4CACT,OAAM;4CACN,MAAK;4CACL,MAAK;4CACL,UAAU;4CACV,OAAO,MAAM,QAAQ;4CACrB,UAAU,CAAC,IACT,kBAAkB,OAAO,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;8CAKzD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,sJAAA,CAAA,UAAa;wCACZ,OAAM;wCACN,MAAM,CAAC,gBAAgB,EAAE,OAAO;wCAChC,aAAY;wCACZ,OAAO,MAAM,eAAe;wCAC5B,UAAU,CAAC,QACT,kBAAkB,OAAO,mBAAmB;;;;;;;;;;;gCAKjD,QAAQ,mBACP,8OAAC,gJAAA,CAAA,aAAU;oCACT,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,sBAAsB;8CACtC;;;;;;;2BApIK;;;;;kCA2IZ,8OAAC,gJAAA,CAAA,aAAU;wBACT,SAAQ;wBACR,MAAK;wBACL,SAAS;kCACV;;;;;;kCAID,8OAAC,uJAAA,CAAA,oBAAiB;wBAChB,cAAc;wBACd,QAAQ,IACN,aAAa,oBAAoB,oBAAoB,IAAI;wBAE3D,QAAQ,IACN,aAAa,oBAAoB,oBAAoB,IAAI;wBAE3D,aAAa,CAAC,qBAAqB,oBAAoB;wBACvD,WAAW,OAAO,iBAAiB;;;;;;;;;;;;0BAMzC,8OAAC,+KAAA,CAAA,SAAM;gBACL,MAAM;gBACN,SAAS,IAAM,0BAA0B;gBACzC,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;wBAA4B,eAAY;;;;;;kCACvD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;4BAAC,WAAU;;8CACtB,8OAAC,+KAAA,CAAA,SAAM,CAAC,KAAK;oCAAC,WAAU;8CAAmD;;;;;;8CAK3E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,yBAAyB,EAAE,MAAM,CAAC,KAAK;wCACxD,WAAU;wCACV,SAAS;;;;;;;;;;;8CAKb,8OAAC;oCAAI,WAAU;8CACZ,4BACC,8OAAC;wCAAI,WAAU;kDAAiC;;;;;+CAG9C,sBAAsB,MAAM,GAAG,kBACjC,8OAAC;wCAAI,WAAU;kDAAiC;;;;;+CAG9C,cAAc,MAAM,GAAG,IACzB,cAAc,GAAG,CAAC,CAAC,4BACjB,8OAAC;4CAEC,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,wBAAwB,YAAY,IAAI;;8DAEvD,8OAAC;8DAAK,YAAY,IAAI;;;;;;8DACtB,8OAAC;oDAAI,WAAU;8DACZ,YAAY,OAAO;;;;;;;2CAPjB,GAAG,YAAY,IAAI,CAAC,CAAC,EAAE,YAAY,OAAO,EAAE;;;;kEAYrD,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,wBAAwB;;4CACxC;4CACO;4CAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C"}}, {"offset": {"line": 3138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3144, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/create/ProfessionalExperience.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\n\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { InputField } from \"@components/common/inputField\";\r\nimport { NavigationButtons } from \"@components/common/navigationBtns\";\r\nimport { toast } from \"react-toastify\";\r\nimport { formatNormalDate, formatDateISO } from \"@/app/utils/time-helpers\";\r\nimport LoadingSpinner from \"@components/common/loadingSpinner\";\r\nimport TextAreaLimit from \"@components/common/TeaxAreaLimit\";\r\nimport { ProfileCreateProps } from \"@/app/types/counselor/profile\";\r\nimport { MainButton } from \"@/app/components/common/mainBtn\";\r\n\r\ninterface ProfessionalExperienceEntry {\r\n  role?: string;\r\n  company_name?: string;\r\n  start_date?: string;\r\n  end_date?: string;\r\n  experience_description?: string;\r\n  id?: string;\r\n}\r\n\r\ninterface SocialLinks {\r\n  id?: number;\r\n  counselor_id?: number;\r\n  linkedin_url?: string;\r\n  portfolio_url?: string;\r\n  website_url?: string;\r\n}\r\n\r\nconst initialState = [\r\n  {\r\n    role: \"\",\r\n    company_name: \"\",\r\n    start_date: \"\",\r\n    end_date: \"\",\r\n    experience_description: \"\",\r\n  },\r\n];\r\n\r\nexport default function ProfessionalExperience({\r\n  selectedMenuIndex = 0,\r\n  onMenuSelect = () => {},\r\n  onSectionComplete = () => {},\r\n  isCompleted = false,\r\n  edit = false,\r\n}: ProfileCreateProps & {\r\n  onSectionComplete?: () => void;\r\n  isCompleted?: boolean;\r\n}) {\r\n  const [experiences, setExperiences] =\r\n    useState<ProfessionalExperienceEntry[]>(initialState);\r\n  const [socialLinks, setSocialLinks] = useState<SocialLinks>({\r\n    linkedin_url: \"\",\r\n    portfolio_url: \"\",\r\n    website_url: \"\",\r\n  });\r\n  const [isLoading, setIsLoading] = useState<Boolean>(true);\r\n  const [isDirty, setIsDirty] = useState(false);\r\n\r\n  const apiURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/counselor/profile/professional-experience`;\r\n  const socialLinkURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/counselor/profile/social-links`;\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      setIsLoading(true);\r\n\r\n      try {\r\n        // Fetch Professional Experiences\r\n        const experienceResponse = await apiClient.get(apiURL);\r\n\r\n        if (experienceResponse.status === 200 && experienceResponse.data) {\r\n          const dataReceived = experienceResponse.data.map((entry: any) => {\r\n            return {\r\n              ...entry,\r\n              start_date: entry.start_date\r\n                ? formatNormalDate(entry.start_date)\r\n                : \"\",\r\n              end_date: entry.end_date ? formatNormalDate(entry.end_date) : \"\",\r\n            };\r\n          });\r\n\r\n          setExperiences(dataReceived.length > 0 ? dataReceived : initialState);\r\n        }\r\n\r\n        // Fetch Social Links\r\n        const socialLinksResponse = await apiClient.get(socialLinkURL);\r\n\r\n        if (socialLinksResponse.status === 200 && socialLinksResponse.data) {\r\n          setSocialLinks({\r\n            id: socialLinksResponse.data.id,\r\n            counselor_id: socialLinksResponse.data.counselor_id,\r\n            linkedin_url: socialLinksResponse.data.linkedin_url || \"\",\r\n            portfolio_url: socialLinksResponse.data.portfolio_url || \"\",\r\n            website_url: socialLinksResponse.data.website_url || \"\",\r\n          });\r\n        }\r\n      } catch (error: any) {\r\n        console.log(error?.response?.data?.detail);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, []);\r\n\r\n  const handleExperienceChange = (\r\n    index: number,\r\n    field: keyof ProfessionalExperienceEntry,\r\n    value: string\r\n  ) => {\r\n    setIsDirty(true);\r\n    const updatedExperiences = [...experiences];\r\n    updatedExperiences[index][field] = value;\r\n    setExperiences(updatedExperiences);\r\n  };\r\n\r\n  const handleSocialLinksChange = (field: keyof SocialLinks, value: string) => {\r\n    setIsDirty(true);\r\n    setSocialLinks((prev) => ({\r\n      ...prev,\r\n      [field]: value,\r\n    }));\r\n  };\r\n\r\n  const handleAddExperience = () => {\r\n    setIsDirty(true);\r\n    setExperiences([\r\n      ...experiences,\r\n      {\r\n        role: \"\",\r\n        company_name: \"\",\r\n        start_date: \"\",\r\n        end_date: \"\",\r\n        experience_description: \"\",\r\n      },\r\n    ]);\r\n  };\r\n\r\n  const handleRemoveExperience = async (index: number) => {\r\n    setIsDirty(true);\r\n    const experienceToRemove = experiences[index];\r\n\r\n    if (!experienceToRemove.id) {\r\n      // If the experience doesn't have an ID, it's a new entry and hasn't been saved to the server\r\n      setExperiences((prevExperiences) =>\r\n        prevExperiences.filter((_, i) => i !== index)\r\n      );\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setIsLoading(true);\r\n      const deleteUrl = `${apiURL}/${experienceToRemove.id}`;\r\n      const response = await apiClient.delete(deleteUrl);\r\n\r\n      if (response.status === 200) {\r\n        toast.success(\"Experience removed successfully.\");\r\n        setExperiences((prevExperiences) =>\r\n          prevExperiences.filter((_, i) => i !== index)\r\n        );\r\n      } else {\r\n        throw new Error(\r\n          response.data?.message || \"Failed to delete experience.\"\r\n        );\r\n      }\r\n    } catch (error: any) {\r\n      toast.error(\r\n        error.message || \"Something went wrong while removing experience.\"\r\n      );\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // If form hasn't changed and section is completed, just navigate\r\n    if (!isDirty && isCompleted && !edit) {\r\n      onMenuSelect(selectedMenuIndex + 1);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      // Handle Professional Experiences\r\n      const nonEmptyExperiences = experiences.filter(\r\n        (exp) =>\r\n          exp.role ||\r\n          exp.company_name ||\r\n          exp.start_date ||\r\n          exp.end_date ||\r\n          exp.experience_description\r\n      );\r\n\r\n      for (const entry of nonEmptyExperiences) {\r\n        if (\r\n          entry.start_date &&\r\n          entry.end_date &&\r\n          new Date(entry.start_date) > new Date(entry.end_date)\r\n        ) {\r\n          throw new Error(\"Start date cannot be after end date.\");\r\n        }\r\n\r\n        if (\r\n          entry.start_date ||\r\n          entry.end_date ||\r\n          entry.role ||\r\n          entry.company_name ||\r\n          entry.experience_description\r\n        ) {\r\n          const payload = {\r\n            ...entry,\r\n            start_date: entry.start_date\r\n              ? formatDateISO(entry.start_date)\r\n              : null,\r\n            end_date: entry.end_date ? formatDateISO(entry.end_date) : null,\r\n          };\r\n\r\n          const url = entry.id ? `${apiURL}/${entry.id}` : apiURL;\r\n          const method = entry.id ? \"put\" : \"post\";\r\n          const response = await apiClient[method](url, payload);\r\n\r\n          if (response.status !== 200) {\r\n            throw new Error(response.data.message || \"Something went wrong\");\r\n          }\r\n        }\r\n      }\r\n\r\n      // Handle Social Links\r\n      const socialLinksPayload = {\r\n        linkedin_url: socialLinks.linkedin_url || \"\",\r\n        portfolio_url: socialLinks.portfolio_url || \"\",\r\n        website_url: socialLinks.website_url || \"\",\r\n      };\r\n\r\n      const socialLinksMethod = socialLinks.id ? \"put\" : \"post\";\r\n      const socialLinksResponse = await apiClient[socialLinksMethod](\r\n        socialLinkURL,\r\n        socialLinksPayload\r\n      );\r\n\r\n      if (socialLinksResponse.status !== 200) {\r\n        throw new Error(\r\n          socialLinksResponse.data.message || \"Failed to save social links\"\r\n        );\r\n      }\r\n\r\n      if (!edit) {\r\n        onSectionComplete();\r\n        onMenuSelect(selectedMenuIndex + 1);\r\n      } else {\r\n        toast.success(\"Changes saved successfully\");\r\n      }\r\n      setIsDirty(false);\r\n    } catch (error: any) {\r\n      toast.error(error.message || \"Failed to submit form\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {isLoading ? (\r\n        <LoadingSpinner />\r\n      ) : (\r\n        <form className=\"mt-12\" onSubmit={handleSubmit}>\r\n          {/* Professional Experience Section */}\r\n          {experiences.map((experience, index) => (\r\n            <div key={index}>\r\n              {index === 0 && (\r\n                <>\r\n                  <div className=\"flex items-center justify-between mb-6\">\r\n                    <h3 className=\"font-black\">Professional Experience</h3>\r\n                    {!edit && (\r\n                      <MainButton\r\n                        variant=\"secondary\"\r\n                        type=\"button\"\r\n                        onClick={() => onMenuSelect(selectedMenuIndex + 1)}\r\n                      >\r\n                        Skip this step →\r\n                      </MainButton>\r\n                    )}\r\n                  </div>\r\n\r\n                  <div className=\"mb-6\">\r\n                    <InputField\r\n                      label=\"LinkedIn URL\"\r\n                      type=\"text\"\r\n                      placeholder=\"Enter your LinkedIn Profile URL\"\r\n                      name=\"linkedin_url\"\r\n                      value={socialLinks.linkedin_url}\r\n                      onChange={(e) =>\r\n                        handleSocialLinksChange(\"linkedin_url\", e.target.value)\r\n                      }\r\n                    />\r\n                  </div>\r\n                </>\r\n              )}\r\n\r\n              {index > 0 && (\r\n                <h3 className=\"font-black mb-4\">Add more experience</h3>\r\n              )}\r\n\r\n              <div key={index} className=\"mb-6 border-b pb-4 space-y-4\">\r\n                <InputField\r\n                  label=\"Company Name\"\r\n                  type=\"text\"\r\n                  placeholder=\"Enter your company name\"\r\n                  name={`company_name-${index}`}\r\n                  value={experience.company_name}\r\n                  onChange={(e) =>\r\n                    handleExperienceChange(\r\n                      index,\r\n                      \"company_name\",\r\n                      e.target.value\r\n                    )\r\n                  }\r\n                />\r\n                <div className=\"\">\r\n                  <InputField\r\n                    label=\"Role\"\r\n                    type=\"text\"\r\n                    placeholder=\"Product Designer\"\r\n                    name={`role-${index}`}\r\n                    value={experience.role}\r\n                    onChange={(e) =>\r\n                      handleExperienceChange(index, \"role\", e.target.value)\r\n                    }\r\n                  />\r\n                </div>\r\n\r\n                <div className=\"grid lg:grid-cols-2 gap-4 mt-4 mb-4\">\r\n                  <InputField\r\n                    label=\"Start Date\"\r\n                    type=\"date\"\r\n                    name={`start_date-${index}`}\r\n                    value={experience.start_date}\r\n                    onChange={(e) =>\r\n                      handleExperienceChange(\r\n                        index,\r\n                        \"start_date\",\r\n                        e.target.value\r\n                      )\r\n                    }\r\n                  />\r\n                  <InputField\r\n                    label=\"End Date\"\r\n                    type=\"date\"\r\n                    name={`end_date-${index}`}\r\n                    value={experience.end_date}\r\n                    onChange={(e) =>\r\n                      handleExperienceChange(index, \"end_date\", e.target.value)\r\n                    }\r\n                  />\r\n                </div>\r\n\r\n                <TextAreaLimit\r\n                  label=\"Briefly describe your experience\"\r\n                  name={`experience_description-${index}`}\r\n                  placeholder=\"Share your experience\"\r\n                  value={experience.experience_description}\r\n                  onChange={(value) =>\r\n                    handleExperienceChange(\r\n                      index,\r\n                      \"experience_description\",\r\n                      value\r\n                    )\r\n                  }\r\n                />\r\n\r\n                {index > 0 && (\r\n                  <MainButton\r\n                    variant=\"secondary\"\r\n                    type=\"button\"\r\n                    onClick={() => handleRemoveExperience(index)}\r\n                  >\r\n                    Remove Experience\r\n                  </MainButton>\r\n                )}\r\n              </div>\r\n            </div>\r\n          ))}\r\n\r\n          <MainButton\r\n            variant=\"primary\"\r\n            type=\"button\"\r\n            onClick={handleAddExperience}\r\n          >\r\n            Add More Experience +\r\n          </MainButton>\r\n\r\n          <NavigationButtons\r\n            currentIndex={selectedMenuIndex || 0}\r\n            onBack={() =>\r\n              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex - 1 : 0)\r\n            }\r\n            onNext={() =>\r\n              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex + 1 : 1)\r\n            }\r\n            disableBack={!selectedMenuIndex || selectedMenuIndex < 1}\r\n            nextLabel={edit ? \"Save Changes\" : \"Next\"}\r\n          />\r\n        </form>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAZA;;;;;;;;;;;AA+BA,MAAM,eAAe;IACnB;QACE,MAAM;QACN,cAAc;QACd,YAAY;QACZ,UAAU;QACV,wBAAwB;IAC1B;CACD;AAEc,SAAS,uBAAuB,EAC7C,oBAAoB,CAAC,EACrB,eAAe,KAAO,CAAC,EACvB,oBAAoB,KAAO,CAAC,EAC5B,cAAc,KAAK,EACnB,OAAO,KAAK,EAIb;IACC,MAAM,CAAC,aAAa,eAAe,GACjC,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IAC1C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,cAAc;QACd,eAAe;QACf,aAAa;IACf;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,SAAS,6DAAwC,0CAA0C,CAAC;IAClG,MAAM,gBAAgB,6DAAwC,+BAA+B,CAAC;IAE9F,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,aAAa;YAEb,IAAI;gBACF,iCAAiC;gBACjC,MAAM,qBAAqB,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBAE/C,IAAI,mBAAmB,MAAM,KAAK,OAAO,mBAAmB,IAAI,EAAE;oBAChE,MAAM,eAAe,mBAAmB,IAAI,CAAC,GAAG,CAAC,CAAC;wBAChD,OAAO;4BACL,GAAG,KAAK;4BACR,YAAY,MAAM,UAAU,GACxB,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,UAAU,IACjC;4BACJ,UAAU,MAAM,QAAQ,GAAG,CAAA,GAAA,+HAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,QAAQ,IAAI;wBAChE;oBACF;oBAEA,eAAe,aAAa,MAAM,GAAG,IAAI,eAAe;gBAC1D;gBAEA,qBAAqB;gBACrB,MAAM,sBAAsB,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBAEhD,IAAI,oBAAoB,MAAM,KAAK,OAAO,oBAAoB,IAAI,EAAE;oBAClE,eAAe;wBACb,IAAI,oBAAoB,IAAI,CAAC,EAAE;wBAC/B,cAAc,oBAAoB,IAAI,CAAC,YAAY;wBACnD,cAAc,oBAAoB,IAAI,CAAC,YAAY,IAAI;wBACvD,eAAe,oBAAoB,IAAI,CAAC,aAAa,IAAI;wBACzD,aAAa,oBAAoB,IAAI,CAAC,WAAW,IAAI;oBACvD;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,GAAG,CAAC,OAAO,UAAU,MAAM;YACrC,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,yBAAyB,CAC7B,OACA,OACA;QAEA,WAAW;QACX,MAAM,qBAAqB;eAAI;SAAY;QAC3C,kBAAkB,CAAC,MAAM,CAAC,MAAM,GAAG;QACnC,eAAe;IACjB;IAEA,MAAM,0BAA0B,CAAC,OAA0B;QACzD,WAAW;QACX,eAAe,CAAC,OAAS,CAAC;gBACxB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,sBAAsB;QAC1B,WAAW;QACX,eAAe;eACV;YACH;gBACE,MAAM;gBACN,cAAc;gBACd,YAAY;gBACZ,UAAU;gBACV,wBAAwB;YAC1B;SACD;IACH;IAEA,MAAM,yBAAyB,OAAO;QACpC,WAAW;QACX,MAAM,qBAAqB,WAAW,CAAC,MAAM;QAE7C,IAAI,CAAC,mBAAmB,EAAE,EAAE;YAC1B,6FAA6F;YAC7F,eAAe,CAAC,kBACd,gBAAgB,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAEzC;QACF;QAEA,IAAI;YACF,aAAa;YACb,MAAM,YAAY,GAAG,OAAO,CAAC,EAAE,mBAAmB,EAAE,EAAE;YACtD,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,MAAM,CAAC;YAExC,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,eAAe,CAAC,kBACd,gBAAgB,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAE3C,OAAO;gBACL,MAAM,IAAI,MACR,SAAS,IAAI,EAAE,WAAW;YAE9B;QACF,EAAE,OAAO,OAAY;YACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,OAAO,IAAI;QAErB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,iEAAiE;QACjE,IAAI,CAAC,WAAW,eAAe,CAAC,MAAM;YACpC,aAAa,oBAAoB;YACjC;QACF;QAEA,aAAa;QACb,IAAI;YACF,kCAAkC;YAClC,MAAM,sBAAsB,YAAY,MAAM,CAC5C,CAAC,MACC,IAAI,IAAI,IACR,IAAI,YAAY,IAChB,IAAI,UAAU,IACd,IAAI,QAAQ,IACZ,IAAI,sBAAsB;YAG9B,KAAK,MAAM,SAAS,oBAAqB;gBACvC,IACE,MAAM,UAAU,IAChB,MAAM,QAAQ,IACd,IAAI,KAAK,MAAM,UAAU,IAAI,IAAI,KAAK,MAAM,QAAQ,GACpD;oBACA,MAAM,IAAI,MAAM;gBAClB;gBAEA,IACE,MAAM,UAAU,IAChB,MAAM,QAAQ,IACd,MAAM,IAAI,IACV,MAAM,YAAY,IAClB,MAAM,sBAAsB,EAC5B;oBACA,MAAM,UAAU;wBACd,GAAG,KAAK;wBACR,YAAY,MAAM,UAAU,GACxB,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,UAAU,IAC9B;wBACJ,UAAU,MAAM,QAAQ,GAAG,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,QAAQ,IAAI;oBAC7D;oBAEA,MAAM,MAAM,MAAM,EAAE,GAAG,GAAG,OAAO,CAAC,EAAE,MAAM,EAAE,EAAE,GAAG;oBACjD,MAAM,SAAS,MAAM,EAAE,GAAG,QAAQ;oBAClC,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,OAAO,CAAC,KAAK;oBAE9C,IAAI,SAAS,MAAM,KAAK,KAAK;wBAC3B,MAAM,IAAI,MAAM,SAAS,IAAI,CAAC,OAAO,IAAI;oBAC3C;gBACF;YACF;YAEA,sBAAsB;YACtB,MAAM,qBAAqB;gBACzB,cAAc,YAAY,YAAY,IAAI;gBAC1C,eAAe,YAAY,aAAa,IAAI;gBAC5C,aAAa,YAAY,WAAW,IAAI;YAC1C;YAEA,MAAM,oBAAoB,YAAY,EAAE,GAAG,QAAQ;YACnD,MAAM,sBAAsB,MAAM,gHAAA,CAAA,UAAS,CAAC,kBAAkB,CAC5D,eACA;YAGF,IAAI,oBAAoB,MAAM,KAAK,KAAK;gBACtC,MAAM,IAAI,MACR,oBAAoB,IAAI,CAAC,OAAO,IAAI;YAExC;YAEA,IAAI,CAAC,MAAM;gBACT;gBACA,aAAa,oBAAoB;YACnC,OAAO;gBACL,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YACA,WAAW;QACb,EAAE,OAAO,OAAY;YACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;QAC/B,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,0BACC,8OAAC,uJAAA,CAAA,UAAc;;;;iCAEf,8OAAC;YAAK,WAAU;YAAQ,UAAU;;gBAE/B,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC;;4BACE,UAAU,mBACT;;kDACE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAa;;;;;;4CAC1B,CAAC,sBACA,8OAAC,gJAAA,CAAA,aAAU;gDACT,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,aAAa,oBAAoB;0DACjD;;;;;;;;;;;;kDAML,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,mJAAA,CAAA,aAAU;4CACT,OAAM;4CACN,MAAK;4CACL,aAAY;4CACZ,MAAK;4CACL,OAAO,YAAY,YAAY;4CAC/B,UAAU,CAAC,IACT,wBAAwB,gBAAgB,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;4BAO/D,QAAQ,mBACP,8OAAC;gCAAG,WAAU;0CAAkB;;;;;;0CAGlC,8OAAC;gCAAgB,WAAU;;kDACzB,8OAAC,mJAAA,CAAA,aAAU;wCACT,OAAM;wCACN,MAAK;wCACL,aAAY;wCACZ,MAAM,CAAC,aAAa,EAAE,OAAO;wCAC7B,OAAO,WAAW,YAAY;wCAC9B,UAAU,CAAC,IACT,uBACE,OACA,gBACA,EAAE,MAAM,CAAC,KAAK;;;;;;kDAIpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,mJAAA,CAAA,aAAU;4CACT,OAAM;4CACN,MAAK;4CACL,aAAY;4CACZ,MAAM,CAAC,KAAK,EAAE,OAAO;4CACrB,OAAO,WAAW,IAAI;4CACtB,UAAU,CAAC,IACT,uBAAuB,OAAO,QAAQ,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;kDAK1D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,mJAAA,CAAA,aAAU;gDACT,OAAM;gDACN,MAAK;gDACL,MAAM,CAAC,WAAW,EAAE,OAAO;gDAC3B,OAAO,WAAW,UAAU;gDAC5B,UAAU,CAAC,IACT,uBACE,OACA,cACA,EAAE,MAAM,CAAC,KAAK;;;;;;0DAIpB,8OAAC,mJAAA,CAAA,aAAU;gDACT,OAAM;gDACN,MAAK;gDACL,MAAM,CAAC,SAAS,EAAE,OAAO;gDACzB,OAAO,WAAW,QAAQ;gDAC1B,UAAU,CAAC,IACT,uBAAuB,OAAO,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kDAK9D,8OAAC,sJAAA,CAAA,UAAa;wCACZ,OAAM;wCACN,MAAM,CAAC,uBAAuB,EAAE,OAAO;wCACvC,aAAY;wCACZ,OAAO,WAAW,sBAAsB;wCACxC,UAAU,CAAC,QACT,uBACE,OACA,0BACA;;;;;;oCAKL,QAAQ,mBACP,8OAAC,gJAAA,CAAA,aAAU;wCACT,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,uBAAuB;kDACvC;;;;;;;+BAxEK;;;;;;uBAnCF;;;;;8BAmHZ,8OAAC,gJAAA,CAAA,aAAU;oBACT,SAAQ;oBACR,MAAK;oBACL,SAAS;8BACV;;;;;;8BAID,8OAAC,uJAAA,CAAA,oBAAiB;oBAChB,cAAc,qBAAqB;oBACnC,QAAQ,IACN,eAAe,oBAAoB,oBAAoB,IAAI;oBAE7D,QAAQ,IACN,eAAe,oBAAoB,oBAAoB,IAAI;oBAE7D,aAAa,CAAC,qBAAqB,oBAAoB;oBACvD,WAAW,OAAO,iBAAiB;;;;;;;;;;;;;;;;;AAM/C"}}, {"offset": {"line": 3529, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3535, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/create/CounsellingExperience.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { NavigationButtons } from \"@components/common/navigationBtns\";\r\nimport useFormState from \"@hooks/useFormState\";\r\nimport { toast } from \"react-toastify\";\r\nimport LoadingSpinner from \"@components/common/loadingSpinner\";\r\nimport { ProfileCreateProps } from \"@/app/types/counselor/profile\";\r\nimport TextAreaLimit from \"@/app/components/common/TeaxAreaLimit\";\r\nimport { MainButton } from \"@/app/components/common/mainBtn\";\r\nimport { RadioGroup } from \"@headlessui/react\";\r\n\r\nconst initialState = {\r\n  mentor: \"\",\r\n  expDesc: \"\",\r\n  counselorId: 0,\r\n};\r\n\r\nfunction CheckIcon(props: any) {\r\n  return (\r\n    <svg viewBox=\"0 0 24 24\" fill=\"none\" {...props}>\r\n      <circle cx={12} cy={12} r={12} fill=\"#fff\" opacity=\"0.2\" />\r\n      <path\r\n        d=\"M7 13l3 3 7-7\"\r\n        stroke=\"currentColor\"\r\n        strokeWidth={1.5}\r\n        strokeLinecap=\"round\"\r\n        strokeLinejoin=\"round\"\r\n      />\r\n    </svg>\r\n  );\r\n}\r\n\r\nexport default function CounsellingExperience({\r\n  selectedMenuIndex = 0,\r\n  onMenuSelect = () => {},\r\n  onSectionComplete = () => {},\r\n  isCompleted = false,\r\n  edit = false,\r\n}: ProfileCreateProps & {\r\n  onSectionComplete?: () => void;\r\n  isCompleted?: boolean;\r\n}) {\r\n  const { formData, setFormData, handleChange } = useFormState(initialState);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isDirty, setIsDirty] = useState(false);\r\n\r\n  const apiURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/counselor/profile/counseling-experience`;\r\n\r\n  useEffect(() => {\r\n    const fetchCounselingExperience = async () => {\r\n      setIsLoading(true);\r\n\r\n      try {\r\n        const response = await apiClient.get(apiURL);\r\n        const data = response.data;\r\n\r\n        if (data) {\r\n          setFormData({\r\n            mentor: data.has_mentored_before ? \"yes\" : \"no\",\r\n            expDesc: data.experience_description || \"\",\r\n            counselorId: data.counselor_id,\r\n          });\r\n        }\r\n      } catch (error: any) {\r\n        // If no data exists, keep the empty initial state\r\n        console.log(error?.response?.data?.detail);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchCounselingExperience();\r\n  }, [setFormData]);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // If form hasn't changed and section is completed, just navigate\r\n    if (!isDirty && isCompleted && !edit) {\r\n      onMenuSelect(selectedMenuIndex + 1);\r\n      return;\r\n    }\r\n\r\n    // Validate that an option is selected\r\n    if (!formData.mentor) {\r\n      toast.error(\"Please select whether you have mentoring experience\");\r\n      return;\r\n    }\r\n\r\n    // Validate description if \"Yes\" is selected\r\n    if (formData.mentor === \"yes\" && !formData.expDesc.trim()) {\r\n      toast.error(\"Please describe your mentoring experience\");\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    const payload = {\r\n      has_mentored_before: formData.mentor === \"yes\",\r\n      experience_description: formData.mentor === \"yes\" ? formData.expDesc : \"\",\r\n    };\r\n\r\n    try {\r\n      if (formData.counselorId) {\r\n        await apiClient.put(apiURL, payload);\r\n      } else {\r\n        await apiClient.post(apiURL, payload);\r\n      }\r\n\r\n      toast.success(\"Counselling experience saved successfully!\");\r\n      setIsDirty(false);\r\n      if (!edit) {\r\n        onSectionComplete();\r\n        onMenuSelect(selectedMenuIndex + 1);\r\n      }\r\n    } catch (error) {\r\n      toast.error(\"Failed to save counseling experience.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleMentorChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    handleChange(e);\r\n    setIsDirty(true);\r\n    if (e.target.value === \"no\") {\r\n      setFormData((prev) => ({ ...prev, expDesc: \"\" }));\r\n    }\r\n  };\r\n\r\n  const handleExpDescChange = (value: string) => {\r\n    setFormData((prev) => ({ ...prev, expDesc: value }));\r\n    setIsDirty(true);\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {isLoading ? (\r\n        <LoadingSpinner />\r\n      ) : (\r\n        <form className=\"mt-12\" onSubmit={handleSubmit}>\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"font-black\">Counselling Experience</h3>\r\n            {!edit && (\r\n              <MainButton\r\n                variant=\"secondary\"\r\n                type=\"button\"\r\n                onClick={() => onMenuSelect(selectedMenuIndex + 1)}\r\n              >\r\n                Skip this step →\r\n              </MainButton>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"mt-4\">\r\n            <label className=\"block font-medium mb-2 text-sm\">\r\n              Have you mentored students for college applications before?\r\n            </label>\r\n            <RadioGroup\r\n              value={formData.mentor}\r\n              onChange={(value) => {\r\n                handleMentorChange({\r\n                  target: { name: \"mentor\", value },\r\n                } as React.ChangeEvent<HTMLInputElement>);\r\n              }}\r\n              className=\"mt-2\"\r\n            >\r\n              <div className=\"flex gap-4\">\r\n                <RadioGroup.Option\r\n                  value=\"yes\"\r\n                  className={({ checked }) =>\r\n                    `relative flex cursor-pointer rounded-lg px-5 py-3 border ${\r\n                      checked\r\n                        ? \"bg-blue-50 border-blue-500\"\r\n                        : \"border-gray-300 hover:bg-gray-50\"\r\n                    }`\r\n                  }\r\n                >\r\n                  {({ checked }) => (\r\n                    <div className=\"flex w-full items-center justify-between\">\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"text-sm\">\r\n                          <RadioGroup.Label\r\n                            as=\"p\"\r\n                            className={`font-medium ${checked ? \"text-blue-900\" : \"text-gray-900\"}`}\r\n                          >\r\n                            Yes\r\n                          </RadioGroup.Label>\r\n                        </div>\r\n                      </div>\r\n                      {checked && (\r\n                        <div className=\"shrink-0 text-blue-500\">\r\n                          <CheckIcon className=\"h-6 w-6\" />\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  )}\r\n                </RadioGroup.Option>\r\n\r\n                <RadioGroup.Option\r\n                  value=\"no\"\r\n                  className={({ checked }) =>\r\n                    `relative flex cursor-pointer rounded-lg px-5 py-3 border ${\r\n                      checked\r\n                        ? \"bg-blue-50 border-blue-500\"\r\n                        : \"border-gray-300 hover:bg-gray-50\"\r\n                    }`\r\n                  }\r\n                >\r\n                  {({ checked }) => (\r\n                    <div className=\"flex w-full items-center justify-between\">\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"text-sm\">\r\n                          <RadioGroup.Label\r\n                            as=\"p\"\r\n                            className={`font-medium ${checked ? \"text-blue-900\" : \"text-gray-900\"}`}\r\n                          >\r\n                            No\r\n                          </RadioGroup.Label>\r\n                        </div>\r\n                      </div>\r\n                      {checked && (\r\n                        <div className=\"shrink-0 text-blue-500\">\r\n                          <CheckIcon className=\"h-6 w-6\" />\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  )}\r\n                </RadioGroup.Option>\r\n              </div>\r\n            </RadioGroup>\r\n          </div>\r\n\r\n          {formData.mentor === \"yes\" && (\r\n            <div className=\"mt-4\">\r\n              <TextAreaLimit\r\n                label=\"Briefly describe your experience\"\r\n                name=\"expDesc\"\r\n                placeholder=\"Share your experience with college applications mentoring\"\r\n                value={formData.expDesc}\r\n                rows={8}\r\n                onChange={handleExpDescChange}\r\n                maxLength={400}\r\n                required\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          <NavigationButtons\r\n            currentIndex={selectedMenuIndex || 0}\r\n            onBack={() =>\r\n              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex - 1 : 0)\r\n            }\r\n            onNext={() =>\r\n              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex + 1 : 1)\r\n            }\r\n            disableBack={!selectedMenuIndex || selectedMenuIndex < 1}\r\n            nextLabel={edit ? \"Save Changes\" : \"Next\"}\r\n          />\r\n        </form>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAXA;;;;;;;;;;;AAaA,MAAM,eAAe;IACnB,QAAQ;IACR,SAAS;IACT,aAAa;AACf;AAEA,SAAS,UAAU,KAAU;IAC3B,qBACE,8OAAC;QAAI,SAAQ;QAAY,MAAK;QAAQ,GAAG,KAAK;;0BAC5C,8OAAC;gBAAO,IAAI;gBAAI,IAAI;gBAAI,GAAG;gBAAI,MAAK;gBAAO,SAAQ;;;;;;0BACnD,8OAAC;gBACC,GAAE;gBACF,QAAO;gBACP,aAAa;gBACb,eAAc;gBACd,gBAAe;;;;;;;;;;;;AAIvB;AAEe,SAAS,sBAAsB,EAC5C,oBAAoB,CAAC,EACrB,eAAe,KAAO,CAAC,EACvB,oBAAoB,KAAO,CAAC,EAC5B,cAAc,KAAK,EACnB,OAAO,KAAK,EAIb;IACC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD,EAAE;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,SAAS,6DAAwC,wCAAwC,CAAC;IAEhG,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,4BAA4B;YAChC,aAAa;YAEb,IAAI;gBACF,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,MAAM;oBACR,YAAY;wBACV,QAAQ,KAAK,mBAAmB,GAAG,QAAQ;wBAC3C,SAAS,KAAK,sBAAsB,IAAI;wBACxC,aAAa,KAAK,YAAY;oBAChC;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,kDAAkD;gBAClD,QAAQ,GAAG,CAAC,OAAO,UAAU,MAAM;YACrC,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,iEAAiE;QACjE,IAAI,CAAC,WAAW,eAAe,CAAC,MAAM;YACpC,aAAa,oBAAoB;YACjC;QACF;QAEA,sCAAsC;QACtC,IAAI,CAAC,SAAS,MAAM,EAAE;YACpB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,4CAA4C;QAC5C,IAAI,SAAS,MAAM,KAAK,SAAS,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YACzD,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QAEb,MAAM,UAAU;YACd,qBAAqB,SAAS,MAAM,KAAK;YACzC,wBAAwB,SAAS,MAAM,KAAK,QAAQ,SAAS,OAAO,GAAG;QACzE;QAEA,IAAI;YACF,IAAI,SAAS,WAAW,EAAE;gBACxB,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAAC,QAAQ;YAC9B,OAAO;gBACL,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,QAAQ;YAC/B;YAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,WAAW;YACX,IAAI,CAAC,MAAM;gBACT;gBACA,aAAa,oBAAoB;YACnC;QACF,EAAE,OAAO,OAAO;YACd,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,aAAa;QACb,WAAW;QACX,IAAI,EAAE,MAAM,CAAC,KAAK,KAAK,MAAM;YAC3B,YAAY,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAG,CAAC;QACjD;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,SAAS;YAAM,CAAC;QAClD,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,0BACC,8OAAC,uJAAA,CAAA,UAAc;;;;iCAEf,8OAAC;YAAK,WAAU;YAAQ,UAAU;;8BAChC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAa;;;;;;wBAC1B,CAAC,sBACA,8OAAC,gJAAA,CAAA,aAAU;4BACT,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,aAAa,oBAAoB;sCACjD;;;;;;;;;;;;8BAML,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;sCAAiC;;;;;;sCAGlD,8OAAC,+LAAA,CAAA,aAAU;4BACT,OAAO,SAAS,MAAM;4BACtB,UAAU,CAAC;gCACT,mBAAmB;oCACjB,QAAQ;wCAAE,MAAM;wCAAU;oCAAM;gCAClC;4BACF;4BACA,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,+LAAA,CAAA,aAAU,CAAC,MAAM;wCAChB,OAAM;wCACN,WAAW,CAAC,EAAE,OAAO,EAAE,GACrB,CAAC,yDAAyD,EACxD,UACI,+BACA,oCACJ;kDAGH,CAAC,EAAE,OAAO,EAAE,iBACX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,+LAAA,CAAA,aAAU,CAAC,KAAK;gEACf,IAAG;gEACH,WAAW,CAAC,YAAY,EAAE,UAAU,kBAAkB,iBAAiB;0EACxE;;;;;;;;;;;;;;;;oDAKJ,yBACC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAU,WAAU;;;;;;;;;;;;;;;;;;;;;;kDAO/B,8OAAC,+LAAA,CAAA,aAAU,CAAC,MAAM;wCAChB,OAAM;wCACN,WAAW,CAAC,EAAE,OAAO,EAAE,GACrB,CAAC,yDAAyD,EACxD,UACI,+BACA,oCACJ;kDAGH,CAAC,EAAE,OAAO,EAAE,iBACX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,+LAAA,CAAA,aAAU,CAAC,KAAK;gEACf,IAAG;gEACH,WAAW,CAAC,YAAY,EAAE,UAAU,kBAAkB,iBAAiB;0EACxE;;;;;;;;;;;;;;;;oDAKJ,yBACC,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAU,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAUpC,SAAS,MAAM,KAAK,uBACnB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,sJAAA,CAAA,UAAa;wBACZ,OAAM;wBACN,MAAK;wBACL,aAAY;wBACZ,OAAO,SAAS,OAAO;wBACvB,MAAM;wBACN,UAAU;wBACV,WAAW;wBACX,QAAQ;;;;;;;;;;;8BAKd,8OAAC,uJAAA,CAAA,oBAAiB;oBAChB,cAAc,qBAAqB;oBACnC,QAAQ,IACN,eAAe,oBAAoB,oBAAoB,IAAI;oBAE7D,QAAQ,IACN,eAAe,oBAAoB,oBAAoB,IAAI;oBAE7D,aAAa,CAAC,qBAAqB,oBAAoB;oBACvD,WAAW,OAAO,iBAAiB;;;;;;;;;;;;;;;;;AAM/C"}}, {"offset": {"line": 3916, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3922, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/create/Services.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\n\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { InputField } from \"@components/common/inputField\";\r\nimport { NavigationButtons } from \"@components/common/navigationBtns\";\r\nimport useFormState from \"@hooks/useFormState\";\r\nimport { toast } from \"react-toastify\";\r\nimport LoadingSpinner from \"@components/common/loadingSpinner\";\r\nimport { ProfileCreateProps } from \"@/app/types/counselor/profile\";\r\n\r\nexport default function Services({\r\n  selectedMenuIndex = 0, // Default to 0\r\n  onMenuSelect = () => {}, // Default to a no-op function\r\n  onSectionComplete = () => {}, // Default to a no-op function\r\n  isCompleted = false,\r\n  edit = false,\r\n}: ProfileCreateProps & {\r\n  onSectionComplete?: () => void;\r\n  isCompleted?: boolean;\r\n}) {\r\n  const apiURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/counselor/profile/services`;\r\n\r\n  const { formData, setFormData, handleChange } = useFormState<{\r\n    services: string[];\r\n    pleaseSpecify: string;\r\n  }>({\r\n    services: [],\r\n    pleaseSpecify: \"\",\r\n  });\r\n\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isDirty, setIsDirty] = useState(false);\r\n\r\n  useEffect(() => {\r\n    // Fetch existing data on mount\r\n    const fetchServices = async () => {\r\n      try {\r\n        setIsLoading(true);\r\n        const response = await apiClient.get(apiURL);\r\n        const data = response.data;\r\n\r\n        // Separate \"Specify Other\" values from services\r\n        const services = data || [];\r\n        const otherValue = services.filter((service: string) =>\r\n          service.startsWith(\"Specify:\")\r\n        );\r\n        const other = otherValue.length\r\n          ? otherValue[0].replace(\"Specify: \", \"\")\r\n          : \"\";\r\n\r\n        setFormData({\r\n          services: services.filter(\r\n            (service: string) => !service.startsWith(\"Specify:\")\r\n          ),\r\n          pleaseSpecify: other,\r\n        });\r\n      } catch (error: any) {\r\n        console.log(error?.response?.data?.detail);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchServices();\r\n  }, [setFormData]);\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // If form hasn't changed and section is completed, just navigate\r\n    if (!isDirty && isCompleted && !edit) {\r\n      onMenuSelect(selectedMenuIndex + 1);\r\n      return;\r\n    }\r\n\r\n    if (!formData.services.length) {\r\n      toast.error(\"Please select atleast one service\");\r\n      return;\r\n    }\r\n\r\n    // Combine services and \"Specify Other\" field value\r\n    const payload = {\r\n      services: [\r\n        ...formData.services,\r\n        ...(formData.pleaseSpecify\r\n          ? [`Specify: ${formData.pleaseSpecify}`]\r\n          : []),\r\n      ],\r\n    };\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      await apiClient.post(apiURL, payload); // Adjust with PUT logic if necessary\r\n      if (!edit) {\r\n        onSectionComplete();\r\n        onMenuSelect(selectedMenuIndex + 1);\r\n      }\r\n      setIsDirty(false);\r\n    } catch (error) {\r\n      toast.error(\"Failed to save services.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleServiceSelect = (item: string) => {\r\n    setIsDirty(true);\r\n    setFormData((prevState) => {\r\n      const isSelected = prevState.services.includes(item);\r\n      const updatedServices = isSelected\r\n        ? prevState.services.filter((service) => service !== item) // Remove if already selected\r\n        : [...prevState.services, item]; // Add if not selected\r\n\r\n      return {\r\n        ...prevState,\r\n        services: updatedServices,\r\n        // Clear \"pleaseSpecify\" only when \"Other\" is deselected\r\n        pleaseSpecify:\r\n          item === \"Other\" && isSelected ? \"\" : prevState.pleaseSpecify,\r\n      };\r\n    });\r\n  };\r\n\r\n  const serviceItems = [\r\n    \"Essay Review\",\r\n    \"Personal Statement Guidance\",\r\n    \"University Shortlisting\",\r\n    \"Entire Curricular Profile Building\",\r\n    \"Supplementary Essay Guidance\",\r\n    \"Financial Aid Advice\",\r\n    \"Interview Preparation\",\r\n    \"Other\",\r\n  ];\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {isLoading ? (\r\n        <LoadingSpinner />\r\n      ) : (\r\n        <form className=\"mt-12\" onSubmit={handleSubmit}>\r\n          <h3 className=\"font-black mb-4\">Services</h3>\r\n\r\n          <h5 className=\"text-md font-large text-gray-900\">Select services <span className=\"text-red-500\">*</span></h5>\r\n\r\n          <ul className=\"mt-4 flex flex-wrap gap-2\">\r\n            {serviceItems.map((item) => (\r\n              <li\r\n                key={item}\r\n                onClick={() => handleServiceSelect(item)}\r\n                className={`border px-2 py-3 rounded cursor-pointer ${\r\n                  formData.services.includes(item)\r\n                    ? \"text-blue-500\"\r\n                    : \"text-black\"\r\n                }`}\r\n              >\r\n                {item}\r\n              </li>\r\n            ))}\r\n          </ul>\r\n\r\n          {/* Show InputField only if \"Other\" is clicked */}\r\n          {formData.services.includes(\"Other\") && (\r\n            <div className=\"mt-4\">\r\n              <InputField\r\n                label=\"Specify Others\"\r\n                required={false}\r\n                type=\"text\"\r\n                placeholder=\"Enter here\"\r\n                name=\"pleaseSpecify\"\r\n                value={formData.pleaseSpecify}\r\n                onChange={(e) => {\r\n                  setIsDirty(true);\r\n                  handleChange(e);\r\n                }}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          {/* Navigation buttons */}\r\n          <NavigationButtons\r\n            currentIndex={selectedMenuIndex || 0} // Fallback to 0 if undefined\r\n            onBack={() =>\r\n              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex - 1 : 0)\r\n            }\r\n            onNext={() =>\r\n              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex + 1 : 1)\r\n            }\r\n            disableBack={!selectedMenuIndex || selectedMenuIndex < 1}\r\n            nextLabel={edit ? \"Save Changes\" : \"Next\"}\r\n          />\r\n        </form>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAYe,SAAS,SAAS,EAC/B,oBAAoB,CAAC,EACrB,eAAe,KAAO,CAAC,EACvB,oBAAoB,KAAO,CAAC,EAC5B,cAAc,KAAK,EACnB,OAAO,KAAK,EAIb;IACC,MAAM,SAAS,6DAAwC,2BAA2B,CAAC;IAEnF,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD,EAGxD;QACD,UAAU,EAAE;QACZ,eAAe;IACjB;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,MAAM,gBAAgB;YACpB,IAAI;gBACF,aAAa;gBACb,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,MAAM,OAAO,SAAS,IAAI;gBAE1B,gDAAgD;gBAChD,MAAM,WAAW,QAAQ,EAAE;gBAC3B,MAAM,aAAa,SAAS,MAAM,CAAC,CAAC,UAClC,QAAQ,UAAU,CAAC;gBAErB,MAAM,QAAQ,WAAW,MAAM,GAC3B,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,MACnC;gBAEJ,YAAY;oBACV,UAAU,SAAS,MAAM,CACvB,CAAC,UAAoB,CAAC,QAAQ,UAAU,CAAC;oBAE3C,eAAe;gBACjB;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,GAAG,CAAC,OAAO,UAAU,MAAM;YACrC,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;KAAY;IAEhB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,iEAAiE;QACjE,IAAI,CAAC,WAAW,eAAe,CAAC,MAAM;YACpC,aAAa,oBAAoB;YACjC;QACF;QAEA,IAAI,CAAC,SAAS,QAAQ,CAAC,MAAM,EAAE;YAC7B,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,mDAAmD;QACnD,MAAM,UAAU;YACd,UAAU;mBACL,SAAS,QAAQ;mBAChB,SAAS,aAAa,GACtB;oBAAC,CAAC,SAAS,EAAE,SAAS,aAAa,EAAE;iBAAC,GACtC,EAAE;aACP;QACH;QAEA,aAAa;QACb,IAAI;YACF,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,QAAQ,UAAU,qCAAqC;YAC5E,IAAI,CAAC,MAAM;gBACT;gBACA,aAAa,oBAAoB;YACnC;YACA,WAAW;QACb,EAAE,OAAO,OAAO;YACd,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QACX,YAAY,CAAC;YACX,MAAM,aAAa,UAAU,QAAQ,CAAC,QAAQ,CAAC;YAC/C,MAAM,kBAAkB,aACpB,UAAU,QAAQ,CAAC,MAAM,CAAC,CAAC,UAAY,YAAY,MAAM,6BAA6B;eACtF;mBAAI,UAAU,QAAQ;gBAAE;aAAK,EAAE,sBAAsB;YAEzD,OAAO;gBACL,GAAG,SAAS;gBACZ,UAAU;gBACV,wDAAwD;gBACxD,eACE,SAAS,WAAW,aAAa,KAAK,UAAU,aAAa;YACjE;QACF;IACF;IAEA,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACZ,0BACC,8OAAC,uJAAA,CAAA,UAAc;;;;iCAEf,8OAAC;YAAK,WAAU;YAAQ,UAAU;;8BAChC,8OAAC;oBAAG,WAAU;8BAAkB;;;;;;8BAEhC,8OAAC;oBAAG,WAAU;;wBAAmC;sCAAgB,8OAAC;4BAAK,WAAU;sCAAe;;;;;;;;;;;;8BAEhG,8OAAC;oBAAG,WAAU;8BACX,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;4BAEC,SAAS,IAAM,oBAAoB;4BACnC,WAAW,CAAC,wCAAwC,EAClD,SAAS,QAAQ,CAAC,QAAQ,CAAC,QACvB,kBACA,cACJ;sCAED;2BARI;;;;;;;;;;gBAcV,SAAS,QAAQ,CAAC,QAAQ,CAAC,0BAC1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,mJAAA,CAAA,aAAU;wBACT,OAAM;wBACN,UAAU;wBACV,MAAK;wBACL,aAAY;wBACZ,MAAK;wBACL,OAAO,SAAS,aAAa;wBAC7B,UAAU,CAAC;4BACT,WAAW;4BACX,aAAa;wBACf;;;;;;;;;;;8BAMN,8OAAC,uJAAA,CAAA,oBAAiB;oBAChB,cAAc,qBAAqB;oBACnC,QAAQ,IACN,eAAe,oBAAoB,oBAAoB,IAAI;oBAE7D,QAAQ,IACN,eAAe,oBAAoB,oBAAoB,IAAI;oBAE7D,aAAa,CAAC,qBAAqB,oBAAoB;oBACvD,WAAW,OAAO,iBAAiB;;;;;;;;;;;;;;;;;AAM/C"}}, {"offset": {"line": 4134, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4140, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/dropdown/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from \"react\";\r\n\r\ninterface DropdownProps {\r\n  label: string;\r\n  name: string;\r\n  options: { value: string | number; label: string }[];\r\n  value: string | number;\r\n  onChange: React.ChangeEventHandler<HTMLSelectElement>;\r\n  required?: boolean;\r\n  className?: string;\r\n  selectStyle?: string;\r\n}\r\n\r\nconst Dropdown: React.FC<DropdownProps> = ({\r\n  label,\r\n  name,\r\n  options,\r\n  value,\r\n  onChange,\r\n  required = false,\r\n  className = \"\",\r\n  selectStyle = \"\",\r\n}) => {\r\n  return (\r\n    <div className={`dropdown relative ${className}`}>\r\n      <label htmlFor={name} className=\"block font-medium mb-2 text-sm\">\r\n        {label} {required && <span>*</span>}\r\n      </label>\r\n      <div className=\"relative\">\r\n        <select\r\n          name={name}\r\n          id={name}\r\n          value={value}\r\n          onChange={onChange}\r\n          required={required}\r\n          className={`appearance-none py-3.5 px-3 pr-10 rounded border border-solid border-slate-200 w-full bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${selectStyle}`}\r\n        >\r\n          {options.map((option) => (\r\n            <option key={option.value} value={option.value}>\r\n              {option.label}\r\n            </option>\r\n          ))}\r\n        </select>\r\n        {/* Custom Down Arrow Icon */}\r\n        <span className=\"absolute inset-y-0 right-4 flex items-center pointer-events-none\">\r\n          <svg\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"h-5 w-5 text-gray-500\"\r\n            viewBox=\"0 0 20 20\"\r\n            fill=\"currentColor\"\r\n          >\r\n            <path\r\n              fillRule=\"evenodd\"\r\n              d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\"\r\n              clipRule=\"evenodd\"\r\n            />\r\n          </svg>\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Dropdown;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAeA,MAAM,WAAoC,CAAC,EACzC,KAAK,EACL,IAAI,EACJ,OAAO,EACP,KAAK,EACL,QAAQ,EACR,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,cAAc,EAAE,EACjB;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW;;0BAC9C,8OAAC;gBAAM,SAAS;gBAAM,WAAU;;oBAC7B;oBAAM;oBAAE,0BAAY,8OAAC;kCAAK;;;;;;;;;;;;0BAE7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,MAAM;wBACN,IAAI;wBACJ,OAAO;wBACP,UAAU;wBACV,UAAU;wBACV,WAAW,CAAC,qKAAqK,EAAE,aAAa;kCAE/L,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;gCAA0B,OAAO,OAAO,KAAK;0CAC3C,OAAO,KAAK;+BADF,OAAO,KAAK;;;;;;;;;;kCAM7B,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BACC,OAAM;4BACN,WAAU;4BACV,SAAQ;4BACR,MAAK;sCAEL,cAAA,8OAAC;gCACC,UAAS;gCACT,GAAE;gCACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvB;uCAEe"}}, {"offset": {"line": 4232, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4238, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/create/Commitment.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\n\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport Dropdown from \"@components/common/dropdown\";\r\nimport { NavigationButtons } from \"@components/common/navigationBtns\";\r\nimport useFormState from \"@hooks/useFormState\";\r\nimport { toast } from \"react-toastify\";\r\nimport LoadingSpinner from \"@components/common/loadingSpinner\";\r\nimport { ProfileCreateProps } from \"@/app/types/counselor/profile\";\r\nimport { InputField } from \"@/app/components/common/inputField\";\r\nimport { MainButton } from \"@/app/components/common/mainBtn\";\r\n\r\nconst hoursPerWeekOptions = [\r\n  { label: \"Please select\", value: \"\" },\r\n  { label: \"1-5 hours\", value: \"1-5\" },\r\n  { label: \"6-10 hours\", value: \"6-10\" },\r\n  { label: \"11-20 hours\", value: \"11-20\" },\r\n  { label: \"21-30 hours\", value: \"21-30\" },\r\n  { label: \"31-40 hours\", value: \"31-40\" },\r\n  { label: \"40+ hours\", value: \"40+\" },\r\n];\r\n\r\nconst pricePerHourOptions = [\r\n  { label: \"Please select\", value: \"\" },\r\n  { label: \"30\", value: 30 },\r\n  { label: \"40\", value: 40 },\r\n  { label: \"50\", value: 50 },\r\n  { label: \"60\", value: 60 },\r\n];\r\n\r\ninterface CommitmentData {\r\n  hours_per_week: string;\r\n  hourly_rate: number;\r\n  counselor_id: number;\r\n}\r\n\r\nconst initialState: CommitmentData = {\r\n  hours_per_week: \"\",\r\n  hourly_rate: 0,\r\n  counselor_id: 0,\r\n};\r\n\r\nexport default function CommitmentPricing({\r\n  selectedMenuIndex = 0, // Default to 0\r\n  onMenuSelect = () => {}, // Default to a no-op function\r\n  onSectionComplete = () => {},\r\n  isCompleted = false,\r\n  edit = false,\r\n}: ProfileCreateProps & {\r\n  onSectionComplete?: () => void;\r\n  isCompleted?: boolean;\r\n}) {\r\n  const [formData, setFormData] = useState<CommitmentData>(initialState);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isDirty, setIsDirty] = useState(false);\r\n\r\n  const apiURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/counselor/profile/commitment`;\r\n\r\n  useEffect(() => {\r\n    const fetchCommitmentData = async () => {\r\n      setIsLoading(true);\r\n\r\n      try {\r\n        const { data } = await apiClient.get(apiURL);\r\n\r\n        setFormData({\r\n          hours_per_week: data.hours_per_week || \"\",\r\n          hourly_rate: data.hourly_rate || 0,\r\n          counselor_id: data.counselor_id || 0,\r\n        });\r\n      } catch (error: any) {\r\n        console.log(error.response.data.detail);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchCommitmentData();\r\n  }, []);\r\n\r\n  const handleFieldChange = (\r\n    field: keyof CommitmentData,\r\n    value: string | number\r\n  ) => {\r\n    setIsDirty(true);\r\n    setFormData((prev) => ({\r\n      ...prev,\r\n      [field]: value,\r\n    }));\r\n  };\r\n\r\n  const validateForm = () => {\r\n    if (!formData.hours_per_week || !formData.hourly_rate) {\r\n      toast.error(\"Please fill in all required fields\");\r\n      return false;\r\n    }\r\n    return true;\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // If form hasn't changed and section is completed, just navigate\r\n    if (!isDirty && isCompleted && !edit) {\r\n      onMenuSelect(selectedMenuIndex + 1);\r\n      return;\r\n    }\r\n\r\n    if (!validateForm()) {\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    const payload = {\r\n      hours_per_week: formData.hours_per_week,\r\n      hourly_rate: formData.hourly_rate,\r\n    };\r\n\r\n    try {\r\n      // Check for counselor_id to determine POST or PUT\r\n      if (formData.counselor_id) {\r\n        await apiClient.put(apiURL, payload);\r\n      } else {\r\n        await apiClient.post(apiURL, payload);\r\n      }\r\n\r\n      toast.success(\"Commitment details saved successfully!\");\r\n      setIsDirty(false);\r\n      if (!edit) {\r\n        onSectionComplete();\r\n        onMenuSelect(selectedMenuIndex + 1);\r\n      }\r\n    } catch (error: any) {\r\n      toast.error(error.response?.data?.detail || \"An error occurred\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      {isLoading ? (\r\n        <LoadingSpinner />\r\n      ) : (\r\n        <form className=\"mt-12\" onSubmit={handleSubmit}>\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"font-black\">Commitment & Pricing</h3>\r\n            {!edit && (\r\n              <MainButton\r\n                variant=\"secondary\"\r\n                type=\"button\"\r\n                onClick={() => onMenuSelect(selectedMenuIndex + 1)}\r\n              >\r\n                Skip this step →\r\n              </MainButton>\r\n            )}\r\n          </div>\r\n\r\n          <div className=\"mt-4\">\r\n            <Dropdown\r\n              label=\"How many hours can you commit per week?\"\r\n              name=\"hours_per_week\"\r\n              options={hoursPerWeekOptions}\r\n              value={formData.hours_per_week}\r\n              onChange={(e) =>\r\n                handleFieldChange(\"hours_per_week\", e.target.value)\r\n              }\r\n              selectStyle=\"w-full border p-3\"\r\n            />\r\n          </div>\r\n\r\n          <div className=\"mt-4\">\r\n            <InputField\r\n              label=\"Expected price per hour ($)\"\r\n              type=\"number\"\r\n              placeholder=\"Enter your hourly rate\"\r\n              name=\"hourly_rate\"\r\n              value={formData.hourly_rate === 0 ? \"\" : formData.hourly_rate}\r\n              onChange={(e) =>\r\n                handleFieldChange(\"hourly_rate\", e.target.valueAsNumber)\r\n              }\r\n              min={1}\r\n              step={1}\r\n            />\r\n            <p className=\"text-sm text-gray-500 mt-1\">\r\n              Enter a whole number greater than 0\r\n            </p>\r\n          </div>\r\n\r\n          <NavigationButtons\r\n            currentIndex={selectedMenuIndex || 0}\r\n            onBack={() =>\r\n              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex - 1 : 0)\r\n            }\r\n            onNext={() =>\r\n              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex + 1 : 1)\r\n            }\r\n            disableBack={!selectedMenuIndex || selectedMenuIndex < 1}\r\n            nextLabel={edit ? \"Save Changes\" : \"Next\"}\r\n          />\r\n        </form>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAZA;;;;;;;;;;AAcA,MAAM,sBAAsB;IAC1B;QAAE,OAAO;QAAiB,OAAO;IAAG;IACpC;QAAE,OAAO;QAAa,OAAO;IAAM;IACnC;QAAE,OAAO;QAAc,OAAO;IAAO;IACrC;QAAE,OAAO;QAAe,OAAO;IAAQ;IACvC;QAAE,OAAO;QAAe,OAAO;IAAQ;IACvC;QAAE,OAAO;QAAe,OAAO;IAAQ;IACvC;QAAE,OAAO;QAAa,OAAO;IAAM;CACpC;AAED,MAAM,sBAAsB;IAC1B;QAAE,OAAO;QAAiB,OAAO;IAAG;IACpC;QAAE,OAAO;QAAM,OAAO;IAAG;IACzB;QAAE,OAAO;QAAM,OAAO;IAAG;IACzB;QAAE,OAAO;QAAM,OAAO;IAAG;IACzB;QAAE,OAAO;QAAM,OAAO;IAAG;CAC1B;AAQD,MAAM,eAA+B;IACnC,gBAAgB;IAChB,aAAa;IACb,cAAc;AAChB;AAEe,SAAS,kBAAkB,EACxC,oBAAoB,CAAC,EACrB,eAAe,KAAO,CAAC,EACvB,oBAAoB,KAAO,CAAC,EAC5B,cAAc,KAAK,EACnB,OAAO,KAAK,EAIb;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,SAAS,6DAAwC,6BAA6B,CAAC;IAErF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,aAAa;YAEb,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBAErC,YAAY;oBACV,gBAAgB,KAAK,cAAc,IAAI;oBACvC,aAAa,KAAK,WAAW,IAAI;oBACjC,cAAc,KAAK,YAAY,IAAI;gBACrC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM;YACxC,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CACxB,OACA;QAEA,WAAW;QACX,YAAY,CAAC,OAAS,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,SAAS,cAAc,IAAI,CAAC,SAAS,WAAW,EAAE;YACrD,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,iEAAiE;QACjE,IAAI,CAAC,WAAW,eAAe,CAAC,MAAM;YACpC,aAAa,oBAAoB;YACjC;QACF;QAEA,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,aAAa;QAEb,MAAM,UAAU;YACd,gBAAgB,SAAS,cAAc;YACvC,aAAa,SAAS,WAAW;QACnC;QAEA,IAAI;YACF,kDAAkD;YAClD,IAAI,SAAS,YAAY,EAAE;gBACzB,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAAC,QAAQ;YAC9B,OAAO;gBACL,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,QAAQ;YAC/B;YAEA,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,WAAW;YACX,IAAI,CAAC,MAAM;gBACT;gBACA,aAAa,oBAAoB;YACnC;QACF,EAAE,OAAO,OAAY;YACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,EAAE,MAAM,UAAU;QAC9C,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,0BACC,8OAAC,uJAAA,CAAA,UAAc;;;;iCAEf,8OAAC;YAAK,WAAU;YAAQ,UAAU;;8BAChC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAa;;;;;;wBAC1B,CAAC,sBACA,8OAAC,gJAAA,CAAA,aAAU;4BACT,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,aAAa,oBAAoB;sCACjD;;;;;;;;;;;;8BAML,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,iJAAA,CAAA,UAAQ;wBACP,OAAM;wBACN,MAAK;wBACL,SAAS;wBACT,OAAO,SAAS,cAAc;wBAC9B,UAAU,CAAC,IACT,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;wBAEpD,aAAY;;;;;;;;;;;8BAIhB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,mJAAA,CAAA,aAAU;4BACT,OAAM;4BACN,MAAK;4BACL,aAAY;4BACZ,MAAK;4BACL,OAAO,SAAS,WAAW,KAAK,IAAI,KAAK,SAAS,WAAW;4BAC7D,UAAU,CAAC,IACT,kBAAkB,eAAe,EAAE,MAAM,CAAC,aAAa;4BAEzD,KAAK;4BACL,MAAM;;;;;;sCAER,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,8OAAC,uJAAA,CAAA,oBAAiB;oBAChB,cAAc,qBAAqB;oBACnC,QAAQ,IACN,eAAe,oBAAoB,oBAAoB,IAAI;oBAE7D,QAAQ,IACN,eAAe,oBAAoB,oBAAoB,IAAI;oBAE7D,aAAa,CAAC,qBAAqB,oBAAoB;oBACvD,WAAW,OAAO,iBAAiB;;;;;;;;;;;;;;;;;AAM/C"}}, {"offset": {"line": 4498, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4504, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 4514, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4520, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/create/SupportingDocument.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useRef, useEffect } from \"react\";\r\n\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport axios from \"axios\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faTrash,\r\n  faUpload,\r\n  faDownload,\r\n  faEye,\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport LoadingSpinner from \"@components/common/loadingSpinner\";\r\nimport { NavigationButtons } from \"@components/common/navigationBtns\";\r\nimport { MainButton } from \"@/app/components/common/mainBtn\";\r\nimport useFormState from \"@hooks/useFormState\";\r\nimport { toast } from \"react-toastify\";\r\nimport { ProfileCreateProps } from \"@/app/types/counselor/profile\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst acceptedFileTypes = [\r\n  \"application/pdf\",\r\n  \"application/msword\",\r\n  \"application/vnd.openxmlformats-officedocument.wordprocessingml.document\",\r\n  \"application/vnd.ms-excel\",\r\n  \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\",\r\n];\r\n\r\n//////////////////////////////// MAIN COMPONENT /////////////////////////////////\r\nexport default function SupportingDocuments({\r\n  selectedMenuIndex = 0, // Default to 0\r\n  onMenuSelect = () => {}, // Default to a no-op function\r\n  onSectionComplete = () => {}, // Default to a no-op function\r\n  isCompleted = false, // Default to false\r\n  edit = false,\r\n}: ProfileCreateProps & {\r\n  onSectionComplete?: () => void;\r\n  isCompleted?: boolean;\r\n}) {\r\n  const { formData, setFormData } = useFormState({\r\n    files: [] as File[],\r\n  });\r\n\r\n  const [dragging, setDragging] = useState(false);\r\n  const fileInputRef = useRef<HTMLInputElement | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isDirty, setIsDirty] = useState(false);\r\n\r\n  const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL;\r\n  const token = localStorage.getItem(\"access_token\");\r\n\r\n  // Fetch existing files on mount\r\n  useEffect(() => {\r\n    const fetchFiles = async () => {\r\n      setIsLoading(true);\r\n\r\n      try {\r\n        getLatestFiles();\r\n      } catch (error: any) {\r\n        console.log(error?.response?.data?.detail);\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchFiles();\r\n  }, [setFormData]);\r\n\r\n  async function getLatestFiles() {\r\n    const response = await apiClient.get(\r\n      `${baseURL}/counselor/profile/documents`\r\n    );\r\n\r\n    setFormData({ files: response.data || [] });\r\n  }\r\n\r\n  const handleFileUpload = async (files: FileList | File[]) => {\r\n    const validFiles = Array.from(files).filter((file) =>\r\n      acceptedFileTypes.includes(file.type)\r\n    );\r\n\r\n    const invalidFiles = Array.from(files).filter(\r\n      (file) => !acceptedFileTypes.includes(file.type)\r\n    );\r\n\r\n    if (invalidFiles.length > 0) {\r\n      toast.error(\r\n        \"Some files are invalid. Please upload PDF, Word, or Excel files only.\"\r\n      );\r\n    }\r\n\r\n    setIsLoading(true);\r\n    setIsDirty(true);\r\n\r\n    // Upload each valid file separately\r\n    for (const file of validFiles) {\r\n      try {\r\n        const formData = new FormData();\r\n        formData.append(\"file\", file);\r\n\r\n        await axios.post(\r\n          `${baseURL}/counselor/profile/documents/upload`,\r\n          formData,\r\n          {\r\n            headers: {\r\n              Authorization: `Bearer ${token}`,\r\n            },\r\n          }\r\n        );\r\n\r\n        toast.success(`${file.name} uploaded successfully`);\r\n      } catch (error: any) {\r\n        toast.error(\r\n          `Failed to upload ${file.name}: ${\r\n            error?.response?.data?.detail || \"Unknown error\"\r\n          }`\r\n        );\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    }\r\n\r\n    // Fetch updated file list after all uploads\r\n    getLatestFiles();\r\n  };\r\n\r\n  const handleDrop = async (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setDragging(false);\r\n\r\n    const files = e.dataTransfer.files;\r\n    if (files) {\r\n      await handleFileUpload(files);\r\n    }\r\n  };\r\n\r\n  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const files = e.target.files;\r\n\r\n    if (files) {\r\n      await handleFileUpload(files);\r\n    }\r\n  };\r\n\r\n  const handleDeleteFile = async (fileId: string) => {\r\n    setIsDirty(true);\r\n    try {\r\n      await apiClient.delete(\r\n        `${baseURL}/counselor/profile/documents/${fileId}`\r\n      );\r\n\r\n      // Fetch updated file list\r\n      getLatestFiles();\r\n    } catch (error) {\r\n      toast.error(\"Failed to delete the file. Please try again.\");\r\n    }\r\n  };\r\n\r\n  const handleDragOver = (e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    setDragging(true);\r\n  };\r\n\r\n  const handleDragLeave = () => {\r\n    setDragging(false);\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n\r\n    // If form hasn't changed and section is completed, just navigate\r\n    if (!isDirty && isCompleted && !edit) {\r\n      onMenuSelect(selectedMenuIndex + 1);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      // Since documents are uploaded immediately, we just need to verify and proceed\r\n      setIsDirty(false);\r\n      if (!edit) {\r\n        onSectionComplete();\r\n        onMenuSelect(selectedMenuIndex + 1);\r\n      }\r\n    } catch (error) {\r\n      toast.error(\"Failed to submit\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleClickUpload = () => {\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.click();\r\n    }\r\n  };\r\n\r\n  const handleDownloadFile = (file: any) => {\r\n    if (file?.document_url) {\r\n      const anchor = document.createElement(\"a\");\r\n      anchor.href = file.file_url;\r\n      anchor.download = file.document_name || \"download\";\r\n      anchor.click();\r\n    } else {\r\n      toast.error(\"File URL not available for download.\");\r\n    }\r\n  };\r\n\r\n  ///////////////////////////////////// JSX /////////////////////////////\r\n  return (\r\n    <div className=\"relative\">\r\n      {isLoading ? (\r\n        <LoadingSpinner />\r\n      ) : (\r\n        <form className=\"mt-12\" onSubmit={handleSubmit}>\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"font-black\">Supporting Documents</h3>\r\n            {!edit && (\r\n              <MainButton\r\n                variant=\"secondary\"\r\n                type=\"button\"\r\n                onClick={() => onMenuSelect(selectedMenuIndex + 1)}\r\n              >\r\n                Skip this step →\r\n              </MainButton>\r\n            )}\r\n          </div>\r\n          <p className=\"text-gray-600 mb-6\">\r\n            Feel free to add your resume or any other documents to support your\r\n            case\r\n          </p>\r\n\r\n          <div\r\n            className={`border-2 border-dashed p-6 rounded-md text-center bg-[#EFF0F7] ${\r\n              dragging ? \"border-blue-500\" : \"border-gray-300\"\r\n            } transition-all`}\r\n            onDragOver={handleDragOver}\r\n            onDragLeave={handleDragLeave}\r\n            onDrop={handleDrop}\r\n            onClick={handleClickUpload}\r\n          >\r\n            <FontAwesomeIcon\r\n              icon={faUpload}\r\n              className=\"text-blue-500 text-3xl mb-2\"\r\n            />\r\n            <p className=\"text-gray-500\">\r\n              Click to upload or drag and drop files here\r\n            </p>\r\n            <p className=\"text-sm text-gray-400\">\r\n              Accepted file types: PDF, Word, Excel\r\n            </p>\r\n            <input\r\n              ref={fileInputRef}\r\n              type=\"file\"\r\n              accept=\".pdf,.doc,.docx,.xls,.xlsx\"\r\n              className=\"hidden\"\r\n              multiple\r\n              onChange={handleFileChange}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"mt-6\">\r\n            <h5>Selected Files:</h5>\r\n            <ul className=\"mt-3 grid lg:grid-cols-2 gap-2\">\r\n              {formData.files.length === 0 ? (\r\n                <li>No files selected</li>\r\n              ) : (\r\n                formData.files.map((file: any, index) => (\r\n                  <li\r\n                    key={index}\r\n                    className=\"flex items-center justify-between gap-4 bg-[#EFF0F7] p-3\"\r\n                  >\r\n                    <span className=\"text-gray-700\">{file.document_name}</span>\r\n                    <div className=\"whitespace-nowrap\">\r\n                      <button\r\n                        title=\"View\"\r\n                        type=\"button\"\r\n                        onClick={() => {\r\n                          window.open(file.document_url, \"_blank\"); // Open document in a new tab\r\n                        }}\r\n                        className=\"text-blue-500 hover:text-blue-700 mr-3\"\r\n                      >\r\n                        <FontAwesomeIcon icon={faEye} />{\" \"}\r\n                      </button>\r\n                      <button\r\n                        title=\"Download\"\r\n                        type=\"button\"\r\n                        onClick={() => handleDownloadFile(file)}\r\n                        className=\"text-blue-500 hover:text-blue-700 mr-3\"\r\n                      >\r\n                        <FontAwesomeIcon icon={faDownload} />\r\n                      </button>\r\n                      <button\r\n                        title=\"Remove\"\r\n                        type=\"button\"\r\n                        onClick={() => handleDeleteFile(file.id)}\r\n                        className=\"text-red-500 hover:text-red-700\"\r\n                      >\r\n                        <FontAwesomeIcon icon={faTrash} />\r\n                      </button>\r\n                    </div>\r\n                  </li>\r\n                ))\r\n              )}\r\n            </ul>\r\n          </div>\r\n\r\n          <div className={cn(\"flex justify-end mt-8\", edit && \"hidden\")}>\r\n            <NavigationButtons\r\n              currentIndex={selectedMenuIndex || 0}\r\n              onBack={() =>\r\n                onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex - 1 : 0)\r\n              }\r\n              onNext={() =>\r\n                onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex + 1 : 1)\r\n              }\r\n              disableBack={!selectedMenuIndex || selectedMenuIndex < 1}\r\n              nextLabel={edit ? \"Save Changes\" : \"Next\"}\r\n            />\r\n          </div>\r\n        </form>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AAOA;AACA;AACA;AACA;AACA;AAEA;AAdA;AAEA;AAPA;;;;;;;;;;;;;AAqBA,MAAM,oBAAoB;IACxB;IACA;IACA;IACA;IACA;CACD;AAGc,SAAS,oBAAoB,EAC1C,oBAAoB,CAAC,EACrB,eAAe,KAAO,CAAC,EACvB,oBAAoB,KAAO,CAAC,EAC5B,cAAc,KAAK,EACnB,OAAO,KAAK,EAIb;IACC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,UAAY,AAAD,EAAE;QAC7C,OAAO,EAAE;IACX;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM;IACN,MAAM,QAAQ,aAAa,OAAO,CAAC;IAEnC,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,aAAa;YAEb,IAAI;gBACF;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,GAAG,CAAC,OAAO,UAAU,MAAM;YACrC,SAAU;gBACR,aAAa;YACf;QACF;QAEA;IACF,GAAG;QAAC;KAAY;IAEhB,eAAe;QACb,MAAM,WAAW,MAAM,gHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,GAAG,QAAQ,4BAA4B,CAAC;QAG1C,YAAY;YAAE,OAAO,SAAS,IAAI,IAAI,EAAE;QAAC;IAC3C;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,aAAa,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,CAAC,OAC3C,kBAAkB,QAAQ,CAAC,KAAK,IAAI;QAGtC,MAAM,eAAe,MAAM,IAAI,CAAC,OAAO,MAAM,CAC3C,CAAC,OAAS,CAAC,kBAAkB,QAAQ,CAAC,KAAK,IAAI;QAGjD,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT;QAEJ;QAEA,aAAa;QACb,WAAW;QAEX,oCAAoC;QACpC,KAAK,MAAM,QAAQ,WAAY;YAC7B,IAAI;gBACF,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBAExB,MAAM,qIAAA,CAAA,UAAK,CAAC,IAAI,CACd,GAAG,QAAQ,mCAAmC,CAAC,EAC/C,UACA;oBACE,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,OAAO;oBAClC;gBACF;gBAGF,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,KAAK,IAAI,CAAC,sBAAsB,CAAC;YACpD,EAAE,OAAO,OAAY;gBACnB,mJAAA,CAAA,QAAK,CAAC,KAAK,CACT,CAAC,iBAAiB,EAAE,KAAK,IAAI,CAAC,EAAE,EAC9B,OAAO,UAAU,MAAM,UAAU,iBACjC;YAEN,SAAU;gBACR,aAAa;YACf;QACF;QAEA,4CAA4C;QAC5C;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,EAAE,cAAc;QAChB,YAAY;QAEZ,MAAM,QAAQ,EAAE,YAAY,CAAC,KAAK;QAClC,IAAI,OAAO;YACT,MAAM,iBAAiB;QACzB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAE5B,IAAI,OAAO;YACT,MAAM,iBAAiB;QACzB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,WAAW;QACX,IAAI;YACF,MAAM,gHAAA,CAAA,UAAS,CAAC,MAAM,CACpB,GAAG,QAAQ,6BAA6B,EAAE,QAAQ;YAGpD,0BAA0B;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,MAAM,kBAAkB;QACtB,YAAY;IACd;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,iEAAiE;QACjE,IAAI,CAAC,WAAW,eAAe,CAAC,MAAM;YACpC,aAAa,oBAAoB;YACjC;QACF;QAEA,aAAa;QACb,IAAI;YACF,+EAA+E;YAC/E,WAAW;YACX,IAAI,CAAC,MAAM;gBACT;gBACA,aAAa,oBAAoB;YACnC;QACF,EAAE,OAAO,OAAO;YACd,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK;QAC5B;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,MAAM,cAAc;YACtB,MAAM,SAAS,SAAS,aAAa,CAAC;YACtC,OAAO,IAAI,GAAG,KAAK,QAAQ;YAC3B,OAAO,QAAQ,GAAG,KAAK,aAAa,IAAI;YACxC,OAAO,KAAK;QACd,OAAO;YACL,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,uEAAuE;IACvE,qBACE,8OAAC;QAAI,WAAU;kBACZ,0BACC,8OAAC,uJAAA,CAAA,UAAc;;;;iCAEf,8OAAC;YAAK,WAAU;YAAQ,UAAU;;8BAChC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAa;;;;;;wBAC1B,CAAC,sBACA,8OAAC,gJAAA,CAAA,aAAU;4BACT,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,aAAa,oBAAoB;sCACjD;;;;;;;;;;;;8BAKL,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAKlC,8OAAC;oBACC,WAAW,CAAC,+DAA+D,EACzE,WAAW,oBAAoB,kBAChC,eAAe,CAAC;oBACjB,YAAY;oBACZ,aAAa;oBACb,QAAQ;oBACR,SAAS;;sCAET,8OAAC,oKAAA,CAAA,kBAAe;4BACd,MAAM,wKAAA,CAAA,WAAQ;4BACd,WAAU;;;;;;sCAEZ,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;sCAG7B,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAGrC,8OAAC;4BACC,KAAK;4BACL,MAAK;4BACL,QAAO;4BACP,WAAU;4BACV,QAAQ;4BACR,UAAU;;;;;;;;;;;;8BAId,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAG;;;;;;sCACJ,8OAAC;4BAAG,WAAU;sCACX,SAAS,KAAK,CAAC,MAAM,KAAK,kBACzB,8OAAC;0CAAG;;;;;uCAEJ,SAAS,KAAK,CAAC,GAAG,CAAC,CAAC,MAAW,sBAC7B,8OAAC;oCAEC,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAiB,KAAK,aAAa;;;;;;sDACnD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,OAAM;oDACN,MAAK;oDACL,SAAS;wDACP,OAAO,IAAI,CAAC,KAAK,YAAY,EAAE,WAAW,6BAA6B;oDACzE;oDACA,WAAU;;sEAEV,8OAAC,oKAAA,CAAA,kBAAe;4DAAC,MAAM,wKAAA,CAAA,QAAK;;;;;;wDAAK;;;;;;;8DAEnC,8OAAC;oDACC,OAAM;oDACN,MAAK;oDACL,SAAS,IAAM,mBAAmB;oDAClC,WAAU;8DAEV,cAAA,8OAAC,oKAAA,CAAA,kBAAe;wDAAC,MAAM,wKAAA,CAAA,aAAU;;;;;;;;;;;8DAEnC,8OAAC;oDACC,OAAM;oDACN,MAAK;oDACL,SAAS,IAAM,iBAAiB,KAAK,EAAE;oDACvC,WAAU;8DAEV,cAAA,8OAAC,oKAAA,CAAA,kBAAe;wDAAC,MAAM,wKAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;mCA7B7B;;;;;;;;;;;;;;;;8BAsCf,8OAAC;oBAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB,QAAQ;8BAClD,cAAA,8OAAC,uJAAA,CAAA,oBAAiB;wBAChB,cAAc,qBAAqB;wBACnC,QAAQ,IACN,eAAe,oBAAoB,oBAAoB,IAAI;wBAE7D,QAAQ,IACN,eAAe,oBAAoB,oBAAoB,IAAI;wBAE7D,aAAa,CAAC,qBAAqB,oBAAoB;wBACvD,WAAW,OAAO,iBAAiB;;;;;;;;;;;;;;;;;;;;;;AAOjD"}}, {"offset": {"line": 4918, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4924, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/create/FinalCTA.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Image from \"next/image\";\r\nimport { useState } from \"react\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport LoadingSpinner from \"@components/common/loadingSpinner\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\n\r\nconst CompletionMessage = () => (\r\n  <div className=\"min-h-[80vh] flex items-center justify-center p-4 sm:p-6 md:p-8 bg-gray-50\">\r\n    <div className=\"max-w-2xl w-full bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all animate-fadeIn\">\r\n      {/* Top Section with Gradient */}\r\n      <div className=\"relative bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-12 text-center\">\r\n        {/* Decorative top border */}\r\n        <div className=\"absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#2F80ED] to-blue-400\" />\r\n        \r\n        {/* Success Icon and Title */}\r\n        <div className=\"mb-8\">\r\n          <div className=\"relative w-28 h-28 mx-auto mb-8 transform transition-all duration-500 hover:scale-105\">\r\n            <Image\r\n              src=\"/images/011---Approved-Cleaning.png\"\r\n              alt=\"Success\"\r\n              fill\r\n              className=\"object-contain drop-shadow-md\"\r\n              priority\r\n            />\r\n          </div>\r\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-gray-900 mb-3 tracking-tight\">\r\n            Your profile has been{\" \"}\r\n            <span className=\"text-[#2F80ED] relative\">\r\n              submitted\r\n              <span className=\"absolute bottom-0 left-0 w-full h-1 bg-[#2F80ED]/10\"></span>\r\n            </span>\r\n          </h2>\r\n          <p className=\"text-gray-600 text-lg sm:text-xl font-medium\">\r\n            You have successfully completed the application form\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Content Section */}\r\n      <div className=\"px-6 py-10 sm:px-12 sm:py-12\">\r\n        <div className=\"space-y-8 text-center sm:text-left max-w-xl mx-auto\">\r\n          <h4 className=\"text-2xl font-semibold text-gray-900\">\r\n            Thank you for applying to become a counselor\r\n          </h4>\r\n          <div className=\"prose prose-lg prose-blue max-w-none\">\r\n            <p className=\"text-gray-600 leading-relaxed\">\r\n              Our team will review your application and get back to you within{\" \"}\r\n              <span className=\"font-semibold text-gray-900\">1-3 business days</span>.\r\n              {\" \"}If approved, you'll gain access to your dashboard to start mentoring\r\n              students.\r\n            </p>\r\n            <p className=\"text-gray-600 mt-4\">\r\n              Please feel free to reach out in the meantime if you have any questions.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nexport default function FinalCTA({\r\n  selectedMenuIndex = 0,\r\n  onMenuSelect = () => {},\r\n}: {\r\n  selectedMenuIndex?: number;\r\n  onMenuSelect?: (index: number) => void;\r\n}) {\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n\r\n  // If we're on the profile-complete page, show completion message directly\r\n  if (pathname === \"/counselor/profile-complete\") {\r\n    return <CompletionMessage />;\r\n  }\r\n\r\n  const handleSubmit = async () => {\r\n    setIsSubmitting(true);\r\n    try {\r\n      await apiClient.post(\"/counselor/profile/submit\");\r\n      toast.success(\"Profile submitted successfully!\");\r\n      // Immediately redirect to profile-complete page\r\n      router.push(\"/counselor/profile-complete\");\r\n    } catch (error) {\r\n      toast.error(\"Failed to submit profile. Please try again.\");\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex-1 flex items-center justify-center min-h-[80vh] p-4 sm:p-6 md:p-8 bg-gray-50\">\r\n      <div className=\"w-full max-w-3xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden\">\r\n        {/* Top Section with Gradient */}\r\n        <div className=\"relative bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-10\">\r\n          {/* Decorative top border */}\r\n          <div className=\"absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#2F80ED] to-blue-400\" />\r\n          \r\n          <div className=\"text-center max-w-2xl mx-auto\">\r\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\r\n              Ready to Submit Your Profile?\r\n            </h2>\r\n            <p className=\"text-gray-600 text-lg font-medium\">\r\n              Once you submit your profile, our team will process your application and let you know the next steps within 1-3 business days.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Checklist Section */}\r\n        <div className=\"px-6 sm:px-8 py-10 space-y-8\">\r\n          <div className=\"bg-blue-50 border border-blue-100 p-6 sm:p-8 rounded-xl mx-auto\">\r\n            <h3 className=\"font-semibold text-blue-900 mb-6 text-xl text-center\">Final Checklist</h3>\r\n            <ul className=\"space-y-5 max-w-2xl mx-auto\">\r\n              <li className=\"flex items-start space-x-4\">\r\n                <div className=\"flex-shrink-0 w-6 h-6 mt-1\">\r\n                  <svg className=\"w-6 h-6 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                  </svg>\r\n                </div>\r\n                <span className=\"text-gray-700 text-lg\">Your personal information is accurate and complete</span>\r\n              </li>\r\n              <li className=\"flex items-start space-x-4\">\r\n                <div className=\"flex-shrink-0 w-6 h-6 mt-1\">\r\n                  <svg className=\"w-6 h-6 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                  </svg>\r\n                </div>\r\n                <span className=\"text-gray-700 text-lg\">Your educational background details are verified</span>\r\n              </li>\r\n              <li className=\"flex items-start space-x-4\">\r\n                <div className=\"flex-shrink-0 w-6 h-6 mt-1\">\r\n                  <svg className=\"w-6 h-6 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                  </svg>\r\n                </div>\r\n                <span className=\"text-gray-700 text-lg\">You've reviewed all sections for accuracy</span>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Action Buttons */}\r\n          <div className=\"flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto\">\r\n            <button\r\n              onClick={() => onMenuSelect(selectedMenuIndex - 1)}\r\n              className=\"flex-1 px-6 py-4 border-2 border-gray-200 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors duration-200 text-lg\"\r\n            >\r\n              Review Previous Section\r\n            </button>\r\n            <button\r\n              onClick={handleSubmit}\r\n              disabled={isSubmitting}\r\n              className=\"flex-1 bg-[#2F80ED] px-6 py-4 text-white rounded-xl font-medium transition-all duration-200 relative overflow-hidden group hover:shadow-lg disabled:opacity-70 disabled:cursor-not-allowed text-lg\"\r\n            >\r\n              <div className=\"absolute inset-0 bg-black/10 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-200\" />\r\n              <div className=\"relative flex items-center justify-center space-x-3\">\r\n                {isSubmitting ? (\r\n                  <>\r\n                    <LoadingSpinner />\r\n                    <span>Submitting...</span>\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                    <span>Submit Profile</span>\r\n                  </>\r\n                )}\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,oBAAoB,kBACxB,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;;;;;sCAGf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,QAAQ;;;;;;;;;;;8CAGZ,8OAAC;oCAAG,WAAU;;wCAAmE;wCACzD;sDACtB,8OAAC;4CAAK,WAAU;;gDAA0B;8DAExC,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;;8CAGpB,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;;;;;;;8BAOhE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CAGrD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;4CAAgC;4CACsB;0DACjE,8OAAC;gDAAK,WAAU;0DAA8B;;;;;;4CAAwB;4CACrE;4CAAI;;;;;;;kDAGP,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU/B,SAAS,SAAS,EAC/B,oBAAoB,CAAC,EACrB,eAAe,KAAO,CAAC,EAIxB;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,0EAA0E;IAC1E,IAAI,aAAa,+BAA+B;QAC9C,qBAAO,8OAAC;;;;;IACV;IAEA,MAAM,eAAe;QACnB,gBAAgB;QAChB,IAAI;YACF,MAAM,gHAAA,CAAA,UAAS,CAAC,IAAI,CAAC;YACrB,mJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,gDAAgD;YAChD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,mJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;;;;;sCAEf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;;;;;;;;8BAOrD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuD;;;;;;8CACrE,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAE1C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAE1C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;sCAM9C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,aAAa,oBAAoB;oCAChD,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;sDACZ,6BACC;;kEACE,8OAAC,uJAAA,CAAA,UAAc;;;;;kEACf,8OAAC;kEAAK;;;;;;;6EAGR;;kEACE,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;kEAEvE,8OAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B"}}, {"offset": {"line": 5438, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5444, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/create/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { FC, useState } from \"react\";\r\nimport Image from \"next/image\";\r\nimport PersonalInfo from \"./PersonalInfo\";\r\nimport EducationalBackground from \"./EducationalBackground\";\r\nimport ProfessionalExperience from \"./ProfessionalExperience\";\r\nimport CounsellingExperience from \"./CounsellingExperience\";\r\nimport Services from \"./Services\";\r\nimport Commitment from \"./Commitment\";\r\nimport SupportingDocument from \"./SupportingDocument\";\r\nimport FinalCTA from \"./FinalCTA\";\r\n\r\nexport const OnboardingPage: FC = () => {\r\n  const [selectedMenuIndex, setSelectedMenuIndex] = useState(0);\r\n  const [completedSections, setCompletedSections] = useState<number[]>([]);\r\n\r\n  const handleSectionComplete = (index: number) => {\r\n    if (!completedSections.includes(index)) {\r\n      setCompletedSections(prev => [...prev, index].sort((a, b) => a - b));\r\n    }\r\n  };\r\n\r\n  function menuItemObj(CompName: any) {\r\n    return (\r\n      <CompName\r\n        onMenuSelect={setSelectedMenuIndex}\r\n        selectedMenuIndex={selectedMenuIndex}\r\n        onSectionComplete={() => handleSectionComplete(selectedMenuIndex)}\r\n        isCompleted={completedSections.includes(selectedMenuIndex)}\r\n      />\r\n    );\r\n  }\r\n\r\n  // Define the mapping between menu items and components\r\n  const menuItems = [\r\n    {\r\n      label: \"Personal Information\",\r\n      component: menuItemObj(PersonalInfo),\r\n    },\r\n    {\r\n      label: \"Educational Background\",\r\n      component: menuItemObj(EducationalBackground),\r\n    },\r\n    {\r\n      label: \"Professional Experience\",\r\n      component: menuItemObj(ProfessionalExperience),\r\n    },\r\n    {\r\n      label: \"Counselling Experience\",\r\n      component: menuItemObj(CounsellingExperience),\r\n    },\r\n    {\r\n      label: \"Services you can offer\",\r\n      component: menuItemObj(Services),\r\n    },\r\n    {\r\n      label: \"Commitment & Pricing\",\r\n      component: menuItemObj(Commitment),\r\n    },\r\n    {\r\n      label: \"Supporting Documents\",\r\n      component: menuItemObj(SupportingDocument),\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"onboarding-container bg-white px-3 pb-8\">\r\n      <div className=\"overflow-hidden\">\r\n        <figure className=\"top-bg absolute -top-16 left-0 h-80 w-full\">\r\n          <Image\r\n            src=\"/images/counselor-bg-onboarding.jpg\"\r\n            alt=\"banner\"\r\n            layout=\"fill\"\r\n          />\r\n\r\n          <Image\r\n            width={100}\r\n            height={100}\r\n            src=\"/images/white-logo.png\"\r\n            alt=\"Logo\"\r\n            className=\"transition-all duration-300 absolute top-24 left-7\"\r\n          />\r\n        </figure>\r\n      </div>\r\n\r\n      <div className=\"container relative z-10 mx-auto mt-32\">\r\n        <div className=\"content flex gap-6 justify-between\">\r\n          <div className=\"hidden md:block left-side shadow-md rounded-xl overflow-hidden max-h-min\">\r\n            <ul className=\"bg-white grid gap-8 py-10 pl-5 pr-8 lg:pr-24\">\r\n              {menuItems.map((item, index) => (\r\n                <li\r\n                  key={index}\r\n                  onClick={() => completedSections.includes(index) && setSelectedMenuIndex(index)}\r\n                  className={`flex gap-3 items-center ${\r\n                    completedSections.includes(index) ? \"cursor-pointer hover:opacity-80\" : \"cursor-not-allowed\"\r\n                  } ${\r\n                    index <= selectedMenuIndex\r\n                      ? \"text-[#141519]\"\r\n                      : \"text-[#9EA2B3]\"\r\n                  }`}\r\n                >\r\n                  <span\r\n                    className={`relative rounded-full border h-8 w-8 text-sm flex justify-center items-center ${\r\n                      completedSections.includes(index)\r\n                        ? \"bg-mainClr text-white\"\r\n                        : index === selectedMenuIndex\r\n                        ? \"border-[#141519]\"\r\n                        : \"border-[#9EA2B3]\"\r\n                    }`}\r\n                  >\r\n                    {completedSections.includes(index) ? (\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        className=\"h-4 w-4\"\r\n                        viewBox=\"0 0 20 20\"\r\n                        fill=\"currentColor\"\r\n                      >\r\n                        <path\r\n                          fillRule=\"evenodd\"\r\n                          d=\"M16.707 5.293a1 1 0 00-1.414 0L8 12.586 4.707 9.293a1 1 0 10-1.414 1.414l4 4a1 1 0 001.414 0l8-8a1 1 0 000-1.414z\"\r\n                          clipRule=\"evenodd\"\r\n                        />\r\n                      </svg>\r\n                    ) : (\r\n                      (index + 1).toString().padStart(2, \"0\")\r\n                    )}\r\n                    {menuItems.length > index + 1 && (\r\n                      <span\r\n                        className={`absolute top-full ml-2.5 mt-2 flex justify-center rotate-90 ${\r\n                          completedSections.includes(index)\r\n                            ? \"text-[#141519]\"\r\n                            : \"text-[#9EA2B3]\"\r\n                        }`}\r\n                      >\r\n                        ...... {/* Visual separator */}\r\n                      </span>\r\n                    )}\r\n                  </span>\r\n                  <h5 className=\"transition-all duration-200\">{item.label}</h5>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          {selectedMenuIndex < menuItems.length ? (\r\n            <div className=\"right-side bg-white shadow-md rounded-lg px-5 py-10 lg:px-10 flex-1\">\r\n              <h2 className=\"text-2xl mb-5\">\r\n                Tell us about <span className=\"text-[#2F80ED]\">yourself</span>\r\n              </h2>\r\n              <p className=\"mb-6 text-[#141519]\">\r\n                Please complete the following details to let us know more about\r\n                you. It will take less than 5 minutes!\r\n              </p>\r\n              <hr />\r\n\r\n              {menuItems[selectedMenuIndex]?.component || \"No Component\"}\r\n            </div>\r\n          ) : (\r\n            <FinalCTA\r\n              selectedMenuIndex={selectedMenuIndex}\r\n              onMenuSelect={setSelectedMenuIndex}\r\n            />\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAaO,MAAM,iBAAqB;IAChC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEvE,MAAM,wBAAwB,CAAC;QAC7B,IAAI,CAAC,kBAAkB,QAAQ,CAAC,QAAQ;YACtC,qBAAqB,CAAA,OAAQ;uBAAI;oBAAM;iBAAM,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QACnE;IACF;IAEA,SAAS,YAAY,QAAa;QAChC,qBACE,8OAAC;YACC,cAAc;YACd,mBAAmB;YACnB,mBAAmB,IAAM,sBAAsB;YAC/C,aAAa,kBAAkB,QAAQ,CAAC;;;;;;IAG9C;IAEA,uDAAuD;IACvD,MAAM,YAAY;QAChB;YACE,OAAO;YACP,WAAW,YAAY,oKAAA,CAAA,UAAY;QACrC;QACA;YACE,OAAO;YACP,WAAW,YAAY,6KAAA,CAAA,UAAqB;QAC9C;QACA;YACE,OAAO;YACP,WAAW,YAAY,8KAAA,CAAA,UAAsB;QAC/C;QACA;YACE,OAAO;YACP,WAAW,YAAY,6KAAA,CAAA,UAAqB;QAC9C;QACA;YACE,OAAO;YACP,WAAW,YAAY,gKAAA,CAAA,UAAQ;QACjC;QACA;YACE,OAAO;YACP,WAAW,YAAY,kKAAA,CAAA,UAAU;QACnC;QACA;YACE,OAAO;YACP,WAAW,YAAY,0KAAA,CAAA,UAAkB;QAC3C;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAO;;;;;;sCAGT,8OAAC,6HAAA,CAAA,UAAK;4BACJ,OAAO;4BACP,QAAQ;4BACR,KAAI;4BACJ,KAAI;4BACJ,WAAU;;;;;;;;;;;;;;;;;0BAKhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;wCAEC,SAAS,IAAM,kBAAkB,QAAQ,CAAC,UAAU,qBAAqB;wCACzE,WAAW,CAAC,wBAAwB,EAClC,kBAAkB,QAAQ,CAAC,SAAS,oCAAoC,qBACzE,CAAC,EACA,SAAS,oBACL,mBACA,kBACJ;;0DAEF,8OAAC;gDACC,WAAW,CAAC,8EAA8E,EACxF,kBAAkB,QAAQ,CAAC,SACvB,0BACA,UAAU,oBACV,qBACA,oBACJ;;oDAED,kBAAkB,QAAQ,CAAC,uBAC1B,8OAAC;wDACC,OAAM;wDACN,WAAU;wDACV,SAAQ;wDACR,MAAK;kEAEL,cAAA,8OAAC;4DACC,UAAS;4DACT,GAAE;4DACF,UAAS;;;;;;;;;;+DAIb,CAAC,QAAQ,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;oDAEpC,UAAU,MAAM,GAAG,QAAQ,mBAC1B,8OAAC;wDACC,WAAW,CAAC,4DAA4D,EACtE,kBAAkB,QAAQ,CAAC,SACvB,mBACA,kBACJ;kEACH;;;;;;;;;;;;0DAKL,8OAAC;gDAAG,WAAU;0DAA+B,KAAK,KAAK;;;;;;;uCA/ClD;;;;;;;;;;;;;;;wBAqDZ,oBAAoB,UAAU,MAAM,iBACnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAAgB;sDACd,8OAAC;4CAAK,WAAU;sDAAiB;;;;;;;;;;;;8CAEjD,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;8CAInC,8OAAC;;;;;gCAEA,SAAS,CAAC,kBAAkB,EAAE,aAAa;;;;;;iDAG9C,8OAAC,gKAAA,CAAA,UAAQ;4BACP,mBAAmB;4BACnB,cAAc;;;;;;;;;;;;;;;;;;;;;;;AAO5B"}}, {"offset": {"line": 5702, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}