{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/components/auth/index.module.css"], "sourcesContent": [".layout {\r\n\r\n    display: flex;\r\n\r\n    height: 100vh;\r\n\r\n    flex-direction: column;\r\n\r\n    overflow: hidden\n}\r\n\r\n@media (min-width: 1024px) {\r\n\r\n    .layout {\r\n\r\n        flex-direction: row\n    }\n}\r\n\r\n.formSection {\r\n\r\n    display: flex;\r\n\r\n    width: 100%;\r\n\r\n    flex-direction: column;\r\n\r\n    overflow-y: auto;\r\n\r\n    --tw-bg-opacity: 1;\r\n\r\n    background-color: rgb(245 245 244 / var(--tw-bg-opacity, 1))\n}\r\n\r\n@media (min-width: 1024px) {\r\n\r\n    .formSection {\r\n\r\n        width: 50%\n    }\n}\r\n\r\n.formWrapper {\r\n\r\n    display: flex;\r\n\r\n    align-items: center;\r\n\r\n    justify-content: center\n}\r\n\r\n@media (min-width: 768px) {\r\n\r\n    .formWrapper {\r\n\r\n        margin-top: auto;\r\n\r\n        margin-bottom: auto;\r\n\r\n        margin-left: auto;\r\n\r\n        margin-right: auto\n    }\n}\r\n\r\n.mainContent {\r\n\r\n    margin-left: 0.75rem;\r\n\r\n    margin-right: 0.75rem;\r\n\r\n    width: max-content;\r\n\r\n    border-radius: 0.75rem;\r\n\r\n    --tw-bg-opacity: 1;\r\n\r\n    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\r\n\r\n    padding: 1.5rem\n}\r\n\r\n@media (min-width: 640px) {\r\n\r\n    .mainContent {\r\n\r\n        padding: 2rem\n    }\n}\r\n\r\n.header {\r\n\r\n    padding: 1rem\n}\r\n\r\n@media (min-width: 640px) {\r\n\r\n    .header {\r\n\r\n        padding: 1.5rem\n    }\n}\r\n\r\n.logo {\r\n\r\n    width: 180px\n}\r\n\r\n.title {\r\n\r\n    margin-bottom: 1.5rem;\r\n\r\n    font-size: 1.5rem;\r\n\r\n    line-height: 2rem;\r\n\r\n    font-weight: 500\n}\r\n\r\n@media (min-width: 640px) {\r\n\r\n    .title {\r\n\r\n        font-size: 2.25rem;\r\n\r\n        line-height: 2.5rem\n    }\n}\r\n\r\n.highlight {\r\n\r\n    --tw-text-opacity: 1;\r\n\r\n    color: rgb(59 130 246 / var(--tw-text-opacity, 1))\n}\r\n\r\n.terms {\r\n\r\n    margin-bottom: 1.5rem;\r\n\r\n    font-size: 0.875rem;\r\n\r\n    line-height: 1.25rem;\r\n\r\n    --tw-text-opacity: 1;\r\n\r\n    color: rgb(75 85 99 / var(--tw-text-opacity, 1))\n}\r\n\r\n@media (min-width: 640px) {\r\n\r\n    .terms {\r\n\r\n        font-size: 1rem;\r\n\r\n        line-height: 1.5rem\n    }\n}\r\n\r\n.link {\r\n\r\n    --tw-text-opacity: 1;\r\n\r\n    color: rgb(59 130 246 / var(--tw-text-opacity, 1))\n}\r\n\r\n.link:hover {\r\n\r\n    text-decoration-line: underline\n}\r\n\r\n.divider {\r\n\r\n    margin-top: 1.5rem;\r\n\r\n    margin-bottom: 1.5rem;\r\n\r\n    display: flex;\r\n\r\n    align-items: center;\r\n\r\n    gap: 1rem\n}\r\n\r\n.divider hr {\r\n\r\n    flex: 1 1 0%;\r\n\r\n    --tw-border-opacity: 1;\r\n\r\n    border-color: rgb(229 231 235 / var(--tw-border-opacity, 1))\n}\r\n\r\n.divider span {\r\n\r\n    white-space: nowrap;\r\n\r\n    font-size: 0.75rem;\r\n\r\n    line-height: 1rem;\r\n\r\n    --tw-text-opacity: 1;\r\n\r\n    color: rgb(107 114 128 / var(--tw-text-opacity, 1))\n}\r\n\r\n@media (min-width: 640px) {\r\n\r\n    .divider span {\r\n\r\n        font-size: 0.875rem;\r\n\r\n        line-height: 1.25rem\n    }\n}\r\n\r\n.socialButtons {\r\n\r\n    margin-top: 1.5rem;\r\n\r\n    margin-bottom: 1.5rem;\r\n\r\n    display: flex;\r\n\r\n    width: 100%;\r\n\r\n    flex-direction: column;\r\n\r\n    gap: 0.75rem\n}\r\n\r\n@media (min-width: 640px) {\r\n\r\n    .socialButtons {\r\n\r\n        flex-direction: row\n    }\n}\r\n\r\n.form {\r\n\r\n    display: flex;\r\n\r\n    width: 100%;\r\n\r\n    flex-direction: column;\r\n\r\n    gap: 1rem\n}\r\n\r\n@media (min-width: 640px) {\r\n\r\n    .form {\r\n\r\n        gap: 1.5rem\n    }\n}\r\n\r\n.nameFields {\r\n\r\n    display: flex;\r\n\r\n    flex-wrap: wrap;\r\n\r\n    gap: 1rem\n}\r\n\r\n.emailField {\r\n\r\n    width: 100%\n}\r\n\r\n.formFooter {\r\n\r\n    margin-top: 1.5rem;\r\n\r\n    display: flex;\r\n\r\n    flex-direction: column;\r\n\r\n    align-items: center;\r\n\r\n    justify-content: space-between;\r\n\r\n    gap: 1rem\n}\r\n\r\n@media (min-width: 640px) {\r\n\r\n    .formFooter {\r\n\r\n        flex-direction: row\n    }\n}\r\n\r\n.loginLink {\r\n\r\n    display: flex;\r\n\r\n    flex-direction: row;\r\n\r\n    align-items: center;\r\n\r\n    gap: 0.5rem;\r\n\r\n    font-size: 0.75rem;\r\n\r\n    line-height: 1rem\n}\r\n\r\n@media (min-width: 640px) {\r\n\r\n    .loginLink {\r\n\r\n        font-size: 0.875rem;\r\n\r\n        line-height: 1.25rem\n    }\n}\r\n\r\n.loginLink span {\r\n\r\n    --tw-text-opacity: 1;\r\n\r\n    color: rgb(75 85 99 / var(--tw-text-opacity, 1))\n}\r\n\r\n.loginLink a {\r\n\r\n    white-space: nowrap;\r\n\r\n    --tw-text-opacity: 1;\r\n\r\n    color: rgb(59 130 246 / var(--tw-text-opacity, 1))\n}\r\n\r\n.loginLink a:hover {\r\n\r\n    text-decoration-line: underline\n}\r\n\r\n.imageSection {\r\n\r\n    display: none;\r\n\r\n    --tw-bg-opacity: 1;\r\n\r\n    background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1))\n}\r\n\r\n@media (min-width: 1024px) {\r\n\r\n    .imageSection {\r\n\r\n        display: block;\r\n\r\n        width: 50%\n    }\n}\r\n\r\n.heroImage {\r\n\r\n    height: 100%;\r\n\r\n    width: 100%;\r\n\r\n    object-fit: cover\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;AAWA;EAEI;;;;;AAMJ;;;;;;;;;AAeA;EAEI;;;;;AAMJ;;;;;;AASA;EAEI;;;;;;;;AAYJ;;;;;;;;;;AAiBA;EAEI;;;;;AAMJ;;;;AAKA;EAEI;;;;;AAMJ;;;;AAKA;;;;;;;AAWA;EAEI;;;;;;AAQJ;;;;;AAOA;;;;;;;;AAaA;EAEI;;;;;;AAQJ;;;;;AAOA;;;;AAKA;;;;;;;;AAaA;;;;;;AASA;;;;;;;;AAaA;EAEI;;;;;;AAQJ;;;;;;;;;AAeA;EAEI;;;;;AAMJ;;;;;;;AAWA;EAEI;;;;;AAMJ;;;;;;AASA;;;;AAKA;;;;;;;;;AAeA;EAEI;;;;;AAMJ;;;;;;;;;AAeA;EAEI;;;;;;AAQJ;;;;;AAOA;;;;;;AASA;;;;AAKA;;;;;;AASA;EAEI;;;;;;AAQJ"}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}