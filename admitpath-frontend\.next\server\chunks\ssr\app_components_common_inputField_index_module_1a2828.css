/* [project]/app/components/common/inputField/index.module.css [app-client] (css) */
.index-module__8yU72G__container {
  display: flex;
  min-width: 240px;
  flex: 1;
  flex-shrink: 1;
  flex-basis: 0;
  flex-direction: column;
}

.index-module__8yU72G__label {
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(23 23 23 / var(--tw-text-opacity, 1));
}

.index-module__8yU72G__required {
  --tw-text-opacity: 1;
  color: rgb(244 63 94 / var(--tw-text-opacity, 1));
}

.index-module__8yU72G__input {
  margin-top: .25rem;
  width: 100%;
  border-radius: .25rem;
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: .75rem;
  padding-bottom: .75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -.025em;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.index-module__8yU72G__input:focus {
  outline: 2px solid #0000;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.index-module__8yU72G__input[type="password"] {
  padding-right: 3rem;
}

.index-module__8yU72G__input.index-module__8yU72G__error {
  --tw-border-opacity: 1;
  border-color: rgb(244 63 94 / var(--tw-border-opacity, 1));
}

.index-module__8yU72G__input.index-module__8yU72G__error:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(244 63 94 / var(--tw-ring-opacity, 1));
}

.index-module__8yU72G__errorMessage {
  margin-top: .25rem;
  font-size: .875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(244 63 94 / var(--tw-text-opacity, 1));
}


/*# sourceMappingURL=app_components_common_inputField_index_module_1a2828.css.map*/
