{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/inputField/index.module.css"], "sourcesContent": [".container {\r\n\r\n    display: flex;\r\n\r\n    min-width: 240px;\r\n\r\n    flex: 1 1 0%;\r\n\r\n    flex-shrink: 1;\r\n\r\n    flex-basis: 0px;\r\n\r\n    flex-direction: column\n}\r\n\r\n.label {\r\n\r\n    font-size: 0.875rem;\r\n\r\n    font-weight: 500;\r\n\r\n    line-height: 1.5rem;\r\n\r\n    --tw-text-opacity: 1;\r\n\r\n    color: rgb(23 23 23 / var(--tw-text-opacity, 1))\n}\r\n\r\n.required {\r\n\r\n    --tw-text-opacity: 1;\r\n\r\n    color: rgb(244 63 94 / var(--tw-text-opacity, 1))\n}\r\n\r\n.input {\r\n\r\n    margin-top: 0.25rem;\r\n\r\n    width: 100%;\r\n\r\n    border-radius: 0.25rem;\r\n\r\n    border-width: 1px;\r\n\r\n    border-style: solid;\r\n\r\n    --tw-border-opacity: 1;\r\n\r\n    border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\r\n\r\n    --tw-bg-opacity: 1;\r\n\r\n    background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\r\n\r\n    padding-left: 1rem;\r\n\r\n    padding-right: 1rem;\r\n\r\n    padding-top: 0.75rem;\r\n\r\n    padding-bottom: 0.75rem;\r\n\r\n    font-size: 1rem;\r\n\r\n    line-height: 1.5rem;\r\n\r\n    letter-spacing: -0.025em;\r\n\r\n    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\r\n\r\n    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\r\n\r\n    transition-duration: 150ms\n}\r\n\r\n.input:focus {\r\n\r\n    outline: 2px solid transparent;\r\n\r\n    outline-offset: 2px;\r\n\r\n    --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\r\n\r\n    --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\r\n\r\n    box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\r\n\r\n    --tw-ring-opacity: 1;\r\n\r\n    --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1))\n}\r\n\r\n.input[type=\"password\"] {\r\n\r\n    padding-right: 3rem\n}\r\n\r\n.input.error {\r\n\r\n    --tw-border-opacity: 1;\r\n\r\n    border-color: rgb(244 63 94 / var(--tw-border-opacity, 1))\n}\r\n\r\n.input.error:focus {\r\n\r\n    --tw-ring-opacity: 1;\r\n\r\n    --tw-ring-color: rgb(244 63 94 / var(--tw-ring-opacity, 1))\n}\r\n\r\n.errorMessage {\r\n\r\n    margin-top: 0.25rem;\r\n\r\n    font-size: 0.875rem;\r\n\r\n    line-height: 1.25rem;\r\n\r\n    --tw-text-opacity: 1;\r\n\r\n    color: rgb(244 63 94 / var(--tw-text-opacity, 1))\n}"], "names": [], "mappings": "AAAA;;;;;;;;;AAeA;;;;;;;;AAaA;;;;;AAOA;;;;;;;;;;;;;;;;;;;;;;AAyCA;;;;;;;;;;AAiBA;;;;AAKA;;;;;AAOA;;;;;AAOA"}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}