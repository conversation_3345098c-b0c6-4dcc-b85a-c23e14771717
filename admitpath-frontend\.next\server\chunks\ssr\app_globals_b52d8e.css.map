{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/app/globals.css"], "sourcesContent": ["*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.16 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  tab-size: 4; /* 3 */\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\r\n    :root {\r\n        --background: 0 0% 100%;\r\n        --foreground: 0 0% 3.9%;\r\n        --card: 0 0% 100%;\r\n        --card-foreground: 0 0% 3.9%;\r\n        --popover: 0 0% 100%;\r\n        --popover-foreground: 0 0% 3.9%;\r\n        --primary: 0 0% 9%;\r\n        --primary-foreground: 0 0% 98%;\r\n        --secondary: 0 0% 96.1%;\r\n        --secondary-foreground: 0 0% 9%;\r\n        --muted: 0 0% 96.1%;\r\n        --muted-foreground: 0 0% 45.1%;\r\n        --accent: 0 0% 96.1%;\r\n        --accent-foreground: 0 0% 9%;\r\n        --destructive: 0 84.2% 60.2%;\r\n        --destructive-foreground: 0 0% 98%;\r\n        --border: 0 0% 89.8%;\r\n        --input: 0 0% 89.8%;\r\n        --ring: 0 0% 3.9%;\r\n        --chart-1: 12 76% 61%;\r\n        --chart-2: 173 58% 39%;\r\n        --chart-3: 197 37% 24%;\r\n        --chart-4: 43 74% 66%;\r\n        --chart-5: 27 87% 67%;\r\n        --radius: 0.5rem;\r\n    }\r\n    * {\n  border-color: hsl(var(--border));\n  outline-color: hsl(var(--ring) / 0.5);\n}\r\n    body {\n  background-color: hsl(var(--background));\n  color: hsl(var(--foreground));\n}\r\n.container {\n  width: 100%;\n}\r\n@media (min-width: 640px) {\n\n  .container {\n    max-width: 640px;\n  }\n}\r\n@media (min-width: 768px) {\n\n  .container {\n    max-width: 768px;\n  }\n}\r\n@media (min-width: 1024px) {\n\n  .container {\n    max-width: 1024px;\n  }\n}\r\n@media (min-width: 1280px) {\n\n  .container {\n    max-width: 1280px;\n  }\n}\r\n@media (min-width: 1536px) {\n\n  .container {\n    max-width: 1536px;\n  }\n}\r\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\r\n.pointer-events-none {\n  pointer-events: none;\n}\r\n.visible {\n  visibility: visible;\n}\r\n.invisible {\n  visibility: hidden;\n}\r\n.fixed {\n  position: fixed;\n}\r\n.absolute {\n  position: absolute;\n}\r\n.relative {\n  position: relative;\n}\r\n.sticky {\n  position: sticky;\n}\r\n.inset-0 {\n  inset: 0px;\n}\r\n.inset-x-0 {\n  left: 0px;\n  right: 0px;\n}\r\n.inset-y-0 {\n  top: 0px;\n  bottom: 0px;\n}\r\n.\\!bottom-0 {\n  bottom: 0px !important;\n}\r\n.\\!left-\\[45\\%\\] {\n  left: 45% !important;\n}\r\n.\\!right-\\[45\\%\\] {\n  right: 45% !important;\n}\r\n.\\!top-auto {\n  top: auto !important;\n}\r\n.-bottom-8 {\n  bottom: -2rem;\n}\r\n.-left-4 {\n  left: -1rem;\n}\r\n.-right-2 {\n  right: -0.5rem;\n}\r\n.-right-4 {\n  right: -1rem;\n}\r\n.-top-16 {\n  top: -4rem;\n}\r\n.bottom-0 {\n  bottom: 0px;\n}\r\n.bottom-1\\.5 {\n  bottom: 0.375rem;\n}\r\n.bottom-2 {\n  bottom: 0.5rem;\n}\r\n.bottom-3 {\n  bottom: 0.75rem;\n}\r\n.bottom-6 {\n  bottom: 1.5rem;\n}\r\n.left-0 {\n  left: 0px;\n}\r\n.left-1 {\n  left: 0.25rem;\n}\r\n.left-1\\/2 {\n  left: 50%;\n}\r\n.left-2 {\n  left: 0.5rem;\n}\r\n.left-3 {\n  left: 0.75rem;\n}\r\n.left-4 {\n  left: 1rem;\n}\r\n.left-6 {\n  left: 1.5rem;\n}\r\n.left-7 {\n  left: 1.75rem;\n}\r\n.left-\\[50\\%\\] {\n  left: 50%;\n}\r\n.right-0 {\n  right: 0px;\n}\r\n.right-1 {\n  right: 0.25rem;\n}\r\n.right-1\\.5 {\n  right: 0.375rem;\n}\r\n.right-2 {\n  right: 0.5rem;\n}\r\n.right-3 {\n  right: 0.75rem;\n}\r\n.right-4 {\n  right: 1rem;\n}\r\n.right-6 {\n  right: 1.5rem;\n}\r\n.right-\\[1rem\\] {\n  right: 1rem;\n}\r\n.top-0 {\n  top: 0px;\n}\r\n.top-1\\/2 {\n  top: 50%;\n}\r\n.top-2 {\n  top: 0.5rem;\n}\r\n.top-24 {\n  top: 6rem;\n}\r\n.top-4 {\n  top: 1rem;\n}\r\n.top-5 {\n  top: 1.25rem;\n}\r\n.top-7 {\n  top: 1.75rem;\n}\r\n.top-8 {\n  top: 2rem;\n}\r\n.top-\\[1px\\] {\n  top: 1px;\n}\r\n.top-\\[50\\%\\] {\n  top: 50%;\n}\r\n.top-\\[60\\%\\] {\n  top: 60%;\n}\r\n.top-full {\n  top: 100%;\n}\r\n.z-0 {\n  z-index: 0;\n}\r\n.z-10 {\n  z-index: 10;\n}\r\n.z-20 {\n  z-index: 20;\n}\r\n.z-40 {\n  z-index: 40;\n}\r\n.z-50 {\n  z-index: 50;\n}\r\n.z-\\[1\\] {\n  z-index: 1;\n}\r\n.z-\\[9999\\] {\n  z-index: 9999;\n}\r\n.col-span-1 {\n  grid-column: span 1 / span 1;\n}\r\n.col-span-4 {\n  grid-column: span 4 / span 4;\n}\r\n.col-span-full {\n  grid-column: 1 / -1;\n}\r\n.m-0 {\n  margin: 0px;\n}\r\n.m-2 {\n  margin: 0.5rem;\n}\r\n.-mx-1 {\n  margin-left: -0.25rem;\n  margin-right: -0.25rem;\n}\r\n.-mx-2 {\n  margin-left: -0.5rem;\n  margin-right: -0.5rem;\n}\r\n.mx-2 {\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n}\r\n.mx-3 {\n  margin-left: 0.75rem;\n  margin-right: 0.75rem;\n}\r\n.mx-4 {\n  margin-left: 1rem;\n  margin-right: 1rem;\n}\r\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\r\n.my-1 {\n  margin-top: 0.25rem;\n  margin-bottom: 0.25rem;\n}\r\n.my-10 {\n  margin-top: 2.5rem;\n  margin-bottom: 2.5rem;\n}\r\n.my-12 {\n  margin-top: 3rem;\n  margin-bottom: 3rem;\n}\r\n.my-4 {\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n}\r\n.my-6 {\n  margin-top: 1.5rem;\n  margin-bottom: 1.5rem;\n}\r\n.my-8 {\n  margin-top: 2rem;\n  margin-bottom: 2rem;\n}\r\n.\\!mt-0 {\n  margin-top: 0px !important;\n}\r\n.-ml-1 {\n  margin-left: -0.25rem;\n}\r\n.mb-1 {\n  margin-bottom: 0.25rem;\n}\r\n.mb-10 {\n  margin-bottom: 2.5rem;\n}\r\n.mb-12 {\n  margin-bottom: 3rem;\n}\r\n.mb-16 {\n  margin-bottom: 4rem;\n}\r\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\r\n.mb-3 {\n  margin-bottom: 0.75rem;\n}\r\n.mb-4 {\n  margin-bottom: 1rem;\n}\r\n.mb-5 {\n  margin-bottom: 1.25rem;\n}\r\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\r\n.mb-7 {\n  margin-bottom: 1.75rem;\n}\r\n.mb-8 {\n  margin-bottom: 2rem;\n}\r\n.ml-1 {\n  margin-left: 0.25rem;\n}\r\n.ml-1\\.5 {\n  margin-left: 0.375rem;\n}\r\n.ml-2 {\n  margin-left: 0.5rem;\n}\r\n.ml-2\\.5 {\n  margin-left: 0.625rem;\n}\r\n.ml-4 {\n  margin-left: 1rem;\n}\r\n.ml-auto {\n  margin-left: auto;\n}\r\n.mr-1 {\n  margin-right: 0.25rem;\n}\r\n.mr-1\\.5 {\n  margin-right: 0.375rem;\n}\r\n.mr-2 {\n  margin-right: 0.5rem;\n}\r\n.mr-3 {\n  margin-right: 0.75rem;\n}\r\n.mr-4 {\n  margin-right: 1rem;\n}\r\n.mr-auto {\n  margin-right: auto;\n}\r\n.mt-0 {\n  margin-top: 0px;\n}\r\n.mt-0\\.5 {\n  margin-top: 0.125rem;\n}\r\n.mt-1 {\n  margin-top: 0.25rem;\n}\r\n.mt-1\\.5 {\n  margin-top: 0.375rem;\n}\r\n.mt-10 {\n  margin-top: 2.5rem;\n}\r\n.mt-12 {\n  margin-top: 3rem;\n}\r\n.mt-2 {\n  margin-top: 0.5rem;\n}\r\n.mt-2\\.5 {\n  margin-top: 0.625rem;\n}\r\n.mt-20 {\n  margin-top: 5rem;\n}\r\n.mt-3 {\n  margin-top: 0.75rem;\n}\r\n.mt-32 {\n  margin-top: 8rem;\n}\r\n.mt-4 {\n  margin-top: 1rem;\n}\r\n.mt-5 {\n  margin-top: 1.25rem;\n}\r\n.mt-6 {\n  margin-top: 1.5rem;\n}\r\n.mt-7 {\n  margin-top: 1.75rem;\n}\r\n.mt-8 {\n  margin-top: 2rem;\n}\r\n.mt-auto {\n  margin-top: auto;\n}\r\n.line-clamp-1 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\r\n.line-clamp-2 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\r\n.line-clamp-3 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 3;\n}\r\n.line-clamp-4 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 4;\n}\r\n.block {\n  display: block;\n}\r\n.inline-block {\n  display: inline-block;\n}\r\n.flex {\n  display: flex;\n}\r\n.inline-flex {\n  display: inline-flex;\n}\r\n.table {\n  display: table;\n}\r\n.grid {\n  display: grid;\n}\r\n.hidden {\n  display: none;\n}\r\n.aspect-square {\n  aspect-ratio: 1 / 1;\n}\r\n.aspect-video {\n  aspect-ratio: 16 / 9;\n}\r\n.size-5 {\n  width: 1.25rem;\n  height: 1.25rem;\n}\r\n.size-full {\n  width: 100%;\n  height: 100%;\n}\r\n.\\!h-10 {\n  height: 2.5rem !important;\n}\r\n.h-0 {\n  height: 0px;\n}\r\n.h-0\\.5 {\n  height: 0.125rem;\n}\r\n.h-1 {\n  height: 0.25rem;\n}\r\n.h-1\\.5 {\n  height: 0.375rem;\n}\r\n.h-10 {\n  height: 2.5rem;\n}\r\n.h-12 {\n  height: 3rem;\n}\r\n.h-16 {\n  height: 4rem;\n}\r\n.h-2 {\n  height: 0.5rem;\n}\r\n.h-2\\.5 {\n  height: 0.625rem;\n}\r\n.h-20 {\n  height: 5rem;\n}\r\n.h-24 {\n  height: 6rem;\n}\r\n.h-28 {\n  height: 7rem;\n}\r\n.h-3 {\n  height: 0.75rem;\n}\r\n.h-3\\.5 {\n  height: 0.875rem;\n}\r\n.h-32 {\n  height: 8rem;\n}\r\n.h-4 {\n  height: 1rem;\n}\r\n.h-5 {\n  height: 1.25rem;\n}\r\n.h-6 {\n  height: 1.5rem;\n}\r\n.h-64 {\n  height: 16rem;\n}\r\n.h-7 {\n  height: 1.75rem;\n}\r\n.h-8 {\n  height: 2rem;\n}\r\n.h-80 {\n  height: 20rem;\n}\r\n.h-9 {\n  height: 2.25rem;\n}\r\n.h-\\[0\\.25rem\\] {\n  height: 0.25rem;\n}\r\n.h-\\[0\\.875rem\\] {\n  height: 0.875rem;\n}\r\n.h-\\[190px\\] {\n  height: 190px;\n}\r\n.h-\\[220px\\] {\n  height: 220px;\n}\r\n.h-\\[24px\\] {\n  height: 24px;\n}\r\n.h-\\[24rem\\] {\n  height: 24rem;\n}\r\n.h-\\[28rem\\] {\n  height: 28rem;\n}\r\n.h-\\[3\\.8rem\\] {\n  height: 3.8rem;\n}\r\n.h-\\[330px\\] {\n  height: 330px;\n}\r\n.h-\\[36px\\] {\n  height: 36px;\n}\r\n.h-\\[3rem\\] {\n  height: 3rem;\n}\r\n.h-\\[400px\\] {\n  height: 400px;\n}\r\n.h-\\[40px\\] {\n  height: 40px;\n}\r\n.h-\\[42px\\] {\n  height: 42px;\n}\r\n.h-\\[482px\\] {\n  height: 482px;\n}\r\n.h-\\[500px\\] {\n  height: 500px;\n}\r\n.h-\\[600px\\] {\n  height: 600px;\n}\r\n.h-\\[60vh\\] {\n  height: 60vh;\n}\r\n.h-\\[76px\\] {\n  height: 76px;\n}\r\n.h-\\[90\\%\\] {\n  height: 90%;\n}\r\n.h-\\[calc\\(100\\%-8rem\\)\\] {\n  height: calc(100% - 8rem);\n}\r\n.h-\\[calc\\(100vh-7rem\\)\\] {\n  height: calc(100vh - 7rem);\n}\r\n.h-\\[var\\(--radix-navigation-menu-viewport-height\\)\\] {\n  height: var(--radix-navigation-menu-viewport-height);\n}\r\n.h-\\[var\\(--radix-select-trigger-height\\)\\] {\n  height: var(--radix-select-trigger-height);\n}\r\n.h-auto {\n  height: auto;\n}\r\n.h-full {\n  height: 100%;\n}\r\n.h-max {\n  height: max-content;\n}\r\n.h-min {\n  height: min-content;\n}\r\n.h-px {\n  height: 1px;\n}\r\n.h-screen {\n  height: 100vh;\n}\r\n.max-h-32 {\n  max-height: 8rem;\n}\r\n.max-h-96 {\n  max-height: 24rem;\n}\r\n.max-h-\\[300px\\] {\n  max-height: 300px;\n}\r\n.max-h-\\[390px\\] {\n  max-height: 390px;\n}\r\n.max-h-\\[400px\\] {\n  max-height: 400px;\n}\r\n.max-h-\\[60vh\\] {\n  max-height: 60vh;\n}\r\n.max-h-\\[90vh\\] {\n  max-height: 90vh;\n}\r\n.max-h-full {\n  max-height: 100%;\n}\r\n.max-h-min {\n  max-height: min-content;\n}\r\n.min-h-0 {\n  min-height: 0px;\n}\r\n.min-h-\\[100px\\] {\n  min-height: 100px;\n}\r\n.min-h-\\[120px\\] {\n  min-height: 120px;\n}\r\n.min-h-\\[200px\\] {\n  min-height: 200px;\n}\r\n.min-h-\\[300px\\] {\n  min-height: 300px;\n}\r\n.min-h-\\[340px\\] {\n  min-height: 340px;\n}\r\n.min-h-\\[390px\\] {\n  min-height: 390px;\n}\r\n.min-h-\\[400px\\] {\n  min-height: 400px;\n}\r\n.min-h-\\[60px\\] {\n  min-height: 60px;\n}\r\n.min-h-\\[80px\\] {\n  min-height: 80px;\n}\r\n.min-h-\\[80vh\\] {\n  min-height: 80vh;\n}\r\n.min-h-\\[calc\\(100vh-72px\\)\\] {\n  min-height: calc(100vh - 72px);\n}\r\n.min-h-screen {\n  min-height: 100vh;\n}\r\n.\\!w-10 {\n  width: 2.5rem !important;\n}\r\n.w-1\\/2 {\n  width: 50%;\n}\r\n.w-1\\/3 {\n  width: 33.333333%;\n}\r\n.w-10 {\n  width: 2.5rem;\n}\r\n.w-12 {\n  width: 3rem;\n}\r\n.w-14 {\n  width: 3.5rem;\n}\r\n.w-16 {\n  width: 4rem;\n}\r\n.w-2 {\n  width: 0.5rem;\n}\r\n.w-2\\.5 {\n  width: 0.625rem;\n}\r\n.w-2\\/3 {\n  width: 66.666667%;\n}\r\n.w-20 {\n  width: 5rem;\n}\r\n.w-24 {\n  width: 6rem;\n}\r\n.w-28 {\n  width: 7rem;\n}\r\n.w-3 {\n  width: 0.75rem;\n}\r\n.w-3\\.5 {\n  width: 0.875rem;\n}\r\n.w-3\\/4 {\n  width: 75%;\n}\r\n.w-32 {\n  width: 8rem;\n}\r\n.w-36 {\n  width: 9rem;\n}\r\n.w-4 {\n  width: 1rem;\n}\r\n.w-4\\/5 {\n  width: 80%;\n}\r\n.w-40 {\n  width: 10rem;\n}\r\n.w-48 {\n  width: 12rem;\n}\r\n.w-5 {\n  width: 1.25rem;\n}\r\n.w-5\\/6 {\n  width: 83.333333%;\n}\r\n.w-6 {\n  width: 1.5rem;\n}\r\n.w-64 {\n  width: 16rem;\n}\r\n.w-7 {\n  width: 1.75rem;\n}\r\n.w-72 {\n  width: 18rem;\n}\r\n.w-8 {\n  width: 2rem;\n}\r\n.w-9 {\n  width: 2.25rem;\n}\r\n.w-96 {\n  width: 24rem;\n}\r\n.w-\\[0\\.875rem\\] {\n  width: 0.875rem;\n}\r\n.w-\\[1102px\\] {\n  width: 1102px;\n}\r\n.w-\\[110px\\] {\n  width: 110px;\n}\r\n.w-\\[140px\\] {\n  width: 140px;\n}\r\n.w-\\[160px\\] {\n  width: 160px;\n}\r\n.w-\\[180px\\] {\n  width: 180px;\n}\r\n.w-\\[19px\\] {\n  width: 19px;\n}\r\n.w-\\[200px\\] {\n  width: 200px;\n}\r\n.w-\\[250px\\] {\n  width: 250px;\n}\r\n.w-\\[280px\\] {\n  width: 280px;\n}\r\n.w-\\[3\\.3rem\\] {\n  width: 3.3rem;\n}\r\n.w-\\[3\\.8rem\\] {\n  width: 3.8rem;\n}\r\n.w-\\[300px\\] {\n  width: 300px;\n}\r\n.w-\\[3rem\\] {\n  width: 3rem;\n}\r\n.w-\\[4\\.5rem\\] {\n  width: 4.5rem;\n}\r\n.w-\\[40px\\] {\n  width: 40px;\n}\r\n.w-\\[45\\%\\] {\n  width: 45%;\n}\r\n.w-\\[503px\\] {\n  width: 503px;\n}\r\n.w-\\[50px\\] {\n  width: 50px;\n}\r\n.w-\\[5rem\\] {\n  width: 5rem;\n}\r\n.w-\\[70\\%\\] {\n  width: 70%;\n}\r\n.w-\\[76px\\] {\n  width: 76px;\n}\r\n.w-\\[84\\%\\] {\n  width: 84%;\n}\r\n.w-\\[896px\\] {\n  width: 896px;\n}\r\n.w-\\[90\\%\\] {\n  width: 90%;\n}\r\n.w-\\[95\\%\\] {\n  width: 95%;\n}\r\n.w-\\[95vw\\] {\n  width: 95vw;\n}\r\n.w-auto {\n  width: auto;\n}\r\n.w-fit {\n  width: fit-content;\n}\r\n.w-full {\n  width: 100%;\n}\r\n.w-max {\n  width: max-content;\n}\r\n.w-min {\n  width: min-content;\n}\r\n.min-w-0 {\n  min-width: 0px;\n}\r\n.min-w-\\[100px\\] {\n  min-width: 100px;\n}\r\n.min-w-\\[120px\\] {\n  min-width: 120px;\n}\r\n.min-w-\\[150px\\] {\n  min-width: 150px;\n}\r\n.min-w-\\[8rem\\] {\n  min-width: 8rem;\n}\r\n.min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n  min-width: var(--radix-select-trigger-width);\n}\r\n.min-w-max {\n  min-width: max-content;\n}\r\n.max-w-2xl {\n  max-width: 42rem;\n}\r\n.max-w-3xl {\n  max-width: 48rem;\n}\r\n.max-w-4xl {\n  max-width: 56rem;\n}\r\n.max-w-5xl {\n  max-width: 64rem;\n}\r\n.max-w-6xl {\n  max-width: 72rem;\n}\r\n.max-w-7xl {\n  max-width: 80rem;\n}\r\n.max-w-\\[10rem\\] {\n  max-width: 10rem;\n}\r\n.max-w-\\[480px\\] {\n  max-width: 480px;\n}\r\n.max-w-\\[600px\\] {\n  max-width: 600px;\n}\r\n.max-w-\\[70\\%\\] {\n  max-width: 70%;\n}\r\n.max-w-full {\n  max-width: 100%;\n}\r\n.max-w-lg {\n  max-width: 32rem;\n}\r\n.max-w-max {\n  max-width: max-content;\n}\r\n.max-w-md {\n  max-width: 28rem;\n}\r\n.max-w-none {\n  max-width: none;\n}\r\n.max-w-sm {\n  max-width: 24rem;\n}\r\n.max-w-xl {\n  max-width: 36rem;\n}\r\n.flex-1 {\n  flex: 1 1 0%;\n}\r\n.flex-shrink {\n  flex-shrink: 1;\n}\r\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\r\n.shrink-0 {\n  flex-shrink: 0;\n}\r\n.flex-grow {\n  flex-grow: 1;\n}\r\n.flex-grow-0 {\n  flex-grow: 0;\n}\r\n.grow {\n  flex-grow: 1;\n}\r\n.table-auto {\n  table-layout: auto;\n}\r\n.caption-bottom {\n  caption-side: bottom;\n}\r\n.border-collapse {\n  border-collapse: collapse;\n}\r\n.origin-center {\n  transform-origin: center;\n}\r\n.-translate-x-1\\/2 {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.-translate-x-\\[120\\%\\] {\n  --tw-translate-x: -120%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.-translate-x-full {\n  --tw-translate-x: -100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.-translate-y-1\\/2 {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.-translate-y-2 {\n  --tw-translate-y: -0.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-x-0 {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-x-\\[-100\\%\\] {\n  --tw-translate-x: -100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-x-\\[-50\\%\\] {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-x-\\[8\\%\\] {\n  --tw-translate-x: 8%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-0 {\n  --tw-translate-y: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-2 {\n  --tw-translate-y: 0.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-\\[-50\\%\\] {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-\\[5\\%\\] {\n  --tw-translate-y: 5%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-full {\n  --tw-translate-y: 100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.-rotate-45 {\n  --tw-rotate: -45deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-0 {\n  --tw-rotate: 0deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-45 {\n  --tw-rotate: 45deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-90 {\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.animate-\\[scroll_15s_linear_infinite\\] {\n  animation: scroll 15s linear infinite;\n}\r\n@keyframes pendulum {\n\n  0%, 100% {\n    transform: rotate(0);\n  }\n\n  50% {\n    transform: rotate(15deg);\n  }\n}\r\n.animate-pendulum {\n  animation: pendulum 1s ease-in-out infinite;\n}\r\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\r\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\r\n@keyframes spin {\n\n  to {\n    transform: rotate(360deg);\n  }\n}\r\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\r\n.cursor-default {\n  cursor: default;\n}\r\n.cursor-not-allowed {\n  cursor: not-allowed;\n}\r\n.cursor-pointer {\n  cursor: pointer;\n}\r\n.cursor-wait {\n  cursor: wait;\n}\r\n.touch-none {\n  touch-action: none;\n}\r\n.select-none {\n  user-select: none;\n}\r\n.resize-none {\n  resize: none;\n}\r\n.resize {\n  resize: both;\n}\r\n.list-inside {\n  list-style-position: inside;\n}\r\n.list-disc {\n  list-style-type: disc;\n}\r\n.list-none {\n  list-style-type: none;\n}\r\n.appearance-none {\n  appearance: none;\n}\r\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\r\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\r\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\r\n.grid-cols-4 {\n  grid-template-columns: repeat(4, minmax(0, 1fr));\n}\r\n.grid-cols-5 {\n  grid-template-columns: repeat(5, minmax(0, 1fr));\n}\r\n.grid-cols-6 {\n  grid-template-columns: repeat(6, minmax(0, 1fr));\n}\r\n.grid-cols-7 {\n  grid-template-columns: repeat(7, minmax(0, 1fr));\n}\r\n.grid-cols-\\[80px_repeat\\(7\\2c 1fr\\)\\] {\n  grid-template-columns: 80px repeat(7,1fr);\n}\r\n.flex-row {\n  flex-direction: row;\n}\r\n.flex-col {\n  flex-direction: column;\n}\r\n.flex-col-reverse {\n  flex-direction: column-reverse;\n}\r\n.flex-wrap {\n  flex-wrap: wrap;\n}\r\n.items-start {\n  align-items: flex-start;\n}\r\n.items-end {\n  align-items: flex-end;\n}\r\n.items-center {\n  align-items: center;\n}\r\n.items-baseline {\n  align-items: baseline;\n}\r\n.justify-start {\n  justify-content: flex-start;\n}\r\n.justify-end {\n  justify-content: flex-end;\n}\r\n.justify-center {\n  justify-content: center;\n}\r\n.justify-between {\n  justify-content: space-between;\n}\r\n.gap-0\\.5 {\n  gap: 0.125rem;\n}\r\n.gap-1 {\n  gap: 0.25rem;\n}\r\n.gap-1\\.5 {\n  gap: 0.375rem;\n}\r\n.gap-10 {\n  gap: 2.5rem;\n}\r\n.gap-12 {\n  gap: 3rem;\n}\r\n.gap-2 {\n  gap: 0.5rem;\n}\r\n.gap-3 {\n  gap: 0.75rem;\n}\r\n.gap-4 {\n  gap: 1rem;\n}\r\n.gap-5 {\n  gap: 1.25rem;\n}\r\n.gap-6 {\n  gap: 1.5rem;\n}\r\n.gap-8 {\n  gap: 2rem;\n}\r\n.gap-x-16 {\n  column-gap: 4rem;\n}\r\n.gap-x-2 {\n  column-gap: 0.5rem;\n}\r\n.gap-x-3 {\n  column-gap: 0.75rem;\n}\r\n.gap-x-4 {\n  column-gap: 1rem;\n}\r\n.gap-x-8 {\n  column-gap: 2rem;\n}\r\n.gap-x-\\[9px\\] {\n  column-gap: 9px;\n}\r\n.gap-y-0\\.5 {\n  row-gap: 0.125rem;\n}\r\n.gap-y-1 {\n  row-gap: 0.25rem;\n}\r\n.gap-y-2 {\n  row-gap: 0.5rem;\n}\r\n.gap-y-2\\.5 {\n  row-gap: 0.625rem;\n}\r\n.gap-y-20 {\n  row-gap: 5rem;\n}\r\n.gap-y-3 {\n  row-gap: 0.75rem;\n}\r\n.gap-y-4 {\n  row-gap: 1rem;\n}\r\n.gap-y-5 {\n  row-gap: 1.25rem;\n}\r\n.gap-y-6 {\n  row-gap: 1.5rem;\n}\r\n.gap-y-8 {\n  row-gap: 2rem;\n}\r\n.gap-y-\\[13px\\] {\n  row-gap: 13px;\n}\r\n.gap-y-\\[15px\\] {\n  row-gap: 15px;\n}\r\n.gap-y-\\[9px\\] {\n  row-gap: 9px;\n}\r\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\r\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\r\n.space-y-10 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-2\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.625rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.625rem * var(--tw-space-y-reverse));\n}\r\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\r\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\r\n.space-y-5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));\n}\r\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\r\n.self-end {\n  align-self: flex-end;\n}\r\n.self-stretch {\n  align-self: stretch;\n}\r\n.overflow-auto {\n  overflow: auto;\n}\r\n.overflow-hidden {\n  overflow: hidden;\n}\r\n.overflow-clip {\n  overflow: clip;\n}\r\n.overflow-x-auto {\n  overflow-x: auto;\n}\r\n.overflow-y-auto {\n  overflow-y: auto;\n}\r\n.overflow-x-hidden {\n  overflow-x: hidden;\n}\r\n.truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\r\n.whitespace-nowrap {\n  white-space: nowrap;\n}\r\n.whitespace-pre-wrap {\n  white-space: pre-wrap;\n}\r\n.break-words {\n  overflow-wrap: break-word;\n}\r\n.rounded {\n  border-radius: 0.25rem;\n}\r\n.rounded-2xl {\n  border-radius: 1rem;\n}\r\n.rounded-3xl {\n  border-radius: 1.5rem;\n}\r\n.rounded-\\[2px\\] {\n  border-radius: 2px;\n}\r\n.rounded-\\[6px\\] {\n  border-radius: 6px;\n}\r\n.rounded-\\[inherit\\] {\n  border-radius: inherit;\n}\r\n.rounded-full {\n  border-radius: 9999px;\n}\r\n.rounded-lg {\n  border-radius: 0.5rem;\n}\r\n.rounded-md {\n  border-radius: 0.375rem;\n}\r\n.rounded-sm {\n  border-radius: 0.125rem;\n}\r\n.rounded-xl {\n  border-radius: 0.75rem;\n}\r\n.rounded-l {\n  border-top-left-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n}\r\n.rounded-r {\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem;\n}\r\n.rounded-r-\\[10px\\] {\n  border-top-right-radius: 10px;\n  border-bottom-right-radius: 10px;\n}\r\n.rounded-t-2xl {\n  border-top-left-radius: 1rem;\n  border-top-right-radius: 1rem;\n}\r\n.rounded-t-xl {\n  border-top-left-radius: 0.75rem;\n  border-top-right-radius: 0.75rem;\n}\r\n.rounded-tl-sm {\n  border-top-left-radius: 0.125rem;\n}\r\n.border {\n  border-width: 1px;\n}\r\n.border-0 {\n  border-width: 0px;\n}\r\n.border-2 {\n  border-width: 2px;\n}\r\n.border-4 {\n  border-width: 4px;\n}\r\n.border-8 {\n  border-width: 8px;\n}\r\n.border-y {\n  border-top-width: 1px;\n  border-bottom-width: 1px;\n}\r\n.border-b {\n  border-bottom-width: 1px;\n}\r\n.border-b-2 {\n  border-bottom-width: 2px;\n}\r\n.border-l {\n  border-left-width: 1px;\n}\r\n.border-l-4 {\n  border-left-width: 4px;\n}\r\n.border-r {\n  border-right-width: 1px;\n}\r\n.border-t {\n  border-top-width: 1px;\n}\r\n.border-solid {\n  border-style: solid;\n}\r\n.border-dashed {\n  border-style: dashed;\n}\r\n.border-none {\n  border-style: none;\n}\r\n.border-\\[\\#141519\\] {\n  --tw-border-opacity: 1;\n  border-color: rgb(20 21 25 / var(--tw-border-opacity, 1));\n}\r\n.border-\\[\\#9EA2B3\\] {\n  --tw-border-opacity: 1;\n  border-color: rgb(158 162 179 / var(--tw-border-opacity, 1));\n}\r\n.border-\\[\\#E2E8F0\\] {\n  --tw-border-opacity: 1;\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-100 {\n  --tw-border-opacity: 1;\n  border-color: rgb(219 234 254 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-950 {\n  --tw-border-opacity: 1;\n  border-color: rgb(23 37 84 / var(--tw-border-opacity, 1));\n}\r\n.border-blue-950\\/10 {\n  border-color: rgb(23 37 84 / 0.1);\n}\r\n.border-gray-100 {\n  --tw-border-opacity: 1;\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\r\n.border-gray-900 {\n  --tw-border-opacity: 1;\n  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));\n}\r\n.border-green-100 {\n  --tw-border-opacity: 1;\n  border-color: rgb(220 252 231 / var(--tw-border-opacity, 1));\n}\r\n.border-green-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\n}\r\n.border-input {\n  border-color: hsl(var(--input));\n}\r\n.border-mainClr {\n  --tw-border-opacity: 1;\n  border-color: rgb(21 32 68 / var(--tw-border-opacity, 1));\n}\r\n.border-orange-100 {\n  --tw-border-opacity: 1;\n  border-color: rgb(255 237 213 / var(--tw-border-opacity, 1));\n}\r\n.border-primary {\n  border-color: hsl(var(--primary));\n}\r\n.border-primary\\/50 {\n  border-color: hsl(var(--primary) / 0.5);\n}\r\n.border-purple-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(216 180 254 / var(--tw-border-opacity, 1));\n}\r\n.border-red-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\n}\r\n.border-red-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\r\n.border-slate-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));\n}\r\n.border-transparent {\n  border-color: transparent;\n}\r\n.border-white {\n  --tw-border-opacity: 1;\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\r\n.border-white\\/10 {\n  border-color: rgb(255 255 255 / 0.1);\n}\r\n.border-x-\\[ghostwhite\\] {\n  --tw-border-opacity: 1;\n  border-left-color: rgb(248 248 255 / var(--tw-border-opacity, 1));\n  border-right-color: rgb(248 248 255 / var(--tw-border-opacity, 1));\n}\r\n.border-x-\\[lightgray\\] {\n  --tw-border-opacity: 1;\n  border-left-color: rgb(211 211 211 / var(--tw-border-opacity, 1));\n  border-right-color: rgb(211 211 211 / var(--tw-border-opacity, 1));\n}\r\n.border-y-\\[ghostwhite\\] {\n  --tw-border-opacity: 1;\n  border-top-color: rgb(248 248 255 / var(--tw-border-opacity, 1));\n  border-bottom-color: rgb(248 248 255 / var(--tw-border-opacity, 1));\n}\r\n.border-y-\\[lightgray\\] {\n  --tw-border-opacity: 1;\n  border-top-color: rgb(211 211 211 / var(--tw-border-opacity, 1));\n  border-bottom-color: rgb(211 211 211 / var(--tw-border-opacity, 1));\n}\r\n.border-b-transparent {\n  border-bottom-color: transparent;\n}\r\n.border-l-transparent {\n  border-left-color: transparent;\n}\r\n.border-r-transparent {\n  border-right-color: transparent;\n}\r\n.border-t-\\[\\#0F1C2D\\] {\n  --tw-border-opacity: 1;\n  border-top-color: rgb(15 28 45 / var(--tw-border-opacity, 1));\n}\r\n.border-t-transparent {\n  border-top-color: transparent;\n}\r\n.bg-\\[\\#0F172A\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#0F1C2D\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(15 28 45 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#14162F\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(20 22 47 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#15194B\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(21 25 75 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#1B1F3B\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(27 31 59 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#1E2875\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 40 117 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#2F80ED\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(47 128 237 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#2F80ED\\]\\/10 {\n  background-color: rgb(47 128 237 / 0.1);\n}\r\n.bg-\\[\\#3E84F8\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(62 132 248 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#6FCF9733\\] {\n  background-color: #6FCF9733;\n}\r\n.bg-\\[\\#6b6b6b\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(107 107 107 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#800000\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(128 0 0 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#8B141A\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(139 20 26 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#9C0E22\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(156 14 34 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#EB575733\\] {\n  background-color: #EB575733;\n}\r\n.bg-\\[\\#EB5757\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(235 87 87 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#EFF0F7\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 240 247 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#F8F8F8\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(248 248 248 / var(--tw-bg-opacity, 1));\n}\r\n.bg-\\[\\#F8FAFC\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));\n}\r\n.bg-background {\n  background-color: hsl(var(--background));\n}\r\n.bg-black {\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\r\n.bg-black\\/10 {\n  background-color: rgb(0 0 0 / 0.1);\n}\r\n.bg-black\\/30 {\n  background-color: rgb(0 0 0 / 0.3);\n}\r\n.bg-black\\/80 {\n  background-color: rgb(0 0 0 / 0.8);\n}\r\n.bg-blue-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-300 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-600\\/90 {\n  background-color: rgb(37 99 235 / 0.9);\n}\r\n.bg-blue-700\\/30 {\n  background-color: rgb(29 78 216 / 0.3);\n}\r\n.bg-blue-800 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-950 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(23 37 84 / var(--tw-bg-opacity, 1));\n}\r\n.bg-blue-950\\/5 {\n  background-color: rgb(23 37 84 / 0.05);\n}\r\n.bg-border {\n  background-color: hsl(var(--border));\n}\r\n.bg-emerald-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-100\\/60 {\n  background-color: rgb(243 244 246 / 0.6);\n}\r\n.bg-gray-100\\/80 {\n  background-color: rgb(243 244 246 / 0.8);\n}\r\n.bg-gray-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-300 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-50\\/50 {\n  background-color: rgb(249 250 251 / 0.5);\n}\r\n.bg-gray-800 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n}\r\n.bg-gray-900 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\n}\r\n.bg-grayLight {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 240 247 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-300 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(134 239 172 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\n}\r\n.bg-green-700 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\n}\r\n.bg-indigo-950 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 27 75 / var(--tw-bg-opacity, 1));\n}\r\n.bg-mainClr {\n  --tw-bg-opacity: 1;\n  background-color: rgb(21 32 68 / var(--tw-bg-opacity, 1));\n}\r\n.bg-neutral-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(245 245 245 / var(--tw-bg-opacity, 1));\n}\r\n.bg-neutral1 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(248 248 248 / var(--tw-bg-opacity, 1));\n}\r\n.bg-orange-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\n}\r\n.bg-orange-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));\n}\r\n.bg-primary {\n  background-color: hsl(var(--primary));\n}\r\n.bg-primary\\/10 {\n  background-color: hsl(var(--primary) / 0.1);\n}\r\n.bg-primary\\/20 {\n  background-color: hsl(var(--primary) / 0.2);\n}\r\n.bg-purple-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-purple-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(147 51 234 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(248 113 113 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n}\r\n.bg-red-700 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\n}\r\n.bg-slate-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));\n}\r\n.bg-slate-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(248 250 252 / var(--tw-bg-opacity, 1));\n}\r\n.bg-slate-800 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\n}\r\n.bg-stone-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(245 245 244 / var(--tw-bg-opacity, 1));\n}\r\n.bg-transparent {\n  background-color: transparent;\n}\r\n.bg-white {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\r\n.bg-white\\/10 {\n  background-color: rgb(255 255 255 / 0.1);\n}\r\n.bg-white\\/95 {\n  background-color: rgb(255 255 255 / 0.95);\n}\r\n.bg-yellow-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\n}\r\n.bg-yellow-300 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(253 224 71 / var(--tw-bg-opacity, 1));\n}\r\n.bg-yellow-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 204 21 / var(--tw-bg-opacity, 1));\n}\r\n.bg-yellow-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\n}\r\n.bg-yellow-700 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));\n}\r\n.bg-zinc-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(244 244 245 / var(--tw-bg-opacity, 1));\n}\r\n.bg-opacity-0 {\n  --tw-bg-opacity: 0;\n}\r\n.bg-opacity-30 {\n  --tw-bg-opacity: 0.3;\n}\r\n.bg-opacity-50 {\n  --tw-bg-opacity: 0.5;\n}\r\n.bg-gradient-to-br {\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\r\n.bg-gradient-to-l {\n  background-image: linear-gradient(to left, var(--tw-gradient-stops));\n}\r\n.bg-gradient-to-r {\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\r\n.bg-gradient-to-t {\n  background-image: linear-gradient(to top, var(--tw-gradient-stops));\n}\r\n.from-\\[\\#2F80ED\\] {\n  --tw-gradient-from: #2F80ED var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(47 128 237 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-black\\/20 {\n  --tw-gradient-from: rgb(0 0 0 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-blue-50 {\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-blue-950 {\n  --tw-gradient-from: #172554 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(23 37 84 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.from-white {\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.via-white {\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\r\n.to-black\\/90 {\n  --tw-gradient-to: rgb(0 0 0 / 0.9) var(--tw-gradient-to-position);\n}\r\n.to-blue-100 {\n  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);\n}\r\n.to-blue-400 {\n  --tw-gradient-to: #60a5fa var(--tw-gradient-to-position);\n}\r\n.to-transparent {\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\n}\r\n.bg-cover {\n  background-size: cover;\n}\r\n.bg-center {\n  background-position: center;\n}\r\n.bg-no-repeat {\n  background-repeat: no-repeat;\n}\r\n.fill-blue-600 {\n  fill: #2563eb;\n}\r\n.fill-current {\n  fill: currentColor;\n}\r\n.object-contain {\n  object-fit: contain;\n}\r\n.object-cover {\n  object-fit: cover;\n}\r\n.object-center {\n  object-position: center;\n}\r\n.object-right {\n  object-position: right;\n}\r\n.object-top {\n  object-position: top;\n}\r\n.p-0 {\n  padding: 0px;\n}\r\n.p-1 {\n  padding: 0.25rem;\n}\r\n.p-10 {\n  padding: 2.5rem;\n}\r\n.p-2 {\n  padding: 0.5rem;\n}\r\n.p-2\\.5 {\n  padding: 0.625rem;\n}\r\n.p-3 {\n  padding: 0.75rem;\n}\r\n.p-4 {\n  padding: 1rem;\n}\r\n.p-5 {\n  padding: 1.25rem;\n}\r\n.p-6 {\n  padding: 1.5rem;\n}\r\n.p-8 {\n  padding: 2rem;\n}\r\n.p-\\[1px\\] {\n  padding: 1px;\n}\r\n.px-1 {\n  padding-left: 0.25rem;\n  padding-right: 0.25rem;\n}\r\n.px-11 {\n  padding-left: 2.75rem;\n  padding-right: 2.75rem;\n}\r\n.px-2 {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\r\n.px-2\\.5 {\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\r\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\r\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\r\n.px-5 {\n  padding-left: 1.25rem;\n  padding-right: 1.25rem;\n}\r\n.px-6 {\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\r\n.px-8 {\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\r\n.px-9 {\n  padding-left: 2.25rem;\n  padding-right: 2.25rem;\n}\r\n.px-\\[120px\\] {\n  padding-left: 120px;\n  padding-right: 120px;\n}\r\n.py-0\\.5 {\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\r\n.py-1 {\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\r\n.py-1\\.5 {\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\r\n.py-10 {\n  padding-top: 2.5rem;\n  padding-bottom: 2.5rem;\n}\r\n.py-12 {\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\r\n.py-16 {\n  padding-top: 4rem;\n  padding-bottom: 4rem;\n}\r\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\r\n.py-2\\.5 {\n  padding-top: 0.625rem;\n  padding-bottom: 0.625rem;\n}\r\n.py-20 {\n  padding-top: 5rem;\n  padding-bottom: 5rem;\n}\r\n.py-24 {\n  padding-top: 6rem;\n  padding-bottom: 6rem;\n}\r\n.py-3 {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\r\n.py-3\\.5 {\n  padding-top: 0.875rem;\n  padding-bottom: 0.875rem;\n}\r\n.py-4 {\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\r\n.py-5 {\n  padding-top: 1.25rem;\n  padding-bottom: 1.25rem;\n}\r\n.py-6 {\n  padding-top: 1.5rem;\n  padding-bottom: 1.5rem;\n}\r\n.py-8 {\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\r\n.py-9 {\n  padding-top: 2.25rem;\n  padding-bottom: 2.25rem;\n}\r\n.pb-1 {\n  padding-bottom: 0.25rem;\n}\r\n.pb-10 {\n  padding-bottom: 2.5rem;\n}\r\n.pb-16 {\n  padding-bottom: 4rem;\n}\r\n.pb-2 {\n  padding-bottom: 0.5rem;\n}\r\n.pb-3 {\n  padding-bottom: 0.75rem;\n}\r\n.pb-4 {\n  padding-bottom: 1rem;\n}\r\n.pb-6 {\n  padding-bottom: 1.5rem;\n}\r\n.pb-7 {\n  padding-bottom: 1.75rem;\n}\r\n.pb-8 {\n  padding-bottom: 2rem;\n}\r\n.pl-1 {\n  padding-left: 0.25rem;\n}\r\n.pl-10 {\n  padding-left: 2.5rem;\n}\r\n.pl-12 {\n  padding-left: 3rem;\n}\r\n.pl-2 {\n  padding-left: 0.5rem;\n}\r\n.pl-4 {\n  padding-left: 1rem;\n}\r\n.pl-5 {\n  padding-left: 1.25rem;\n}\r\n.pl-6 {\n  padding-left: 1.5rem;\n}\r\n.pl-8 {\n  padding-left: 2rem;\n}\r\n.pr-10 {\n  padding-right: 2.5rem;\n}\r\n.pr-12 {\n  padding-right: 3rem;\n}\r\n.pr-2 {\n  padding-right: 0.5rem;\n}\r\n.pr-3 {\n  padding-right: 0.75rem;\n}\r\n.pr-4 {\n  padding-right: 1rem;\n}\r\n.pr-8 {\n  padding-right: 2rem;\n}\r\n.pt-0 {\n  padding-top: 0px;\n}\r\n.pt-1 {\n  padding-top: 0.25rem;\n}\r\n.pt-1\\.5 {\n  padding-top: 0.375rem;\n}\r\n.pt-10 {\n  padding-top: 2.5rem;\n}\r\n.pt-12 {\n  padding-top: 3rem;\n}\r\n.pt-14 {\n  padding-top: 3.5rem;\n}\r\n.pt-2 {\n  padding-top: 0.5rem;\n}\r\n.pt-3 {\n  padding-top: 0.75rem;\n}\r\n.pt-4 {\n  padding-top: 1rem;\n}\r\n.pt-6 {\n  padding-top: 1.5rem;\n}\r\n.pt-8 {\n  padding-top: 2rem;\n}\r\n.pt-\\[18px\\] {\n  padding-top: 18px;\n}\r\n.pt-\\[22px\\] {\n  padding-top: 22px;\n}\r\n.pt-\\[76px\\] {\n  padding-top: 76px;\n}\r\n.text-left {\n  text-align: left;\n}\r\n.text-center {\n  text-align: center;\n}\r\n.text-right {\n  text-align: right;\n}\r\n.align-middle {\n  vertical-align: middle;\n}\r\n.font-clash-display {\n  font-family: ClashDisplay, sans-serif;\n}\r\n.font-sans {\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\r\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\r\n.text-3xl {\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\r\n.text-4xl {\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\r\n.text-5xl {\n  font-size: 3rem;\n  line-height: 1;\n}\r\n.text-\\[0\\.8rem\\] {\n  font-size: 0.8rem;\n}\r\n.text-\\[10px\\] {\n  font-size: 10px;\n}\r\n.text-\\[11px\\] {\n  font-size: 11px;\n}\r\n.text-\\[13px\\] {\n  font-size: 13px;\n}\r\n.text-\\[15px\\] {\n  font-size: 15px;\n}\r\n.text-\\[16px\\] {\n  font-size: 16px;\n}\r\n.text-\\[2\\.2rem\\] {\n  font-size: 2.2rem;\n}\r\n.text-base {\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\r\n.text-lg {\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\r\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\r\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\r\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\r\n.font-\\[400\\] {\n  font-weight: 400;\n}\r\n.font-\\[500\\] {\n  font-weight: 500;\n}\r\n.font-\\[600\\] {\n  font-weight: 600;\n}\r\n.font-black {\n  font-weight: 900;\n}\r\n.font-bold {\n  font-weight: 700;\n}\r\n.font-light {\n  font-weight: 300;\n}\r\n.font-medium {\n  font-weight: 500;\n}\r\n.font-normal {\n  font-weight: 400;\n}\r\n.font-semibold {\n  font-weight: 600;\n}\r\n.uppercase {\n  text-transform: uppercase;\n}\r\n.lowercase {\n  text-transform: lowercase;\n}\r\n.capitalize {\n  text-transform: capitalize;\n}\r\n.italic {\n  font-style: italic;\n}\r\n.leading-10 {\n  line-height: 2.5rem;\n}\r\n.leading-6 {\n  line-height: 1.5rem;\n}\r\n.leading-\\[1\\.2\\] {\n  line-height: 1.2;\n}\r\n.leading-\\[1\\.4\\] {\n  line-height: 1.4;\n}\r\n.leading-\\[1\\.6\\] {\n  line-height: 1.6;\n}\r\n.leading-\\[1\\.8\\] {\n  line-height: 1.8;\n}\r\n.leading-\\[38px\\] {\n  line-height: 38px;\n}\r\n.leading-\\[normal\\] {\n  line-height: normal;\n}\r\n.leading-none {\n  line-height: 1;\n}\r\n.leading-relaxed {\n  line-height: 1.625;\n}\r\n.leading-tight {\n  line-height: 1.25;\n}\r\n.tracking-\\[0\\.16px\\] {\n  letter-spacing: 0.16px;\n}\r\n.tracking-tight {\n  letter-spacing: -0.025em;\n}\r\n.tracking-wider {\n  letter-spacing: 0.05em;\n}\r\n.tracking-widest {\n  letter-spacing: 0.1em;\n}\r\n.text-\\[\\#0F1C2D\\] {\n  --tw-text-opacity: 1;\n  color: rgb(15 28 45 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#111827\\] {\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#141519\\] {\n  --tw-text-opacity: 1;\n  color: rgb(20 21 25 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#14162F\\] {\n  --tw-text-opacity: 1;\n  color: rgb(20 22 47 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#15194B\\] {\n  --tw-text-opacity: 1;\n  color: rgb(21 25 75 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#2F80ED\\] {\n  --tw-text-opacity: 1;\n  color: rgb(47 128 237 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#800000\\] {\n  --tw-text-opacity: 1;\n  color: rgb(128 0 0 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#9C0E22\\] {\n  --tw-text-opacity: 1;\n  color: rgb(156 14 34 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#9EA2B3\\] {\n  --tw-text-opacity: 1;\n  color: rgb(158 162 179 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#EB5757\\] {\n  --tw-text-opacity: 1;\n  color: rgb(235 87 87 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[\\#F2994A\\] {\n  --tw-text-opacity: 1;\n  color: rgb(242 153 74 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[dimgray\\] {\n  --tw-text-opacity: 1;\n  color: rgb(105 105 105 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[lightslategray\\] {\n  --tw-text-opacity: 1;\n  color: rgb(119 136 153 / var(--tw-text-opacity, 1));\n}\r\n.text-\\[neutral8\\] {\n  color: neutral8;\n}\r\n.text-black {\n  --tw-text-opacity: 1;\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-100 {\n  --tw-text-opacity: 1;\n  color: rgb(219 234 254 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-500 {\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-600 {\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-700 {\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-800 {\n  --tw-text-opacity: 1;\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-900 {\n  --tw-text-opacity: 1;\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-950 {\n  --tw-text-opacity: 1;\n  color: rgb(23 37 84 / var(--tw-text-opacity, 1));\n}\r\n.text-blue-950\\/90 {\n  color: rgb(23 37 84 / 0.9);\n}\r\n.text-current {\n  color: currentColor;\n}\r\n.text-foreground {\n  color: hsl(var(--foreground));\n}\r\n.text-gray-100 {\n  --tw-text-opacity: 1;\n  color: rgb(243 244 246 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-200 {\n  --tw-text-opacity: 1;\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-300 {\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-400 {\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-500 {\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-600 {\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-600\\/70 {\n  color: rgb(75 85 99 / 0.7);\n}\r\n.text-gray-700 {\n  --tw-text-opacity: 1;\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-700\\/80 {\n  color: rgb(55 65 81 / 0.8);\n}\r\n.text-gray-800 {\n  --tw-text-opacity: 1;\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\n}\r\n.text-gray-800\\/60 {\n  color: rgb(31 41 55 / 0.6);\n}\r\n.text-gray-800\\/80 {\n  color: rgb(31 41 55 / 0.8);\n}\r\n.text-gray-800\\/90 {\n  color: rgb(31 41 55 / 0.9);\n}\r\n.text-gray-900 {\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\r\n.text-green-600 {\n  --tw-text-opacity: 1;\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\n}\r\n.text-green-700 {\n  --tw-text-opacity: 1;\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\n}\r\n.text-green-800 {\n  --tw-text-opacity: 1;\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\n}\r\n.text-indigo-950 {\n  --tw-text-opacity: 1;\n  color: rgb(30 27 75 / var(--tw-text-opacity, 1));\n}\r\n.text-neutral-300 {\n  --tw-text-opacity: 1;\n  color: rgb(212 212 212 / var(--tw-text-opacity, 1));\n}\r\n.text-neutral-500 {\n  --tw-text-opacity: 1;\n  color: rgb(115 115 115 / var(--tw-text-opacity, 1));\n}\r\n.text-neutral-900 {\n  --tw-text-opacity: 1;\n  color: rgb(23 23 23 / var(--tw-text-opacity, 1));\n}\r\n.text-neutral-950 {\n  --tw-text-opacity: 1;\n  color: rgb(10 10 10 / var(--tw-text-opacity, 1));\n}\r\n.text-neutral10 {\n  --tw-text-opacity: 1;\n  color: rgb(20 21 25 / var(--tw-text-opacity, 1));\n}\r\n.text-neutral4 {\n  --tw-text-opacity: 1;\n  color: rgb(158 162 179 / var(--tw-text-opacity, 1));\n}\r\n.text-neutral5 {\n  --tw-text-opacity: 1;\n  color: rgb(131 135 153 / var(--tw-text-opacity, 1));\n}\r\n.text-neutral7 {\n  --tw-text-opacity: 1;\n  color: rgb(107 111 128 / var(--tw-text-opacity, 1));\n}\r\n.text-neutral8 {\n  --tw-text-opacity: 1;\n  color: rgb(62 65 76 / var(--tw-text-opacity, 1));\n}\r\n.text-neutral9 {\n  --tw-text-opacity: 1;\n  color: rgb(41 43 51 / var(--tw-text-opacity, 1));\n}\r\n.text-orange-600 {\n  --tw-text-opacity: 1;\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\n}\r\n.text-primary {\n  color: hsl(var(--primary));\n}\r\n.text-primary-foreground {\n  color: hsl(var(--primary-foreground));\n}\r\n.text-purple-600 {\n  --tw-text-opacity: 1;\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\n}\r\n.text-purple-700 {\n  --tw-text-opacity: 1;\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\n}\r\n.text-red-400 {\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\r\n.text-red-500 {\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\r\n.text-red-600 {\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\r\n.text-red-700 {\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\r\n.text-red-800 {\n  --tw-text-opacity: 1;\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\n}\r\n.text-slate-600 {\n  --tw-text-opacity: 1;\n  color: rgb(71 85 105 / var(--tw-text-opacity, 1));\n}\r\n.text-slate-700 {\n  --tw-text-opacity: 1;\n  color: rgb(51 65 85 / var(--tw-text-opacity, 1));\n}\r\n.text-slate-800 {\n  --tw-text-opacity: 1;\n  color: rgb(30 41 59 / var(--tw-text-opacity, 1));\n}\r\n.text-textLight {\n  --tw-text-opacity: 1;\n  color: rgb(158 162 179 / var(--tw-text-opacity, 1));\n}\r\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.text-white\\/90 {\n  color: rgb(255 255 255 / 0.9);\n}\r\n.text-yellow-400 {\n  --tw-text-opacity: 1;\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\n}\r\n.text-yellow-500 {\n  --tw-text-opacity: 1;\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\n}\r\n.text-yellow-600 {\n  --tw-text-opacity: 1;\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\n}\r\n.text-yellow-700 {\n  --tw-text-opacity: 1;\n  color: rgb(161 98 7 / var(--tw-text-opacity, 1));\n}\r\n.text-yellow-800 {\n  --tw-text-opacity: 1;\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\n}\r\n.text-zinc-100 {\n  --tw-text-opacity: 1;\n  color: rgb(244 244 245 / var(--tw-text-opacity, 1));\n}\r\n.text-zinc-100\\/90 {\n  color: rgb(244 244 245 / 0.9);\n}\r\n.text-zinc-50 {\n  --tw-text-opacity: 1;\n  color: rgb(250 250 250 / var(--tw-text-opacity, 1));\n}\r\n.text-zinc-900 {\n  --tw-text-opacity: 1;\n  color: rgb(24 24 27 / var(--tw-text-opacity, 1));\n}\r\n.underline {\n  text-decoration-line: underline;\n}\r\n.line-through {\n  text-decoration-line: line-through;\n}\r\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\r\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\r\n.placeholder-gray-400::placeholder {\n  --tw-placeholder-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));\n}\r\n.opacity-0 {\n  opacity: 0;\n}\r\n.opacity-100 {\n  opacity: 1;\n}\r\n.opacity-20 {\n  opacity: 0.2;\n}\r\n.opacity-25 {\n  opacity: 0.25;\n}\r\n.opacity-30 {\n  opacity: 0.3;\n}\r\n.opacity-40 {\n  opacity: 0.4;\n}\r\n.opacity-50 {\n  opacity: 0.5;\n}\r\n.opacity-60 {\n  opacity: 0.6;\n}\r\n.opacity-70 {\n  opacity: 0.7;\n}\r\n.opacity-75 {\n  opacity: 0.75;\n}\r\n.opacity-90 {\n  opacity: 0.9;\n}\r\n.shadow {\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-2xl {\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-\\[0_4px_24px_-8px_rgba\\(0\\2c 0\\2c 0\\2c 0\\.1\\)\\] {\n  --tw-shadow: 0 4px 24px -8px rgba(0,0,0,0.1);\n  --tw-shadow-colored: 0 4px 24px -8px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-md {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-none {\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-xl {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-transparent {\n  --tw-shadow-color: transparent;\n  --tw-shadow: var(--tw-shadow-colored);\n}\r\n.outline-none {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.outline {\n  outline-style: solid;\n}\r\n.ring-0 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.ring-2 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.ring-4 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.ring-blue-500 {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n}\r\n.ring-gray-50 {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(249 250 251 / var(--tw-ring-opacity, 1));\n}\r\n.ring-offset-background {\n  --tw-ring-offset-color: hsl(var(--background));\n}\r\n.ring-offset-white {\n  --tw-ring-offset-color: #fff;\n}\r\n.blur {\n  --tw-blur: blur(8px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.drop-shadow-2xl {\n  --tw-drop-shadow: drop-shadow(0 25px 25px rgb(0 0 0 / 0.15));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.drop-shadow-\\[0_10px_30px_rgba\\(0\\2c 0\\2c 0\\2c 0\\.05\\)\\] {\n  --tw-drop-shadow: drop-shadow(0 10px 30px rgba(0,0,0,0.05));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.drop-shadow-lg {\n  --tw-drop-shadow: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.drop-shadow-md {\n  --tw-drop-shadow: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.drop-shadow-sm {\n  --tw-drop-shadow: drop-shadow(0 1px 1px rgb(0 0 0 / 0.05));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.drop-shadow-xl {\n  --tw-drop-shadow: drop-shadow(0 20px 13px rgb(0 0 0 / 0.03)) drop-shadow(0 8px 5px rgb(0 0 0 / 0.08));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.backdrop-blur-lg {\n  --tw-backdrop-blur: blur(16px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.backdrop-blur-sm {\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.transition {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-colors {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-shadow {\n  transition-property: box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-transform {\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.duration-200 {\n  transition-duration: 200ms;\n}\r\n.duration-300 {\n  transition-duration: 300ms;\n}\r\n.duration-500 {\n  transition-duration: 500ms;\n}\r\n.ease-in-out {\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\r\n.ease-out {\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\r\n@keyframes enter {\n\n  from {\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\r\n@keyframes exit {\n\n  to {\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\r\n.animate-in {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.fade-in-0 {\n  --tw-enter-opacity: 0;\n}\r\n.zoom-in-95 {\n  --tw-enter-scale: .95;\n}\r\n.duration-200 {\n  animation-duration: 200ms;\n}\r\n.duration-300 {\n  animation-duration: 300ms;\n}\r\n.duration-500 {\n  animation-duration: 500ms;\n}\r\n.ease-in-out {\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\r\n.ease-out {\n  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\r\n.scrollbar-hide {\r\n        -ms-overflow-style: none; /* IE and Edge */\r\n        scrollbar-width: none; /* Firefox */\r\n    }\r\n.scrollbar-hide::-webkit-scrollbar {\r\n        display: none; /* Chrome, Safari and Opera */\r\n    }\r\n.\\[background-position\\:0px_-1\\.26px\\] {\n  background-position: 0px -1.26px;\n}\r\n.\\[background-size\\:100\\%_150\\%\\] {\n  background-size: 100% 150%;\n}\r\n.\\[max-width\\:426px\\] {\n  max-width: 426px;\n}\r\n\r\n@keyframes scroll {\r\n    0% {\r\n        transform: translateX(0);\r\n    }\r\n    100% {\r\n        transform: translateX(-50%);\r\n    }\r\n}\r\n\r\nhtml,\r\nbody {\r\n    /* height: 100%; */\r\n}\r\n\r\n/* width */\r\n.popupz::-webkit-scrollbar {\r\n    width: 10px;\r\n}\r\n\r\n/* Track */\r\n.popupz::-webkit-scrollbar-track {\r\n    background: #f1f1f1;\r\n    border-radius: 8px;\r\n}\r\n\r\n/* Handle */\r\n.popupz::-webkit-scrollbar-thumb {\r\n    background: #998989;\r\n    border-radius: 8px;\r\n}\r\n\r\n/* Handle on hover */\r\n.popupz::-webkit-scrollbar-thumb:hover {\r\n    background: #5e5858;\r\n}\r\n\r\n/* Montserrat Font */\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-ExtraLight.woff2\") format(\"woff2\");\r\n    font-weight: 200;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-ExtraLightItalic.woff2\") format(\"woff2\");\r\n    font-weight: 200;\r\n    font-style: italic;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-Light.woff2\") format(\"woff2\");\r\n    font-weight: 300;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-LightItalic.woff2\") format(\"woff2\");\r\n    font-weight: 300;\r\n    font-style: italic;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-Regular.woff2\") format(\"woff2\");\r\n    font-weight: 400;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-Italic.woff2\") format(\"woff2\");\r\n    font-weight: 400;\r\n    font-style: italic;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-Medium.woff2\") format(\"woff2\");\r\n    font-weight: 500;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-MediumItalic.woff2\") format(\"woff2\");\r\n    font-weight: 500;\r\n    font-style: italic;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-SemiBold.woff2\") format(\"woff2\");\r\n    font-weight: 600;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-SemiBoldItalic.woff2\") format(\"woff2\");\r\n    font-weight: 600;\r\n    font-style: italic;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-Bold.woff2\") format(\"woff2\");\r\n    font-weight: 700;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-BoldItalic.woff2\") format(\"woff2\");\r\n    font-weight: 700;\r\n    font-style: italic;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-ExtraBold.woff2\") format(\"woff2\");\r\n    font-weight: 800;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-ExtraBoldItalic.woff2\") format(\"woff2\");\r\n    font-weight: 800;\r\n    font-style: italic;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-Black.woff2\") format(\"woff2\");\r\n    font-weight: 900;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-BlackItalic.woff2\") format(\"woff2\");\r\n    font-weight: 900;\r\n    font-style: italic;\r\n}\r\n\r\n/* Variable weight fonts */\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat[wght].woff2\") format(\"woff2\");\r\n    font-weight: 100 900;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"Montserrat\";\r\n    src: url(\"/fonts/Montserrat/Montserrat-Italic[wght].woff2\") format(\"woff2\");\r\n    font-weight: 100 900;\r\n    font-style: italic;\r\n}\r\n\r\n/* ClashDisplay Font */\r\n\r\n@font-face {\r\n    font-family: \"ClashDisplay\";\r\n    src: url(\"/fonts/ClashDisplay/ClashDisplay-ExtraLight.woff2\") format(\"woff2\");\r\n    font-weight: 200;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"ClashDisplay\";\r\n    src: url(\"/fonts/ClashDisplay/ClashDisplay-Light.woff2\") format(\"woff2\");\r\n    font-weight: 300;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"ClashDisplay\";\r\n    src: url(\"/fonts/ClashDisplay/ClashDisplay-Regular.woff2\") format(\"woff2\");\r\n    font-weight: 400;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"ClashDisplay\";\r\n    src: url(\"/fonts/ClashDisplay/ClashDisplay-Medium.woff2\") format(\"woff2\");\r\n    font-weight: 500;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"ClashDisplay\";\r\n    src: url(\"/fonts/ClashDisplay/ClashDisplay-SemiBold.woff2\") format(\"woff2\");\r\n    font-weight: 600;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"ClashDisplay\";\r\n    src: url(\"/fonts/ClashDisplay/ClashDisplay-Bold.woff2\") format(\"woff2\");\r\n    font-weight: 700;\r\n    font-style: normal;\r\n}\r\n\r\n@font-face {\r\n    font-family: \"ClashDisplay\";\r\n    src: url(\"/fonts/ClashDisplay/ClashDisplay-ExtraBold.woff2\") format(\"woff2\");\r\n    font-weight: 800;\r\n    font-style: normal;\r\n}\r\n\r\n\r\n/* Variable weight fonts */\r\n@font-face {\r\n    font-family: \"ClashDisplay\";\r\n    src: url(\"/fonts/ClashDisplay/ClashDisplay-Variable.woff2\") format(\"woff2\");\r\n    font-weight: 100 900;\r\n    font-style: normal;\r\n}\r\n.file\\:border-0::file-selector-button {\n  border-width: 0px;\n}\r\n.file\\:bg-transparent::file-selector-button {\n  background-color: transparent;\n}\r\n.file\\:text-sm::file-selector-button {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\r\n.file\\:font-medium::file-selector-button {\n  font-weight: 500;\n}\r\n.file\\:text-foreground::file-selector-button {\n  color: hsl(var(--foreground));\n}\r\n.placeholder\\:text-\\[13px\\]::placeholder {\n  font-size: 13px;\n}\r\n.placeholder\\:text-gray-400::placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\r\n.placeholder\\:text-gray-500::placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\r\n.after\\:absolute::after {\n  content: var(--tw-content);\n  position: absolute;\n}\r\n.after\\:-bottom-1::after {\n  content: var(--tw-content);\n  bottom: -0.25rem;\n}\r\n.after\\:left-0::after {\n  content: var(--tw-content);\n  left: 0px;\n}\r\n.after\\:h-0\\.5::after {\n  content: var(--tw-content);\n  height: 0.125rem;\n}\r\n.after\\:w-0::after {\n  content: var(--tw-content);\n  width: 0px;\n}\r\n.after\\:bg-\\[\\#9C0E22\\]::after {\n  content: var(--tw-content);\n  --tw-bg-opacity: 1;\n  background-color: rgb(156 14 34 / var(--tw-bg-opacity, 1));\n}\r\n.after\\:transition-all::after {\n  content: var(--tw-content);\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.after\\:duration-300::after {\n  content: var(--tw-content);\n  transition-duration: 300ms;\n}\r\n.after\\:content-\\[\\'\\'\\]::after {\n  --tw-content: '';\n  content: var(--tw-content);\n}\r\n.after\\:duration-300::after {\n  content: var(--tw-content);\n  animation-duration: 300ms;\n}\r\n.last\\:mb-0:last-child {\n  margin-bottom: 0px;\n}\r\n.last\\:border-0:last-child {\n  border-width: 0px;\n}\r\n.last\\:border-b-0:last-child {\n  border-bottom-width: 0px;\n}\r\n.focus-within\\:relative:focus-within {\n  position: relative;\n}\r\n.focus-within\\:z-20:focus-within {\n  z-index: 20;\n}\r\n.hover\\:-translate-y-1:hover {\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:translate-y-\\[-2px\\]:hover {\n  --tw-translate-y: -2px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:rotate-180:hover {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:rotate-3:hover {\n  --tw-rotate: 3deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:scale-105:hover {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:scale-110:hover {\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:scale-\\[1\\.02\\]:hover {\n  --tw-scale-x: 1.02;\n  --tw-scale-y: 1.02;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:scale-\\[100\\.7\\%\\]:hover {\n  --tw-scale-x: 100.7%;\n  --tw-scale-y: 100.7%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:scale-\\[102\\%\\]:hover {\n  --tw-scale-x: 102%;\n  --tw-scale-y: 102%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:border-blue-200:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\n}\r\n.hover\\:border-gray-100:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\n}\r\n.hover\\:border-gray-300:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\r\n.hover\\:border-gray-400:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\n}\r\n.hover\\:bg-\\[\\#0F1C2D\\]\\/90:hover {\n  background-color: rgb(15 28 45 / 0.9);\n}\r\n.hover\\:bg-\\[\\#15154B\\]\\/90:hover {\n  background-color: rgb(21 21 75 / 0.9);\n}\r\n.hover\\:bg-\\[\\#152070\\]:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(21 32 112 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-\\[\\#1E2875\\]\\/90:hover {\n  background-color: rgb(30 40 117 / 0.9);\n}\r\n.hover\\:bg-\\[\\#2d2f3b\\]:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(45 47 59 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-\\[\\#600000\\]:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(96 0 0 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-\\[\\#9C0E22\\]:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(156 14 34 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-\\[\\#9C0E22\\]\\/90:hover {\n  background-color: rgb(156 14 34 / 0.9);\n}\r\n.hover\\:bg-\\[\\#EB6767\\]:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(235 103 103 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-\\[\\#f5eaea\\]:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(245 234 234 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-blue-100:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-blue-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-blue-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-blue-800\\/90:hover {\n  background-color: rgb(30 64 175 / 0.9);\n}\r\n.hover\\:bg-blue-900:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 58 138 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-blue-950\\/10:hover {\n  background-color: rgb(23 37 84 / 0.1);\n}\r\n.hover\\:bg-blue-950\\/90:hover {\n  background-color: rgb(23 37 84 / 0.9);\n}\r\n.hover\\:bg-emerald-300:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(110 231 183 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-emerald-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(5 150 105 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-gray-100:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-gray-200:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-gray-400:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-gray-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-gray-900:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-gray-950:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(3 7 18 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-green-200:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-indigo-950\\/90:hover {\n  background-color: rgb(30 27 75 / 0.9);\n}\r\n.hover\\:bg-neutral2:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 240 247 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-primary:hover {\n  background-color: hsl(var(--primary));\n}\r\n.hover\\:bg-primary\\/80:hover {\n  background-color: hsl(var(--primary) / 0.8);\n}\r\n.hover\\:bg-primary\\/90:hover {\n  background-color: hsl(var(--primary) / 0.9);\n}\r\n.hover\\:bg-red-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-red-600\\/90:hover {\n  background-color: rgb(220 38 38 / 0.9);\n}\r\n.hover\\:bg-slate-100:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(241 245 249 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-slate-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:bg-white\\/15:hover {\n  background-color: rgb(255 255 255 / 0.15);\n}\r\n.hover\\:bg-white\\/20:hover {\n  background-color: rgb(255 255 255 / 0.2);\n}\r\n.hover\\:bg-zinc-300:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(212 212 216 / var(--tw-bg-opacity, 1));\n}\r\n.hover\\:pl-4:hover {\n  padding-left: 1rem;\n}\r\n.hover\\:text-\\[\\#8B0000\\]:hover {\n  --tw-text-opacity: 1;\n  color: rgb(139 0 0 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-black:hover {\n  --tw-text-opacity: 1;\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-blue-500:hover {\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-blue-700:hover {\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-blue-800:hover {\n  --tw-text-opacity: 1;\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-blue-950:hover {\n  --tw-text-opacity: 1;\n  color: rgb(23 37 84 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-gray-300:hover {\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-gray-600:hover {\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-gray-700:hover {\n  --tw-text-opacity: 1;\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-gray-900:hover {\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-neutral-700:hover {\n  --tw-text-opacity: 1;\n  color: rgb(64 64 64 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-primary:hover {\n  color: hsl(var(--primary));\n}\r\n.hover\\:text-primary-foreground:hover {\n  color: hsl(var(--primary-foreground));\n}\r\n.hover\\:text-red-500:hover {\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-red-600:hover {\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:text-red-700:hover {\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\r\n.hover\\:underline:hover {\n  text-decoration-line: underline;\n}\r\n.hover\\:no-underline:hover {\n  text-decoration-line: none;\n}\r\n.hover\\:opacity-100:hover {\n  opacity: 1;\n}\r\n.hover\\:opacity-80:hover {\n  opacity: 0.8;\n}\r\n.hover\\:shadow:hover {\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:shadow-\\[0_8px_30px_rgb\\(0\\2c 0\\2c 0\\2c 0\\.06\\)\\]:hover {\n  --tw-shadow: 0 8px 30px rgb(0,0,0,0.06);\n  --tw-shadow-colored: 0 8px 30px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:shadow-lg:hover {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:shadow-md:hover {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:shadow-sm:hover {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:shadow-xl:hover {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:ring-1:hover {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.hover\\:ring-blue-950\\/30:hover {\n  --tw-ring-color: rgb(23 37 84 / 0.3);\n}\r\n.hover\\:drop-shadow-2xl:hover {\n  --tw-drop-shadow: drop-shadow(0 25px 25px rgb(0 0 0 / 0.15));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.hover\\:drop-shadow-sm:hover {\n  --tw-drop-shadow: drop-shadow(0 1px 1px rgb(0 0 0 / 0.05));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.hover\\:pause-animation:hover {\r\n        animation-play-state: paused;\r\n    }\r\n.hover\\:after\\:w-full:hover::after {\n  content: var(--tw-content);\n  width: 100%;\n}\r\n.focus\\:border-blue-500:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\r\n.focus\\:border-transparent:focus {\n  border-color: transparent;\n}\r\n.focus\\:bg-gray-100:focus {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\r\n.focus\\:bg-primary:focus {\n  background-color: hsl(var(--primary));\n}\r\n.focus\\:text-primary-foreground:focus {\n  color: hsl(var(--primary-foreground));\n}\r\n.focus\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.focus\\:ring-1:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.focus\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.focus\\:ring-blue-300:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity, 1));\n}\r\n.focus\\:ring-blue-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n}\r\n.focus\\:ring-blue-800:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(30 64 175 / var(--tw-ring-opacity, 1));\n}\r\n.focus\\:ring-gray-200:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity, 1));\n}\r\n.focus\\:ring-indigo-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));\n}\r\n.focus\\:ring-primary\\/20:focus {\n  --tw-ring-color: hsl(var(--primary) / 0.2);\n}\r\n.focus\\:ring-ring:focus {\n  --tw-ring-color: hsl(var(--ring));\n}\r\n.focus\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\r\n.focus-visible\\:outline-none:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.focus-visible\\:ring-1:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.focus-visible\\:ring-2:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.focus-visible\\:ring-gray-400:focus-visible {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(156 163 175 / var(--tw-ring-opacity, 1));\n}\r\n.focus-visible\\:ring-ring:focus-visible {\n  --tw-ring-color: hsl(var(--ring));\n}\r\n.focus-visible\\:ring-offset-2:focus-visible {\n  --tw-ring-offset-width: 2px;\n}\r\n.focus-visible\\:ring-offset-background:focus-visible {\n  --tw-ring-offset-color: hsl(var(--background));\n}\r\n.disabled\\:pointer-events-none:disabled {\n  pointer-events: none;\n}\r\n.disabled\\:cursor-not-allowed:disabled {\n  cursor: not-allowed;\n}\r\n.disabled\\:opacity-40:disabled {\n  opacity: 0.4;\n}\r\n.disabled\\:opacity-50:disabled {\n  opacity: 0.5;\n}\r\n.disabled\\:opacity-70:disabled {\n  opacity: 0.7;\n}\r\n.group[open] .group-open\\:rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.group:hover .group-hover\\:-translate-x-1 {\n  --tw-translate-x: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.group:hover .group-hover\\:translate-x-0 {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.group:hover .group-hover\\:translate-x-0\\.5 {\n  --tw-translate-x: 0.125rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.group:hover .group-hover\\:scale-105 {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.group:hover .group-hover\\:scale-\\[1\\.02\\] {\n  --tw-scale-x: 1.02;\n  --tw-scale-y: 1.02;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.group:hover .group-hover\\:bg-\\[\\#9C0E22\\] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(156 14 34 / var(--tw-bg-opacity, 1));\n}\r\n.group:hover .group-hover\\:bg-opacity-50 {\n  --tw-bg-opacity: 0.5;\n}\r\n.group:hover .group-hover\\:text-\\[\\#9C0E22\\] {\n  --tw-text-opacity: 1;\n  color: rgb(156 14 34 / var(--tw-text-opacity, 1));\n}\r\n.group:hover .group-hover\\:text-blue-900 {\n  --tw-text-opacity: 1;\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\n}\r\n.group:hover .group-hover\\:text-blue-950 {\n  --tw-text-opacity: 1;\n  color: rgb(23 37 84 / var(--tw-text-opacity, 1));\n}\r\n.group:hover .group-hover\\:opacity-100 {\n  opacity: 1;\n}\r\n.group:hover .group-hover\\:opacity-95 {\n  opacity: 0.95;\n}\r\n.peer:disabled ~ .peer-disabled\\:cursor-not-allowed {\n  cursor: not-allowed;\n}\r\n.peer:disabled ~ .peer-disabled\\:opacity-70 {\n  opacity: 0.7;\n}\r\n.aria-selected\\:opacity-100[aria-selected=\"true\"] {\n  opacity: 1;\n}\r\n.data-\\[disabled\\]\\:pointer-events-none[data-disabled] {\n  pointer-events: none;\n}\r\n.data-\\[side\\=bottom\\]\\:translate-y-1[data-side=\"bottom\"] {\n  --tw-translate-y: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[side\\=left\\]\\:-translate-x-1[data-side=\"left\"] {\n  --tw-translate-x: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[side\\=right\\]\\:translate-x-1[data-side=\"right\"] {\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[side\\=top\\]\\:-translate-y-1[data-side=\"top\"] {\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[state\\=checked\\]\\:translate-x-4[data-state=\"checked\"] {\n  --tw-translate-x: 1rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[state\\=unchecked\\]\\:translate-x-0[data-state=\"unchecked\"] {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n@keyframes accordion-up {\n\n  from {\n    height: var(--radix-accordion-content-height);\n  }\n\n  to {\n    height: 0;\n  }\n}\r\n.data-\\[state\\=closed\\]\\:animate-accordion-up[data-state=\"closed\"] {\n  animation: accordion-up 0.2s ease-out;\n}\r\n@keyframes accordion-down {\n\n  from {\n    height: 0;\n  }\n\n  to {\n    height: var(--radix-accordion-content-height);\n  }\n}\r\n.data-\\[state\\=open\\]\\:animate-accordion-down[data-state=\"open\"] {\n  animation: accordion-down 0.2s ease-out;\n}\r\n.data-\\[state\\=active\\]\\:border-gray-900[data-state=\"active\"] {\n  --tw-border-opacity: 1;\n  border-color: rgb(17 24 39 / var(--tw-border-opacity, 1));\n}\r\n.data-\\[state\\=active\\]\\:bg-white[data-state=\"active\"] {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\r\n.data-\\[state\\=checked\\]\\:bg-primary[data-state=\"checked\"] {\n  background-color: hsl(var(--primary));\n}\r\n.data-\\[state\\=unchecked\\]\\:bg-input[data-state=\"unchecked\"] {\n  background-color: hsl(var(--input));\n}\r\n.data-\\[state\\=active\\]\\:text-gray-900[data-state=\"active\"] {\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\r\n.data-\\[state\\=checked\\]\\:text-primary-foreground[data-state=\"checked\"] {\n  color: hsl(var(--primary-foreground));\n}\r\n.data-\\[disabled\\]\\:opacity-50[data-disabled] {\n  opacity: 0.5;\n}\r\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  transition-duration: 300ms;\n}\r\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  transition-duration: 500ms;\n}\r\n.data-\\[motion\\^\\=from-\\]\\:animate-in[data-motion^=\"from-\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.data-\\[state\\=visible\\]\\:animate-in[data-state=\"visible\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.data-\\[motion\\^\\=to-\\]\\:animate-out[data-motion^=\"to-\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n.data-\\[state\\=hidden\\]\\:animate-out[data-state=\"hidden\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n.data-\\[motion\\^\\=from-\\]\\:fade-in[data-motion^=\"from-\"] {\n  --tw-enter-opacity: 0;\n}\r\n.data-\\[motion\\^\\=to-\\]\\:fade-out[data-motion^=\"to-\"] {\n  --tw-exit-opacity: 0;\n}\r\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"] {\n  --tw-exit-opacity: 0;\n}\r\n.data-\\[state\\=hidden\\]\\:fade-out[data-state=\"hidden\"] {\n  --tw-exit-opacity: 0;\n}\r\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"] {\n  --tw-enter-opacity: 0;\n}\r\n.data-\\[state\\=visible\\]\\:fade-in[data-state=\"visible\"] {\n  --tw-enter-opacity: 0;\n}\r\n.data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"] {\n  --tw-exit-scale: .95;\n}\r\n.data-\\[state\\=open\\]\\:zoom-in-90[data-state=\"open\"] {\n  --tw-enter-scale: .9;\n}\r\n.data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"] {\n  --tw-enter-scale: .95;\n}\r\n.data-\\[motion\\=from-end\\]\\:slide-in-from-right-52[data-motion=\"from-end\"] {\n  --tw-enter-translate-x: 13rem;\n}\r\n.data-\\[motion\\=from-start\\]\\:slide-in-from-left-52[data-motion=\"from-start\"] {\n  --tw-enter-translate-x: -13rem;\n}\r\n.data-\\[motion\\=to-end\\]\\:slide-out-to-right-52[data-motion=\"to-end\"] {\n  --tw-exit-translate-x: 13rem;\n}\r\n.data-\\[motion\\=to-start\\]\\:slide-out-to-left-52[data-motion=\"to-start\"] {\n  --tw-exit-translate-x: -13rem;\n}\r\n.data-\\[side\\=bottom\\]\\:slide-in-from-top-2[data-side=\"bottom\"] {\n  --tw-enter-translate-y: -0.5rem;\n}\r\n.data-\\[side\\=left\\]\\:slide-in-from-right-2[data-side=\"left\"] {\n  --tw-enter-translate-x: 0.5rem;\n}\r\n.data-\\[side\\=right\\]\\:slide-in-from-left-2[data-side=\"right\"] {\n  --tw-enter-translate-x: -0.5rem;\n}\r\n.data-\\[side\\=top\\]\\:slide-in-from-bottom-2[data-side=\"top\"] {\n  --tw-enter-translate-y: 0.5rem;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-bottom[data-state=\"closed\"] {\n  --tw-exit-translate-y: 100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-left[data-state=\"closed\"] {\n  --tw-exit-translate-x: -100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2[data-state=\"closed\"] {\n  --tw-exit-translate-x: -50%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-right[data-state=\"closed\"] {\n  --tw-exit-translate-x: 100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-top[data-state=\"closed\"] {\n  --tw-exit-translate-y: -100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-top-\\[48\\%\\][data-state=\"closed\"] {\n  --tw-exit-translate-y: -48%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-bottom[data-state=\"open\"] {\n  --tw-enter-translate-y: 100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-left[data-state=\"open\"] {\n  --tw-enter-translate-x: -100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2[data-state=\"open\"] {\n  --tw-enter-translate-x: -50%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-right[data-state=\"open\"] {\n  --tw-enter-translate-x: 100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-top[data-state=\"open\"] {\n  --tw-enter-translate-y: -100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-top-\\[48\\%\\][data-state=\"open\"] {\n  --tw-enter-translate-y: -48%;\n}\r\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  animation-duration: 300ms;\n}\r\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  animation-duration: 500ms;\n}\r\n.group[data-state=\"open\"] .group-data-\\[state\\=open\\]\\:rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.dark\\:border-green-800:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(22 101 52 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-red-900:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(127 29 29 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:border-slate-700:is(.dark *) {\n  --tw-border-opacity: 1;\n  border-color: rgb(51 65 85 / var(--tw-border-opacity, 1));\n}\r\n.dark\\:bg-blue-900\\/20:is(.dark *) {\n  background-color: rgb(30 58 138 / 0.2);\n}\r\n.dark\\:bg-green-600:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-green-800\\/30:is(.dark *) {\n  background-color: rgb(22 101 52 / 0.3);\n}\r\n.dark\\:bg-green-900\\/20:is(.dark *) {\n  background-color: rgb(20 83 45 / 0.2);\n}\r\n.dark\\:bg-red-600:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-red-900\\/20:is(.dark *) {\n  background-color: rgb(127 29 29 / 0.2);\n}\r\n.dark\\:bg-red-950\\/20:is(.dark *) {\n  background-color: rgb(69 10 10 / 0.2);\n}\r\n.dark\\:bg-slate-700:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(51 65 85 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-slate-800:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-slate-900:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(15 23 42 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:bg-yellow-900\\/20:is(.dark *) {\n  background-color: rgb(113 63 18 / 0.2);\n}\r\n.dark\\:text-blue-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-gray-600:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-green-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-red-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-slate-200:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(226 232 240 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-slate-300:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(203 213 225 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-slate-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(148 163 184 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:text-yellow-400:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\n}\r\n.dark\\:hover\\:bg-green-700:hover:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:hover\\:bg-red-700:hover:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:hover\\:bg-slate-600:hover:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(71 85 105 / var(--tw-bg-opacity, 1));\n}\r\n.dark\\:hover\\:bg-slate-800:hover:is(.dark *) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 41 59 / var(--tw-bg-opacity, 1));\n}\r\n@media (min-width: 640px) {\n\n  .sm\\:mx-0 {\n    margin-left: 0px;\n    margin-right: 0px;\n  }\n\n  .sm\\:mx-4 {\n    margin-left: 1rem;\n    margin-right: 1rem;\n  }\n\n  .sm\\:my-12 {\n    margin-top: 3rem;\n    margin-bottom: 3rem;\n  }\n\n  .sm\\:my-6 {\n    margin-top: 1.5rem;\n    margin-bottom: 1.5rem;\n  }\n\n  .sm\\:my-8 {\n    margin-top: 2rem;\n    margin-bottom: 2rem;\n  }\n\n  .sm\\:mb-10 {\n    margin-bottom: 2.5rem;\n  }\n\n  .sm\\:ml-4 {\n    margin-left: 1rem;\n  }\n\n  .sm\\:mt-0 {\n    margin-top: 0px;\n  }\n\n  .sm\\:mt-4 {\n    margin-top: 1rem;\n  }\n\n  .sm\\:mt-6 {\n    margin-top: 1.5rem;\n  }\n\n  .sm\\:block {\n    display: block;\n  }\n\n  .sm\\:flex {\n    display: flex;\n  }\n\n  .sm\\:grid {\n    display: grid;\n  }\n\n  .sm\\:h-12 {\n    height: 3rem;\n  }\n\n  .sm\\:h-16 {\n    height: 4rem;\n  }\n\n  .sm\\:h-20 {\n    height: 5rem;\n  }\n\n  .sm\\:h-24 {\n    height: 6rem;\n  }\n\n  .sm\\:h-6 {\n    height: 1.5rem;\n  }\n\n  .sm\\:h-8 {\n    height: 2rem;\n  }\n\n  .sm\\:h-80 {\n    height: 20rem;\n  }\n\n  .sm\\:h-\\[140px\\] {\n    height: 140px;\n  }\n\n  .sm\\:max-h-\\[410px\\] {\n    max-height: 410px;\n  }\n\n  .sm\\:min-h-\\[410px\\] {\n    min-height: 410px;\n  }\n\n  .sm\\:w-12 {\n    width: 3rem;\n  }\n\n  .sm\\:w-16 {\n    width: 4rem;\n  }\n\n  .sm\\:w-20 {\n    width: 5rem;\n  }\n\n  .sm\\:w-24 {\n    width: 6rem;\n  }\n\n  .sm\\:w-32 {\n    width: 8rem;\n  }\n\n  .sm\\:w-40 {\n    width: 10rem;\n  }\n\n  .sm\\:w-6 {\n    width: 1.5rem;\n  }\n\n  .sm\\:w-64 {\n    width: 16rem;\n  }\n\n  .sm\\:w-8 {\n    width: 2rem;\n  }\n\n  .sm\\:w-\\[140px\\] {\n    width: 140px;\n  }\n\n  .sm\\:w-\\[300px\\] {\n    width: 300px;\n  }\n\n  .sm\\:w-\\[400px\\] {\n    width: 400px;\n  }\n\n  .sm\\:w-auto {\n    width: auto;\n  }\n\n  .sm\\:max-w-3xl {\n    max-width: 48rem;\n  }\n\n  .sm\\:max-w-\\[425px\\] {\n    max-width: 425px;\n  }\n\n  .sm\\:max-w-md {\n    max-width: 28rem;\n  }\n\n  .sm\\:max-w-sm {\n    max-width: 24rem;\n  }\n\n  .sm\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .sm\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .sm\\:flex-row {\n    flex-direction: row;\n  }\n\n  .sm\\:flex-col {\n    flex-direction: column;\n  }\n\n  .sm\\:flex-wrap {\n    flex-wrap: wrap;\n  }\n\n  .sm\\:items-start {\n    align-items: flex-start;\n  }\n\n  .sm\\:items-center {\n    align-items: center;\n  }\n\n  .sm\\:justify-end {\n    justify-content: flex-end;\n  }\n\n  .sm\\:justify-between {\n    justify-content: space-between;\n  }\n\n  .sm\\:gap-12 {\n    gap: 3rem;\n  }\n\n  .sm\\:gap-4 {\n    gap: 1rem;\n  }\n\n  .sm\\:gap-6 {\n    gap: 1.5rem;\n  }\n\n  .sm\\:gap-8 {\n    gap: 2rem;\n  }\n\n  .sm\\:gap-y-6 {\n    row-gap: 1.5rem;\n  }\n\n  .sm\\:gap-y-8 {\n    row-gap: 2rem;\n  }\n\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:space-x-4 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\n  }\n\n  .sm\\:space-y-2 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n  }\n\n  .sm\\:space-y-3 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n  }\n\n  .sm\\:space-y-4 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n  }\n\n  .sm\\:space-y-8 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n  }\n\n  .sm\\:rounded-lg {\n    border-radius: 0.5rem;\n  }\n\n  .sm\\:p-3 {\n    padding: 0.75rem;\n  }\n\n  .sm\\:p-4 {\n    padding: 1rem;\n  }\n\n  .sm\\:p-6 {\n    padding: 1.5rem;\n  }\n\n  .sm\\:p-8 {\n    padding: 2rem;\n  }\n\n  .sm\\:px-12 {\n    padding-left: 3rem;\n    padding-right: 3rem;\n  }\n\n  .sm\\:px-3 {\n    padding-left: 0.75rem;\n    padding-right: 0.75rem;\n  }\n\n  .sm\\:px-4 {\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\n\n  .sm\\:px-5 {\n    padding-left: 1.25rem;\n    padding-right: 1.25rem;\n  }\n\n  .sm\\:px-6 {\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .sm\\:px-8 {\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n\n  .sm\\:py-1\\.5 {\n    padding-top: 0.375rem;\n    padding-bottom: 0.375rem;\n  }\n\n  .sm\\:py-10 {\n    padding-top: 2.5rem;\n    padding-bottom: 2.5rem;\n  }\n\n  .sm\\:py-12 {\n    padding-top: 3rem;\n    padding-bottom: 3rem;\n  }\n\n  .sm\\:py-16 {\n    padding-top: 4rem;\n    padding-bottom: 4rem;\n  }\n\n  .sm\\:py-2 {\n    padding-top: 0.5rem;\n    padding-bottom: 0.5rem;\n  }\n\n  .sm\\:py-5 {\n    padding-top: 1.25rem;\n    padding-bottom: 1.25rem;\n  }\n\n  .sm\\:py-6 {\n    padding-top: 1.5rem;\n    padding-bottom: 1.5rem;\n  }\n\n  .sm\\:py-8 {\n    padding-top: 2rem;\n    padding-bottom: 2rem;\n  }\n\n  .sm\\:pb-6 {\n    padding-bottom: 1.5rem;\n  }\n\n  .sm\\:pt-4 {\n    padding-top: 1rem;\n  }\n\n  .sm\\:text-left {\n    text-align: left;\n  }\n\n  .sm\\:text-center {\n    text-align: center;\n  }\n\n  .sm\\:text-right {\n    text-align: right;\n  }\n\n  .sm\\:text-2xl {\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .sm\\:text-3xl {\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n\n  .sm\\:text-4xl {\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\n\n  .sm\\:text-\\[1rem\\] {\n    font-size: 1rem;\n  }\n\n  .sm\\:text-base {\n    font-size: 1rem;\n    line-height: 1.5rem;\n  }\n\n  .sm\\:text-lg {\n    font-size: 1.125rem;\n    line-height: 1.75rem;\n  }\n\n  .sm\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n  }\n\n  .sm\\:text-xl {\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n}\r\n@media (min-width: 768px) {\n\n  .md\\:absolute {\n    position: absolute;\n  }\n\n  .md\\:relative {\n    position: relative;\n  }\n\n  .md\\:sticky {\n    position: sticky;\n  }\n\n  .md\\:bottom-2 {\n    bottom: 0.5rem;\n  }\n\n  .md\\:bottom-auto {\n    bottom: auto;\n  }\n\n  .md\\:right-2 {\n    right: 0.5rem;\n  }\n\n  .md\\:top-8 {\n    top: 2rem;\n  }\n\n  .md\\:top-full {\n    top: 100%;\n  }\n\n  .md\\:z-0 {\n    z-index: 0;\n  }\n\n  .md\\:col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n\n  .md\\:mx-0 {\n    margin-left: 0px;\n    margin-right: 0px;\n  }\n\n  .md\\:mx-5 {\n    margin-left: 1.25rem;\n    margin-right: 1.25rem;\n  }\n\n  .md\\:my-10 {\n    margin-top: 2.5rem;\n    margin-bottom: 2.5rem;\n  }\n\n  .md\\:my-12 {\n    margin-top: 3rem;\n    margin-bottom: 3rem;\n  }\n\n  .md\\:my-20 {\n    margin-top: 5rem;\n    margin-bottom: 5rem;\n  }\n\n  .md\\:my-8 {\n    margin-top: 2rem;\n    margin-bottom: 2rem;\n  }\n\n  .md\\:-mt-24 {\n    margin-top: -6rem;\n  }\n\n  .md\\:-mt-48 {\n    margin-top: -12rem;\n  }\n\n  .md\\:mb-0 {\n    margin-bottom: 0px;\n  }\n\n  .md\\:mb-10 {\n    margin-bottom: 2.5rem;\n  }\n\n  .md\\:mb-12 {\n    margin-bottom: 3rem;\n  }\n\n  .md\\:mb-16 {\n    margin-bottom: 4rem;\n  }\n\n  .md\\:mb-4 {\n    margin-bottom: 1rem;\n  }\n\n  .md\\:ml-20 {\n    margin-left: 5rem;\n  }\n\n  .md\\:mt-10 {\n    margin-top: 2.5rem;\n  }\n\n  .md\\:mt-16 {\n    margin-top: 4rem;\n  }\n\n  .md\\:mt-2 {\n    margin-top: 0.5rem;\n  }\n\n  .md\\:mt-20 {\n    margin-top: 5rem;\n  }\n\n  .md\\:mt-8 {\n    margin-top: 2rem;\n  }\n\n  .md\\:block {\n    display: block;\n  }\n\n  .md\\:inline-block {\n    display: inline-block;\n  }\n\n  .md\\:flex {\n    display: flex;\n  }\n\n  .md\\:hidden {\n    display: none;\n  }\n\n  .md\\:h-10 {\n    height: 2.5rem;\n  }\n\n  .md\\:h-16 {\n    height: 4rem;\n  }\n\n  .md\\:h-20 {\n    height: 5rem;\n  }\n\n  .md\\:h-24 {\n    height: 6rem;\n  }\n\n  .md\\:h-96 {\n    height: 24rem;\n  }\n\n  .md\\:h-\\[150px\\] {\n    height: 150px;\n  }\n\n  .md\\:h-\\[3\\.6rem\\] {\n    height: 3.6rem;\n  }\n\n  .md\\:h-\\[calc\\(100vh-9rem\\)\\] {\n    height: calc(100vh - 9rem);\n  }\n\n  .md\\:h-auto {\n    height: auto;\n  }\n\n  .md\\:max-h-\\[300px\\] {\n    max-height: 300px;\n  }\n\n  .md\\:max-h-\\[400px\\] {\n    max-height: 400px;\n  }\n\n  .md\\:min-h-\\[160px\\] {\n    min-height: 160px;\n  }\n\n  .md\\:w-1\\/2 {\n    width: 50%;\n  }\n\n  .md\\:w-10 {\n    width: 2.5rem;\n  }\n\n  .md\\:w-16 {\n    width: 4rem;\n  }\n\n  .md\\:w-2\\/5 {\n    width: 40%;\n  }\n\n  .md\\:w-20 {\n    width: 5rem;\n  }\n\n  .md\\:w-24 {\n    width: 6rem;\n  }\n\n  .md\\:w-3\\/5 {\n    width: 60%;\n  }\n\n  .md\\:w-32 {\n    width: 8rem;\n  }\n\n  .md\\:w-40 {\n    width: 10rem;\n  }\n\n  .md\\:w-48 {\n    width: 12rem;\n  }\n\n  .md\\:w-64 {\n    width: 16rem;\n  }\n\n  .md\\:w-80 {\n    width: 20rem;\n  }\n\n  .md\\:w-\\[150px\\] {\n    width: 150px;\n  }\n\n  .md\\:w-\\[240px\\] {\n    width: 240px;\n  }\n\n  .md\\:w-\\[280px\\] {\n    width: 280px;\n  }\n\n  .md\\:w-\\[3\\.6rem\\] {\n    width: 3.6rem;\n  }\n\n  .md\\:w-\\[320px\\] {\n    width: 320px;\n  }\n\n  .md\\:w-\\[40\\%\\] {\n    width: 40%;\n  }\n\n  .md\\:w-\\[400px\\] {\n    width: 400px;\n  }\n\n  .md\\:w-\\[50vw\\] {\n    width: 50vw;\n  }\n\n  .md\\:w-\\[60\\%\\] {\n    width: 60%;\n  }\n\n  .md\\:w-\\[80px\\] {\n    width: 80px;\n  }\n\n  .md\\:w-\\[80vw\\] {\n    width: 80vw;\n  }\n\n  .md\\:w-\\[var\\(--radix-navigation-menu-viewport-width\\)\\] {\n    width: var(--radix-navigation-menu-viewport-width);\n  }\n\n  .md\\:w-auto {\n    width: auto;\n  }\n\n  .md\\:min-w-\\[380px\\] {\n    min-width: 380px;\n  }\n\n  .md\\:max-w-xl {\n    max-width: 36rem;\n  }\n\n  .md\\:translate-x-0 {\n    --tw-translate-x: 0px;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  }\n\n  .md\\:translate-y-2 {\n    --tw-translate-y: 0.5rem;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  }\n\n  .md\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .md\\:flex-row {\n    flex-direction: row;\n  }\n\n  .md\\:flex-col {\n    flex-direction: column;\n  }\n\n  .md\\:items-start {\n    align-items: flex-start;\n  }\n\n  .md\\:items-center {\n    align-items: center;\n  }\n\n  .md\\:justify-start {\n    justify-content: flex-start;\n  }\n\n  .md\\:justify-center {\n    justify-content: center;\n  }\n\n  .md\\:justify-between {\n    justify-content: space-between;\n  }\n\n  .md\\:gap-12 {\n    gap: 3rem;\n  }\n\n  .md\\:gap-16 {\n    gap: 4rem;\n  }\n\n  .md\\:gap-4 {\n    gap: 1rem;\n  }\n\n  .md\\:gap-6 {\n    gap: 1.5rem;\n  }\n\n  .md\\:gap-y-12 {\n    row-gap: 3rem;\n  }\n\n  .md\\:gap-y-4 {\n    row-gap: 1rem;\n  }\n\n  .md\\:gap-y-6 {\n    row-gap: 1.5rem;\n  }\n\n  .md\\:gap-y-8 {\n    row-gap: 2rem;\n  }\n\n  .md\\:space-y-12 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(3rem * var(--tw-space-y-reverse));\n  }\n\n  .md\\:space-y-20 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(5rem * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(5rem * var(--tw-space-y-reverse));\n  }\n\n  .md\\:space-y-4 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n  }\n\n  .md\\:space-y-6 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n  }\n\n  .md\\:self-center {\n    align-self: center;\n  }\n\n  .md\\:overflow-hidden {\n    overflow: hidden;\n  }\n\n  .md\\:rounded-2xl {\n    border-radius: 1rem;\n  }\n\n  .md\\:rounded-lg {\n    border-radius: 0.5rem;\n  }\n\n  .md\\:p-3 {\n    padding: 0.75rem;\n  }\n\n  .md\\:p-4 {\n    padding: 1rem;\n  }\n\n  .md\\:p-5 {\n    padding: 1.25rem;\n  }\n\n  .md\\:p-6 {\n    padding: 1.5rem;\n  }\n\n  .md\\:p-8 {\n    padding: 2rem;\n  }\n\n  .md\\:px-10 {\n    padding-left: 2.5rem;\n    padding-right: 2.5rem;\n  }\n\n  .md\\:px-12 {\n    padding-left: 3rem;\n    padding-right: 3rem;\n  }\n\n  .md\\:px-4 {\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\n\n  .md\\:px-6 {\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n\n  .md\\:px-7 {\n    padding-left: 1.75rem;\n    padding-right: 1.75rem;\n  }\n\n  .md\\:px-8 {\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n\n  .md\\:py-12 {\n    padding-top: 3rem;\n    padding-bottom: 3rem;\n  }\n\n  .md\\:py-16 {\n    padding-top: 4rem;\n    padding-bottom: 4rem;\n  }\n\n  .md\\:py-2 {\n    padding-top: 0.5rem;\n    padding-bottom: 0.5rem;\n  }\n\n  .md\\:py-20 {\n    padding-top: 5rem;\n    padding-bottom: 5rem;\n  }\n\n  .md\\:py-24 {\n    padding-top: 6rem;\n    padding-bottom: 6rem;\n  }\n\n  .md\\:py-8 {\n    padding-top: 2rem;\n    padding-bottom: 2rem;\n  }\n\n  .md\\:pb-10 {\n    padding-bottom: 2.5rem;\n  }\n\n  .md\\:pl-\\[95px\\] {\n    padding-left: 95px;\n  }\n\n  .md\\:pt-20 {\n    padding-top: 5rem;\n  }\n\n  .md\\:pt-\\[15vh\\] {\n    padding-top: 15vh;\n  }\n\n  .md\\:text-left {\n    text-align: left;\n  }\n\n  .md\\:text-2xl {\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .md\\:text-3xl {\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n\n  .md\\:text-4xl {\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\n\n  .md\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .md\\:text-\\[14px\\] {\n    font-size: 14px;\n  }\n\n  .md\\:text-\\[16px\\] {\n    font-size: 16px;\n  }\n\n  .md\\:text-\\[20px\\] {\n    font-size: 20px;\n  }\n\n  .md\\:text-\\[24px\\] {\n    font-size: 24px;\n  }\n\n  .md\\:text-\\[4\\.5vw\\] {\n    font-size: 4.5vw;\n  }\n\n  .md\\:text-base {\n    font-size: 1rem;\n    line-height: 1.5rem;\n  }\n\n  .md\\:text-lg {\n    font-size: 1.125rem;\n    line-height: 1.75rem;\n  }\n\n  .md\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n  }\n\n  .md\\:text-xl {\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n\n  .md\\:leading-\\[1\\.34\\] {\n    line-height: 1.34;\n  }\n\n  .md\\:opacity-0 {\n    opacity: 0;\n  }\n\n  .md\\:opacity-100 {\n    opacity: 1;\n  }\n\n  .md\\:shadow-lg {\n    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n  }\n\n  .md\\:drop-shadow-none {\n    --tw-drop-shadow: drop-shadow(0 0 #0000);\n    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n  }\n\n  .md\\:drop-shadow-sm {\n    --tw-drop-shadow: drop-shadow(0 1px 1px rgb(0 0 0 / 0.05));\n    filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n  }\n\n  .md\\:placeholder\\:text-base::placeholder {\n    font-size: 1rem;\n    line-height: 1.5rem;\n  }\n}\r\n@media (min-width: 1024px) {\n\n  .lg\\:absolute {\n    position: absolute;\n  }\n\n  .lg\\:right-2 {\n    right: 0.5rem;\n  }\n\n  .lg\\:right-2\\/4 {\n    right: 50%;\n  }\n\n  .lg\\:col-span-5 {\n    grid-column: span 5 / span 5;\n  }\n\n  .lg\\:col-span-7 {\n    grid-column: span 7 / span 7;\n  }\n\n  .lg\\:my-10 {\n    margin-top: 2.5rem;\n    margin-bottom: 2.5rem;\n  }\n\n  .lg\\:-mt-32 {\n    margin-top: -8rem;\n  }\n\n  .lg\\:block {\n    display: block;\n  }\n\n  .lg\\:inline-block {\n    display: inline-block;\n  }\n\n  .lg\\:inline {\n    display: inline;\n  }\n\n  .lg\\:flex {\n    display: flex;\n  }\n\n  .lg\\:hidden {\n    display: none;\n  }\n\n  .lg\\:h-32 {\n    height: 8rem;\n  }\n\n  .lg\\:h-36 {\n    height: 9rem;\n  }\n\n  .lg\\:w-1\\/2 {\n    width: 50%;\n  }\n\n  .lg\\:w-1\\/3 {\n    width: 33.333333%;\n  }\n\n  .lg\\:w-1\\/4 {\n    width: 25%;\n  }\n\n  .lg\\:w-1\\/6 {\n    width: 16.666667%;\n  }\n\n  .lg\\:w-2\\/3 {\n    width: 66.666667%;\n  }\n\n  .lg\\:w-2\\/4 {\n    width: 50%;\n  }\n\n  .lg\\:w-3\\/4 {\n    width: 75%;\n  }\n\n  .lg\\:w-32 {\n    width: 8rem;\n  }\n\n  .lg\\:w-36 {\n    width: 9rem;\n  }\n\n  .lg\\:w-6 {\n    width: 1.5rem;\n  }\n\n  .lg\\:w-\\[40vw\\] {\n    width: 40vw;\n  }\n\n  .lg\\:w-\\[500px\\] {\n    width: 500px;\n  }\n\n  .lg\\:w-\\[60vw\\] {\n    width: 60vw;\n  }\n\n  .lg\\:w-full {\n    width: 100%;\n  }\n\n  .lg\\:max-w-3xl {\n    max-width: 48rem;\n  }\n\n  .lg\\:grid-cols-12 {\n    grid-template-columns: repeat(12, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .lg\\:flex-row {\n    flex-direction: row;\n  }\n\n  .lg\\:flex-col {\n    flex-direction: column;\n  }\n\n  .lg\\:flex-nowrap {\n    flex-wrap: nowrap;\n  }\n\n  .lg\\:items-center {\n    align-items: center;\n  }\n\n  .lg\\:justify-between {\n    justify-content: space-between;\n  }\n\n  .lg\\:gap-12 {\n    gap: 3rem;\n  }\n\n  .lg\\:gap-6 {\n    gap: 1.5rem;\n  }\n\n  .lg\\:gap-8 {\n    gap: 2rem;\n  }\n\n  .lg\\:gap-y-6 {\n    row-gap: 1.5rem;\n  }\n\n  .lg\\:p-12 {\n    padding: 3rem;\n  }\n\n  .lg\\:p-6 {\n    padding: 1.5rem;\n  }\n\n  .lg\\:p-8 {\n    padding: 2rem;\n  }\n\n  .lg\\:px-10 {\n    padding-left: 2.5rem;\n    padding-right: 2.5rem;\n  }\n\n  .lg\\:px-12 {\n    padding-left: 3rem;\n    padding-right: 3rem;\n  }\n\n  .lg\\:px-16 {\n    padding-left: 4rem;\n    padding-right: 4rem;\n  }\n\n  .lg\\:px-20 {\n    padding-left: 5rem;\n    padding-right: 5rem;\n  }\n\n  .lg\\:px-8 {\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n\n  .lg\\:px-\\[15\\%\\] {\n    padding-left: 15%;\n    padding-right: 15%;\n  }\n\n  .lg\\:px-\\[5\\%\\] {\n    padding-left: 5%;\n    padding-right: 5%;\n  }\n\n  .lg\\:py-12 {\n    padding-top: 3rem;\n    padding-bottom: 3rem;\n  }\n\n  .lg\\:pl-12 {\n    padding-left: 3rem;\n  }\n\n  .lg\\:pl-\\[18\\%\\] {\n    padding-left: 18%;\n  }\n\n  .lg\\:pr-24 {\n    padding-right: 6rem;\n  }\n\n  .lg\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .lg\\:text-6xl {\n    font-size: 3.75rem;\n    line-height: 1;\n  }\n\n  .lg\\:text-\\[4vw\\] {\n    font-size: 4vw;\n  }\n}\r\n@media (min-width: 1280px) {\n\n  .xl\\:-mt-12 {\n    margin-top: -3rem;\n  }\n\n  .xl\\:ml-64 {\n    margin-left: 16rem;\n  }\n\n  .xl\\:h-\\[4\\.8rem\\] {\n    height: 4.8rem;\n  }\n\n  .xl\\:w-64 {\n    width: 16rem;\n  }\n\n  .xl\\:w-\\[320px\\] {\n    width: 320px;\n  }\n\n  .xl\\:w-\\[4\\.8rem\\] {\n    width: 4.8rem;\n  }\n\n  .xl\\:max-w-4xl {\n    max-width: 56rem;\n  }\n\n  .xl\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .xl\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .xl\\:flex-row {\n    flex-direction: row;\n  }\n\n  .xl\\:gap-8 {\n    gap: 2rem;\n  }\n\n  .xl\\:px-10 {\n    padding-left: 2.5rem;\n    padding-right: 2.5rem;\n  }\n\n  .xl\\:px-16 {\n    padding-left: 4rem;\n    padding-right: 4rem;\n  }\n\n  .xl\\:pt-32 {\n    padding-top: 8rem;\n  }\n\n  .xl\\:text-\\[3vw\\] {\n    font-size: 3vw;\n  }\n\n  .xl\\:opacity-100 {\n    opacity: 1;\n  }\n}\r\n@media (min-width: 1536px) {\n\n  .\\32xl\\:-mt-0 {\n    margin-top: -0px;\n  }\n\n  .\\32xl\\:mt-10 {\n    margin-top: 2.5rem;\n  }\n\n  .\\32xl\\:w-\\[40\\%\\] {\n    width: 40%;\n  }\n\n  .\\32xl\\:w-\\[60\\%\\] {\n    width: 60%;\n  }\n\n  .\\32xl\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .\\32xl\\:flex-row {\n    flex-direction: row;\n  }\n\n  .\\32xl\\:px-24 {\n    padding-left: 6rem;\n    padding-right: 6rem;\n  }\n\n  .\\32xl\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1;\n  }\n}\r\n@media (min-width: 1910px) {\n\n  .min-\\[1910px\\]\\:flex-nowrap {\n    flex-wrap: nowrap;\n  }\n}\r\n.\\[\\&\\:has\\(\\>\\.day-range-end\\)\\]\\:rounded-r-md:has(>.day-range-end) {\n  border-top-right-radius: 0.375rem;\n  border-bottom-right-radius: 0.375rem;\n}\r\n.\\[\\&\\:has\\(\\>\\.day-range-start\\)\\]\\:rounded-l-md:has(>.day-range-start) {\n  border-top-left-radius: 0.375rem;\n  border-bottom-left-radius: 0.375rem;\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-md:has([aria-selected]) {\n  border-radius: 0.375rem;\n}\r\n.first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md:has([aria-selected]):first-child {\n  border-top-left-radius: 0.375rem;\n  border-bottom-left-radius: 0.375rem;\n}\r\n.last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md:has([aria-selected]):last-child {\n  border-top-right-radius: 0.375rem;\n  border-bottom-right-radius: 0.375rem;\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md:has([aria-selected].day-range-end) {\n  border-top-right-radius: 0.375rem;\n  border-bottom-right-radius: 0.375rem;\n}\r\n.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0:has([role=checkbox]) {\n  padding-right: 0px;\n}\r\n.\\[\\&\\>\\[role\\=checkbox\\]\\]\\:translate-y-\\[2px\\]>[role=checkbox] {\n  --tw-translate-y: 2px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.\\[\\&\\>span\\]\\:line-clamp-1>span {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\r\n.\\[\\&\\>svg\\+div\\]\\:translate-y-\\[-3px\\]>svg+div {\n  --tw-translate-y: -3px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.\\[\\&\\>svg\\]\\:absolute>svg {\n  position: absolute;\n}\r\n.\\[\\&\\>svg\\]\\:left-4>svg {\n  left: 1rem;\n}\r\n.\\[\\&\\>svg\\]\\:top-4>svg {\n  top: 1rem;\n}\r\n.\\[\\&\\>svg\\]\\:size-4>svg {\n  width: 1rem;\n  height: 1rem;\n}\r\n.\\[\\&\\>svg\\]\\:shrink-0>svg {\n  flex-shrink: 0;\n}\r\n.\\[\\&\\>svg\\]\\:text-foreground>svg {\n  color: hsl(var(--foreground));\n}\r\n.\\[\\&\\>svg\\~\\*\\]\\:pl-7>svg~* {\n  padding-left: 1.75rem;\n}\r\n.\\[\\&\\>tr\\]\\:last\\:border-b-0:last-child>tr {\n  border-bottom-width: 0px;\n}\r\n.\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180[data-state=open]>svg {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.\\[\\&_p\\]\\:leading-relaxed p {\n  line-height: 1.625;\n}\r\n.\\[\\&_svg\\]\\:pointer-events-none svg {\n  pointer-events: none;\n}\r\n.\\[\\&_svg\\]\\:size-4 svg {\n  width: 1rem;\n  height: 1rem;\n}\r\n.\\[\\&_svg\\]\\:shrink-0 svg {\n  flex-shrink: 0;\n}\r\n.\\[\\&_tr\\:last-child\\]\\:border-0 tr:last-child {\n  border-width: 0px;\n}\r\n.\\[\\&_tr\\]\\:border-b tr {\n  border-bottom-width: 1px;\n}"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA;;;;;;;AASA;;;;AAeA;;;;;;;;;;;AAiBA;;;;;AAWA;;;;;;AAUA;;;;AAQA;;;;;AAcA;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAWA;;;;AAQA;;;;AASA;;;;;AAUA;;;;AAQA;;;;AAUA;;;;;AAgBA;;;;;AAOA;;;;AAGI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;;;;;AAIA;;;;;AAIJ;;;;AAGA;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAIA;;;;;;;;;;AASA;;;AAMA;;;;AAKA;;;;;AAMA;;;;;AAMA;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAQA;;;;;;;AAOA;;;;;;;AASA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AASA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;EAEE;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;EAKA;;;;;EAKA;;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;AAIF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;;AAIF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;;AAKF;EAEE;;;;;AAIF;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;;;AAMA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA"}}, {"offset": {"line": 7100, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}