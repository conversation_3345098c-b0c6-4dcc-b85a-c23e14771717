{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/for-counselors/hero.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport <PERSON><PERSON> from \"lottie-react\";\r\nimport { ArrowR<PERSON> } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@components/ui/button\";\r\nimport animationData from \"@/app/assets/lotties/CounselorPageHeader.json\";\r\nimport Link from \"next/link\";\r\n\r\nexport default function HeroSection() {\r\n  return (\r\n    <section className=\"px-6 mx-auto container my-10 md:my-12 items-center gap-6 lg:gap-12 flex flex-col lg:flex-row lg:justify-between lg:px-[5%]\">\r\n      <div className=\"max-w-2xl flex-col flex gap-y-4 md:gap-y-6\">\r\n        <h1 className=\"font-[500] tracking-tight text-blue-950 text-3xl md:text-4xl 2xl:text-5xl\">\r\n          Join <PERSON>mit<PERSON>ath and Empower Students to Achieve Their Dreams\r\n        </h1>\r\n        <p className=\"text-lg text-gray-600\">\r\n          Become a mentor and help students navigate their academic and career\r\n          journeys. Share your expertise, provide valuable guidance, and make a\r\n          lasting impact on the next generation.\r\n        </p>\r\n        <Link href=\"/auth/signup/counselor\">\r\n          <Button className=\"bg-blue-950 rounded-xl p-6  text-white text-md sm:text-lg flex justify-center items-center\">\r\n            Become a Counselor <ArrowRight className=\"h-6 w-6 ml-2\" />\r\n          </Button>\r\n        </Link>\r\n      </div>\r\n      <div className=\"relative\">\r\n        <Lottie\r\n          animationData={animationData}\r\n          loop={true}\r\n          className=\"h-full w-full\"\r\n        />\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAHA;AAHA;;;;;;;AAQe,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4E;;;;;;kCAG1F,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;kCAKrC,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;;gCAA6F;8CAC1F,6LAAC,qNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAI/C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2JAAA,CAAA,UAAM;oBACL,eAAe,uHAAA,CAAA,UAAa;oBAC5B,MAAM;oBACN,WAAU;;;;;;;;;;;;;;;;;AAKpB;KA3BwB"}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/for-counselors/mission.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect } from \"react\";\r\n\r\nconst Mission = () => {\r\n  const missionStatement: string =\r\n    \"The college admissions system is outdated. Traditional agencies lock students into rigid, overpriced packages, and limited personalized support. AdmitPath empowers counselors to offer flexible, on-demand guidance, ensuring every student gets the right advice from the right mentor. Together, we’re making college admissions more accessible.\";\r\n\r\n  useEffect(() => {\r\n    const missionSection: HTMLElement | null =\r\n      document.querySelector(\".mission-section\");\r\n    const words: NodeListOf<HTMLElement> = document.querySelectorAll(\r\n      \".mission-section .word\"\r\n    );\r\n\r\n    const updateOpacity = (): void => {\r\n      if (!missionSection) return;\r\n\r\n      const sectionTop: number = missionSection.offsetTop;\r\n      const sectionHeight: number = missionSection.offsetHeight;\r\n      const scrollY: number = window.scrollY;\r\n      const windowHeight: number = window.innerHeight;\r\n\r\n      const sectionStart: number =\r\n        sectionTop - windowHeight + sectionHeight * 0.4;\r\n      const sectionEnd: number = sectionTop + sectionHeight;\r\n\r\n      if (scrollY >= sectionStart && scrollY <= sectionEnd) {\r\n        const scrollProgress: number =\r\n          (scrollY - sectionStart) / (sectionEnd - sectionStart);\r\n\r\n        words.forEach((word: HTMLElement, index: number) => {\r\n          const wordProgress: number =\r\n            scrollProgress - index * (0.5 / words.length);\r\n          const opacity: number = Math.min(\r\n            Math.max(wordProgress * words.length, 0.2),\r\n            1\r\n          );\r\n          word.style.opacity = opacity.toString();\r\n        });\r\n      } else if (scrollY < sectionStart) {\r\n        words.forEach((word: HTMLElement) => (word.style.opacity = \"0.2\"));\r\n      } else if (scrollY > sectionEnd) {\r\n        words.forEach((word: HTMLElement) => (word.style.opacity = \"1\"));\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"scroll\", updateOpacity);\r\n    updateOpacity();\r\n\r\n    return () => window.removeEventListener(\"scroll\", updateOpacity);\r\n  }, []);\r\n\r\n  return (\r\n    <section className=\"mission-section py-12 px-4 md:px-10 lg:px-[15%] bg-mainClr text-white text-center\">\r\n      <h2 className=\"text-3xl font-medium mb-6\">Mission</h2>\r\n      <h2 className=\"mission_text text-xl md:text-3xl\">\r\n        {missionStatement.split(\" \").map((word: string, index: number) => (\r\n          <span\r\n            key={index}\r\n            className=\"word inline-block opacity-20 leading-10 transition-opacity duration-300 ease-in-out\"\r\n          >\r\n            {word}&nbsp;\r\n          </span>\r\n        ))}\r\n      </h2>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Mission;\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIA,MAAM,UAAU;;IACd,MAAM,mBACJ;IAEF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM,iBACJ,SAAS,aAAa,CAAC;YACzB,MAAM,QAAiC,SAAS,gBAAgB,CAC9D;YAGF,MAAM;mDAAgB;oBACpB,IAAI,CAAC,gBAAgB;oBAErB,MAAM,aAAqB,eAAe,SAAS;oBACnD,MAAM,gBAAwB,eAAe,YAAY;oBACzD,MAAM,UAAkB,OAAO,OAAO;oBACtC,MAAM,eAAuB,OAAO,WAAW;oBAE/C,MAAM,eACJ,aAAa,eAAe,gBAAgB;oBAC9C,MAAM,aAAqB,aAAa;oBAExC,IAAI,WAAW,gBAAgB,WAAW,YAAY;wBACpD,MAAM,iBACJ,CAAC,UAAU,YAAY,IAAI,CAAC,aAAa,YAAY;wBAEvD,MAAM,OAAO;+DAAC,CAAC,MAAmB;gCAChC,MAAM,eACJ,iBAAiB,QAAQ,CAAC,MAAM,MAAM,MAAM;gCAC9C,MAAM,UAAkB,KAAK,GAAG,CAC9B,KAAK,GAAG,CAAC,eAAe,MAAM,MAAM,EAAE,MACtC;gCAEF,KAAK,KAAK,CAAC,OAAO,GAAG,QAAQ,QAAQ;4BACvC;;oBACF,OAAO,IAAI,UAAU,cAAc;wBACjC,MAAM,OAAO;+DAAC,CAAC,OAAuB,KAAK,KAAK,CAAC,OAAO,GAAG;;oBAC7D,OAAO,IAAI,UAAU,YAAY;wBAC/B,MAAM,OAAO;+DAAC,CAAC,OAAuB,KAAK,KAAK,CAAC,OAAO,GAAG;;oBAC7D;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;YAEA;qCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;4BAAG,EAAE;IAEL,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAG,WAAU;0BAA4B;;;;;;0BAC1C,6LAAC;gBAAG,WAAU;0BACX,iBAAiB,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAc,sBAC9C,6LAAC;wBAEC,WAAU;;4BAET;4BAAK;;uBAHD;;;;;;;;;;;;;;;;AASjB;GAhEM;KAAA;uCAkES"}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/for-counselors/onboarding-process.tsx"], "sourcesContent": ["import { UserPlus, CheckCircle, Briefcase, TrendingUp } from \"lucide-react\";\r\n\r\nconst onboardingSteps = [\r\n  {\r\n    id: 1,\r\n    icon: <UserPlus size={40} color=\"#4F46E5\" />,\r\n    title: \"Apply\",\r\n    desc: \"Create your counselor profile and showcase your expertise to connect with students seeking guidance.\",\r\n  },\r\n  {\r\n    id: 2,\r\n    icon: <CheckCircle size={40} color=\"#22C55E\" />,\r\n    title: \"Get Verified\",\r\n    desc: \"Our team carefully reviews applications to ensure top-quality counselors join our platform.\",\r\n  },\r\n  {\r\n    id: 3,\r\n    icon: <Briefcase size={40} color=\"#F59E0B\" />,\r\n    title: \"Start Coaching\",\r\n    desc: \"Once approved, begin offering personalized guidance, helping students navigate their university admissions journey with confidence.\",\r\n  },\r\n  {\r\n    id: 4,\r\n    icon: <TrendingUp size={40} color=\"#EF4444\" />,\r\n    title: \"Grow Your Practice\",\r\n    desc: \"Earn income, build credibility, and create an impact on lives of many.\",\r\n  },\r\n];\r\n\r\nexport default function OnboardingProcess() {\r\n  return (\r\n    <section>\r\n      <div className=\"my-8 md:my-12 border rounded-xl container mx-auto px-8 py-8 lg:px-16 xl:px-10 space-y-10 md:space-y-20 w-[84%]\">\r\n        <div className=\"content\">\r\n          <ul className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-10 lg:gap-8\">\r\n            {onboardingSteps.map((item) => {\r\n              const { id, icon, title, desc } = item;\r\n\r\n              return (\r\n                <li key={id}>\r\n                  <span className=\"mb-2 md:mb-4 inline-block\">{icon}</span>\r\n                  <h3 className=\"title font-semibold mb-3\">{title}</h3>\r\n                  <p className=\"desc text-gray-600\">{desc}</p>\r\n                </li>\r\n              );\r\n            })}\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;;;AAEA,MAAM,kBAAkB;IACtB;QACE,IAAI;QACJ,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;YAAC,MAAM;YAAI,OAAM;;;;;;QAChC,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,oBAAM,6LAAC,8NAAA,CAAA,cAAW;YAAC,MAAM;YAAI,OAAM;;;;;;QACnC,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,oBAAM,6LAAC,+MAAA,CAAA,YAAS;YAAC,MAAM;YAAI,OAAM;;;;;;QACjC,OAAO;QACP,MAAM;IACR;IACA;QACE,IAAI;QACJ,oBAAM,6LAAC,qNAAA,CAAA,aAAU;YAAC,MAAM;YAAI,OAAM;;;;;;QAClC,OAAO;QACP,MAAM;IACR;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;kBACC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BACX,gBAAgB,GAAG,CAAC,CAAC;wBACpB,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;wBAElC,qBACE,6LAAC;;8CACC,6LAAC;oCAAK,WAAU;8CAA6B;;;;;;8CAC7C,6LAAC;oCAAG,WAAU;8CAA4B;;;;;;8CAC1C,6LAAC;oCAAE,WAAU;8CAAsB;;;;;;;2BAH5B;;;;;oBAMb;;;;;;;;;;;;;;;;;;;;;AAMZ;KAtBwB"}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/for-counselors/opportunity.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\nimport Link from \"next/link\";\r\n\r\nimport { ArrowR<PERSON> } from \"lucide-react\";\r\nimport { But<PERSON> } from \"@components/ui/button\";\r\n\r\nexport default function Opportunity() {\r\n  return (\r\n    <section>\r\n      <div className=\"my-8 md:my-12 container mx-auto px-8 lg:px-16 xl:px-10 space-y-10 md:space-y-20\">\r\n        <div className=\"relative flex w-full flex-wrap items-center justify-end overflow-hidden rounded-3xl border bg-white bg-cover bg-center lg:flex-nowrap\">\r\n          {/* Left Image */}\r\n          <div className=\"left-0 top-0 size-full lg:absolute lg:w-1/3\">\r\n            <Image\r\n              src=\"/images/happy-college-students.jpg\"\r\n              alt=\"Unlock-Opportunity-Graphic\"\r\n              width={800}\r\n              height={500}\r\n              className=\"size-full object-right\"\r\n            />\r\n          </div>\r\n\r\n          {/* Right Text Content */}\r\n          <div className=\"w-full px-4 py-10 lg:w-2/3 lg:py-12 lg:pl-12 lg:pr-24\">\r\n            <h2 className=\"mb-5 text-lg font-medium\">Open New Doors</h2>\r\n            <p className=\"mb-5 text-lg text-gray-600\">\r\n              Helping others achieve their dreams is truly rewarding. Join our\r\n              platform and make a lasting impact by guiding individuals toward\r\n              success.\r\n            </p>\r\n\r\n            {/* Call-to-Action Link */}\r\n            <Link href=\"/auth/signup/counselor\">\r\n              <Button className=\"bg-blue-950 rounded-xl p-6  text-white text-md sm:text-lg flex justify-center items-center\">\r\n                Become a Counselor <ArrowRight className=\"h-6 w-6 ml-2\" />\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAGA;AADA;;;;;;AAGe,SAAS;IACtB,qBACE,6LAAC;kBACC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,OAAO;4BACP,QAAQ;4BACR,WAAU;;;;;;;;;;;kCAKd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,6LAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAO1C,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;;wCAA6F;sDAC1F,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzD;KApCwB"}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/for-counselors/testimonials.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport {\r\n  Navigation,\r\n  Pagination,\r\n  Scrollbar,\r\n  A11y,\r\n  EffectCoverflow,\r\n  Autoplay,\r\n} from \"swiper/modules\";\r\nimport { Swiper, SwiperSlide } from \"swiper/react\";\r\nimport \"swiper/css\";\r\nimport \"swiper/css/navigation\";\r\nimport \"swiper/css/pagination\";\r\nimport \"swiper/css/scrollbar\";\r\nimport \"swiper/css/effect-coverflow\";\r\n\r\nconst testimonials = [\r\n  {\r\n    id: 1,\r\n    photo: \"\",\r\n    name: \"<PERSON><PERSON>\",\r\n    logo: \"./images/testimonials/bryn-mawr.png\",\r\n    text: \"It’s fulfilling to help students who wouldn’t normally have access to personalized admissions advice. AdmitPath makes that possible, and I love being part of that journey.\",\r\n  },\r\n  {\r\n    id: 2,\r\n    photo: \"./images/testimonials/maaz.jpg\",\r\n    name: \"<PERSON>\",\r\n    logo: \"./images/testimonials/yale-nus-college.png\",\r\n    text: \"It’s fulfilling to help students who wouldn’t normally have access to personalized admissions advice. AdmitPath makes that possible, and I love being part of that journey.\",\r\n  },\r\n  {\r\n    id: 3,\r\n    photo: \"\",\r\n    name: \"<PERSON>\",\r\n    logo: \"./images/testimonials/ucla.png\",\r\n    text: \"This platform makes it so easy to connect with students who are specifically looking for someone with my background — it feels personalized for both sides.\",\r\n  },\r\n  {\r\n    id: 4,\r\n    photo: \"\",\r\n    name: \"Anwaar <PERSON>\",\r\n    logo: \"./images/testimonials/georgetown.png\",\r\n    text: \"I love that AdmitPath lets students search by school and major — it means the students I work with genuinely value my experience and insights.\",\r\n  },\r\n  {\r\n    id: 5,\r\n    photo: \"\",\r\n    name: \"Ahmed Zafar\",\r\n    logo: \"./images/testimonials/stanford.png\",\r\n    text: \"AdmitPath isn’t just a platform — it’s a community of students and counselors working together to make admissions more accessible and personalized.\",\r\n  },\r\n  {\r\n    id: 6,\r\n    photo: \"./images/testimonials/anvay.jpg\",\r\n    name: \"Anvay Dixit\",\r\n    logo: \"./images/testimonials/yale-nus-college.png\",\r\n    text: \"AdmitPath treats counselors as true partners. We’re empowered to price fairly, work flexibly, and focus on what we do best — guiding students.\",\r\n  },\r\n  {\r\n    id: 7,\r\n    photo: \"./images/testimonials/arsum.png\",\r\n    name: \"Arsum Nadeem\",\r\n    logo: \"./images/testimonials/pomona.png\",\r\n    text: \"I love that AdmitPath lets students search by school and major — it means the students I work with genuinely value my experience and insights.\",\r\n  },\r\n  {\r\n    id: 8,\r\n    photo: \"./images/testimonials/aizaz.png\",\r\n    name: \"Aizaz Salahuddin\",\r\n    logo: \"./images/testimonials/dartmouth.png\",\r\n    text: \"AdmitPath is built for counselors like me who want flexibility. Whether I have 1 hour or 3 hours, I can easily find students who need exactly what I offer.\",\r\n  },\r\n];\r\n\r\nexport default function Testimonials() {\r\n  return (\r\n    <section className=\"w-full bg-mainClr px-8 lg:px-16 xl:px-10 py-20 mt-10 md:mt-20 relative overflow-hidden\">\r\n      <div className=\"container w-[90%] mx-auto\">\r\n        <div className=\"\">\r\n          <h2 className=\"text-4xl md:text-5xl font-semibold text-white font-space\">\r\n            We&apos;re being talked about\r\n          </h2>\r\n\r\n          <p className=\"mt-4 text-lg text-white/90\">\r\n            Take a look at what they have to say about us - it&apos;s always\r\n            interesting to hear different perspectives!\r\n          </p>\r\n        </div>\r\n\r\n        <div className=\"py-20 relative\">\r\n          {/* Swiper Container */}\r\n          <Swiper\r\n            modules={[\r\n              Navigation,\r\n              Pagination,\r\n              Scrollbar,\r\n              A11y,\r\n              EffectCoverflow,\r\n              Autoplay,\r\n            ]}\r\n            effect=\"coverflow\"\r\n            grabCursor={true}\r\n            centeredSlides={true}\r\n            coverflowEffect={{\r\n              rotate: 0,\r\n              stretch: 0,\r\n              depth: 100,\r\n              modifier: 2,\r\n              slideShadows: true,\r\n            }}\r\n            loop\r\n            loopAdditionalSlides={2}\r\n            autoplay={{\r\n              delay: 3000,\r\n              disableOnInteraction: false,\r\n            }}\r\n            navigation={{\r\n              nextEl: \".swiper-button-next\",\r\n              prevEl: \".swiper-button-prev\",\r\n            }}\r\n            breakpoints={{\r\n              1024: { slidesPerView: 3 },\r\n              768: { slidesPerView: 2 },\r\n              480: { slidesPerView: 1 },\r\n            }}\r\n          >\r\n            {testimonials.map(({ id, photo, name, logo, text }) => (\r\n              <SwiperSlide key={id}>\r\n                <div className=\"group relative bg-white shadow-2xl rounded-xl min-h-[340px] overflow-hidden\">\r\n                  {/* Testimonial Content */}\r\n                  <div className=\"relative p-6 flex flex-col items-center text-center\">\r\n                    {/* Profile Photo */}\r\n                    <div className=\"w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center mb-4 overflow-hidden\">\r\n                      {photo ? (\r\n                        <img\r\n                          src={photo}\r\n                          alt={name}\r\n                          className=\"w-full h-full object-cover\"\r\n                        />\r\n                      ) : (\r\n                        <span className=\"text-gray-500 text-2xl font-bold\">\r\n                          {name.split(\" \").map((word) => word[0])}\r\n                        </span>\r\n                      )}\r\n                    </div>\r\n\r\n                    {/* Testimonial Text */}\r\n                    <p className=\"text-gray-700 text-sm leading-relaxed mb-4 flex-1\">\r\n                      {text}\r\n                    </p>\r\n\r\n                    {/* Name and Logo */}\r\n                    <div className=\"mt-auto\">\r\n                      <h3 className=\"text-lg font-semibold text-gray-900\">\r\n                        {name}\r\n                      </h3>\r\n                      <div className=\"mt-2\">\r\n                        <img\r\n                          src={logo}\r\n                          alt={`${name}'s Logo`}\r\n                          className=\"w-14 h-10 mx-auto\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </SwiperSlide>\r\n            ))}\r\n          </Swiper>\r\n\r\n          {/* Custom Navigation Arrows */}\r\n          <div className=\"swiper-button-prev !bottom-0 !top-auto !left-[45%] !w-10 !h-10 !mt-0\"></div>\r\n          <div className=\"swiper-button-next !bottom-0 !top-auto !right-[45%] !w-10 !h-10 !mt-0\"></div>\r\n\r\n          {/* Custom CSS for Navigation Arrows */}\r\n          <style>\r\n            {`\r\n          .swiper-button-prev,\r\n          .swiper-button-next {\r\n            position: absolute;\r\n            bottom: 0;\r\n            top: auto;\r\n            width: 40px;\r\n            height: 40px;\r\n            border-radius: 50%;\r\n            color: white;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            transition: background-color 0.3s ease;\r\n          }\r\n\r\n          .swiper-button-prev::after,\r\n          .swiper-button-next::after {\r\n            font-size: 20px;\r\n            font-weight: bold;\r\n          }\r\n\r\n          .swiper-button-prev {\r\n            left: 45%;\r\n            transform: translateX(-50%);\r\n          }\r\n\r\n          .swiper-button-next {\r\n            right: 45%;\r\n            transform: translateX(50%);\r\n          }\r\n        `}\r\n          </style>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAQA;AARA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;;;;;;;AAiBA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;IACR;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAIzE,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAM5C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,6IAAA,CAAA,SAAM;4BACL,SAAS;gCACP,4LAAA,CAAA,aAAU;gCACV,4LAAA,CAAA,aAAU;gCACV,0LAAA,CAAA,YAAS;gCACT,gLAAA,CAAA,OAAI;gCACJ,0MAAA,CAAA,kBAAe;gCACf,wLAAA,CAAA,WAAQ;6BACT;4BACD,QAAO;4BACP,YAAY;4BACZ,gBAAgB;4BAChB,iBAAiB;gCACf,QAAQ;gCACR,SAAS;gCACT,OAAO;gCACP,UAAU;gCACV,cAAc;4BAChB;4BACA,IAAI;4BACJ,sBAAsB;4BACtB,UAAU;gCACR,OAAO;gCACP,sBAAsB;4BACxB;4BACA,YAAY;gCACV,QAAQ;gCACR,QAAQ;4BACV;4BACA,aAAa;gCACX,MAAM;oCAAE,eAAe;gCAAE;gCACzB,KAAK;oCAAE,eAAe;gCAAE;gCACxB,KAAK;oCAAE,eAAe;gCAAE;4BAC1B;sCAEC,aAAa,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,iBAChD,6LAAC,6IAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDAEb,cAAA,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;8DACZ,sBACC,6LAAC;wDACC,KAAK;wDACL,KAAK;wDACL,WAAU;;;;;6EAGZ,6LAAC;wDAAK,WAAU;kEACb,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,OAAS,IAAI,CAAC,EAAE;;;;;;;;;;;8DAM5C,6LAAC;oDAAE,WAAU;8DACV;;;;;;8DAIH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX;;;;;;sEAEH,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,KAAK;gEACL,KAAK,GAAG,KAAK,OAAO,CAAC;gEACrB,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAjCJ;;;;;;;;;;sCA4CtB,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;sCACE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA+BN,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAMT;KA3IwB"}}, {"offset": {"line": 789, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 795, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/for-counselors/why-choose-us.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport <PERSON><PERSON> from \"lottie-react\";\r\nimport { <PERSON><PERSON> } from \"@components/ui/button\";\r\nimport { ArrowR<PERSON> } from \"lucide-react\";\r\nimport webappAnimation from \"@/app/assets/lotties/WebappScreen.json\";\r\nimport Link from \"next/link\";\r\n\r\nexport default function WhyChooseUs() {\r\n  return (\r\n    <section className=\" bg-white my-8 md:my-12 container mx-auto px-8 lg:px-16 xl:px-10 space-y-10 md:space-y-20\">\r\n      <div className=\"text-center\">\r\n        <h2 className=\"text-3xl font-semibold tracking-tight text-[#14162F] sm:text-4xl\">\r\n          Why Join AdmitPath?\r\n        </h2>\r\n        <p className=\"mx-auto mt-4 max-w-3xl text-lg text-gray-600\">\r\n          Guide. Grow. Earn.\r\n        </p>\r\n        <p className=\"mx-auto mt-4 max-w-3xl text-lg text-gray-600\">\r\n          Join <PERSON>mit<PERSON> and become part of a network of experienced mentors,\r\n          university alumni, and academic professionals making a real impact.\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\" grid grid-cols-1 gap-12 lg:grid-cols-2\">\r\n        <div className=\"flex flex-col gap-y-8 md:gap-y-12 justify-center\">\r\n          <Feature\r\n            icon=\"🌟\"\r\n            title=\"Expand Your Impact\"\r\n            description=\"Help students navigate the complex university admissions process by sharing your expertise and firsthand experience.\"\r\n          />\r\n          <Feature\r\n            icon=\"💰\"\r\n            title=\"Earn for Your Expertise\"\r\n            description=\"Monetize your counseling skills and academic knowledge. Set your availability, offer personalized guidance, and get compensated for your time while working flexibly on your own terms.\"\r\n          />\r\n          <Feature\r\n            icon=\"🤝\"\r\n            title=\"Grow Your Professional Network\"\r\n            description=\"Connect with like-minded professionals, experienced mentors, and top university alumni. Stay updated on industry trends, and build valuable relationships.\"\r\n          />\r\n          <Link href=\"/auth/signup/counselor\">\r\n            <Button className=\"bg-blue-950 rounded-xl p-6  text-white text-md sm:text-lg flex justify-center items-center\">\r\n              Become a Counselor <ArrowRight className=\"h-6 w-6 ml-2\" />\r\n            </Button>\r\n          </Link>\r\n        </div>\r\n        <div className=\"hidden lg:inline-block relative h-[600px] rounded-2xl bg-[#14162F] pt-8 pl-8\">\r\n          <Lottie\r\n            animationData={webappAnimation}\r\n            loop={true}\r\n            className=\"h-full w-full\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n\r\nfunction Feature({\r\n  icon,\r\n  title,\r\n  description,\r\n}: {\r\n  icon: string;\r\n  title: string;\r\n  description: string;\r\n}) {\r\n  return (\r\n    <div className=\"flex gap-4\">\r\n      <div className=\"flex sm:h-16 sm:w-16 h-12 w-12 items-center justify-center rounded-lg bg-blue-50 text-2xl sm:text-3xl\">\r\n        {icon}\r\n      </div>\r\n      <div>\r\n        <h1 className=\"font-[500] text-[#14162F] text-xl md:text-2xl\">\r\n          {title}\r\n        </h1>\r\n        <p className=\"mt-2 text-gray-600\">{description}</p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AAFA;AAJA;;;;;;;AAQe,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;;0BACjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmE;;;;;;kCAGjF,6LAAC;wBAAE,WAAU;kCAA+C;;;;;;kCAG5D,6LAAC;wBAAE,WAAU;kCAA+C;;;;;;;;;;;;0BAM9D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,OAAM;gCACN,aAAY;;;;;;0CAEd,6LAAC;gCACC,MAAK;gCACL,OAAM;gCACN,aAAY;;;;;;0CAEd,6LAAC;gCACC,MAAK;gCACL,OAAM;gCACN,aAAY;;;;;;0CAEd,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;;wCAA6F;sDAC1F,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAI/C,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,2JAAA,CAAA,UAAM;4BACL,eAAe,gHAAA,CAAA,UAAe;4BAC9B,MAAM;4BACN,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAMtB;KAjDwB;AAmDxB,SAAS,QAAQ,EACf,IAAI,EACJ,KAAK,EACL,WAAW,EAKZ;IACC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAEH,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCACX;;;;;;kCAEH,6LAAC;wBAAE,WAAU;kCAAsB;;;;;;;;;;;;;;;;;;AAI3C;MAtBS"}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 997, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/%28landing%29/for-counselors/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport HeroS<PERSON>tion from \"@/app/components/public/for-counselors/hero\";\r\nimport Mission from \"@/app/components/public/for-counselors/mission\";\r\nimport OnboardingProcess from \"@/app/components/public/for-counselors/onboarding-process\";\r\nimport Opportunity from \"@/app/components/public/for-counselors/opportunity\";\r\nimport Testimonials from \"@/app/components/public/for-counselors/testimonials\";\r\nimport WhyChooseUs from \"@/app/components/public/for-counselors/why-choose-us\";\r\n\r\nexport default function ForCounselorsPage() {\r\n  return (\r\n    <div className=\"flex flex-col font-clash-display\">\r\n      <HeroSection />\r\n      <OnboardingProcess />\r\n      <Mission />\r\n      <WhyChooseUs />\r\n      <Testimonials />\r\n      <Opportunity />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,4JAAA,CAAA,UAAW;;;;;0BACZ,6LAAC,6KAAA,CAAA,UAAiB;;;;;0BAClB,6LAAC,+JAAA,CAAA,UAAO;;;;;0BACR,6LAAC,2KAAA,CAAA,UAAW;;;;;0BACZ,6LAAC,oKAAA,CAAA,UAAY;;;;;0BACb,6LAAC,mKAAA,CAAA,UAAW;;;;;;;;;;;AAGlB;KAXwB"}}, {"offset": {"line": 1062, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}