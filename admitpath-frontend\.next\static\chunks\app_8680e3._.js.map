{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/student/useResources.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\n\r\nexport interface Resource {\r\n  id: number;\r\n  title: string;\r\n  description: string;\r\n  file_url: string;\r\n  file_type: string;\r\n  image_url?: string;\r\n  category: string;\r\n  audience: string;\r\n  uploaded_by: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\ninterface ResourcesState {\r\n  resources: Resource[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  page: number;\r\n  hasMore: boolean;\r\n  total: number;\r\n  searchTerm: string;\r\n  category: string | null;\r\n  audience: string | null;\r\n\r\n  // Actions\r\n  setSearchTerm: (term: string) => void;\r\n  setCategory: (category: string | null) => void;\r\n  setAudience: (audience: string | null) => void;\r\n  fetchResources: () => Promise<void>;\r\n  loadMore: () => void;\r\n  clearError: () => void;\r\n}\r\n\r\nexport const useResources = create<ResourcesState>((set, get) => ({\r\n  resources: [],\r\n  loading: false,\r\n  error: null,\r\n  page: 1,\r\n  hasMore: true,\r\n  total: 0,\r\n  searchTerm: \"\",\r\n  category: null,\r\n  audience: null,\r\n\r\n  setSearchTerm: (term) => {\r\n    set({ searchTerm: term, page: 1, resources: [], hasMore: true });\r\n    get().fetchResources();\r\n  },\r\n\r\n  setCategory: (category) => {\r\n    set({ category, page: 1, resources: [], hasMore: true });\r\n    get().fetchResources();\r\n  },\r\n\r\n  setAudience: (audience) => {\r\n    set({ audience, page: 1, resources: [], hasMore: true });\r\n    get().fetchResources();\r\n  },\r\n\r\n  fetchResources: async () => {\r\n    try {\r\n      const { page, category, searchTerm, audience } = get();\r\n      set({ loading: true, error: null });\r\n\r\n      const queryParams = new URLSearchParams();\r\n      if (category) queryParams.append(\"category\", category);\r\n      if (searchTerm) queryParams.append(\"search\", searchTerm);\r\n      if (audience) queryParams.append(\"audience\", audience);\r\n      queryParams.append(\"page\", page.toString());\r\n      queryParams.append(\"page_size\", \"10\"); // Fixed page size\r\n      \r\n      const response = await apiClient.get(`/resources?${queryParams.toString()}`);\r\n      const data = response.data;\r\n\r\n      set((state) => ({\r\n        resources: page === 1 ? data.items : [...state.resources, ...data.items],\r\n        total: data.total,\r\n        hasMore: data.items.length > 0, // If no items returned, we've reached the end\r\n        loading: false\r\n      }));\r\n    } catch (error: any) {\r\n      const errorMessage = error.response?.data?.detail || \"Failed to fetch resources\";\r\n      set({ error: errorMessage, loading: false });\r\n      toast.error(errorMessage);\r\n    }\r\n  },\r\n\r\n  loadMore: () => {\r\n    const { loading, hasMore } = get();\r\n    if (!loading && hasMore) {\r\n      set((state) => ({ page: state.page + 1 }));\r\n      get().fetchResources();\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null })\r\n}));"], "names": [], "mappings": ";;;AAGA;AACA;AAFA;AAFA;;;;AAwCO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAkB,CAAC,KAAK,MAAQ,CAAC;QAChE,WAAW,EAAE;QACb,SAAS;QACT,OAAO;QACP,MAAM;QACN,SAAS;QACT,OAAO;QACP,YAAY;QACZ,UAAU;QACV,UAAU;QAEV,eAAe,CAAC;YACd,IAAI;gBAAE,YAAY;gBAAM,MAAM;gBAAG,WAAW,EAAE;gBAAE,SAAS;YAAK;YAC9D,MAAM,cAAc;QACtB;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;gBAAU,MAAM;gBAAG,WAAW,EAAE;gBAAE,SAAS;YAAK;YACtD,MAAM,cAAc;QACtB;QAEA,aAAa,CAAC;YACZ,IAAI;gBAAE;gBAAU,MAAM;gBAAG,WAAW,EAAE;gBAAE,SAAS;YAAK;YACtD,MAAM,cAAc;QACtB;QAEA,gBAAgB;YACd,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG;gBACjD,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBAEjC,MAAM,cAAc,IAAI;gBACxB,IAAI,UAAU,YAAY,MAAM,CAAC,YAAY;gBAC7C,IAAI,YAAY,YAAY,MAAM,CAAC,UAAU;gBAC7C,IAAI,UAAU,YAAY,MAAM,CAAC,YAAY;gBAC7C,YAAY,MAAM,CAAC,QAAQ,KAAK,QAAQ;gBACxC,YAAY,MAAM,CAAC,aAAa,OAAO,kBAAkB;gBAEzD,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,YAAY,QAAQ,IAAI;gBAC3E,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,CAAC,QAAU,CAAC;wBACd,WAAW,SAAS,IAAI,KAAK,KAAK,GAAG;+BAAI,MAAM,SAAS;+BAAK,KAAK,KAAK;yBAAC;wBACxE,OAAO,KAAK,KAAK;wBACjB,SAAS,KAAK,KAAK,CAAC,MAAM,GAAG;wBAC7B,SAAS;oBACX,CAAC;YACH,EAAE,OAAO,OAAY;gBACnB,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,UAAU;gBACrD,IAAI;oBAAE,OAAO;oBAAc,SAAS;gBAAM;gBAC1C,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,UAAU;YACR,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;YAC7B,IAAI,CAAC,WAAW,SAAS;gBACvB,IAAI,CAAC,QAAU,CAAC;wBAAE,MAAM,MAAM,IAAI,GAAG;oBAAE,CAAC;gBACxC,MAAM,cAAc;YACtB;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC"}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/public/useCounselors.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport {\r\n  CounselorPublicProfile,\r\n  CounselorListResponse,\r\n} from \"@/app/types/counselor\";\r\nimport { format } from \"date-fns\";\r\nimport { preloadUniversityLogos } from \"@/app/utils/uni-logo\";\r\n\r\ninterface Filters {\r\n  service_type?: string[];\r\n  country?: string[];\r\n  university?: string[];\r\n  name?: string;\r\n}\r\n\r\ninterface ApiParams extends Filters {\r\n  page: number;\r\n  limit: number;\r\n  service_types?: string;\r\n  countries?: string;\r\n  universities?: string;\r\n  name?: string;\r\n}\r\n\r\ninterface CounselorsState {\r\n  loading: boolean;\r\n  profileLoading: boolean;\r\n  availabilityLoading: boolean;\r\n  datesLoading: boolean;\r\n  error: string | null;\r\n  counselors: CounselorPublicProfile[] | null;\r\n  counselorIds: number[];\r\n  filters: Filters;\r\n  currentPage: number;\r\n  totalPages: number;\r\n  hasMore: boolean;\r\n  availableDates: string[];\r\n  timeSlots: string[];\r\n\r\n  fetchAllCounselors: () => Promise<void>;\r\n  fetchCounselorProfile: (userId: number) => Promise<CounselorPublicProfile>;\r\n  setFilters: (filters: Filters) => void;\r\n  fetchTopCounselors: () => Promise<CounselorPublicProfile[]>;\r\n  fetchTimeSlots: (\r\n    counselorUserId: number,\r\n    targetDate: Date,\r\n    timezone: string\r\n  ) => Promise<string[]>;\r\n  fetchAvailableDates: (\r\n    counselorUserId: number,\r\n    startDate: Date,\r\n    endDate: Date,\r\n    timezone: string\r\n  ) => Promise<string[]>;\r\n  loadMore: () => Promise<void>;\r\n  setLoading: (loading: boolean) => void;\r\n  clearFilters: () => void;\r\n}\r\n\r\nexport const useCounselors = create<CounselorsState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      loading: false,\r\n      profileLoading: false,\r\n      availabilityLoading: false,\r\n      datesLoading: false,\r\n      error: null,\r\n      counselors: null,\r\n      counselorIds: [],\r\n      filters: {},\r\n      currentPage: 1,\r\n      totalPages: 1,\r\n      hasMore: false,\r\n      availableDates: [],\r\n      timeSlots: [],\r\n\r\n      setLoading: (loading) => {\r\n        set({ loading });\r\n      },\r\n      fetchAllCounselors: async () => {\r\n        try {\r\n          const { filters, currentPage } = get();\r\n\r\n          // Only set loading true if this is the first page\r\n          if (currentPage === 1) {\r\n            set({ loading: true, error: null });\r\n          }\r\n\r\n          // Format the parameters for the API call\r\n          const params: ApiParams = {\r\n            page: currentPage,\r\n            limit: 16,\r\n          };\r\n\r\n          // Only add filters that have a value\r\n          if (filters.service_type && filters.service_type.length > 0) {\r\n            params.service_types = filters.service_type.join(\",\");\r\n          }\r\n\r\n          if (filters.country && filters.country.length > 0) {\r\n            params.countries = filters.country.join(\",\");\r\n          }\r\n\r\n          if (filters.university && filters.university.length > 0) {\r\n            params.universities = filters.university.join(\",\");\r\n          }\r\n\r\n          // Only add name if it exists and is not empty\r\n          if (filters.name && filters.name.trim() !== \"\") {\r\n            params.name = filters.name.trim();\r\n          }\r\n\r\n          const { data } = await apiClient.get<CounselorListResponse>(\r\n            \"/counselor/profiles/public-profile/list\",\r\n            { params }\r\n          );\r\n\r\n          if (data.items && data.items.length > 0) {\r\n            // Preload university logos for better performance\r\n            const universityNames = data.items\r\n              .filter(\r\n                (counselor) =>\r\n                  counselor.education && counselor.education.length > 0\r\n              )\r\n              .map((counselor) => counselor.education[0].university_name)\r\n              .filter(Boolean);\r\n\r\n            if (universityNames.length > 0) {\r\n              preloadUniversityLogos(universityNames);\r\n            }\r\n\r\n            set((state) => {\r\n              // For first page or filter changes, replace the list\r\n              const isFirstPage = currentPage === 1;\r\n\r\n              return {\r\n                counselors: isFirstPage\r\n                  ? data.items\r\n                  : [...(state.counselors || []), ...data.items],\r\n                counselorIds: isFirstPage\r\n                  ? data.items.map((c) => c.user_id)\r\n                  : [\r\n                      ...new Set([\r\n                        ...state.counselorIds,\r\n                        ...data.items.map((c) => c.user_id),\r\n                      ]),\r\n                    ],\r\n                totalPages: data.pages,\r\n                hasMore: data.has_next,\r\n                loading: false,\r\n                error: null,\r\n              };\r\n            });\r\n          } else {\r\n            set((state) => ({\r\n              ...state,\r\n              counselors: currentPage === 1 ? [] : state.counselors,\r\n              counselorIds: currentPage === 1 ? [] : state.counselorIds,\r\n              totalPages: currentPage === 1 ? 0 : state.totalPages,\r\n              hasMore: false,\r\n              loading: false,\r\n              error: currentPage === 1 ? \"No counselors found\" : null,\r\n            }));\r\n          }\r\n        } catch (error) {\r\n          set({\r\n            error: \"Failed to fetch counselors\",\r\n            loading: false,\r\n          });\r\n          console.error(\"Error fetching counselors:\", error);\r\n        }\r\n      },\r\n\r\n      fetchCounselorProfile: async (userId: number) => {\r\n        try {\r\n          set({ profileLoading: true, error: null });\r\n          const { data } = await apiClient.get<CounselorPublicProfile>(\r\n            `/counselor/profile/public-profile/user/${userId}`\r\n          );\r\n\r\n          // Update counselors state with the fetched profile\r\n          set((state) => {\r\n            // Create a new array with existing counselors\r\n            const updatedCounselors = [...(state.counselors || [])];\r\n\r\n            // Find if this counselor already exists in our array\r\n            const existingIndex = updatedCounselors.findIndex(\r\n              (c) => c.user_id === userId\r\n            );\r\n\r\n            // If counselor exists, update it, otherwise add it\r\n            if (existingIndex >= 0) {\r\n              updatedCounselors[existingIndex] = data;\r\n            } else {\r\n              updatedCounselors.push(data);\r\n            }\r\n\r\n            // Update counselorIds if needed\r\n            let updatedIds = [...state.counselorIds];\r\n            if (!updatedIds.includes(userId)) {\r\n              updatedIds.push(userId);\r\n            }\r\n\r\n            return {\r\n              counselors: updatedCounselors,\r\n              counselorIds: updatedIds,\r\n              profileLoading: false,\r\n            };\r\n          });\r\n\r\n          return data;\r\n        } catch (error) {\r\n          console.error(\"Error fetching counselor profile:\", error);\r\n          set({\r\n            error: \"Failed to fetch counselor profile\",\r\n            profileLoading: false,\r\n          });\r\n          throw error;\r\n        }\r\n      },\r\n\r\n      fetchTopCounselors: async () => {\r\n        try {\r\n          set({ loading: true, error: null });\r\n          const { data } = await apiClient.get<CounselorListResponse>(\r\n            \"/counselor/profiles/public-profile/list\",\r\n            { params: { page: 1, limit: 8 } }\r\n          );\r\n\r\n          // Check if we have any items before updating state\r\n          if (data.items && data.items.length > 0) {\r\n            // Preload university logos for top counselors\r\n            const universityNames = data.items\r\n              .filter(\r\n                (counselor) =>\r\n                  counselor.education && counselor.education.length > 0\r\n              )\r\n              .map((counselor) => counselor.education[0].university_name)\r\n              .filter(Boolean);\r\n\r\n            if (universityNames.length > 0) {\r\n              preloadUniversityLogos(universityNames);\r\n            }\r\n\r\n            set({\r\n              counselors: data.items,\r\n              counselorIds: data.items.map((c) => c.user_id),\r\n              loading: false,\r\n            });\r\n            return data.items;\r\n          } else {\r\n            set({\r\n              counselors: [],\r\n              counselorIds: [],\r\n              loading: false,\r\n              error: \"No counselors found\",\r\n            });\r\n            return [];\r\n          }\r\n        } catch (error) {\r\n          set({\r\n            error: \"Failed to fetch top counselors\",\r\n            loading: false,\r\n            counselors: [],\r\n            counselorIds: [],\r\n          });\r\n          console.error(\"Error fetching top counselors:\", error);\r\n          return [];\r\n        }\r\n      },\r\n\r\n      fetchAvailableDates: async (\r\n        counselorUserId,\r\n        startDate,\r\n        endDate,\r\n        timezone\r\n      ) => {\r\n        try {\r\n          set({ datesLoading: true });\r\n          const response = await apiClient.get<string[]>(\r\n            `/counselor/availability/public/${counselorUserId}/dates`,\r\n            {\r\n              params: {\r\n                start_date: format(startDate, \"yyyy-MM-dd\"),\r\n                end_date: format(endDate, \"yyyy-MM-dd\"),\r\n                timezone,\r\n              },\r\n            }\r\n          );\r\n          set({ availableDates: response.data, datesLoading: false });\r\n          return response.data;\r\n        } catch (error) {\r\n          set({\r\n            error: \"Failed to fetch dates\",\r\n            datesLoading: false,\r\n          });\r\n          throw error;\r\n        }\r\n      },\r\n\r\n      fetchTimeSlots: async (counselorUserId, targetDate, timezone) => {\r\n        try {\r\n          set({ availabilityLoading: true });\r\n          const response = await apiClient.get<string[]>(\r\n            `/counselor/availability/public/${counselorUserId}/slots`,\r\n            {\r\n              params: {\r\n                target_date: format(targetDate, \"yyyy-MM-dd\"),\r\n                timezone,\r\n              },\r\n            }\r\n          );\r\n          set({\r\n            timeSlots: response.data,\r\n            availabilityLoading: false,\r\n          });\r\n          return response.data;\r\n        } catch (error) {\r\n          set({\r\n            error: \"Failed to fetch time slots\",\r\n            availabilityLoading: false,\r\n          });\r\n          throw error;\r\n        }\r\n      },\r\n\r\n      setFilters: (filters: Filters) => {\r\n        set({\r\n          filters,\r\n          currentPage: 1,\r\n        });\r\n      },\r\n      clearFilters: () => {\r\n        set({\r\n          filters: {},\r\n          counselors: [],\r\n          currentPage: 1,\r\n          hasMore: false,\r\n        });\r\n      },\r\n\r\n      loadMore: async () => {\r\n        const { currentPage, hasMore } = get();\r\n        if (!hasMore) return;\r\n        set({ currentPage: currentPage + 1 });\r\n        await get().fetchAllCounselors();\r\n      },\r\n    }),\r\n    {\r\n      name: \"counselors-storage\",\r\n      partialize: (state) =>\r\n        Object.fromEntries(\r\n          Object.entries(state).filter(([key]) => [\"filters\"].includes(key))\r\n        ),\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": ";;;AAIA;AAMA;AARA;AACA;AAMA;AATA;;;;;;AA+DO,MAAM,gBAAgB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAChC,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,SAAS;QACT,gBAAgB;QAChB,qBAAqB;QACrB,cAAc;QACd,OAAO;QACP,YAAY;QACZ,cAAc,EAAE;QAChB,SAAS,CAAC;QACV,aAAa;QACb,YAAY;QACZ,SAAS;QACT,gBAAgB,EAAE;QAClB,WAAW,EAAE;QAEb,YAAY,CAAC;YACX,IAAI;gBAAE;YAAQ;QAChB;QACA,oBAAoB;YAClB,IAAI;gBACF,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG;gBAEjC,kDAAkD;gBAClD,IAAI,gBAAgB,GAAG;oBACrB,IAAI;wBAAE,SAAS;wBAAM,OAAO;oBAAK;gBACnC;gBAEA,yCAAyC;gBACzC,MAAM,SAAoB;oBACxB,MAAM;oBACN,OAAO;gBACT;gBAEA,qCAAqC;gBACrC,IAAI,QAAQ,YAAY,IAAI,QAAQ,YAAY,CAAC,MAAM,GAAG,GAAG;oBAC3D,OAAO,aAAa,GAAG,QAAQ,YAAY,CAAC,IAAI,CAAC;gBACnD;gBAEA,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,GAAG;oBACjD,OAAO,SAAS,GAAG,QAAQ,OAAO,CAAC,IAAI,CAAC;gBAC1C;gBAEA,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,GAAG;oBACvD,OAAO,YAAY,GAAG,QAAQ,UAAU,CAAC,IAAI,CAAC;gBAChD;gBAEA,8CAA8C;gBAC9C,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,IAAI,OAAO,IAAI;oBAC9C,OAAO,IAAI,GAAG,QAAQ,IAAI,CAAC,IAAI;gBACjC;gBAEA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,2CACA;oBAAE;gBAAO;gBAGX,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;oBACvC,kDAAkD;oBAClD,MAAM,kBAAkB,KAAK,KAAK,CAC/B,MAAM,CACL,CAAC,YACC,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,MAAM,GAAG,GAEvD,GAAG,CAAC,CAAC,YAAc,UAAU,SAAS,CAAC,EAAE,CAAC,eAAe,EACzD,MAAM,CAAC;oBAEV,IAAI,gBAAgB,MAAM,GAAG,GAAG;wBAC9B,CAAA,GAAA,8HAAA,CAAA,yBAAsB,AAAD,EAAE;oBACzB;oBAEA,IAAI,CAAC;wBACH,qDAAqD;wBACrD,MAAM,cAAc,gBAAgB;wBAEpC,OAAO;4BACL,YAAY,cACR,KAAK,KAAK,GACV;mCAAK,MAAM,UAAU,IAAI,EAAE;mCAAM,KAAK,KAAK;6BAAC;4BAChD,cAAc,cACV,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,IAAM,EAAE,OAAO,IAC/B;mCACK,IAAI,IAAI;uCACN,MAAM,YAAY;uCAClB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,IAAM,EAAE,OAAO;iCACnC;6BACF;4BACL,YAAY,KAAK,KAAK;4BACtB,SAAS,KAAK,QAAQ;4BACtB,SAAS;4BACT,OAAO;wBACT;oBACF;gBACF,OAAO;oBACL,IAAI,CAAC,QAAU,CAAC;4BACd,GAAG,KAAK;4BACR,YAAY,gBAAgB,IAAI,EAAE,GAAG,MAAM,UAAU;4BACrD,cAAc,gBAAgB,IAAI,EAAE,GAAG,MAAM,YAAY;4BACzD,YAAY,gBAAgB,IAAI,IAAI,MAAM,UAAU;4BACpD,SAAS;4BACT,SAAS;4BACT,OAAO,gBAAgB,IAAI,wBAAwB;wBACrD,CAAC;gBACH;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO;oBACP,SAAS;gBACX;gBACA,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;QACF;QAEA,uBAAuB,OAAO;YAC5B,IAAI;gBACF,IAAI;oBAAE,gBAAgB;oBAAM,OAAO;gBAAK;gBACxC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,uCAAuC,EAAE,QAAQ;gBAGpD,mDAAmD;gBACnD,IAAI,CAAC;oBACH,8CAA8C;oBAC9C,MAAM,oBAAoB;2BAAK,MAAM,UAAU,IAAI,EAAE;qBAAE;oBAEvD,qDAAqD;oBACrD,MAAM,gBAAgB,kBAAkB,SAAS,CAC/C,CAAC,IAAM,EAAE,OAAO,KAAK;oBAGvB,mDAAmD;oBACnD,IAAI,iBAAiB,GAAG;wBACtB,iBAAiB,CAAC,cAAc,GAAG;oBACrC,OAAO;wBACL,kBAAkB,IAAI,CAAC;oBACzB;oBAEA,gCAAgC;oBAChC,IAAI,aAAa;2BAAI,MAAM,YAAY;qBAAC;oBACxC,IAAI,CAAC,WAAW,QAAQ,CAAC,SAAS;wBAChC,WAAW,IAAI,CAAC;oBAClB;oBAEA,OAAO;wBACL,YAAY;wBACZ,cAAc;wBACd,gBAAgB;oBAClB;gBACF;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,IAAI;oBACF,OAAO;oBACP,gBAAgB;gBAClB;gBACA,MAAM;YACR;QACF;QAEA,oBAAoB;YAClB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,2CACA;oBAAE,QAAQ;wBAAE,MAAM;wBAAG,OAAO;oBAAE;gBAAE;gBAGlC,mDAAmD;gBACnD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;oBACvC,8CAA8C;oBAC9C,MAAM,kBAAkB,KAAK,KAAK,CAC/B,MAAM,CACL,CAAC,YACC,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,MAAM,GAAG,GAEvD,GAAG,CAAC,CAAC,YAAc,UAAU,SAAS,CAAC,EAAE,CAAC,eAAe,EACzD,MAAM,CAAC;oBAEV,IAAI,gBAAgB,MAAM,GAAG,GAAG;wBAC9B,CAAA,GAAA,8HAAA,CAAA,yBAAsB,AAAD,EAAE;oBACzB;oBAEA,IAAI;wBACF,YAAY,KAAK,KAAK;wBACtB,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,IAAM,EAAE,OAAO;wBAC7C,SAAS;oBACX;oBACA,OAAO,KAAK,KAAK;gBACnB,OAAO;oBACL,IAAI;wBACF,YAAY,EAAE;wBACd,cAAc,EAAE;wBAChB,SAAS;wBACT,OAAO;oBACT;oBACA,OAAO,EAAE;gBACX;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO;oBACP,SAAS;oBACT,YAAY,EAAE;oBACd,cAAc,EAAE;gBAClB;gBACA,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,OAAO,EAAE;YACX;QACF;QAEA,qBAAqB,OACnB,iBACA,WACA,SACA;YAEA,IAAI;gBACF,IAAI;oBAAE,cAAc;gBAAK;gBACzB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,+BAA+B,EAAE,gBAAgB,MAAM,CAAC,EACzD;oBACE,QAAQ;wBACN,YAAY,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;wBAC9B,UAAU,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;wBAC1B;oBACF;gBACF;gBAEF,IAAI;oBAAE,gBAAgB,SAAS,IAAI;oBAAE,cAAc;gBAAM;gBACzD,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO;oBACP,cAAc;gBAChB;gBACA,MAAM;YACR;QACF;QAEA,gBAAgB,OAAO,iBAAiB,YAAY;YAClD,IAAI;gBACF,IAAI;oBAAE,qBAAqB;gBAAK;gBAChC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,+BAA+B,EAAE,gBAAgB,MAAM,CAAC,EACzD;oBACE,QAAQ;wBACN,aAAa,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,YAAY;wBAChC;oBACF;gBACF;gBAEF,IAAI;oBACF,WAAW,SAAS,IAAI;oBACxB,qBAAqB;gBACvB;gBACA,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO;oBACP,qBAAqB;gBACvB;gBACA,MAAM;YACR;QACF;QAEA,YAAY,CAAC;YACX,IAAI;gBACF;gBACA,aAAa;YACf;QACF;QACA,cAAc;YACZ,IAAI;gBACF,SAAS,CAAC;gBACV,YAAY,EAAE;gBACd,aAAa;gBACb,SAAS;YACX;QACF;QAEA,UAAU;YACR,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG;YACjC,IAAI,CAAC,SAAS;YACd,IAAI;gBAAE,aAAa,cAAc;YAAE;YACnC,MAAM,MAAM,kBAAkB;QAChC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QACX,OAAO,WAAW,CAChB,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,GAAK;gBAAC;aAAU,CAAC,QAAQ,CAAC;AAEnE"}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 394, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/student/useClickOutside.ts"], "sourcesContent": ["import { useEffect } from \"react\";\r\n\r\nfunction useClickOutside(\r\n  ref: React.RefObject<HTMLElement | null>,\r\n  callback: () => void\r\n) {\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent | TouchEvent) => {\r\n      const target = (event as TouchEvent).touches?.[0]?.target || event.target;\r\n\r\n      if (ref.current && target && !ref.current.contains(target as Node)) {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    const options = { passive: true };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    document.addEventListener(\"touchstart\", handleClickOutside, options);\r\n\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n      document.removeEventListener(\"touchstart\", handleClickOutside);\r\n    };\r\n  }, [ref, callback]);\r\n}\r\n\r\nexport default useClickOutside;\r\n"], "names": [], "mappings": ";;;AAAA;;;AAEA,SAAS,gBACP,GAAwC,EACxC,QAAoB;;IAEpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;gEAAqB,CAAC;oBAC1B,MAAM,SAAS,AAAC,MAAqB,OAAO,EAAE,CAAC,EAAE,EAAE,UAAU,MAAM,MAAM;oBAEzE,IAAI,IAAI,OAAO,IAAI,UAAU,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAiB;wBAClE;oBACF;gBACF;;YAEA,MAAM,UAAU;gBAAE,SAAS;YAAK;YAEhC,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,cAAc,oBAAoB;YAE5D;6CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;oBAC1C,SAAS,mBAAmB,CAAC,cAAc;gBAC7C;;QACF;oCAAG;QAAC;QAAK;KAAS;AACpB;GAvBS;uCAyBM"}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/public/useNewsletter.tsx"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { toast } from \"react-toastify\";\r\nimport apiClient from \"@/lib/apiClient\";\r\n\r\ninterface NewsletterSubscription {\r\n  name: string;\r\n  email: string;\r\n}\r\n\r\ninterface NewsletterResponse {\r\n  id: number;\r\n  name: string;\r\n  email: string;\r\n  subscribed_at: string;\r\n}\r\n\r\ninterface NewsletterState {\r\n  loading: boolean;\r\n  error: string | null;\r\n  lastSubscription: NewsletterResponse | null;\r\n  subscribe: (data: NewsletterSubscription) => Promise<void>;\r\n  clearError: () => void;\r\n}\r\n\r\nexport const useNewsletter = create<NewsletterState>((set) => ({\r\n  loading: false,\r\n  error: null,\r\n  lastSubscription: null,\r\n\r\n  subscribe: async (data: NewsletterSubscription) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.post<NewsletterResponse>(\r\n        \"/newsletter/subscribe\",\r\n        data\r\n      );\r\n\r\n      set({\r\n        lastSubscription: response.data,\r\n        error: null,\r\n      });\r\n\r\n      toast.success(\"Successfully subscribed to newsletter!\");\r\n    } catch (error: any) {\r\n      const errorMessage =\r\n        error.response?.data?.detail || \"Failed to subscribe to newsletter\";\r\n      set({\r\n        error: errorMessage,\r\n        lastSubscription: null,\r\n      });\r\n\r\n      toast.error(\"Failed to subscribe to newsletter\");\r\n\r\n      throw error;\r\n    } finally {\r\n      set({ loading: false });\r\n    }\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n}));\r\n"], "names": [], "mappings": ";;;AACA;AACA;AAFA;;;;AAwBO,MAAM,gBAAgB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAmB,CAAC,MAAQ,CAAC;QAC7D,SAAS;QACT,OAAO;QACP,kBAAkB;QAElB,WAAW,OAAO;YAChB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CACnC,yBACA;gBAGF,IAAI;oBACF,kBAAkB,SAAS,IAAI;oBAC/B,OAAO;gBACT;gBAEA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAY;gBACnB,MAAM,eACJ,MAAM,QAAQ,EAAE,MAAM,UAAU;gBAClC,IAAI;oBACF,OAAO;oBACP,kBAAkB;gBACpB;gBAEA,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBAEZ,MAAM;YACR,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;IACtC,CAAC"}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/public/useFeaturedCounselors.tsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport apiClient from \"@/lib/apiClient\";\r\n\r\nexport type CounselorCategory =\r\n  | \"top-ivy-league\"\r\n  | \"russel-group\"\r\n  | \"liberal-arts\"\r\n  | \"top-ucs\";\r\n\r\ninterface FeaturedCounselorsResponse {\r\n  category: string;\r\n  items: Partial<CounselorPublicProfile>[];\r\n}\r\n\r\nexport const useFeaturedCounselors = (\r\n  initialCategory: CounselorCategory = \"top-ivy-league\"\r\n) => {\r\n  const [category, setCategory] = useState<CounselorCategory>(initialCategory);\r\n  const [counselors, setCounselors] = useState<\r\n    Partial<CounselorPublicProfile>[]\r\n  >([]);\r\n  const [isLoading, setIsLoading] = useState<boolean>(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const fetchCounselors = async (selectedCategory: CounselorCategory) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const response = await apiClient.get<FeaturedCounselorsResponse>(\r\n        `/counselor/profiles/public-profile/list/featured?category=${selectedCategory}`\r\n      );\r\n      setCounselors(response.data.items);\r\n    } catch (err) {\r\n      console.error(\"Error fetching featured counselors:\", err);\r\n      setError(\"Failed to load counselors. Please try again later.\");\r\n      setCounselors([]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchCounselors(category);\r\n  }, [category]);\r\n\r\n  const changeCategory = (newCategory: CounselorCategory) => {\r\n    setCategory(newCategory);\r\n  };\r\n\r\n  return {\r\n    counselors,\r\n    isLoading,\r\n    error,\r\n    category,\r\n    changeCategory,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;;AAaO,MAAM,wBAAwB,CACnC,kBAAqC,gBAAgB;;IAErD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAC5D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAEzC,EAAE;IACJ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,kBAAkB,OAAO;QAC7B,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,0DAA0D,EAAE,kBAAkB;YAEjF,cAAc,SAAS,IAAI,CAAC,KAAK;QACnC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,uCAAuC;YACrD,SAAS;YACT,cAAc,EAAE;QAClB,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,gBAAgB;QAClB;0CAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB,CAAC;QACtB,YAAY;IACd;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;GA3Ca"}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/utils/fuzzy-search.ts"], "sourcesContent": ["export function fuzzySearch(needle: string, haystack: string): boolean {\r\n  const hLen = haystack.length;\r\n  const nLen = needle.length;\r\n  if (nLen > hLen) {\r\n    return false;\r\n  }\r\n  if (nLen === hLen) {\r\n    return needle.toLowerCase() === haystack.toLowerCase();\r\n  }\r\n\r\n  needle = needle.toLowerCase();\r\n  haystack = haystack.toLowerCase();\r\n\r\n  let nIdx = 0;\r\n  let hIdx = 0;\r\n\r\n  while (nIdx < nLen && hIdx < hLen) {\r\n    if (needle[nIdx] === haystack[hIdx]) {\r\n      nIdx++;\r\n    }\r\n    hIdx++;\r\n  }\r\n  return nIdx === nLen;\r\n}\r\n\r\nexport function findClosestMatch(\r\n  input: string,\r\n  options: string[]\r\n): string | null {\r\n  const matches = options.filter((option) => fuzzySearch(input, option));\r\n  return matches.length > 0 ? matches[0] : null;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAO,SAAS,YAAY,MAAc,EAAE,QAAgB;IAC1D,MAAM,OAAO,SAAS,MAAM;IAC5B,MAAM,OAAO,OAAO,MAAM;IAC1B,IAAI,OAAO,MAAM;QACf,OAAO;IACT;IACA,IAAI,SAAS,MAAM;QACjB,OAAO,OAAO,WAAW,OAAO,SAAS,WAAW;IACtD;IAEA,SAAS,OAAO,WAAW;IAC3B,WAAW,SAAS,WAAW;IAE/B,IAAI,OAAO;IACX,IAAI,OAAO;IAEX,MAAO,OAAO,QAAQ,OAAO,KAAM;QACjC,IAAI,MAAM,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,EAAE;YACnC;QACF;QACA;IACF;IACA,OAAO,SAAS;AAClB;AAEO,SAAS,iBACd,KAAa,EACb,OAAiB;IAEjB,MAAM,UAAU,QAAQ,MAAM,CAAC,CAAC,SAAW,YAAY,OAAO;IAC9D,OAAO,QAAQ,MAAM,GAAG,IAAI,OAAO,CAAC,EAAE,GAAG;AAC3C"}}, {"offset": {"line": 580, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 586, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/utils/uni-logo.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nconst CLIENT_ID = \"1idDNCRTv8QuIQpUJ7P\";\r\n\r\n// In-memory cache for university logos\r\nconst logoCache = new Map<string, string>();\r\nconst domainCache = new Map<string, string>();\r\n\r\n// Cache expiry time (24 hours)\r\nconst CACHE_EXPIRY = 24 * 60 * 60 * 1000;\r\nconst cacheTimestamps = new Map<string, number>();\r\n\r\n// Fallback logo for when university logo cannot be found\r\nconst FALLBACK_LOGO = \"/images/university-placeholder.svg\";\r\n\r\nfunction isCacheValid(key: string): boolean {\r\n  const timestamp = cacheTimestamps.get(key);\r\n  if (!timestamp) return false;\r\n  return Date.now() - timestamp < CACHE_EXPIRY;\r\n}\r\n\r\nfunction setCacheWithTimestamp(key: string, value: string): void {\r\n  logoCache.set(key, value);\r\n  cacheTimestamps.set(key, Date.now());\r\n}\r\n\r\nasync function getUniversityDomain(universityName: string): Promise<string> {\r\n  const cacheKey = `domain_${universityName.toLowerCase()}`;\r\n\r\n  // Check domain cache first\r\n  if (domainCache.has(cacheKey) && isCacheValid(cacheKey)) {\r\n    return domainCache.get(cacheKey)!;\r\n  }\r\n\r\n  try {\r\n    const encodedName = encodeURIComponent(universityName);\r\n    const response = await axios.get(\r\n      `https://universities.hipolabs.com/search?name=${encodedName}`,\r\n      { timeout: 5000 } // 5 second timeout\r\n    );\r\n\r\n    if (response.data.length === 0) {\r\n      throw new Error(\"University not found!\");\r\n    }\r\n\r\n    const domain = response.data[0].domains[0];\r\n    domainCache.set(cacheKey, domain);\r\n    cacheTimestamps.set(cacheKey, Date.now());\r\n\r\n    return domain;\r\n  } catch (error) {\r\n    console.warn(`Failed to fetch domain for ${universityName}:`, error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function getUniversityLogoUrl(\r\n  universityName: string\r\n): Promise<string> {\r\n  if (!universityName || typeof universityName !== \"string\") {\r\n    return FALLBACK_LOGO;\r\n  }\r\n\r\n  const cacheKey = universityName.toLowerCase().trim();\r\n\r\n  // Check cache first\r\n  if (logoCache.has(cacheKey) && isCacheValid(cacheKey)) {\r\n    return logoCache.get(cacheKey)!;\r\n  }\r\n\r\n  try {\r\n    const domain = await getUniversityDomain(universityName);\r\n    const brandfetchUrl = `https://cdn.brandfetch.io/${domain}/w/200/h/200?c=${CLIENT_ID}`;\r\n\r\n    // Verify the logo URL is accessible\r\n    try {\r\n      await axios.head(brandfetchUrl, { timeout: 3000 });\r\n      setCacheWithTimestamp(cacheKey, brandfetchUrl);\r\n      return brandfetchUrl;\r\n    } catch (logoError) {\r\n      // If brandfetch fails, cache the fallback\r\n      setCacheWithTimestamp(cacheKey, FALLBACK_LOGO);\r\n      return FALLBACK_LOGO;\r\n    }\r\n  } catch (error) {\r\n    console.warn(\r\n      `Error fetching university logo for ${universityName}:`,\r\n      error\r\n    );\r\n    // Cache the fallback to prevent repeated failed requests\r\n    setCacheWithTimestamp(cacheKey, FALLBACK_LOGO);\r\n    return FALLBACK_LOGO;\r\n  }\r\n}\r\n\r\n// Preload university logos for better performance\r\nexport function preloadUniversityLogos(universityNames: string[]): void {\r\n  universityNames.forEach((name) => {\r\n    if (name && !logoCache.has(name.toLowerCase().trim())) {\r\n      // Fire and forget - don't await\r\n      getUniversityLogoUrl(name).catch(() => {\r\n        // Silently handle errors for preloading\r\n      });\r\n    }\r\n  });\r\n}\r\n\r\n// Clear expired cache entries\r\nexport function clearExpiredCache(): void {\r\n  const now = Date.now();\r\n  for (const [key, timestamp] of cacheTimestamps.entries()) {\r\n    if (now - timestamp > CACHE_EXPIRY) {\r\n      logoCache.delete(key);\r\n      domainCache.delete(key);\r\n      cacheTimestamps.delete(key);\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,YAAY;AAElB,uCAAuC;AACvC,MAAM,YAAY,IAAI;AACtB,MAAM,cAAc,IAAI;AAExB,+BAA+B;AAC/B,MAAM,eAAe,KAAK,KAAK,KAAK;AACpC,MAAM,kBAAkB,IAAI;AAE5B,yDAAyD;AACzD,MAAM,gBAAgB;AAEtB,SAAS,aAAa,GAAW;IAC/B,MAAM,YAAY,gBAAgB,GAAG,CAAC;IACtC,IAAI,CAAC,WAAW,OAAO;IACvB,OAAO,KAAK,GAAG,KAAK,YAAY;AAClC;AAEA,SAAS,sBAAsB,GAAW,EAAE,KAAa;IACvD,UAAU,GAAG,CAAC,KAAK;IACnB,gBAAgB,GAAG,CAAC,KAAK,KAAK,GAAG;AACnC;AAEA,eAAe,oBAAoB,cAAsB;IACvD,MAAM,WAAW,CAAC,OAAO,EAAE,eAAe,WAAW,IAAI;IAEzD,2BAA2B;IAC3B,IAAI,YAAY,GAAG,CAAC,aAAa,aAAa,WAAW;QACvD,OAAO,YAAY,GAAG,CAAC;IACzB;IAEA,IAAI;QACF,MAAM,cAAc,mBAAmB;QACvC,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,CAAC,8CAA8C,EAAE,aAAa,EAC9D;YAAE,SAAS;QAAK,EAAE,mBAAmB;;QAGvC,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,GAAG;YAC9B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,SAAS,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;QAC1C,YAAY,GAAG,CAAC,UAAU;QAC1B,gBAAgB,GAAG,CAAC,UAAU,KAAK,GAAG;QAEtC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,eAAe,CAAC,CAAC,EAAE;QAC9D,MAAM;IACR;AACF;AAEO,eAAe,qBACpB,cAAsB;IAEtB,IAAI,CAAC,kBAAkB,OAAO,mBAAmB,UAAU;QACzD,OAAO;IACT;IAEA,MAAM,WAAW,eAAe,WAAW,GAAG,IAAI;IAElD,oBAAoB;IACpB,IAAI,UAAU,GAAG,CAAC,aAAa,aAAa,WAAW;QACrD,OAAO,UAAU,GAAG,CAAC;IACvB;IAEA,IAAI;QACF,MAAM,SAAS,MAAM,oBAAoB;QACzC,MAAM,gBAAgB,CAAC,0BAA0B,EAAE,OAAO,eAAe,EAAE,WAAW;QAEtF,oCAAoC;QACpC,IAAI;YACF,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,eAAe;gBAAE,SAAS;YAAK;YAChD,sBAAsB,UAAU;YAChC,OAAO;QACT,EAAE,OAAO,WAAW;YAClB,0CAA0C;YAC1C,sBAAsB,UAAU;YAChC,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CACV,CAAC,mCAAmC,EAAE,eAAe,CAAC,CAAC,EACvD;QAEF,yDAAyD;QACzD,sBAAsB,UAAU;QAChC,OAAO;IACT;AACF;AAGO,SAAS,uBAAuB,eAAyB;IAC9D,gBAAgB,OAAO,CAAC,CAAC;QACvB,IAAI,QAAQ,CAAC,UAAU,GAAG,CAAC,KAAK,WAAW,GAAG,IAAI,KAAK;YACrD,gCAAgC;YAChC,qBAAqB,MAAM,KAAK,CAAC;YAC/B,wCAAwC;YAC1C;QACF;IACF;AACF;AAGO,SAAS;IACd,MAAM,MAAM,KAAK,GAAG;IACpB,KAAK,MAAM,CAAC,KAAK,UAAU,IAAI,gBAAgB,OAAO,GAAI;QACxD,IAAI,MAAM,YAAY,cAAc;YAClC,UAAU,MAAM,CAAC;YACjB,YAAY,MAAM,CAAC;YACnB,gBAAgB,MAAM,CAAC;QACzB;IACF;AACF"}}, {"offset": {"line": 689, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/%28landing%29/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport BecomeCounselor from \"../components/public/landing/BecomeCounselor\";\r\nimport GetExpertHelpSection from \"../components/public/landing/GetExpertHelp\";\r\nimport Guides from \"../components/public/landing/Guides\";\r\nimport Hero from \"../components/public/landing/Hero\";\r\nimport LogoSlider from \"../components/public/landing/LogoSlider\";\r\nimport { NewsletterForm } from \"../components/public/landing/newsletter\";\r\nimport TestimonialsSection from \"../components/public/landing/Testimonials\";\r\nimport TopCounselors from \"../components/public/landing/TopCounselors\";\r\nimport StickyFindCounselor from \"../components/public/landing/StickyFindCounselor\";\r\nimport FeaturedCategories from \"../components/public/landing/FeaturedCategories\";\r\n\r\nexport default function LandingPage() {\r\n  return (\r\n    <div className=\"flex flex-col bg-white font-clash-display\">\r\n      <StickyFindCounselor />\r\n      <Hero />\r\n      <LogoSlider />\r\n      <TopCounselors />\r\n      <GetExpertHelpSection />\r\n      <FeaturedCategories />\r\n      <BecomeCounselor />\r\n      <Guides />\r\n      <TestimonialsSection />\r\n      <NewsletterForm />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAae,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,iKAAA,CAAA,UAAmB;;;;;0BACpB,6LAAC,kJAAA,CAAA,UAAI;;;;;0BACL,6LAAC,wJAAA,CAAA,UAAU;;;;;0BACX,6LAAC,2JAAA,CAAA,UAAa;;;;;0BACd,6LAAC,2JAAA,CAAA,UAAoB;;;;;0BACrB,6LAAC,gKAAA,CAAA,UAAkB;;;;;0BACnB,6LAAC,6JAAA,CAAA,UAAe;;;;;0BAChB,6LAAC,oJAAA,CAAA,UAAM;;;;;0BACP,6LAAC,0JAAA,CAAA,UAAmB;;;;;0BACpB,6LAAC,iKAAA,CAAA,iBAAc;;;;;;;;;;;AAGrB;KAfwB"}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}