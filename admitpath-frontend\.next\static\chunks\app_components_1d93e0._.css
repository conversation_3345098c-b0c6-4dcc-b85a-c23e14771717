/* [project]/app/components/common/inputField/index.module.css [app-client] (css) */
.index-module__8yU72G__container {
  display: flex;
  min-width: 240px;
  flex: 1;
  flex-shrink: 1;
  flex-basis: 0;
  flex-direction: column;
}

.index-module__8yU72G__label {
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(23 23 23 / var(--tw-text-opacity, 1));
}

.index-module__8yU72G__required {
  --tw-text-opacity: 1;
  color: rgb(244 63 94 / var(--tw-text-opacity, 1));
}

.index-module__8yU72G__input {
  margin-top: .25rem;
  width: 100%;
  border-radius: .25rem;
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(226 232 240 / var(--tw-border-opacity, 1));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: .75rem;
  padding-bottom: .75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -.025em;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
  transition-duration: .15s;
}

.index-module__8yU72G__input:focus {
  outline: 2px solid #0000;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));
}

.index-module__8yU72G__input[type="password"] {
  padding-right: 3rem;
}

.index-module__8yU72G__input.index-module__8yU72G__error {
  --tw-border-opacity: 1;
  border-color: rgb(244 63 94 / var(--tw-border-opacity, 1));
}

.index-module__8yU72G__input.index-module__8yU72G__error:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(244 63 94 / var(--tw-ring-opacity, 1));
}

.index-module__8yU72G__errorMessage {
  margin-top: .25rem;
  font-size: .875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(244 63 94 / var(--tw-text-opacity, 1));
}


/* [project]/app/components/common/button/index.module.css [app-client] (css) */
.index-module__Mmy9Oa__buttonBase {
  margin-top: auto;
  margin-bottom: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: .75rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-top: .5rem;
  padding-bottom: .5rem;
  font-size: .875rem;
  font-weight: 500;
  line-height: 1.5rem;
  letter-spacing: .025em;
  --tw-shadow: 0 4px 6px -1px #0000001a, 0 2px 4px -2px #0000001a;
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.index-module__Mmy9Oa__buttonPrimary {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.index-module__Mmy9Oa__buttonSecondary {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.index-module__Mmy9Oa__buttonBase:disabled {
  cursor: not-allowed;
  opacity: .5;
}

.index-module__Mmy9Oa__buttonContent {
  display: flex;
  align-items: center;
  gap: .5rem;
  text-wrap: nowrap;
}

.index-module__Mmy9Oa__icon {
  width: .75rem;
}


/* [project]/app/components/auth/index.module.css [app-client] (css) */
.index-module__2JOImG__layout {
  display: flex;
  height: 100vh;
  flex-direction: column;
  overflow: hidden;
}

@media (width >= 1024px) {
  .index-module__2JOImG__layout {
    flex-direction: row;
  }
}

.index-module__2JOImG__formSection {
  display: flex;
  width: 100%;
  flex-direction: column;
  overflow-y: auto;
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 244 / var(--tw-bg-opacity, 1));
}

@media (width >= 1024px) {
  .index-module__2JOImG__formSection {
    width: 50%;
  }
}

.index-module__2JOImG__formWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (width >= 768px) {
  .index-module__2JOImG__formWrapper {
    margin-top: auto;
    margin-bottom: auto;
    margin-left: auto;
    margin-right: auto;
  }
}

.index-module__2JOImG__mainContent {
  margin-left: .75rem;
  margin-right: .75rem;
  width: max-content;
  border-radius: .75rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 1.5rem;
}

@media (width >= 640px) {
  .index-module__2JOImG__mainContent {
    padding: 2rem;
  }
}

.index-module__2JOImG__header {
  padding: 1rem;
}

@media (width >= 640px) {
  .index-module__2JOImG__header {
    padding: 1.5rem;
  }
}

.index-module__2JOImG__logo {
  width: 180px;
}

.index-module__2JOImG__title {
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 500;
}

@media (width >= 640px) {
  .index-module__2JOImG__title {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

.index-module__2JOImG__highlight {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.index-module__2JOImG__terms {
  margin-bottom: 1.5rem;
  font-size: .875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

@media (width >= 640px) {
  .index-module__2JOImG__terms {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.index-module__2JOImG__link {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.index-module__2JOImG__link:hover {
  text-decoration-line: underline;
}

.index-module__2JOImG__divider {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.index-module__2JOImG__divider hr {
  flex: 1;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.index-module__2JOImG__divider span {
  white-space: nowrap;
  font-size: .75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

@media (width >= 640px) {
  .index-module__2JOImG__divider span {
    font-size: .875rem;
    line-height: 1.25rem;
  }
}

.index-module__2JOImG__socialButtons {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: .75rem;
}

@media (width >= 640px) {
  .index-module__2JOImG__socialButtons {
    flex-direction: row;
  }
}

.index-module__2JOImG__form {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 1rem;
}

@media (width >= 640px) {
  .index-module__2JOImG__form {
    gap: 1.5rem;
  }
}

.index-module__2JOImG__nameFields {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.index-module__2JOImG__emailField {
  width: 100%;
}

.index-module__2JOImG__formFooter {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

@media (width >= 640px) {
  .index-module__2JOImG__formFooter {
    flex-direction: row;
  }
}

.index-module__2JOImG__loginLink {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: .5rem;
  font-size: .75rem;
  line-height: 1rem;
}

@media (width >= 640px) {
  .index-module__2JOImG__loginLink {
    font-size: .875rem;
    line-height: 1.25rem;
  }
}

.index-module__2JOImG__loginLink span {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.index-module__2JOImG__loginLink a {
  white-space: nowrap;
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.index-module__2JOImG__loginLink a:hover {
  text-decoration-line: underline;
}

.index-module__2JOImG__imageSection {
  display: none;
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

@media (width >= 1024px) {
  .index-module__2JOImG__imageSection {
    display: block;
    width: 50%;
  }
}

.index-module__2JOImG__heroImage {
  height: 100%;
  width: 100%;
  object-fit: cover;
}


/*# sourceMappingURL=app_components_1d93e0._.css.map*/
