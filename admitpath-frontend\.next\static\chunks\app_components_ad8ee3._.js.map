{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/BecomeCounselor.tsx"], "sourcesContent": ["\"use client\";\r\nimport React from \"react\";\r\nimport { <PERSON>R<PERSON> } from \"lucide-react\";\r\nimport Link from \"next/link\";\r\nimport { But<PERSON> } from \"../../ui/button\";\r\nimport Lottie from \"lottie-react\";\r\nimport arrowAnimation from \"@/app/assets/lotties/Arrow.json\";\r\nimport webappAnimation from \"@/app/assets/lotties/WebappScreen.json\";\r\nimport starsAnimation from \"@/app/assets/lotties/Stars.json\";\r\n\r\nconst BecomeCounselor = () => {\r\n  return (\r\n    <div className=\"my-10 md:my-20 container mx-auto px-4 md:px-8 lg:px-16 xl:px-10 py-3 \">\r\n      {/* Main Content Grid */}\r\n      <div className=\"grid grid-cols-1 p-4 sm:p-6 md:p-8 lg:p-12 bg-gradient-to-br from-blue-950 to-black/90 border rounded-xl overflow-hidden relative\">\r\n        {/* Text Content */}\r\n        <div className=\"flex flex-col gap-y-4 sm:gap-y-6 md:gap-y-8 py-4 sm:py-8 lg:py-12 max-w-xl\">\r\n          <h1 className=\"text-3xl md:text-4xl lg:text-5xl font-medium text-zinc-100 leading-tight\">\r\n            Share Your Knowledge. Make a Difference. Earn\r\n          </h1>\r\n          <p className=\"text-base sm:text-lg md:text-xl text-zinc-100/90\">\r\n            Are you a current university student, alumnus, or certified expert?\r\n            Join our platform and guide aspiring students to success while\r\n            earning income.\r\n          </p>\r\n\r\n          <div className=\"flex gap-2 items-start relative pt-2 sm:pt-4\">\r\n            <Link href=\"/auth/signup\">\r\n              <Button className=\"bg-zinc-100 hover:bg-zinc-300 rounded-xl px-4 py-3 sm:p-6 text-blue-950 text-base sm:text-lg md:text-xl flex justify-center items-center\">\r\n                Join Now <ArrowRight className=\"h-5 w-5 sm:h-6 sm:w-6 ml-2\" />\r\n              </Button>\r\n            </Link>\r\n            {/* Arrow Lottie Animation */}\r\n            <div className=\"w-24 lg:w-36 h-24 lg:h-36 pt-6\">\r\n              <Lottie\r\n                animationData={arrowAnimation}\r\n                loop={true}\r\n                autoplay={true}\r\n                style={{ width: \"100%\", height: \"100%\" }}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n        {/* Stars Animation */}\r\n        <div className=\"hidden sm:block absolute right-[1rem] lg:right-2/4 top-4 w-16 sm:w-20 md:w-24 h-16 sm:h-20 md:h-24\">\r\n          <Lottie\r\n            animationData={starsAnimation}\r\n            loop={true}\r\n            autoplay={true}\r\n            style={{ width: \"100%\", height: \"100%\" }}\r\n          />\r\n        </div>\r\n\r\n        {/* Webapp Screen Animation - Only hidden on mobile */}\r\n        <div className=\"hidden lg:block absolute bottom-0 right-0 w-[45%] h-[90%] transform translate-x-[8%] translate-y-[5%]\">\r\n          <Lottie\r\n            animationData={webappAnimation}\r\n            loop={true}\r\n            autoplay={true}\r\n            style={{ width: \"100%\", height: \"100%\", objectFit: \"contain\" }}\r\n            className=\"rounded-t-xl\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BecomeCounselor;\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AANA;AAFA;;;;;;;;;AAUA,MAAM,kBAAkB;IACtB,qBACE,6LAAC;QAAI,WAAU;kBAEb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2E;;;;;;sCAGzF,6LAAC;4BAAE,WAAU;sCAAmD;;;;;;sCAMhE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;4CAA2I;0DAClJ,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAInC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2JAAA,CAAA,UAAM;wCACL,eAAe,yGAAA,CAAA,UAAc;wCAC7B,MAAM;wCACN,UAAU;wCACV,OAAO;4CAAE,OAAO;4CAAQ,QAAQ;wCAAO;;;;;;;;;;;;;;;;;;;;;;;8BAM/C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,2JAAA,CAAA,UAAM;wBACL,eAAe,yGAAA,CAAA,UAAc;wBAC7B,MAAM;wBACN,UAAU;wBACV,OAAO;4BAAE,OAAO;4BAAQ,QAAQ;wBAAO;;;;;;;;;;;8BAK3C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,2JAAA,CAAA,UAAM;wBACL,eAAe,gHAAA,CAAA,UAAe;wBAC9B,MAAM;wBACN,UAAU;wBACV,OAAO;4BAAE,OAAO;4BAAQ,QAAQ;4BAAQ,WAAW;wBAAU;wBAC7D,WAAU;;;;;;;;;;;;;;;;;;;;;;AAMtB;KAxDM;uCA0DS"}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/GetExpertHelp.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst GetExpertHelpSection = () => {\r\n    const features = [\r\n        {\r\n            icon: \"👨‍🏫\",\r\n            title: \"Expert Advisors Network\",\r\n            description:\r\n                \"Connect with verified advisors including current students and alumni from top universities, former admissions officers, and certified counselors.\",\r\n        },\r\n        {\r\n            icon: \"🎓\",\r\n            title: \"Proven Track Record\",\r\n            description:\r\n                \"Our students have achieved remarkable success, gaining admissions to Ivy League universities, UC schools, and other top-ranked institutions.\",\r\n        },\r\n        {\r\n            icon: \"🛡️\",\r\n            title: \"Secure & Guaranteed\",\r\n            description:\r\n                \"Experience peace of mind with secure transactions, comprehensive dispute resolution, and our satisfaction guarantee.\",\r\n        },\r\n    ];\r\n\r\n    return (\r\n        <div className=\"w-full min-h-[400px] bg-gradient-to-br from-blue-950 to-black/90 text-white py-12 my-4 md:my-10\">\r\n            <div className=\"max-w-7xl mx-auto px-4\">\r\n                <div className=\"text-center mb-12\">\r\n                    <h4 className=\"text-sm uppercase tracking-wider mb-2\">\r\n                        ONE-ON-ONE ADVISING\r\n                    </h4>\r\n                    <h2 className=\"text-4xl font-medium mb-8\">\r\n                        Everything You Need for a Successful College Application\r\n                    </h2>\r\n                </div>\r\n\r\n                <div className=\"grid md:grid-cols-3 gap-6\">\r\n                    {features.map((feature, index) => (\r\n                        <div\r\n                            key={index}\r\n                            className=\"bg-white/10 backdrop-blur-lg rounded-lg p-6 hover:bg-white/15 transition-all\"\r\n                        >\r\n                            <div className=\"flex items-center mb-4\">\r\n                                <span className=\"text-2xl mr-3\">\r\n                                    {feature.icon}\r\n                                </span>\r\n                                <h3 className=\"text-xl font-medium\">\r\n                                    {feature.title}\r\n                                </h3>\r\n                            </div>\r\n                            <p className=\"text-gray-300 leading-relaxed\">\r\n                                {feature.description}\r\n                            </p>\r\n                        </div>\r\n                    ))}\r\n                </div>\r\n            </div>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default GetExpertHelpSection;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,uBAAuB;IACzB,MAAM,WAAW;QACb;YACI,MAAM;YACN,OAAO;YACP,aACI;QACR;QACA;YACI,MAAM;YACN,OAAO;YACP,aACI;QACR;QACA;YACI,MAAM;YACN,OAAO;YACP,aACI;QACR;KACH;IAED,qBACI,6LAAC;QAAI,WAAU;kBACX,cAAA,6LAAC;YAAI,WAAU;;8BACX,6LAAC;oBAAI,WAAU;;sCACX,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAG,WAAU;sCAA4B;;;;;;;;;;;;8BAK9C,6LAAC;oBAAI,WAAU;8BACV,SAAS,GAAG,CAAC,CAAC,SAAS,sBACpB,6LAAC;4BAEG,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACX,6LAAC;4CAAK,WAAU;sDACX,QAAQ,IAAI;;;;;;sDAEjB,6LAAC;4CAAG,WAAU;sDACT,QAAQ,KAAK;;;;;;;;;;;;8CAGtB,6LAAC;oCAAE,WAAU;8CACR,QAAQ,WAAW;;;;;;;2BAZnB;;;;;;;;;;;;;;;;;;;;;AAoBjC;KAzDM;uCA2DS"}}, {"offset": {"line": 299, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/resources/card.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\n\r\nconst ResourceCard = ({ resource }: { resource: any }) => {\r\n  return (\r\n    <div\r\n      className=\"bg-white rounded-xl overflow-hidden cursor-pointer transition-all duration-200 border-2 hover:shadow-md p-2\"\r\n      onClick={() => window.open(resource.file_url)}\r\n    >\r\n      <div className=\"relative aspect-video\">\r\n        <Image\r\n          src={resource.image_url || 'https://placehold.co/600x400/e2e8f0/64748b?text=Resource'}\r\n          alt={resource.title}\r\n          fill\r\n          className=\"object-cover rounded-xl\"\r\n          unoptimized={!resource.image_url}\r\n        />\r\n      </div>\r\n      <div className=\"p-4\">\r\n        <div className=\"flex items-center justify-between mb-2\">\r\n          <span className=\"text-sm font-medium text-gray-500\">\r\n            {resource.category}\r\n          </span>\r\n          <span className=\"text-sm text-gray-400\">\r\n            {new Date(resource.created_at).toLocaleDateString()}\r\n          </span>\r\n        </div>\r\n        <h3 className=\"font-semibold mb-2 line-clamp-2\">{resource.title}</h3>\r\n        <p className=\"text-sm text-gray-600 line-clamp-2\">\r\n          {resource.description}\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ResourceCard;\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAqB;IACnD,qBACE,6LAAC;QACC,WAAU;QACV,SAAS,IAAM,OAAO,IAAI,CAAC,SAAS,QAAQ;;0BAE5C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oBACJ,KAAK,SAAS,SAAS,IAAI;oBAC3B,KAAK,SAAS,KAAK;oBACnB,IAAI;oBACJ,WAAU;oBACV,aAAa,CAAC,SAAS,SAAS;;;;;;;;;;;0BAGpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CACb,SAAS,QAAQ;;;;;;0CAEpB,6LAAC;gCAAK,WAAU;0CACb,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB;;;;;;;;;;;;kCAGrD,6LAAC;wBAAG,WAAU;kCAAmC,SAAS,KAAK;;;;;;kCAC/D,6LAAC;wBAAE,WAAU;kCACV,SAAS,WAAW;;;;;;;;;;;;;;;;;;AAK/B;KA/BM;uCAiCS"}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 405, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/Guides.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { Button } from \"../../ui/button\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport ResourceCard from \"../../student/resources/card\";\r\nimport { useResources } from \"@/app/hooks/student/useResources\";\r\n\r\nconst Guides = () => {\r\n  const { resources, setAudience } = useResources();\r\n\r\n  useEffect(() => {\r\n    setAudience(\"public\");\r\n  }, []);\r\n\r\n  return (\r\n    <div className=\"container mx-auto flex flex-col justify-center items-center my-10 md:my-20 gap-y-6 md:gap-y-12 px-4 md:px-8 lg:px-16 xl:px-10\">\r\n      <div className=\"w-full flex flex-col items-center justify-center gap-y-2.5 md:gap-y-4 text-center drop-shadow-lg px-3\">\r\n        <div className=\"self-stretch text-2xl sm:text-4xl lg:text-5xl font-medium text-zinc-900\">\r\n          Guides to help you grow\r\n        </div>\r\n        <div className=\"text-lg md:text-xl text-blue-950 px-2\">\r\n          Access a library of videos, templates, and examples curated by\r\n          Leland’s top coaches.\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 w-full h-max\">\r\n        {resources.slice(0, 4).map((resource) => (\r\n          <ResourceCard key={resource.id} resource={resource} />\r\n        ))}\r\n      </div>\r\n\r\n      <Link href=\"/library\">\r\n        <Button className=\"bg-blue-950 hover:bg-blue-950/90 rounded-xl p-6 text-white text-lg sm:text-xl flex justify-center items-center\">\r\n          View more <ArrowRight className=\"h-6 w-6 ml-2\" />\r\n        </Button>\r\n      </Link>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Guides;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAFA;;;AALA;;;;;;;AASA,MAAM,SAAS;;IACb,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,eAAY,AAAD;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,YAAY;QACd;2BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA0E;;;;;;kCAGzF,6LAAC;wBAAI,WAAU;kCAAwC;;;;;;;;;;;;0BAMzD,6LAAC;gBAAI,WAAU;0BACZ,UAAU,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAC1B,6LAAC,qJAAA,CAAA,UAAY;wBAAmB,UAAU;uBAAvB,SAAS,EAAE;;;;;;;;;;0BAIlC,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAK;0BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,WAAU;;wBAAiH;sCACvH,6LAAC,qNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK1C;GAhCM;;QAC+B,0IAAA,CAAA,eAAY;;;KAD3C;uCAkCS"}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/Hero.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport type React from \"react\";\r\nimport { useState, useRef, useEffect } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport Lot<PERSON> from \"lottie-react\";\r\nimport { ArrowRight, Search } from \"lucide-react\";\r\nimport animationData from \"@/app/assets/lotties/HomePageHeader.json\";\r\nimport { findClosestMatch } from \"@/app/utils/fuzzy-search\";\r\nimport { useCounselors } from \"@/app/hooks/public/useCounselors\";\r\nimport useClickOutside from \"@/app/hooks/student/useClickOutside\";\r\n\r\nconst SERVICES = [\r\n  \"Personal Statement Guidance\",\r\n  \"Essay Review\",\r\n  \"University Shortlisting\",\r\n  \"Extra Curricular Profile Building\",\r\n  \"Supplementary Essay Guidance\",\r\n  \"Financial Aid Advice\",\r\n  \"Interview Preparation\",\r\n];\r\n\r\nexport default function Hero() {\r\n  const router = useRouter();\r\n  const [isExpanded, setIsExpanded] = useState(false);\r\n  const [searchValue, setSearchValue] = useState(\"\");\r\n  const { setFilters } = useCounselors();\r\n\r\n  const searchContainerRef = useRef<HTMLDivElement>(null);\r\n  const searchInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Scroll search bar into view on mobile when suggestions open\r\n  useEffect(() => {\r\n    if (isExpanded && window.innerWidth < 768) {\r\n      const searchInput = searchInputRef.current;\r\n      if (searchInput) {\r\n        const rect = searchInput.getBoundingClientRect();\r\n        const scrollTop = window.scrollY || document.documentElement.scrollTop;\r\n        const viewportHeight = window.innerHeight;\r\n        // Position the search input 40% from the top of the screen\r\n        const targetPosition = rect.top + scrollTop - viewportHeight * 0.45;\r\n\r\n        window.scrollTo({\r\n          top: targetPosition,\r\n          behavior: \"smooth\",\r\n        });\r\n      }\r\n    }\r\n  }, [isExpanded]);\r\n\r\n  useClickOutside(searchContainerRef, () => {\r\n    setIsExpanded(false);\r\n  });\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    if (!searchValue || !findClosestMatch(searchValue, SERVICES)) {\r\n      router.push(\"/explore\");\r\n      return;\r\n    }\r\n    const match = findClosestMatch(searchValue, SERVICES);\r\n    if (match) {\r\n      setSearchValue(match);\r\n      setFilters({ service_type: [match] });\r\n      router.push(\"/explore\");\r\n    }\r\n  };\r\n\r\n  const handleSuggestionClick = (service: string) => {\r\n    setSearchValue(service);\r\n    setFilters({ service_type: [service] });\r\n    router.push(\"/explore\");\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative w-full pb-10 overflow-hidden bg-white font-clash-display\">\r\n      {/* Lottie Background */}\r\n      <div className=\"hidden md:inline-block w-full transform md:-mt-24 2xl:-mt-0 z-10\">\r\n        <Lottie\r\n          animationData={animationData}\r\n          loop={true}\r\n          autoplay={true}\r\n          style={{ width: \"100%\", height: \"65rem\" }}\r\n        />\r\n      </div>\r\n\r\n      {/* Content Layer */}\r\n      <div className=\"relative md:absolute inset-0 mx-auto container pt-10 md:pt-[15vh] px-3 sm:px-6 lg:px-8 text-center\">\r\n        <h1 className=\"mb-6 font-[600] tracking-tight leading-[1.2] md:leading-[1.34] text-blue-950 text-[2.2rem] md:text-[4.5vw] lg:text-[4vw] xl:text-[3vw]\">\r\n          Find the Right Guidance for\r\n          <br className=\"my-4 md:inline-block hidden\" /> Your{\" \"}\r\n          <span className=\"text-[#800000] transition-all duration-500 ease-in-out hover:text-[#8B0000] hover:drop-shadow-sm cursor-default\">\r\n            College\r\n          </span>{\" \"}\r\n          Journey\r\n        </h1>\r\n        <p\r\n          className=\"mx-auto mb-12 max-w-2xl leading-[1.2] md:leading-[1.34] text-gray-600\"\r\n          style={{\r\n            fontSize: \"clamp(1rem, 2.5vw, 1.25rem)\",\r\n          }}\r\n        >\r\n          Search and connect with experienced mentors\r\n          <br className=\"my-4 md:inline-block hidden\" /> to get personalized\r\n          guidance for your university applications\r\n        </p>\r\n\r\n        {/* Search Form */}\r\n        <div\r\n          ref={searchContainerRef}\r\n          className=\"mx-auto w-[95%] md:w-[50vw] lg:w-[40vw] max-w-2xl relative\"\r\n        >\r\n          <form onSubmit={handleSubmit} className=\"relative\">\r\n            <div className=\"relative flex items-center\">\r\n              <Search className=\"absolute left-4 text-gray-500\" />\r\n              <input\r\n                ref={searchInputRef}\r\n                type=\"text\"\r\n                value={searchValue}\r\n                onChange={(e) => setSearchValue(e.target.value)}\r\n                onFocus={() => setIsExpanded(true)}\r\n                placeholder=\"What do you need help with today?\"\r\n                className=\"w-full rounded-lg bg-white py-4 md:py-8 pl-12 pr-12 text-[13px] md:text-base shadow-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-800 focus:border-transparent placeholder:text-gray-500 placeholder:text-[13px] md:placeholder:text-base\"\r\n              />\r\n              <button\r\n                type=\"submit\"\r\n                className=\"absolute right-2 rounded-lg bg-blue-950 p-2 text-white hover:bg-blue-950/90\"\r\n              >\r\n                <ArrowRight className=\"h-5 w-5\" />\r\n              </button>\r\n            </div>\r\n          </form>\r\n\r\n          {/* Suggestions Dropdown */}\r\n          <div\r\n            className={`\r\n                fixed md:absolute left-0 right-0 md:top-full bottom-0 md:bottom-auto md:mt-2\r\n                bg-white rounded-t-2xl md:rounded-lg shadow-2xl border border-gray-200 p-4\r\n                z-[9999] h-[60vh] md:h-auto md:max-h-[300px] overflow-y-auto transition-all duration-300 ease-out\r\n                ${\r\n                  isExpanded\r\n                    ? \"translate-y-0 opacity-100\"\r\n                    : \"translate-y-full md:translate-y-2 opacity-0 pointer-events-none\"\r\n                }\r\n              `}\r\n          >\r\n            <h3 className=\"text-sm font-medium text-gray-500 mb-3\">\r\n              Popular searches\r\n            </h3>\r\n            <div className=\"grid grid-cols-1 sm:flex sm:flex-wrap gap-2\">\r\n              {SERVICES.map((service) => (\r\n                <button\r\n                  key={service}\r\n                  type=\"button\"\r\n                  onClick={() => {\r\n                    handleSuggestionClick(service);\r\n                    setIsExpanded(false);\r\n                  }}\r\n                  className=\"px-4 py-3 sm:px-3 sm:py-1.5 rounded-full bg-gray-100 text-[15px] sm:text-sm text-gray-700 hover:bg-gray-200 transition-colors cursor-pointer text-left sm:text-center\"\r\n                >\r\n                  {service}\r\n                </button>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;AAJA;AAAA;;;AANA;;;;;;;;;AAYA,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,gBAAa,AAAD;IAEnC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAClD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAEhD,8DAA8D;IAC9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,cAAc,OAAO,UAAU,GAAG,KAAK;gBACzC,MAAM,cAAc,eAAe,OAAO;gBAC1C,IAAI,aAAa;oBACf,MAAM,OAAO,YAAY,qBAAqB;oBAC9C,MAAM,YAAY,OAAO,OAAO,IAAI,SAAS,eAAe,CAAC,SAAS;oBACtE,MAAM,iBAAiB,OAAO,WAAW;oBACzC,2DAA2D;oBAC3D,MAAM,iBAAiB,KAAK,GAAG,GAAG,YAAY,iBAAiB;oBAE/D,OAAO,QAAQ,CAAC;wBACd,KAAK;wBACL,UAAU;oBACZ;gBACF;YACF;QACF;yBAAG;QAAC;KAAW;IAEf,CAAA,GAAA,6IAAA,CAAA,UAAe,AAAD,EAAE;gCAAoB;YAClC,cAAc;QAChB;;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,eAAe,CAAC,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa,WAAW;YAC5D,OAAO,IAAI,CAAC;YACZ;QACF;QACA,MAAM,QAAQ,CAAA,GAAA,kIAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa;QAC5C,IAAI,OAAO;YACT,eAAe;YACf,WAAW;gBAAE,cAAc;oBAAC;iBAAM;YAAC;YACnC,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,eAAe;QACf,WAAW;YAAE,cAAc;gBAAC;aAAQ;QAAC;QACrC,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2JAAA,CAAA,UAAM;oBACL,eAAe,kHAAA,CAAA,UAAa;oBAC5B,MAAM;oBACN,UAAU;oBACV,OAAO;wBAAE,OAAO;wBAAQ,QAAQ;oBAAQ;;;;;;;;;;;0BAK5C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAyI;0CAErJ,6LAAC;gCAAG,WAAU;;;;;;4BAAgC;4BAAM;0CACpD,6LAAC;gCAAK,WAAU;0CAAkH;;;;;;4BAE1H;4BAAI;;;;;;;kCAGd,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,UAAU;wBACZ;;4BACD;0CAEC,6LAAC;gCAAG,WAAU;;;;;;4BAAgC;;;;;;;kCAKhD,6LAAC;wBACC,KAAK;wBACL,WAAU;;0CAEV,6LAAC;gCAAK,UAAU;gCAAc,WAAU;0CACtC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CACC,KAAK;4CACL,MAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,SAAS,IAAM,cAAc;4CAC7B,aAAY;4CACZ,WAAU;;;;;;sDAEZ,6LAAC;4CACC,MAAK;4CACL,WAAU;sDAEV,cAAA,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0CAM5B,6LAAC;gCACC,WAAW,CAAC;;;;gBAIR,EACE,aACI,8BACA,kEACL;cACH,CAAC;;kDAEH,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,6LAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;gDAEC,MAAK;gDACL,SAAS;oDACP,sBAAsB;oDACtB,cAAc;gDAChB;gDACA,WAAU;0DAET;+CARI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBvB;GAnJwB;;QACP,qIAAA,CAAA,YAAS;QAGD,2IAAA,CAAA,gBAAa;QAwBpC,6IAAA,CAAA,UAAe;;;KA5BO"}}, {"offset": {"line": 822, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 828, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/LogoSlider.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Image from \"next/image\";\r\nimport React, { useEffect, useState } from \"react\";\r\nimport Slider from \"react-infinite-logo-slider\";\r\n\r\nconst logos_path = [\r\n  \"/images/institution-logos/1.png\",\r\n  \"/images/institution-logos/2.png\",\r\n  \"/images/institution-logos/3.svg\",\r\n  \"/images/institution-logos/4.png\",\r\n  \"/images/institution-logos/5.png\",\r\n  \"/images/institution-logos/6.png\",\r\n  \"/images/institution-logos/7.png\",\r\n  \"/images/institution-logos/8.png\",\r\n  \"/images/institution-logos/9.png\",\r\n  \"/images/institution-logos/10.png\",\r\n  \"/images/institution-logos/11.png\",\r\n];\r\n\r\nconst LogoSlider = () => {\r\n  const [loaded, setLoaded] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setLoaded(true);\r\n  }, []);\r\n  return (\r\n    <div className=\"container mx-auto flex flex-col justify-center items-start md:-mt-48 lg:-mt-32 xl:-mt-12 2xl:mt-10 mb-8 sm:mb-10 md:mb-16 sm:gap-y-8 gap-y-4 px-4 md:px-8 lg:px-16 xl:px-10 z-10\">\r\n      <div className=\"flex gap-1 md:gap-4 justify-start md:justify-center items-center\">\r\n        <h1 className=\"font-medium text-black text-2xl\">\r\n          Counselors from Top Universities:\r\n        </h1>\r\n        <Image\r\n          src={\"/svgs/red-arrow-diagonal.svg\"}\r\n          width={50}\r\n          height={50}\r\n          alt=\"\"\r\n          className=\"animate-pendulum origin-center\"\r\n        />\r\n      </div>\r\n      <div className=\"w-full overflow-hidden py-6 sm:py-10  bg-white rounded-xl shadow-transparent drop-shadow-[0_10px_30px_rgba(0,0,0,0.05)]\">\r\n        <Slider\r\n          width={loaded && window.innerWidth < 768 ? \"100px\" : \"160px\"}\r\n          duration={40}\r\n          pauseOnHover={false}\r\n          blurBorders={true}\r\n          blurBorderColor=\"#fff\"\r\n        >\r\n          {logos_path.map((logo, index) => (\r\n            <Slider.Slide key={index}>\r\n              <Image\r\n                width={800}\r\n                height={800}\r\n                src={logo}\r\n                alt={`Logo ${index + 1}`}\r\n                className=\"w-[3.3rem] md:w-20 object-cover h-auto\"\r\n              />\r\n            </Slider.Slide>\r\n          ))}\r\n        </Slider>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default LogoSlider;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,aAAa;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,aAAa;;IACjB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,UAAU;QACZ;+BAAG,EAAE;IACL,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAkC;;;;;;kCAGhD,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK;wBACL,OAAO;wBACP,QAAQ;wBACR,KAAI;wBACJ,WAAU;;;;;;;;;;;;0BAGd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,wKAAA,CAAA,UAAM;oBACL,OAAO,UAAU,OAAO,UAAU,GAAG,MAAM,UAAU;oBACrD,UAAU;oBACV,cAAc;oBACd,aAAa;oBACb,iBAAgB;8BAEf,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,6LAAC,wKAAA,CAAA,UAAM,CAAC,KAAK;sCACX,cAAA,6LAAC,gIAAA,CAAA,UAAK;gCACJ,OAAO;gCACP,QAAQ;gCACR,KAAK;gCACL,KAAK,CAAC,KAAK,EAAE,QAAQ,GAAG;gCACxB,WAAU;;;;;;2BANK;;;;;;;;;;;;;;;;;;;;;AAc/B;GA3CM;KAAA;uCA6CS"}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 949, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 979, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 985, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/newsletter/index.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { zod<PERSON><PERSON>ol<PERSON> } from \"@hookform/resolvers/zod\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Input } from \"@components/ui/input\";\r\nimport { useNewsletter } from \"@/app/hooks/public/useNewsletter\";\r\n\r\nconst newsletterSchema = z.object({\r\n  name: z.string().min(2, \"Name must be at least 2 characters\"),\r\n  email: z.string().email(\"Please enter a valid email address\"),\r\n});\r\n\r\ntype NewsletterFormData = z.infer<typeof newsletterSchema>;\r\n\r\nexport function NewsletterForm() {\r\n  const { subscribe, loading } = useNewsletter();\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors },\r\n    reset,\r\n  } = useForm<NewsletterFormData>({\r\n    resolver: zodResolver(newsletterSchema),\r\n  });\r\n\r\n  const onSubmit = async (data: NewsletterFormData) => {\r\n    try {\r\n      await subscribe(data);\r\n      reset();\r\n    } catch (error) {\r\n      console.error(error);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"container mx-auto max-w-7xl my-10 md:my-20  px-4 sm:px-6\">\r\n      <div className=\"bg-gradient-to-br from-blue-950 to-black/90 rounded-xl px-6 md:px-10 py-10 md:py-16 relative overflow-hidden\">\r\n        {/* Decorative dots pattern */}\r\n        {/* <div className=\"absolute top-0 left-0 grid grid-cols-7 gap-8 p-10\">\r\n        {[...Array(28)].map((_, i) => (\r\n          <div key={i} className=\"w-2 h-2 bg-blue-700/30 rounded-full\" />\r\n        ))}\r\n      </div> */}\r\n\r\n        {/* Content */}\r\n        <div className=\"relative z-10 max-w-4xl mx-auto\">\r\n          <h2 className=\"text-3xl md:text-4xl font-medium text-white mb-4\">\r\n            Subscribe to receive future updates\r\n          </h2>\r\n          <p className=\"text-blue-100 mb-8\">\r\n            If you want to get updates from us, please subscribe to our\r\n            newsletter.\r\n          </p>\r\n\r\n          <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\r\n            <div className=\"flex flex-col md:flex-row gap-4\">\r\n              <div className=\"flex-1\">\r\n                <Input\r\n                  {...register(\"name\")}\r\n                  placeholder=\"Enter your name\"\r\n                  className=\"w-full h-12 bg-white/95 border-0 text-gray-900 placeholder:text-gray-500\"\r\n                />\r\n                {errors.name && (\r\n                  <p className=\"text-gray-300 text-sm mt-1\">\r\n                    {errors.name.message}\r\n                  </p>\r\n                )}\r\n              </div>\r\n              <div className=\"flex-1\">\r\n                <Input\r\n                  {...register(\"email\")}\r\n                  type=\"email\"\r\n                  placeholder=\"Enter your email\"\r\n                  className=\"w-full h-12 bg-white/95 border-0 text-gray-900 placeholder:text-gray-500\"\r\n                />\r\n                {errors.email && (\r\n                  <p className=\"text-gray-300 text-sm mt-1\">\r\n                    {errors.email.message}\r\n                  </p>\r\n                )}\r\n              </div>\r\n              <Button\r\n                type=\"submit\"\r\n                className=\"h-12 px-8 bg-blue-950 hover:bg-blue-950/90 text-gray-100 font-semibold transition-colors\"\r\n                disabled={loading}\r\n              >\r\n                {loading ? \"Subscribing...\" : \"Subscribe\"}\r\n              </Button>\r\n            </div>\r\n          </form>\r\n\r\n          {/* Benefits section */}\r\n          {/* <div className=\"mt-12 flex flex-wrap gap-6 text-white text-sm\">\r\n            <span>$50 Raffle Entry</span>\r\n            <span>Essay Feedback from Ivy Experts</span>\r\n            <span>Claim Common App Extracurricular Entry</span>\r\n            <span>Q&A with Top College Admits</span>\r\n          </div> */}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default NewsletterForm;\r\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;AACA;AACA;AAJA;AACA;;;AAJA;;;;;;;AASA,MAAM,mBAAmB,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;AAC1B;AAIO,SAAS;;IACd,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,gBAAa,AAAD;IAC3C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAsB;QAC9B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,UAAU;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBASb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmD;;;;;;kCAGjE,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAKlC,6LAAC;wBAAK,UAAU,aAAa;wBAAW,WAAU;kCAChD,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACH,GAAG,SAAS,OAAO;4CACpB,aAAY;4CACZ,WAAU;;;;;;wCAEX,OAAO,IAAI,kBACV,6LAAC;4CAAE,WAAU;sDACV,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;8CAI1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACH,GAAG,SAAS,QAAQ;4CACrB,MAAK;4CACL,aAAY;4CACZ,WAAU;;;;;;wCAEX,OAAO,KAAK,kBACX,6LAAC;4CAAE,WAAU;sDACV,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAI3B,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,UAAU;8CAET,UAAU,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgB9C;GAxFgB;;QACiB,2IAAA,CAAA,gBAAa;QAMxC,iKAAA,CAAA,UAAO;;;KAPG;uCA0FD"}}, {"offset": {"line": 1156, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1162, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/Testimonials.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\n\r\nconst TestimonialsSection = () => {\r\n  const testimonials = [\r\n    {\r\n      quote:\r\n        \"Before AdmitPath, I was overwhelmed with essays, deadlines, and choosing the right schools. My advisor helped me organize everything and gave me a strategy that worked. I got into my top-choice school!\",\r\n      author: \"<PERSON>\",\r\n      role: \"Incoming College Freshman\",\r\n      highlight: \"I finally had a clear plan for my applications.\",\r\n    },\r\n    {\r\n      quote:\r\n        \"I thought my Common App essay was strong, but my AdmitPath advisor helped me refine it to truly reflect my story. The final version was much more compelling, and I got accepted into multiple Ivy League schools!\",\r\n      author: \"<PERSON>\",\r\n      role: \"Admitted to Columbia University\",\r\n      highlight: \"The essay feedback completely changed my application!\",\r\n    },\r\n    {\r\n      quote:\r\n        \"I was so nervous about my interviews, but my AdmitPath advisor gave me personalized feedback and mock interview practice. I went in prepared and confident—and got into my dream school!\",\r\n      author: \"<PERSON>\",\r\n      role: \"Admitted to Stanford University\",\r\n      highlight:\r\n        \"I booked a session for interview prep, and it made all the difference.\",\r\n    },\r\n    {\r\n      quote:\r\n        \"I needed urgent help with my supplemental essays, and <PERSON>mit<PERSON><PERSON> connected me with an expert in just a few hours. Their feedback was incredibly detailed, and I know it helped strengthen my application!\",\r\n      author: \"<PERSON>\",\r\n      role: \"Admitted to NYU\",\r\n      highlight: \"Fast, reliable, and actually helpful.\",\r\n    },\r\n    {\r\n      quote:\r\n        \"I didn't know how to showcase my extracurriculars to maximize merit-based scholarships. My advisor helped me position everything strategically, and I got into a top university with a full-ride scholarship!\",\r\n      author: \"Daniel R.\",\r\n      role: \"Admitted to USC with a Full-Ride Scholarship\",\r\n      highlight: \"I secured over $80,000 in scholarships thanks to my advisor.\",\r\n    },\r\n  ];\r\n\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n  const [cardWidth, setCardWidth] = useState(33.333);\r\n  const [maxHeight, setMaxHeight] = useState(0);\r\n  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);\r\n\r\n  useEffect(() => {\r\n    const missionSection: HTMLElement | null =\r\n      document.querySelector(\".mission-section\");\r\n    const words: NodeListOf<HTMLElement> = document.querySelectorAll(\r\n      \".mission-section .word\"\r\n    );\r\n\r\n    const updateOpacity = (): void => {\r\n      if (!missionSection) return;\r\n\r\n      const sectionTop: number = missionSection.offsetTop;\r\n      const sectionHeight: number = missionSection.offsetHeight;\r\n      const scrollY: number = window.scrollY;\r\n      const windowHeight: number = window.innerHeight;\r\n\r\n      const sectionStart: number =\r\n        sectionTop - windowHeight + sectionHeight * 0.4;\r\n      const sectionEnd: number = sectionTop + sectionHeight;\r\n\r\n      if (scrollY >= sectionStart && scrollY <= sectionEnd) {\r\n        const scrollProgress: number =\r\n          (scrollY - sectionStart) / (sectionEnd - sectionStart);\r\n\r\n        words.forEach((word: HTMLElement, index: number) => {\r\n          const wordProgress: number =\r\n            scrollProgress - index * (0.5 / words.length);\r\n          const opacity: number = Math.min(\r\n            Math.max(wordProgress * words.length, 0.2),\r\n            1\r\n          );\r\n          word.style.opacity = opacity.toString();\r\n        });\r\n      } else if (scrollY < sectionStart) {\r\n        words.forEach((word: HTMLElement) => (word.style.opacity = \"0.2\"));\r\n      } else if (scrollY > sectionEnd) {\r\n        words.forEach((word: HTMLElement) => (word.style.opacity = \"1\"));\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"scroll\", updateOpacity);\r\n    updateOpacity();\r\n\r\n    return () => window.removeEventListener(\"scroll\", updateOpacity);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      if (window.innerWidth < 640) {\r\n        setCardWidth(100);\r\n      } else if (window.innerWidth < 1024) {\r\n        setCardWidth(50);\r\n      } else {\r\n        setCardWidth(33.333);\r\n      }\r\n\r\n      // Reset height calculation\r\n      setTimeout(() => {\r\n        const heights = cardRefs.current\r\n          .filter((ref): ref is HTMLDivElement => ref !== null)\r\n          .map((ref) => ref.scrollHeight);\r\n        const maxCardHeight = Math.max(...heights);\r\n        setMaxHeight(maxCardHeight);\r\n      }, 100);\r\n    };\r\n\r\n    window.addEventListener(\"resize\", handleResize);\r\n    handleResize();\r\n    return () => window.removeEventListener(\"resize\", handleResize);\r\n  }, []);\r\n\r\n  const nextSlide = () => {\r\n    if (currentIndex < testimonials.length - 1) {\r\n      setCurrentIndex((prev) => prev + 1);\r\n    }\r\n  };\r\n\r\n  const prevSlide = () => {\r\n    if (currentIndex > 0) {\r\n      setCurrentIndex((prev) => prev - 1);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <section className=\"w-full bg-gray-50 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-24 py-12 sm:py-16\">\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        <div className=\"text-center mb-12\">\r\n          <div className=\"mission-section mb-8\">\r\n            <h2 className=\"mission_text text-2xl sm:text-3xl text-blue-950 font-bold\">\r\n              {\"Join thousands of students who found their path to success with AdmitPath\".split(\" \").map((word: string, index: number) => (\r\n                <span\r\n                  key={index}\r\n                  className=\"word inline-block opacity-20 leading-10 transition-opacity duration-300 ease-in-out\"\r\n                >\r\n                  {word}&nbsp;\r\n                </span>\r\n              ))}\r\n            </h2>\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"relative\">\r\n          <div className=\"overflow-hidden\">\r\n            <div\r\n              className=\"flex transition-transform duration-500 ease-in-out py-2\"\r\n              style={{\r\n                transform: `translateX(-${currentIndex * cardWidth}%)`,\r\n              }}\r\n            >\r\n              {testimonials.map((testimonial, index) => (\r\n                <div\r\n                  key={index}\r\n                  className=\"w-full md:w-1/2 lg:w-1/3 flex-shrink-0 px-2 sm:px-3 md:px-4\"\r\n                >\r\n                  <div\r\n                    ref={(el) => {\r\n                      cardRefs.current[index] = el;\r\n                    }}\r\n                    className=\"bg-white rounded-lg p-6 transition-all duration-200 flex flex-col h-full border hover:shadow-md\"\r\n                    style={{ minHeight: maxHeight ? `${maxHeight}px` : \"auto\" }}\r\n                  >\r\n                    <div className=\"text-blue-950 font-medium text-base mb-3\">\r\n                      {testimonial.highlight}\r\n                    </div>\r\n                    <p className=\"text-gray-600 text-sm leading-relaxed flex-grow\">\r\n                      \"{testimonial.quote}\"\r\n                    </p>\r\n                    <div className=\"mt-4 pt-4 border-t\">\r\n                      <p className=\"font-semibold text-gray-900 text-sm\">\r\n                        {testimonial.author}\r\n                      </p>\r\n                      <p className=\"text-gray-500 text-xs\">\r\n                        {testimonial.role}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation Controls */}\r\n          <div className=\"mt-8 flex items-center justify-center gap-4 px-4\">\r\n            <button\r\n              onClick={prevSlide}\r\n              disabled={currentIndex === 0}\r\n              className=\"w-8 h-8 rounded-full bg-white flex items-center justify-center text-blue-950 hover:bg-gray-50 disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 border\"\r\n              aria-label=\"Previous testimonial\"\r\n            >\r\n              <svg\r\n                className=\"w-4 h-4\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  strokeWidth={2}\r\n                  d=\"M15 19l-7-7 7-7\"\r\n                />\r\n              </svg>\r\n            </button>\r\n\r\n            {/* Progress Indicators */}\r\n            <div className=\"flex gap-1.5\">\r\n              {testimonials.map((_, idx) => (\r\n                <button\r\n                  key={idx}\r\n                  onClick={() => setCurrentIndex(idx)}\r\n                  className={`w-2 h-2 rounded-full transition-all duration-200 ${idx === currentIndex ? 'bg-blue-950' : 'bg-gray-300 hover:bg-gray-400'}`}\r\n                  aria-label={`Go to testimonial ${idx + 1}`}\r\n                />\r\n              ))}\r\n            </div>\r\n\r\n            <button\r\n              onClick={nextSlide}\r\n              disabled={currentIndex === testimonials.length - 1}\r\n              className=\"w-8 h-8 rounded-full bg-white flex items-center justify-center text-blue-950 hover:bg-gray-50 disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 border\"\r\n              aria-label=\"Next testimonial\"\r\n            >\r\n              <svg\r\n                className=\"w-4 h-4\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  strokeWidth={2}\r\n                  d=\"M9 5l7 7-7 7\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default TestimonialsSection;\r\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIA,MAAM,sBAAsB;;IAC1B,MAAM,eAAe;QACnB;YACE,OACE;YACF,QAAQ;YACR,MAAM;YACN,WAAW;QACb;QACA;YACE,OACE;YACF,QAAQ;YACR,MAAM;YACN,WAAW;QACb;QACA;YACE,OACE;YACF,QAAQ;YACR,MAAM;YACN,WACE;QACJ;QACA;YACE,OACE;YACF,QAAQ;YACR,MAAM;YACN,WAAW;QACb;QACA;YACE,OACE;YACF,QAAQ;YACR,MAAM;YACN,WAAW;QACb;KACD;IAED,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA6B,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM,iBACJ,SAAS,aAAa,CAAC;YACzB,MAAM,QAAiC,SAAS,gBAAgB,CAC9D;YAGF,MAAM;+DAAgB;oBACpB,IAAI,CAAC,gBAAgB;oBAErB,MAAM,aAAqB,eAAe,SAAS;oBACnD,MAAM,gBAAwB,eAAe,YAAY;oBACzD,MAAM,UAAkB,OAAO,OAAO;oBACtC,MAAM,eAAuB,OAAO,WAAW;oBAE/C,MAAM,eACJ,aAAa,eAAe,gBAAgB;oBAC9C,MAAM,aAAqB,aAAa;oBAExC,IAAI,WAAW,gBAAgB,WAAW,YAAY;wBACpD,MAAM,iBACJ,CAAC,UAAU,YAAY,IAAI,CAAC,aAAa,YAAY;wBAEvD,MAAM,OAAO;2EAAC,CAAC,MAAmB;gCAChC,MAAM,eACJ,iBAAiB,QAAQ,CAAC,MAAM,MAAM,MAAM;gCAC9C,MAAM,UAAkB,KAAK,GAAG,CAC9B,KAAK,GAAG,CAAC,eAAe,MAAM,MAAM,EAAE,MACtC;gCAEF,KAAK,KAAK,CAAC,OAAO,GAAG,QAAQ,QAAQ;4BACvC;;oBACF,OAAO,IAAI,UAAU,cAAc;wBACjC,MAAM,OAAO;2EAAC,CAAC,OAAuB,KAAK,KAAK,CAAC,OAAO,GAAG;;oBAC7D,OAAO,IAAI,UAAU,YAAY;wBAC/B,MAAM,OAAO;2EAAC,CAAC,OAAuB,KAAK,KAAK,CAAC,OAAO,GAAG;;oBAC7D;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;YAEA;iDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;wCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM;8DAAe;oBACnB,IAAI,OAAO,UAAU,GAAG,KAAK;wBAC3B,aAAa;oBACf,OAAO,IAAI,OAAO,UAAU,GAAG,MAAM;wBACnC,aAAa;oBACf,OAAO;wBACL,aAAa;oBACf;oBAEA,2BAA2B;oBAC3B;sEAAW;4BACT,MAAM,UAAU,SAAS,OAAO,CAC7B,MAAM;sFAAC,CAAC,MAA+B,QAAQ;qFAC/C,GAAG;sFAAC,CAAC,MAAQ,IAAI,YAAY;;4BAChC,MAAM,gBAAgB,KAAK,GAAG,IAAI;4BAClC,aAAa;wBACf;qEAAG;gBACL;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;YACA;iDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;wCAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI,eAAe,aAAa,MAAM,GAAG,GAAG;YAC1C,gBAAgB,CAAC,OAAS,OAAO;QACnC;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,eAAe,GAAG;YACpB,gBAAgB,CAAC,OAAS,OAAO;QACnC;IACF;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;sCACX,4EAA4E,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAc,sBACzG,6LAAC;oCAEC,WAAU;;wCAET;wCAAK;;mCAHD;;;;;;;;;;;;;;;;;;;;8BAUf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCACL,WAAW,CAAC,YAAY,EAAE,eAAe,UAAU,EAAE,CAAC;gCACxD;0CAEC,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC;wCAEC,WAAU;kDAEV,cAAA,6LAAC;4CACC,KAAK,CAAC;gDACJ,SAAS,OAAO,CAAC,MAAM,GAAG;4CAC5B;4CACA,WAAU;4CACV,OAAO;gDAAE,WAAW,YAAY,GAAG,UAAU,EAAE,CAAC,GAAG;4CAAO;;8DAE1D,6LAAC;oDAAI,WAAU;8DACZ,YAAY,SAAS;;;;;;8DAExB,6LAAC;oDAAE,WAAU;;wDAAkD;wDAC3D,YAAY,KAAK;wDAAC;;;;;;;8DAEtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEACV,YAAY,MAAM;;;;;;sEAErB,6LAAC;4DAAE,WAAU;sEACV,YAAY,IAAI;;;;;;;;;;;;;;;;;;uCArBlB;;;;;;;;;;;;;;;sCA+Bb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,UAAU,iBAAiB;oCAC3B,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,QAAO;kDAEP,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;8CAMR,6LAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,GAAG,oBACpB,6LAAC;4CAEC,SAAS,IAAM,gBAAgB;4CAC/B,WAAW,CAAC,iDAAiD,EAAE,QAAQ,eAAe,gBAAgB,iCAAiC;4CACvI,cAAY,CAAC,kBAAkB,EAAE,MAAM,GAAG;2CAHrC;;;;;;;;;;8CAQX,6LAAC;oCACC,SAAS;oCACT,UAAU,iBAAiB,aAAa,MAAM,GAAG;oCACjD,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCACC,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,QAAO;kDAEP,cAAA,6LAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpB;GArPM;KAAA;uCAuPS"}}, {"offset": {"line": 1526, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1532, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/optimized-image.tsx"], "sourcesContent": ["\"use client\";\n\nimport Image from \"next/image\";\nimport { useState, useRef, useEffect } from \"react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface OptimizedImageProps {\n  src: string;\n  alt: string;\n  width?: number;\n  height?: number;\n  fill?: boolean;\n  sizes?: string;\n  className?: string;\n  priority?: boolean;\n  quality?: number;\n  placeholder?: \"blur\" | \"empty\";\n  blurDataURL?: string;\n  onLoad?: () => void;\n  onError?: () => void;\n  fallbackSrc?: string;\n  lazy?: boolean;\n  preload?: boolean;\n  aspectRatio?: string;\n}\n\nexport function OptimizedImage({\n  src,\n  alt,\n  width,\n  height,\n  fill = false,\n  sizes,\n  className,\n  priority = false,\n  quality = 75,\n  placeholder = \"blur\",\n  blurDataURL,\n  onLoad,\n  onError,\n  fallbackSrc,\n  lazy = true,\n  preload = false,\n  aspectRatio,\n  ...props\n}: OptimizedImageProps) {\n  const [isLoading, setIsLoading] = useState(true);\n  const [hasError, setHasError] = useState(false);\n  const [isInView, setIsInView] = useState(!lazy || priority);\n  const [currentSrc, setCurrentSrc] = useState(src);\n  const imgRef = useRef<HTMLDivElement>(null);\n\n  // Generate a simple blur placeholder if none provided\n  const defaultBlurDataURL = \n    \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==\";\n\n  // Intersection Observer for lazy loading\n  useEffect(() => {\n    if (!lazy || priority || isInView) return;\n\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting) {\n            setIsInView(true);\n            observer.disconnect();\n          }\n        });\n      },\n      {\n        rootMargin: preload ? \"200px\" : \"50px\", // Load earlier if preload is true\n        threshold: 0.1,\n      }\n    );\n\n    if (imgRef.current) {\n      observer.observe(imgRef.current);\n    }\n\n    return () => observer.disconnect();\n  }, [lazy, priority, isInView, preload]);\n\n  const handleLoad = () => {\n    setIsLoading(false);\n    onLoad?.();\n  };\n\n  const handleError = () => {\n    setHasError(true);\n    setIsLoading(false);\n    if (fallbackSrc && currentSrc !== fallbackSrc) {\n      setCurrentSrc(fallbackSrc);\n      setHasError(false);\n    } else {\n      onError?.();\n    }\n  };\n\n  const containerStyle = aspectRatio\n    ? { aspectRatio }\n    : {};\n\n  return (\n    <div\n      ref={imgRef}\n      className={cn(\"relative overflow-hidden\", className)}\n      style={containerStyle}\n    >\n      {/* Loading skeleton */}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-gray-200 animate-pulse\" />\n      )}\n\n      {/* Error state */}\n      {hasError && !fallbackSrc && (\n        <div className=\"absolute inset-0 bg-gray-100 flex items-center justify-center\">\n          <div className=\"text-gray-400 text-sm\">Failed to load</div>\n        </div>\n      )}\n\n      {/* Actual image */}\n      {isInView && !hasError && (\n        <Image\n          src={currentSrc}\n          alt={alt}\n          width={width}\n          height={height}\n          fill={fill}\n          sizes={sizes}\n          quality={quality}\n          priority={priority}\n          placeholder={placeholder}\n          blurDataURL={blurDataURL || defaultBlurDataURL}\n          onLoad={handleLoad}\n          onError={handleError}\n          className={cn(\n            \"transition-opacity duration-300\",\n            isLoading ? \"opacity-0\" : \"opacity-100\"\n          )}\n          {...props}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AA0BO,SAAS,eAAe,EAC7B,GAAG,EACH,GAAG,EACH,KAAK,EACL,MAAM,EACN,OAAO,KAAK,EACZ,KAAK,EACL,SAAS,EACT,WAAW,KAAK,EAChB,UAAU,EAAE,EACZ,cAAc,MAAM,EACpB,WAAW,EACX,MAAM,EACN,OAAO,EACP,WAAW,EACX,OAAO,IAAI,EACX,UAAU,KAAK,EACf,WAAW,EACX,GAAG,OACiB;;IACpB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,QAAQ;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAEtC,sDAAsD;IACtD,MAAM,qBACJ;IAEF,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,QAAQ,YAAY,UAAU;YAEnC,MAAM,WAAW,IAAI;4CACnB,CAAC;oBACC,QAAQ,OAAO;oDAAC,CAAC;4BACf,IAAI,MAAM,cAAc,EAAE;gCACxB,YAAY;gCACZ,SAAS,UAAU;4BACrB;wBACF;;gBACF;2CACA;gBACE,YAAY,UAAU,UAAU;gBAChC,WAAW;YACb;YAGF,IAAI,OAAO,OAAO,EAAE;gBAClB,SAAS,OAAO,CAAC,OAAO,OAAO;YACjC;YAEA;4CAAO,IAAM,SAAS,UAAU;;QAClC;mCAAG;QAAC;QAAM;QAAU;QAAU;KAAQ;IAEtC,MAAM,aAAa;QACjB,aAAa;QACb;IACF;IAEA,MAAM,cAAc;QAClB,YAAY;QACZ,aAAa;QACb,IAAI,eAAe,eAAe,aAAa;YAC7C,cAAc;YACd,YAAY;QACd,OAAO;YACL;QACF;IACF;IAEA,MAAM,iBAAiB,cACnB;QAAE;IAAY,IACd,CAAC;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QAC1C,OAAO;;YAGN,2BACC,6LAAC;gBAAI,WAAU;;;;;;YAIhB,YAAY,CAAC,6BACZ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAAwB;;;;;;;;;;;YAK1C,YAAY,CAAC,0BACZ,6LAAC,gIAAA,CAAA,UAAK;gBACJ,KAAK;gBACL,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;gBACV,aAAa;gBACb,aAAa,eAAe;gBAC5B,QAAQ;gBACR,SAAS;gBACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mCACA,YAAY,cAAc;gBAE3B,GAAG,KAAK;;;;;;;;;;;;AAKnB;GAtHgB;KAAA"}}, {"offset": {"line": 1664, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1670, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/counselor-card-skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\n\ninterface CounselorCardSkeletonProps {\n  className?: string;\n  variant?: \"explore\" | \"home\";\n}\n\nexport function CounselorCardSkeleton({ \n  className, \n  variant = \"explore\" \n}: CounselorCardSkeletonProps) {\n  if (variant === \"home\") {\n    return (\n      <div className={cn(\n        \"w-[280px] sm:w-[300px] md:w-[320px] min-h-[390px] sm:min-h-[410px] max-h-[390px] sm:max-h-[410px] mx-3 sm:mx-4 md:mx-5 my-4 sm:my-6 md:my-8 rounded-xl bg-white shadow-md overflow-hidden animate-pulse\",\n        className\n      )}>\n        {/* Image skeleton */}\n        <div className=\"relative h-[220px] w-full bg-gray-200\" />\n        \n        {/* Content skeleton */}\n        <div className=\"p-4 space-y-3\">\n          {/* Name */}\n          <div className=\"h-6 bg-gray-200 rounded w-3/4\" />\n          \n          {/* Bio/tagline */}\n          <div className=\"space-y-2\">\n            <div className=\"h-4 bg-gray-200 rounded w-full\" />\n            <div className=\"h-4 bg-gray-200 rounded w-2/3\" />\n          </div>\n          \n          {/* University */}\n          <div className=\"flex items-center gap-2 mt-4\">\n            <div className=\"w-5 h-5 bg-gray-200 rounded\" />\n            <div className=\"h-4 bg-gray-200 rounded w-1/2\" />\n          </div>\n          \n          {/* Price */}\n          <div className=\"mt-4 space-y-1\">\n            <div className=\"h-3 bg-gray-200 rounded w-16\" />\n            <div className=\"h-5 bg-gray-200 rounded w-20\" />\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={cn(\n      \"overflow-hidden rounded-lg flex flex-col bg-white border animate-pulse\",\n      className\n    )}>\n      <div className=\"relative\">\n        {/* Header background */}\n        <div className=\"h-20 relative overflow-hidden bg-gray-200\">\n          <div className=\"h-6 bg-gray-300 w-full\" />\n        </div>\n\n        {/* Profile Picture and University Logo skeleton */}\n        <div className=\"absolute flex items-center justify-between gap-8 -bottom-8 left-4 pr-8 w-full\">\n          <div className=\"relative w-24 h-24 rounded-full bg-gray-200 border-3 border-white shadow-lg\" />\n          \n          {/* University Logo skeleton */}\n          <div className=\"w-16 h-16 bg-gray-200 rounded-lg\" />\n          \n          {/* Country flag skeleton */}\n          <div className=\"w-6 h-4 bg-gray-200 rounded-sm\" />\n        </div>\n      </div>\n      \n      <div className=\"p-4 pt-12 flex-1\">\n        <div className=\"flex flex-col justify-between h-full\">\n          <div className=\"flex flex-col gap-2\">\n            {/* Name skeleton */}\n            <div className=\"h-6 bg-gray-200 rounded w-3/4\" />\n            \n            {/* Bio skeleton */}\n            <div className=\"space-y-2 mt-1\">\n              <div className=\"h-4 bg-gray-200 rounded w-full\" />\n              <div className=\"h-4 bg-gray-200 rounded w-2/3\" />\n            </div>\n          </div>\n          \n          <div>\n            {/* Education skeleton */}\n            <div className=\"mt-4 flex items-center gap-2\">\n              <div className=\"w-5 h-5 bg-gray-200 rounded\" />\n              <div className=\"h-4 bg-gray-200 rounded w-1/2\" />\n            </div>\n\n            {/* Pricing skeleton */}\n            <div className=\"mt-4 space-y-1\">\n              <div className=\"h-3 bg-gray-200 rounded w-16\" />\n              <div className=\"h-5 bg-gray-200 rounded w-20\" />\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      {/* Buttons skeleton */}\n      <div className=\"px-4 pb-4\">\n        <div className=\"flex flex-col sm:grid sm:grid-cols-2 gap-2\">\n          <div className=\"h-10 bg-gray-200 rounded-lg\" />\n          <div className=\"h-10 bg-gray-200 rounded-lg\" />\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Multiple skeletons for loading states\nexport function CounselorCardSkeletonGrid({ \n  count = 8, \n  variant = \"explore\" \n}: { \n  count?: number; \n  variant?: \"explore\" | \"home\";\n}) {\n  if (variant === \"home\") {\n    return (\n      <div className=\"flex flex-wrap justify-center items-center gap-6 w-full\">\n        {Array.from({ length: count }).map((_, index) => (\n          <CounselorCardSkeleton key={index} variant=\"home\" />\n        ))}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4\">\n      {Array.from({ length: count }).map((_, index) => (\n        <CounselorCardSkeleton key={index} variant=\"explore\" />\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAOO,SAAS,sBAAsB,EACpC,SAAS,EACT,UAAU,SAAS,EACQ;IAC3B,IAAI,YAAY,QAAQ;QACtB,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,2MACA;;8BAGA,6LAAC;oBAAI,WAAU;;;;;;8BAGf,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,0EACA;;0BAEA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;0BAInB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;;;;;8CAGf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;sCAInB,6LAAC;;8CAEC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;KArGgB;AAwGT,SAAS,0BAA0B,EACxC,QAAQ,CAAC,EACT,UAAU,SAAS,EAIpB;IACC,IAAI,YAAY,QAAQ;QACtB,qBACE,6LAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC;oBAAkC,SAAQ;mBAAf;;;;;;;;;;IAIpC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC;gBAAkC,SAAQ;eAAf;;;;;;;;;;AAIpC;MAxBgB"}}, {"offset": {"line": 2028, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2034, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/TopCounselors.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport React, { useEffect, useId, useState } from \"react\";\r\nimport * as CountryFlags from \"country-flag-icons/react/3x2\";\r\n\r\nconst getCountryFlag = (countryCode: string = \"US\") => {\r\n  const code = countryCode.toUpperCase();\r\n  const FlagComponent = (CountryFlags as any)[code];\r\n  return FlagComponent ? <FlagComponent /> : <CountryFlags.US />;\r\n};\r\nimport { Button } from \"../../ui/button\";\r\nimport { ArrowRight, GraduationCap } from \"lucide-react\";\r\n\r\nimport { Spinner } from \"@/app/components/ui/spinner\";\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport { useCounselors } from \"@/app/hooks/public/useCounselors\";\r\nimport Slider from \"react-infinite-logo-slider\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\nimport { OptimizedImage } from \"@/app/components/ui/optimized-image\";\r\nimport { CounselorCardSkeletonGrid } from \"@/app/components/ui/counselor-card-skeleton\";\r\n\r\nexport const Card = ({\r\n  profile,\r\n  priority = false,\r\n}: {\r\n  profile: Partial<CounselorPublicProfile>;\r\n  priority?: boolean;\r\n}) => {\r\n  return (\r\n    <Link\r\n      href={`/profile/${profile.user_id}`}\r\n      className=\"block w-[280px] sm:w-[300px] md:w-[320px] min-h-[390px] sm:min-h-[410px] max-h-[390px] sm:max-h-[410px] mx-3 sm:mx-4 md:mx-5 my-4 sm:my-6 md:my-8 z-10 rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-500 cursor-pointer overflow-hidden hover:scale-[1.02] hover:-translate-y-1 hover:rotate-3 hover:ring-1 hover:ring-blue-950/30\"\r\n    >\r\n      <div className=\"relative h-[220px] w-full\">\r\n        <OptimizedImage\r\n          width={320}\r\n          height={220}\r\n          className=\"w-full h-full object-cover\"\r\n          src={\r\n            profile.profile_picture_url ||\r\n            generatePlaceholder(profile.first_name, profile.last_name)\r\n          }\r\n          alt={`${profile.first_name}'s profile`}\r\n          priority={priority}\r\n          lazy={!priority}\r\n          quality={85}\r\n          sizes=\"(max-width: 640px) 280px, (max-width: 768px) 300px, 320px\"\r\n          fallbackSrc={generatePlaceholder(\r\n            profile.first_name,\r\n            profile.last_name\r\n          )}\r\n        />\r\n        <div className=\"absolute top-2 left-2 w-6 h-4 rounded-sm overflow-hidden shadow-md\">\r\n          {getCountryFlag(profile.country_of_residence)}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"p-4 flex flex-col h-[190px]\">\r\n        <div className=\"space-y-2 mb-2\">\r\n          <div className=\"flex flex-col gap-1.5\">\r\n            <div className=\"flex items-start justify-between gap-3\">\r\n              <h3 className=\"text-xl font-semibold text-gray-900 truncate\">\r\n                {profile.first_name}\r\n              </h3>\r\n              <p className=\"text-lg font-[500] text-blue-950 whitespace-nowrap flex-shrink-0\">\r\n                ${profile.hourly_rate}/hr\r\n              </p>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"h-[36px] text-sm text-gray-600 mt-2\">\r\n            <p className=\"line-clamp-2\">\r\n              {profile?.tagline || profile?.bio || \"\"}\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"h-[24px] mt-0.5\">\r\n            {profile.education && profile.education.length > 0 && (\r\n              <div className=\"flex items-center gap-2 text-sm\">\r\n                <GraduationCap className=\"w-4 h-4 text-blue-950 flex-shrink-0\" />\r\n                <p className=\"line-clamp-1 font-[500] text-[15px] text-blue-950\">\r\n                  {profile.education[0].university_name ||\r\n                    profile.education[0].major}\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"border-t pt-1.5 mt-auto pb-1\">\r\n          <div className=\"relative overflow-hidden\">\r\n            <div className=\"absolute left-0 top-0 bottom-0 w-6 bg-gradient-to-r from-white via-white to-transparent z-10\"></div>\r\n            <div className=\"absolute right-0 top-0 bottom-0 w-6 bg-gradient-to-l from-white via-white to-transparent z-10\"></div>\r\n            <div className=\"flex whitespace-nowrap animate-[scroll_15s_linear_infinite] hover:pause-animation\">\r\n              <div className=\"flex gap-1.5 shrink-0\">\r\n                {[\r\n                  \"Essay Review\",\r\n                  \"Personal Statement\",\r\n                  \"College List Building\",\r\n                  \"Essay Brainstorming\",\r\n                ].map((service, index) => (\r\n                  <span\r\n                    key={index}\r\n                    className=\"inline-flex items-center whitespace-nowrap px-2 py-0.5 bg-blue-950/5 text-blue-950 text-[11px] font-medium border border-blue-950/10 rounded hover:bg-blue-950/10 transition-colors\"\r\n                  >\r\n                    {service}\r\n                  </span>\r\n                ))}\r\n              </div>\r\n              <div className=\"flex gap-1.5 shrink-0\">\r\n                {[\r\n                  \"Essay Review\",\r\n                  \"Personal Statement\",\r\n                  \"College List Building\",\r\n                  \"Essay Brainstorming\",\r\n                ].map((service, index) => (\r\n                  <span\r\n                    key={`duplicate-${index}`}\r\n                    className=\"inline-flex items-center whitespace-nowrap px-2 py-0.5 bg-blue-950/5 text-blue-950 text-[11px] font-medium border border-blue-950/10 rounded hover:bg-blue-950/10 transition-colors\"\r\n                  >\r\n                    {service}\r\n                  </span>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </Link>\r\n  );\r\n};\r\n\r\nconst TopCounselors = () => {\r\n  const [topCounselors, setTopCounselors] = useState<CounselorPublicProfile[]>(\r\n    []\r\n  );\r\n  const { fetchTopCounselors, loading } = useCounselors();\r\n  const [loaded, setLoaded] = useState(false);\r\n\r\n  useEffect(() => {\r\n    let isMounted = true;\r\n\r\n    const loadTopCounselors = async () => {\r\n      try {\r\n        const counselors = await fetchTopCounselors();\r\n        setLoaded(true);\r\n        if (isMounted) {\r\n          setTopCounselors(counselors || []);\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error loading counselors:\", error);\r\n        if (isMounted) {\r\n          setTopCounselors([]);\r\n        }\r\n      }\r\n    };\r\n\r\n    loadTopCounselors();\r\n\r\n    return () => {\r\n      isMounted = false;\r\n    };\r\n  }, [fetchTopCounselors]);\r\n\r\n  return (\r\n    <div className=\"container max-w-8xl mx-auto flex flex-col justify-center items-center my-10 md:my-20 gap-y-6 md:gap-y-12 px-4 md:px-8 lg:px-16 xl:px-10\">\r\n      <div className=\"w-full flex flex-col items-center justify-center gap-y-2.5 md:gap-y-4 text-center px-3\">\r\n        <div className=\"self-stretch text-2xl sm:text-4xl lg:text-5xl font-medium text-zinc-900\">\r\n          Meet Our Top Counselors\r\n        </div>\r\n        <div className=\"text-lg md:text-xl text-blue-950 px-2\">\r\n          Explore our most trusted and highly rated mentors, ready to guide you\r\n          to success\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"w-full overflow-hidden\">\r\n        {loading && topCounselors.length === 0 ? (\r\n          <CounselorCardSkeletonGrid count={6} variant=\"home\" />\r\n        ) : (\r\n          <Slider\r\n            width={loaded && window.innerWidth < 640 ? \"330px\" : \"350px\"}\r\n            duration={60}\r\n            pauseOnHover={true}\r\n            blurBorders={false}\r\n          >\r\n            {[...topCounselors, ...topCounselors].map((counselor, index) => (\r\n              <Slider.Slide key={index}>\r\n                <Card\r\n                  profile={counselor}\r\n                  priority={index < 3} // Prioritize first 3 visible cards\r\n                />\r\n              </Slider.Slide>\r\n            ))}\r\n          </Slider>\r\n        )}\r\n      </div>\r\n\r\n      <Link href=\"/explore\">\r\n        <Button className=\"bg-blue-950 hover:bg-blue-950/90 rounded-xl p-6 text-white text-lg sm:text-xl flex justify-center items-center\">\r\n          View More <ArrowRight className=\"h-6 w-6 ml-2\" />\r\n        </Button>\r\n      </Link>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TopCounselors;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAQA;AAKA;AACA;AACA;AACA;AACA;AAhBA;AAQA;AAAA;;;AAZA;;;;AAMA,MAAM,iBAAiB,CAAC,cAAsB,IAAI;IAChD,MAAM,OAAO,YAAY,WAAW;IACpC,MAAM,gBAAgB,AAAC,oKAAoB,CAAC,KAAK;IACjD,OAAO,8BAAgB,6LAAC;;;;6BAAmB,6LAAC,qKAAa,EAAE;;;;;AAC7D;;;;;;;;AAYO,MAAM,OAAO,CAAC,EACnB,OAAO,EACP,WAAW,KAAK,EAIjB;IACC,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;QACnC,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,iJAAA,CAAA,iBAAc;wBACb,OAAO;wBACP,QAAQ;wBACR,WAAU;wBACV,KACE,QAAQ,mBAAmB,IAC3B,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,UAAU,EAAE,QAAQ,SAAS;wBAE3D,KAAK,GAAG,QAAQ,UAAU,CAAC,UAAU,CAAC;wBACtC,UAAU;wBACV,MAAM,CAAC;wBACP,SAAS;wBACT,OAAM;wBACN,aAAa,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD,EAC7B,QAAQ,UAAU,EAClB,QAAQ,SAAS;;;;;;kCAGrB,6LAAC;wBAAI,WAAU;kCACZ,eAAe,QAAQ,oBAAoB;;;;;;;;;;;;0BAIhD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,QAAQ,UAAU;;;;;;sDAErB,6LAAC;4CAAE,WAAU;;gDAAmE;gDAC5E,QAAQ,WAAW;gDAAC;;;;;;;;;;;;;;;;;;0CAK5B,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CACV,SAAS,WAAW,SAAS,OAAO;;;;;;;;;;;0CAIzC,6LAAC;gCAAI,WAAU;0CACZ,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,mBAC/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,SAAS,CAAC,EAAE,CAAC,eAAe,IACnC,QAAQ,SAAS,CAAC,EAAE,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;kCAOtC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ;gDACC;gDACA;gDACA;gDACA;6CACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;sDAOX,6LAAC;4CAAI,WAAU;sDACZ;gDACC;gDACA;gDACA;gDACA;6CACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,6LAAC;oDAEC,WAAU;8DAET;mDAHI,CAAC,UAAU,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAa7C;KA7Ga;AA+Gb,MAAM,gBAAgB;;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC/C,EAAE;IAEJ,MAAM,EAAE,kBAAkB,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2IAAA,CAAA,gBAAa,AAAD;IACpD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,YAAY;YAEhB,MAAM;6DAAoB;oBACxB,IAAI;wBACF,MAAM,aAAa,MAAM;wBACzB,UAAU;wBACV,IAAI,WAAW;4BACb,iBAAiB,cAAc,EAAE;wBACnC;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,6BAA6B;wBAC3C,IAAI,WAAW;4BACb,iBAAiB,EAAE;wBACrB;oBACF;gBACF;;YAEA;YAEA;2CAAO;oBACL,YAAY;gBACd;;QACF;kCAAG;QAAC;KAAmB;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA0E;;;;;;kCAGzF,6LAAC;wBAAI,WAAU;kCAAwC;;;;;;;;;;;;0BAMzD,6LAAC;gBAAI,WAAU;0BACZ,WAAW,cAAc,MAAM,KAAK,kBACnC,6LAAC,4JAAA,CAAA,4BAAyB;oBAAC,OAAO;oBAAG,SAAQ;;;;;yCAE7C,6LAAC,wKAAA,CAAA,UAAM;oBACL,OAAO,UAAU,OAAO,UAAU,GAAG,MAAM,UAAU;oBACrD,UAAU;oBACV,cAAc;oBACd,aAAa;8BAEZ;2BAAI;2BAAkB;qBAAc,CAAC,GAAG,CAAC,CAAC,WAAW,sBACpD,6LAAC,wKAAA,CAAA,UAAM,CAAC,KAAK;sCACX,cAAA,6LAAC;gCACC,SAAS;gCACT,UAAU,QAAQ;;;;;;2BAHH;;;;;;;;;;;;;;;0BAW3B,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAK;0BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBAAC,WAAU;;wBAAiH;sCACvH,6LAAC,qNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAK1C;GAzEM;;QAIoC,2IAAA,CAAA,gBAAa;;;MAJjD;uCA2ES"}}, {"offset": {"line": 2448, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2454, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/StickyFindCounselor.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport Link from 'next/link';\r\n\r\nconst StickyFindCounselor = () => {\r\n  const [isVisible, setIsVisible] = useState(false);\r\n  const [lastScrollY, setLastScrollY] = useState(0);\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      const currentScrollY = window.scrollY;\r\n      \r\n      // Show when scrolling down and past 200px\r\n      if (currentScrollY > 200 && currentScrollY > lastScrollY) {\r\n        setIsVisible(true);\r\n      }\r\n      // Hide when scrolling up\r\n      else if (currentScrollY < lastScrollY) {\r\n        setIsVisible(false);\r\n      }\r\n      \r\n      setLastScrollY(currentScrollY);\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll, { passive: true });\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, [lastScrollY]);\r\n\r\n  if (!isVisible) return null;\r\n\r\n  return (\r\n    <div className=\"fixed bottom-6 right-6 z-50\">\r\n      <Link \r\n        href=\"/explore\"\r\n        className=\"\r\n          flex items-center gap-2\r\n          px-4 py-2\r\n          bg-[#800000]\r\n          hover:bg-[#600000]\r\n          text-white\r\n          rounded-lg\r\n          shadow-md\r\n          font-medium\r\n          text-sm\r\n        \"\r\n      >\r\n        <svg \r\n          xmlns=\"http://www.w3.org/2000/svg\" \r\n          fill=\"none\" \r\n          viewBox=\"0 0 24 24\" \r\n          strokeWidth={2} \r\n          stroke=\"currentColor\" \r\n          className=\"w-4 h-4\"\r\n        >\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z\" />\r\n        </svg>\r\n        Find Counselor\r\n      </Link>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StickyFindCounselor;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,sBAAsB;;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM;8DAAe;oBACnB,MAAM,iBAAiB,OAAO,OAAO;oBAErC,0CAA0C;oBAC1C,IAAI,iBAAiB,OAAO,iBAAiB,aAAa;wBACxD,aAAa;oBACf,OAEK,IAAI,iBAAiB,aAAa;wBACrC,aAAa;oBACf;oBAEA,eAAe;gBACjB;;YAEA,OAAO,gBAAgB,CAAC,UAAU,cAAc;gBAAE,SAAS;YAAK;YAChE;iDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;wCAAG;QAAC;KAAY;IAEhB,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;YACH,MAAK;YACL,WAAU;;8BAYV,6LAAC;oBACC,OAAM;oBACN,MAAK;oBACL,SAAQ;oBACR,aAAa;oBACb,QAAO;oBACP,WAAU;8BAEV,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,GAAE;;;;;;;;;;;gBACjD;;;;;;;;;;;;AAKd;GAxDM;KAAA;uCA0DS"}}, {"offset": {"line": 2542, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2548, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/landing/FeaturedCategories.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Card } from \"./TopCounselors\";\r\nimport { cn } from \"@/lib/utils\";\r\nimport {\r\n  CounselorCategory,\r\n  useFeaturedCounselors,\r\n} from \"@/app/hooks/public/useFeaturedCounselors\";\r\n\r\nconst categories = [\r\n  {\r\n    id: \"top-ivy-league\" as Counselor<PERSON>ate<PERSON><PERSON>,\r\n    name: \"Top Ivy League\",\r\n    description: \"Expert counselors from Ivy League institutions\",\r\n  },\r\n  {\r\n    id: \"liberal-arts\" as CounselorCategory,\r\n    name: \"Liberal Arts\",\r\n    description: \"Specialists in Liberal Arts college admissions\",\r\n  },\r\n  {\r\n    id: \"top-ucs\" as CounselorCategor<PERSON>,\r\n    name: \"Top UCs\",\r\n    description: \"University of California system experts\",\r\n  },\r\n  {\r\n    id: \"russel-group\" as CounselorCategory,\r\n    name: \"Top UK\",\r\n    description: \"Leading counselors from UK's prestigious Russell Group\",\r\n  },\r\n];\r\n\r\nconst FeaturedCategories = () => {\r\n  const { counselors, isLoading, error, category, changeCategory } =\r\n    useFeaturedCounselors(\"top-ivy-league\");\r\n\r\n  return (\r\n    <div className=\"container max-w-8xl mx-auto flex flex-col justify-center items-center my-10 md:my-20 px-4\">\r\n      <div className=\"w-full bg-gradient-to-br from-blue-950 to-black/90 rounded-2xl py-12 px-2 md:px-8 lg:px-16 xl:px-10 shadow-[0_4px_24px_-8px_rgba(0,0,0,0.1)]\">\r\n        <div className=\"w-full flex flex-col items-center justify-center gap-y-2.5 md:gap-y-4 text-center mb-8\">\r\n          <h2 className=\"text-2xl sm:text-4xl lg:text-5xl font-medium text-white\">\r\n            Featured Counselors\r\n          </h2>\r\n          <p className=\"text-lg md:text-xl text-blue-100 px-2\">\r\n            Find specialized counselors from top institutions worldwide\r\n          </p>\r\n        </div>\r\n\r\n        {/* Category Tabs */}\r\n        <div className=\"w-full flex justify-center  mb-6\">\r\n          <div className=\"flex flex-wrap justify-center items-center gap-2 md:gap-4 max-w-4xl\">\r\n            {categories.map((cat) => (\r\n              <button\r\n                key={cat.id}\r\n                onClick={() => changeCategory(cat.id)}\r\n                className={cn(\r\n                  \"px-4 py-2 rounded-full text-sm md:text-base font-medium transition-all duration-200\",\r\n                  category === cat.id\r\n                    ? \"bg-white text-blue-950\"\r\n                    : \"bg-white/10 text-white hover:bg-white/20\"\r\n                )}\r\n              >\r\n                {cat.name}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Category Description */}\r\n        <div className=\"w-full flex justify-center px-4\">\r\n          <p className=\"text-blue-100 text-center max-w-2xl mb-8\">\r\n            {categories.find((c) => c.id === category)?.description}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Loading State */}\r\n        {isLoading && (\r\n          <div className=\"w-full flex justify-center py-12\">\r\n            <div className=\"animate-pulse text-blue-100\">\r\n              Loading counselors...\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Error State */}\r\n        {error && (\r\n          <div className=\"w-full flex justify-center py-8\">\r\n            <div className=\"text-red-400 text-center\">{error}</div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Counselors Grid */}\r\n        {!isLoading && !error && (\r\n          <div className=\"flex flex-wrap justify-center items-center gap-6 w-full\">\r\n            {counselors.length > 0 ? (\r\n              counselors.map((counselor) => (\r\n                <Card key={counselor.user_id} profile={counselor} />\r\n              ))\r\n            ) : (\r\n              <div className=\"w-full text-center text-blue-100 py-8\">\r\n                No counselors found in this category.\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FeaturedCategories;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AASA,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;IACf;CACD;AAED,MAAM,qBAAqB;;IACzB,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,GAC9D,CAAA,GAAA,mJAAA,CAAA,wBAAqB,AAAD,EAAE;IAExB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA0D;;;;;;sCAGxE,6LAAC;4BAAE,WAAU;sCAAwC;;;;;;;;;;;;8BAMvD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,oBACf,6LAAC;gCAEC,SAAS,IAAM,eAAe,IAAI,EAAE;gCACpC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uFACA,aAAa,IAAI,EAAE,GACf,2BACA;0CAGL,IAAI,IAAI;+BATJ,IAAI,EAAE;;;;;;;;;;;;;;;8BAgBnB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;kCACV,WAAW,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,WAAW;;;;;;;;;;;gBAK/C,2BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCAA8B;;;;;;;;;;;gBAOhD,uBACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCAA4B;;;;;;;;;;;gBAK9C,CAAC,aAAa,CAAC,uBACd,6LAAC;oBAAI,WAAU;8BACZ,WAAW,MAAM,GAAG,IACnB,WAAW,GAAG,CAAC,CAAC,0BACd,6LAAC,2JAAA,CAAA,OAAI;4BAAyB,SAAS;2BAA5B,UAAU,OAAO;;;;kDAG9B,6LAAC;wBAAI,WAAU;kCAAwC;;;;;;;;;;;;;;;;;;;;;;AASrE;GA5EM;;QAEF,mJAAA,CAAA,wBAAqB;;;KAFnB;uCA8ES"}}, {"offset": {"line": 2729, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}