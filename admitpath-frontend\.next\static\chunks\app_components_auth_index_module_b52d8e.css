/* [project]/app/components/auth/index.module.css [app-client] (css) */
.index-module__2JOImG__layout {
  display: flex;
  height: 100vh;
  flex-direction: column;
  overflow: hidden;
}

@media (width >= 1024px) {
  .index-module__2JOImG__layout {
    flex-direction: row;
  }
}

.index-module__2JOImG__formSection {
  display: flex;
  width: 100%;
  flex-direction: column;
  overflow-y: auto;
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 244 / var(--tw-bg-opacity, 1));
}

@media (width >= 1024px) {
  .index-module__2JOImG__formSection {
    width: 50%;
  }
}

.index-module__2JOImG__formWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (width >= 768px) {
  .index-module__2JOImG__formWrapper {
    margin-top: auto;
    margin-bottom: auto;
    margin-left: auto;
    margin-right: auto;
  }
}

.index-module__2JOImG__mainContent {
  margin-left: .75rem;
  margin-right: .75rem;
  width: max-content;
  border-radius: .75rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
  padding: 1.5rem;
}

@media (width >= 640px) {
  .index-module__2JOImG__mainContent {
    padding: 2rem;
  }
}

.index-module__2JOImG__header {
  padding: 1rem;
}

@media (width >= 640px) {
  .index-module__2JOImG__header {
    padding: 1.5rem;
  }
}

.index-module__2JOImG__logo {
  width: 180px;
}

.index-module__2JOImG__title {
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 500;
}

@media (width >= 640px) {
  .index-module__2JOImG__title {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

.index-module__2JOImG__highlight {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.index-module__2JOImG__terms {
  margin-bottom: 1.5rem;
  font-size: .875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

@media (width >= 640px) {
  .index-module__2JOImG__terms {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.index-module__2JOImG__link {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.index-module__2JOImG__link:hover {
  text-decoration-line: underline;
}

.index-module__2JOImG__divider {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.index-module__2JOImG__divider hr {
  flex: 1;
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}

.index-module__2JOImG__divider span {
  white-space: nowrap;
  font-size: .75rem;
  line-height: 1rem;
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}

@media (width >= 640px) {
  .index-module__2JOImG__divider span {
    font-size: .875rem;
    line-height: 1.25rem;
  }
}

.index-module__2JOImG__socialButtons {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: .75rem;
}

@media (width >= 640px) {
  .index-module__2JOImG__socialButtons {
    flex-direction: row;
  }
}

.index-module__2JOImG__form {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 1rem;
}

@media (width >= 640px) {
  .index-module__2JOImG__form {
    gap: 1.5rem;
  }
}

.index-module__2JOImG__nameFields {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.index-module__2JOImG__emailField {
  width: 100%;
}

.index-module__2JOImG__formFooter {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

@media (width >= 640px) {
  .index-module__2JOImG__formFooter {
    flex-direction: row;
  }
}

.index-module__2JOImG__loginLink {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: .5rem;
  font-size: .75rem;
  line-height: 1rem;
}

@media (width >= 640px) {
  .index-module__2JOImG__loginLink {
    font-size: .875rem;
    line-height: 1.25rem;
  }
}

.index-module__2JOImG__loginLink span {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.index-module__2JOImG__loginLink a {
  white-space: nowrap;
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.index-module__2JOImG__loginLink a:hover {
  text-decoration-line: underline;
}

.index-module__2JOImG__imageSection {
  display: none;
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}

@media (width >= 1024px) {
  .index-module__2JOImG__imageSection {
    display: block;
    width: 50%;
  }
}

.index-module__2JOImG__heroImage {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

/*# sourceMappingURL=app_components_auth_index_module_b52d8e.css.map*/