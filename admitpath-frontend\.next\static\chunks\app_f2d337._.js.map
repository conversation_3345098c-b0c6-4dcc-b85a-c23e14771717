{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/public/useCounselors.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport {\r\n  CounselorPublicProfile,\r\n  CounselorListResponse,\r\n} from \"@/app/types/counselor\";\r\nimport { format } from \"date-fns\";\r\n\r\ninterface Filters {\r\n  service_type?: string[];\r\n  country?: string[];\r\n  university?: string[];\r\n  name?: string;\r\n}\r\n\r\ninterface ApiParams extends Filters {\r\n  page: number;\r\n  limit: number;\r\n  service_types?: string;\r\n  countries?: string;\r\n  universities?: string;\r\n  name?: string;\r\n}\r\n\r\ninterface CounselorsState {\r\n  loading: boolean;\r\n  profileLoading: boolean;\r\n  availabilityLoading: boolean;\r\n  datesLoading: boolean;\r\n  error: string | null;\r\n  counselors: CounselorPublicProfile[] | null;\r\n  counselorIds: number[];\r\n  filters: Filters;\r\n  currentPage: number;\r\n  totalPages: number;\r\n  hasMore: boolean;\r\n  availableDates: string[];\r\n  timeSlots: string[];\r\n\r\n  fetchAllCounselors: () => Promise<void>;\r\n  fetchCounselorProfile: (userId: number) => Promise<CounselorPublicProfile>;\r\n  setFilters: (filters: Filters) => void;\r\n  fetchTopCounselors: () => Promise<CounselorPublicProfile[]>;\r\n  fetchTimeSlots: (\r\n    counselorUserId: number,\r\n    targetDate: Date,\r\n    timezone: string\r\n  ) => Promise<string[]>;\r\n  fetchAvailableDates: (\r\n    counselorUserId: number,\r\n    startDate: Date,\r\n    endDate: Date,\r\n    timezone: string\r\n  ) => Promise<string[]>;\r\n  loadMore: () => Promise<void>;\r\n  setLoading: (loading: boolean) => void;\r\nclearFilters: () => void;\r\n}\r\n\r\nexport const useCounselors = create<CounselorsState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      loading: false,\r\n      profileLoading: false,\r\n      availabilityLoading: false,\r\n      datesLoading: false,\r\n      error: null,\r\n      counselors: null,\r\n      counselorIds: [],\r\n      filters: {},\r\n      currentPage: 1,\r\n      totalPages: 1,\r\n      hasMore: false,\r\n      availableDates: [],\r\n      timeSlots: [],\r\n\r\n      setLoading: (loading) => {\r\n        set({ loading });\r\n      },\r\n      fetchAllCounselors: async () => {\r\n        try {\r\n          const { filters, currentPage } = get();\r\n\r\n          // Only set loading true if this is the first page\r\n          if (currentPage === 1) {\r\n            set({ loading: true, error: null });\r\n          }\r\n\r\n          // Format the parameters for the API call\r\n          const params: ApiParams = {\r\n            page: currentPage,\r\n            limit: 16,\r\n          };\r\n\r\n          // Only add filters that have a value\r\n          if (filters.service_type && filters.service_type.length > 0) {\r\n            params.service_types = filters.service_type.join(\",\");\r\n          }\r\n\r\n          if (filters.country && filters.country.length > 0) {\r\n            params.countries = filters.country.join(\",\");\r\n          }\r\n\r\n          if (filters.university && filters.university.length > 0) {\r\n            params.universities = filters.university.join(\",\");\r\n          }\r\n\r\n          // Only add name if it exists and is not empty\r\n          if (filters.name && filters.name.trim() !== \"\") {\r\n            params.name = filters.name.trim();\r\n          }\r\n\r\n          const { data } = await apiClient.get<CounselorListResponse>(\r\n            \"/counselor/profiles/public-profile/list\",\r\n            { params }\r\n          );\r\n\r\n          if (data.items && data.items.length > 0) {\r\n            set((state) => {\r\n              // For first page or filter changes, replace the list\r\n              const isFirstPage = currentPage === 1;\r\n\r\n              return {\r\n                counselors: isFirstPage\r\n                  ? data.items\r\n                  : [...(state.counselors || []), ...data.items],\r\n                counselorIds: isFirstPage\r\n                  ? data.items.map((c) => c.user_id)\r\n                  : [\r\n                      ...new Set([\r\n                        ...state.counselorIds,\r\n                        ...data.items.map((c) => c.user_id),\r\n                      ]),\r\n                    ],\r\n                totalPages: data.pages,\r\n                hasMore: data.has_next,\r\n                loading: false,\r\n                error: null,\r\n              };\r\n            });\r\n          } else {\r\n            set((state) => ({\r\n              ...state,\r\n              counselors: currentPage === 1 ? [] : state.counselors,\r\n              counselorIds: currentPage === 1 ? [] : state.counselorIds,\r\n              totalPages: currentPage === 1 ? 0 : state.totalPages,\r\n              hasMore: false,\r\n              loading: false,\r\n              error: currentPage === 1 ? \"No counselors found\" : null,\r\n            }));\r\n          }\r\n        } catch (error) {\r\n          set({\r\n            error: \"Failed to fetch counselors\",\r\n            loading: false,\r\n          });\r\n          console.error(\"Error fetching counselors:\", error);\r\n        }\r\n      },\r\n\r\n      fetchCounselorProfile: async (userId: number) => {\r\n        try {\r\n          set({ profileLoading: true, error: null });\r\n          const { data } = await apiClient.get<CounselorPublicProfile>(\r\n            `/counselor/profile/public-profile/user/${userId}`\r\n          );\r\n\r\n          // Update counselors state with the fetched profile\r\n          set((state) => {\r\n            // Create a new array with existing counselors\r\n            const updatedCounselors = [...(state.counselors || [])];\r\n\r\n            // Find if this counselor already exists in our array\r\n            const existingIndex = updatedCounselors.findIndex(\r\n              (c) => c.user_id === userId\r\n            );\r\n\r\n            // If counselor exists, update it, otherwise add it\r\n            if (existingIndex >= 0) {\r\n              updatedCounselors[existingIndex] = data;\r\n            } else {\r\n              updatedCounselors.push(data);\r\n            }\r\n\r\n            // Update counselorIds if needed\r\n            let updatedIds = [...state.counselorIds];\r\n            if (!updatedIds.includes(userId)) {\r\n              updatedIds.push(userId);\r\n            }\r\n\r\n            return {\r\n              counselors: updatedCounselors,\r\n              counselorIds: updatedIds,\r\n              profileLoading: false,\r\n            };\r\n          });\r\n\r\n          return data;\r\n        } catch (error) {\r\n          console.error(\"Error fetching counselor profile:\", error);\r\n          set({\r\n            error: \"Failed to fetch counselor profile\",\r\n            profileLoading: false,\r\n          });\r\n          throw error;\r\n        }\r\n      },\r\n\r\n      fetchTopCounselors: async () => {\r\n        try {\r\n          set({ loading: true, error: null });\r\n          const { data } = await apiClient.get<CounselorListResponse>(\r\n            \"/counselor/profiles/public-profile/list\",\r\n            { params: { page: 1, limit: 8 } }\r\n          );\r\n\r\n          // Check if we have any items before updating state\r\n          if (data.items && data.items.length > 0) {\r\n            set({\r\n              counselors: data.items,\r\n              counselorIds: data.items.map((c) => c.user_id),\r\n              loading: false,\r\n            });\r\n            return data.items;\r\n          } else {\r\n            set({\r\n              counselors: [],\r\n              counselorIds: [],\r\n              loading: false,\r\n              error: \"No counselors found\",\r\n            });\r\n            return [];\r\n          }\r\n        } catch (error) {\r\n          set({\r\n            error: \"Failed to fetch top counselors\",\r\n            loading: false,\r\n            counselors: [],\r\n            counselorIds: [],\r\n          });\r\n          console.error(\"Error fetching top counselors:\", error);\r\n          return [];\r\n        }\r\n      },\r\n\r\n      fetchAvailableDates: async (\r\n        counselorUserId,\r\n        startDate,\r\n        endDate,\r\n        timezone\r\n      ) => {\r\n        try {\r\n          set({ datesLoading: true });\r\n          const response = await apiClient.get<string[]>(\r\n            `/counselor/availability/public/${counselorUserId}/dates`,\r\n            {\r\n              params: {\r\n                start_date: format(startDate, \"yyyy-MM-dd\"),\r\n                end_date: format(endDate, \"yyyy-MM-dd\"),\r\n                timezone,\r\n              },\r\n            }\r\n          );\r\n          set({ availableDates: response.data, datesLoading: false });\r\n          return response.data;\r\n        } catch (error) {\r\n          set({\r\n            error: \"Failed to fetch dates\",\r\n            datesLoading: false,\r\n          });\r\n          throw error;\r\n        }\r\n      },\r\n\r\n      fetchTimeSlots: async (counselorUserId, targetDate, timezone) => {\r\n        try {\r\n          set({ availabilityLoading: true });\r\n          const response = await apiClient.get<string[]>(\r\n            `/counselor/availability/public/${counselorUserId}/slots`,\r\n            {\r\n              params: {\r\n                target_date: format(targetDate, \"yyyy-MM-dd\"),\r\n                timezone,\r\n              },\r\n            }\r\n          );\r\n          set({\r\n            timeSlots: response.data,\r\n            availabilityLoading: false,\r\n          });\r\n          return response.data;\r\n        } catch (error) {\r\n          set({\r\n            error: \"Failed to fetch time slots\",\r\n            availabilityLoading: false,\r\n          });\r\n          throw error;\r\n        }\r\n      },\r\n\r\n      setFilters: (filters: Filters) => {\r\n        set({\r\n          filters,\r\n          currentPage: 1,\r\n        });\r\n      },\r\nclearFilters: () => {\r\n        set({\r\n          filters: {},\r\n          counselors: [],\r\n          currentPage: 1,\r\n          hasMore: false,\r\n        });\r\n      },\r\n\r\n      loadMore: async () => {\r\n        const { currentPage, hasMore } = get();\r\n        if (!hasMore) return;\r\n        set({ currentPage: currentPage + 1 });\r\n        await get().fetchAllCounselors();\r\n      },\r\n    }),\r\n    {\r\n      name: \"counselors-storage\",\r\n      partialize: (state) =>\r\n        Object.fromEntries(\r\n          Object.entries(state).filter(([key]) => [\"filters\"].includes(key))\r\n        ),\r\n    }\r\n  )\r\n);\r\n"], "names": [], "mappings": ";;;AAIA;AAFA;AACA;AAMA;AATA;;;;;AA8DO,MAAM,gBAAgB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAChC,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,SAAS;QACT,gBAAgB;QAChB,qBAAqB;QACrB,cAAc;QACd,OAAO;QACP,YAAY;QACZ,cAAc,EAAE;QAChB,SAAS,CAAC;QACV,aAAa;QACb,YAAY;QACZ,SAAS;QACT,gBAAgB,EAAE;QAClB,WAAW,EAAE;QAEb,YAAY,CAAC;YACX,IAAI;gBAAE;YAAQ;QAChB;QACA,oBAAoB;YAClB,IAAI;gBACF,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG;gBAEjC,kDAAkD;gBAClD,IAAI,gBAAgB,GAAG;oBACrB,IAAI;wBAAE,SAAS;wBAAM,OAAO;oBAAK;gBACnC;gBAEA,yCAAyC;gBACzC,MAAM,SAAoB;oBACxB,MAAM;oBACN,OAAO;gBACT;gBAEA,qCAAqC;gBACrC,IAAI,QAAQ,YAAY,IAAI,QAAQ,YAAY,CAAC,MAAM,GAAG,GAAG;oBAC3D,OAAO,aAAa,GAAG,QAAQ,YAAY,CAAC,IAAI,CAAC;gBACnD;gBAEA,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO,CAAC,MAAM,GAAG,GAAG;oBACjD,OAAO,SAAS,GAAG,QAAQ,OAAO,CAAC,IAAI,CAAC;gBAC1C;gBAEA,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,GAAG;oBACvD,OAAO,YAAY,GAAG,QAAQ,UAAU,CAAC,IAAI,CAAC;gBAChD;gBAEA,8CAA8C;gBAC9C,IAAI,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,IAAI,OAAO,IAAI;oBAC9C,OAAO,IAAI,GAAG,QAAQ,IAAI,CAAC,IAAI;gBACjC;gBAEA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,2CACA;oBAAE;gBAAO;gBAGX,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;oBACvC,IAAI,CAAC;wBACH,qDAAqD;wBACrD,MAAM,cAAc,gBAAgB;wBAEpC,OAAO;4BACL,YAAY,cACR,KAAK,KAAK,GACV;mCAAK,MAAM,UAAU,IAAI,EAAE;mCAAM,KAAK,KAAK;6BAAC;4BAChD,cAAc,cACV,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,IAAM,EAAE,OAAO,IAC/B;mCACK,IAAI,IAAI;uCACN,MAAM,YAAY;uCAClB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,IAAM,EAAE,OAAO;iCACnC;6BACF;4BACL,YAAY,KAAK,KAAK;4BACtB,SAAS,KAAK,QAAQ;4BACtB,SAAS;4BACT,OAAO;wBACT;oBACF;gBACF,OAAO;oBACL,IAAI,CAAC,QAAU,CAAC;4BACd,GAAG,KAAK;4BACR,YAAY,gBAAgB,IAAI,EAAE,GAAG,MAAM,UAAU;4BACrD,cAAc,gBAAgB,IAAI,EAAE,GAAG,MAAM,YAAY;4BACzD,YAAY,gBAAgB,IAAI,IAAI,MAAM,UAAU;4BACpD,SAAS;4BACT,SAAS;4BACT,OAAO,gBAAgB,IAAI,wBAAwB;wBACrD,CAAC;gBACH;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO;oBACP,SAAS;gBACX;gBACA,QAAQ,KAAK,CAAC,8BAA8B;YAC9C;QACF;QAEA,uBAAuB,OAAO;YAC5B,IAAI;gBACF,IAAI;oBAAE,gBAAgB;oBAAM,OAAO;gBAAK;gBACxC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,uCAAuC,EAAE,QAAQ;gBAGpD,mDAAmD;gBACnD,IAAI,CAAC;oBACH,8CAA8C;oBAC9C,MAAM,oBAAoB;2BAAK,MAAM,UAAU,IAAI,EAAE;qBAAE;oBAEvD,qDAAqD;oBACrD,MAAM,gBAAgB,kBAAkB,SAAS,CAC/C,CAAC,IAAM,EAAE,OAAO,KAAK;oBAGvB,mDAAmD;oBACnD,IAAI,iBAAiB,GAAG;wBACtB,iBAAiB,CAAC,cAAc,GAAG;oBACrC,OAAO;wBACL,kBAAkB,IAAI,CAAC;oBACzB;oBAEA,gCAAgC;oBAChC,IAAI,aAAa;2BAAI,MAAM,YAAY;qBAAC;oBACxC,IAAI,CAAC,WAAW,QAAQ,CAAC,SAAS;wBAChC,WAAW,IAAI,CAAC;oBAClB;oBAEA,OAAO;wBACL,YAAY;wBACZ,cAAc;wBACd,gBAAgB;oBAClB;gBACF;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;gBACnD,IAAI;oBACF,OAAO;oBACP,gBAAgB;gBAClB;gBACA,MAAM;YACR;QACF;QAEA,oBAAoB;YAClB,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,2CACA;oBAAE,QAAQ;wBAAE,MAAM;wBAAG,OAAO;oBAAE;gBAAE;gBAGlC,mDAAmD;gBACnD,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;oBACvC,IAAI;wBACF,YAAY,KAAK,KAAK;wBACtB,cAAc,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,IAAM,EAAE,OAAO;wBAC7C,SAAS;oBACX;oBACA,OAAO,KAAK,KAAK;gBACnB,OAAO;oBACL,IAAI;wBACF,YAAY,EAAE;wBACd,cAAc,EAAE;wBAChB,SAAS;wBACT,OAAO;oBACT;oBACA,OAAO,EAAE;gBACX;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO;oBACP,SAAS;oBACT,YAAY,EAAE;oBACd,cAAc,EAAE;gBAClB;gBACA,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,OAAO,EAAE;YACX;QACF;QAEA,qBAAqB,OACnB,iBACA,WACA,SACA;YAEA,IAAI;gBACF,IAAI;oBAAE,cAAc;gBAAK;gBACzB,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,+BAA+B,EAAE,gBAAgB,MAAM,CAAC,EACzD;oBACE,QAAQ;wBACN,YAAY,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,WAAW;wBAC9B,UAAU,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,SAAS;wBAC1B;oBACF;gBACF;gBAEF,IAAI;oBAAE,gBAAgB,SAAS,IAAI;oBAAE,cAAc;gBAAM;gBACzD,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO;oBACP,cAAc;gBAChB;gBACA,MAAM;YACR;QACF;QAEA,gBAAgB,OAAO,iBAAiB,YAAY;YAClD,IAAI;gBACF,IAAI;oBAAE,qBAAqB;gBAAK;gBAChC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,+BAA+B,EAAE,gBAAgB,MAAM,CAAC,EACzD;oBACE,QAAQ;wBACN,aAAa,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,YAAY;wBAChC;oBACF;gBACF;gBAEF,IAAI;oBACF,WAAW,SAAS,IAAI;oBACxB,qBAAqB;gBACvB;gBACA,OAAO,SAAS,IAAI;YACtB,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO;oBACP,qBAAqB;gBACvB;gBACA,MAAM;YACR;QACF;QAEA,YAAY,CAAC;YACX,IAAI;gBACF;gBACA,aAAa;YACf;QACF;QACN,cAAc;YACN,IAAI;gBACF,SAAS,CAAC;gBACV,YAAY,EAAE;gBACd,aAAa;gBACb,SAAS;YACX;QACF;QAEA,UAAU;YACR,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG;YACjC,IAAI,CAAC,SAAS;YACd,IAAI;gBAAE,aAAa,cAAc;YAAE;YACnC,MAAM,MAAM,kBAAkB;QAChC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QACX,OAAO,WAAW,CAChB,OAAO,OAAO,CAAC,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,GAAK;gBAAC;aAAU,CAAC,QAAQ,CAAC;AAEnE"}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\r\nimport { X } from \"lucide-react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Dialog = DialogPrimitive.Root;\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger;\r\n\r\nconst DialogPortal = DialogPrimitive.Portal;\r\n\r\nconst DialogClose = DialogPrimitive.Close;\r\n\r\nconst DialogOverlay = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\r\n\r\nconst DialogContent = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n));\r\nDialogContent.displayName = DialogPrimitive.Content.displayName;\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogHeader.displayName = \"DialogHeader\";\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) => (\r\n  <div\r\n    className={cn(\r\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n);\r\nDialogFooter.displayName = \"DialogFooter\";\r\n\r\nconst DialogTitle = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Title>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-lg font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\r\n\r\nconst DialogDescription = React.forwardRef<\r\n  React.ElementRef<typeof DialogPrimitive.Description>,\r\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\r\n>(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAJA;;;;;;AAQA,MAAM,SAAS,sKAAgB,IAAI;AAEnC,MAAM,gBAAgB,sKAAgB,OAAO;AAE7C,MAAM,eAAe,sKAAgB,MAAM;AAE3C,MAAM,cAAc,sKAAgB,KAAK;AAEzC,MAAM,8BAAgB,8JAAM,UAAU,CAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,8JAAM,UAAU,OAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,sKAAgB,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,sKAAgB,KAAK;wBAAC,WAAU;;0CAC/B,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,sKAAgB,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;MATP;AAYN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,8JAAM,UAAU,OAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,sKAAgB,WAAW,CAAC,WAAW"}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<\"textarea\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  );\r\n});\r\nTextarea.displayName = \"Textarea\";\r\n\r\nexport { Textarea };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,8JAAM,UAAU,MAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AACA,SAAS,WAAW,GAAG"}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Avatar = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatar.displayName = AvatarPrimitive.Root.displayName;\r\n\r\nconst AvatarImage = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Image>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Image\r\n    ref={ref}\r\n    className={cn(\"aspect-square h-full w-full\", className)}\r\n    {...props}\r\n  />\r\n));\r\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\r\n\r\nconst AvatarFallback = React.forwardRef<\r\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\r\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\r\n>(({ className, ...props }, ref) => (\r\n  <AvatarPrimitive.Fallback\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\r\n\r\nexport { Avatar, AvatarImage, AvatarFallback };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,uBAAS,8JAAM,UAAU,MAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG,sKAAgB,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,+BAAiB,8JAAM,UAAU,OAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,QAAQ;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAgB,QAAQ,CAAC,WAAW"}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 530, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/utils/time-helpers.ts"], "sourcesContent": ["// Convert date to ISO format\r\nexport const formatDateISO = (date: string) => {\r\n  // Create a date object from the date string\r\n  const dateObj = new Date(date);\r\n\r\n  // Set the time to midnight (00:00:00) to get just the date\r\n  dateObj.setHours(0, 0, 0, 0);\r\n\r\n  // Return the ISO string\r\n  return dateObj.toISOString();\r\n};\r\n\r\n// Convert to long date string\r\nexport function formatToLongDateString(dateString: string) {\r\n  const date = new Date(dateString);\r\n\r\n  return date.toLocaleDateString(\"en-US\", {\r\n    year: \"numeric\",\r\n    month: \"long\",\r\n    day: \"numeric\",\r\n  });\r\n}\r\n\r\n// Convert date to 'yyyy-mm-dd' format in LOCAL TIMEZONE\r\nexport const formatNormalDate = (date: string) => {\r\n  const d = new Date(date);\r\n  const year = d.getFullYear();\r\n  const month = String(d.getMonth() + 1).padStart(2, \"0\"); // Months are 0-based\r\n  const day = String(d.getDate()).padStart(2, \"0\");\r\n  return `${year}-${month}-${day}`;\r\n};\r\n\r\n// Convert ISO time to \"HH:mm\" in LOCAL TIMEZONE\r\nexport function formatTimeForInput(isoTime: string) {\r\n  const date = new Date(isoTime);\r\n  const hours = String(date.getHours()).padStart(2, \"0\"); // Local hours\r\n  const minutes = String(date.getMinutes()).padStart(2, \"0\"); // Local minutes\r\n  return `${hours}:${minutes}`;\r\n}\r\n\r\n// Convert \"HH:mm\" from time input back to ISO format\r\nexport function formatTimeToISO(date: string, time: string) {\r\n  // Create a date object from the date and time strings\r\n  const [hours, minutes] = time.split(':').map(Number);\r\n  const dateObj = new Date(date);\r\n\r\n  // Set the hours and minutes\r\n  dateObj.setHours(hours, minutes, 0, 0);\r\n\r\n  // Return the ISO string\r\n  return dateObj.toISOString();\r\n}\r\n\r\nexport function dateNowWithTZOffset() {\r\n  const now = new Date();\r\n\r\n  const year = now.getFullYear();\r\n  const month = String(now.getMonth() + 1).padStart(2, \"0\");\r\n  const day = String(now.getDate()).padStart(2, \"0\");\r\n  const hours = String(now.getHours()).padStart(2, \"0\");\r\n  const minutes = String(now.getMinutes()).padStart(2, \"0\");\r\n  const seconds = String(now.getSeconds()).padStart(2, \"0\");\r\n\r\n  const microseconds = String(Math.floor(Math.random() * 1000000)).padStart(\r\n    6,\r\n    \"0\"\r\n  );\r\n\r\n  const tzOffset = now.getTimezoneOffset();\r\n  const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60));\r\n  const tzOffsetMinutes = Math.abs(tzOffset % 60);\r\n  const tzOffsetSign = tzOffset <= 0 ? \"+\" : \"-\";\r\n\r\n  const tzFormatted = `${tzOffsetSign}${String(tzOffsetHours).padStart(\r\n    2,\r\n    \"0\"\r\n  )}:${String(tzOffsetMinutes).padStart(2, \"0\")}`;\r\n\r\n  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${microseconds}${tzFormatted}`;\r\n}\r\n"], "names": [], "mappings": "AAAA,6BAA6B;;;;;;;;;AACtB,MAAM,gBAAgB,CAAC;IAC5B,4CAA4C;IAC5C,MAAM,UAAU,IAAI,KAAK;IAEzB,2DAA2D;IAC3D,QAAQ,QAAQ,CAAC,GAAG,GAAG,GAAG;IAE1B,wBAAwB;IACxB,OAAO,QAAQ,WAAW;AAC5B;AAGO,SAAS,uBAAuB,UAAkB;IACvD,MAAM,OAAO,IAAI,KAAK;IAEtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,IAAI,IAAI,KAAK;IACnB,MAAM,OAAO,EAAE,WAAW;IAC1B,MAAM,QAAQ,OAAO,EAAE,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG,MAAM,qBAAqB;IAC9E,MAAM,MAAM,OAAO,EAAE,OAAO,IAAI,QAAQ,CAAC,GAAG;IAC5C,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK;AAClC;AAGO,SAAS,mBAAmB,OAAe;IAChD,MAAM,OAAO,IAAI,KAAK;IACtB,MAAM,QAAQ,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,GAAG,MAAM,cAAc;IACtE,MAAM,UAAU,OAAO,KAAK,UAAU,IAAI,QAAQ,CAAC,GAAG,MAAM,gBAAgB;IAC5E,OAAO,GAAG,MAAM,CAAC,EAAE,SAAS;AAC9B;AAGO,SAAS,gBAAgB,IAAY,EAAE,IAAY;IACxD,sDAAsD;IACtD,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;IAC7C,MAAM,UAAU,IAAI,KAAK;IAEzB,4BAA4B;IAC5B,QAAQ,QAAQ,CAAC,OAAO,SAAS,GAAG;IAEpC,wBAAwB;IACxB,OAAO,QAAQ,WAAW;AAC5B;AAEO,SAAS;IACd,MAAM,MAAM,IAAI;IAEhB,MAAM,OAAO,IAAI,WAAW;IAC5B,MAAM,QAAQ,OAAO,IAAI,QAAQ,KAAK,GAAG,QAAQ,CAAC,GAAG;IACrD,MAAM,MAAM,OAAO,IAAI,OAAO,IAAI,QAAQ,CAAC,GAAG;IAC9C,MAAM,QAAQ,OAAO,IAAI,QAAQ,IAAI,QAAQ,CAAC,GAAG;IACjD,MAAM,UAAU,OAAO,IAAI,UAAU,IAAI,QAAQ,CAAC,GAAG;IACrD,MAAM,UAAU,OAAO,IAAI,UAAU,IAAI,QAAQ,CAAC,GAAG;IAErD,MAAM,eAAe,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,UAAU,QAAQ,CACvE,GACA;IAGF,MAAM,WAAW,IAAI,iBAAiB;IACtC,MAAM,gBAAgB,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,WAAW;IACrD,MAAM,kBAAkB,KAAK,GAAG,CAAC,WAAW;IAC5C,MAAM,eAAe,YAAY,IAAI,MAAM;IAE3C,MAAM,cAAc,GAAG,eAAe,OAAO,eAAe,QAAQ,CAClE,GACA,KACA,CAAC,EAAE,OAAO,iBAAiB,QAAQ,CAAC,GAAG,MAAM;IAE/C,OAAO,GAAG,KAAK,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,eAAe,aAAa;AAC/F"}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/useWebSocketChat.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport {\r\n  ChatState,\r\n  Message,\r\n  SendMessagePayload,\r\n  InitialMessagePayload,\r\n  WebSocketMessage,\r\n  WebSocketConnectionStatus,\r\n} from \"@/app/types/chat\";\r\nimport { dateNowWithTZOffset } from \"../utils/time-helpers\";\r\n\r\nconst LOCALSTORAGE_KEY = \"chat_latest_messages\";\r\n\r\ninterface LatestMessagesStorage {\r\n  [channelId: string]: string;\r\n}\r\n\r\nclass WebSocketManager {\r\n  private socket: WebSocket | null = null;\r\n  private channelId: string | null = null;\r\n  private token: string | null = null;\r\n  private reconnectAttempts = 0;\r\n  private maxReconnectAttempts = 5;\r\n  private reconnectInterval = 3000; // 3 seconds\r\n  private reconnectTimeoutId: NodeJS.Timeout | null = null;\r\n  private messageHandler: ((data: any) => void) | null = null;\r\n  private statusChangeHandler: ((status: WebSocketConnectionStatus) => void) | null = null;\r\n  private errorHandler: ((error: any) => void) | null = null;\r\n\r\n  constructor() {\r\n    this.connect = this.connect.bind(this);\r\n    this.disconnect = this.disconnect.bind(this);\r\n    this.sendMessage = this.sendMessage.bind(this);\r\n    this.handleOpen = this.handleOpen.bind(this);\r\n    this.handleMessage = this.handleMessage.bind(this);\r\n    this.handleClose = this.handleClose.bind(this);\r\n    this.handleError = this.handleError.bind(this);\r\n    this.reconnect = this.reconnect.bind(this);\r\n  }\r\n\r\n  public connect(channelId: string, token: string): void {\r\n    this.channelId = channelId;\r\n    this.token = token;\r\n\r\n    if (this.socket) {\r\n      this.socket.close();\r\n    }\r\n\r\n    try {\r\n      const wsUrl = `${this.getWebSocketBaseUrl()}/ws/chat/${channelId}?token=${token}`;\r\n      this.socket = new WebSocket(wsUrl);\r\n\r\n      this.socket.onopen = this.handleOpen;\r\n      this.socket.onmessage = this.handleMessage;\r\n      this.socket.onclose = this.handleClose;\r\n      this.socket.onerror = this.handleError;\r\n\r\n      if (this.statusChangeHandler) {\r\n        this.statusChangeHandler('connecting');\r\n      }\r\n    } catch (error) {\r\n      console.error(\"WebSocket connection error:\", error);\r\n      if (this.errorHandler) {\r\n        this.errorHandler(error);\r\n      }\r\n    }\r\n  }\r\n\r\n  public disconnect(): void {\r\n    if (this.socket) {\r\n      this.socket.close();\r\n      this.socket = null;\r\n    }\r\n\r\n    if (this.reconnectTimeoutId) {\r\n      clearTimeout(this.reconnectTimeoutId);\r\n      this.reconnectTimeoutId = null;\r\n    }\r\n\r\n    this.channelId = null;\r\n    this.token = null;\r\n    this.reconnectAttempts = 0;\r\n\r\n    if (this.statusChangeHandler) {\r\n      this.statusChangeHandler('disconnected');\r\n    }\r\n  }\r\n\r\n  public sendMessage(message: WebSocketMessage): boolean {\r\n    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {\r\n      console.error(\"WebSocket is not connected\");\r\n      return false;\r\n    }\r\n\r\n    try {\r\n      this.socket.send(JSON.stringify(message));\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"Error sending message:\", error);\r\n      if (this.errorHandler) {\r\n        this.errorHandler(error);\r\n      }\r\n      return false;\r\n    }\r\n  }\r\n\r\n  public onMessage(handler: (data: any) => void): void {\r\n    this.messageHandler = handler;\r\n  }\r\n\r\n  public onStatusChange(handler: (status: WebSocketConnectionStatus) => void): void {\r\n    this.statusChangeHandler = handler;\r\n  }\r\n\r\n  public onError(handler: (error: any) => void): void {\r\n    this.errorHandler = handler;\r\n  }\r\n\r\n  public isConnected(): boolean {\r\n    return this.socket !== null && this.socket.readyState === WebSocket.OPEN;\r\n  }\r\n\r\n  private handleOpen(_event: Event): void {\r\n    this.reconnectAttempts = 0;\r\n\r\n    if (this.statusChangeHandler) {\r\n      this.statusChangeHandler('connected');\r\n    }\r\n  }\r\n\r\n  private handleMessage(event: MessageEvent): void {\r\n    try {\r\n      const data = JSON.parse(event.data);\r\n\r\n      const now = new Date();\r\n      data._receivedAt = now.toISOString();\r\n\r\n      if (!this._recentMessages) {\r\n        this._recentMessages = [];\r\n      }\r\n\r\n      const fingerprint = `${data.sender_id || ''}-${data.created_at || ''}-${(data.message || '').substring(0, 20)}`;\r\n\r\n      // Check if we've seen this message recently (within 5 seconds)\r\n      const isDuplicate = this._recentMessages.some(m => {\r\n        // If fingerprints match and received within 5 seconds, it's likely a duplicate\r\n        if (m.fingerprint === fingerprint) {\r\n          const prevTime = new Date(m.receivedAt).getTime();\r\n          const currentTime = now.getTime();\r\n          return (currentTime - prevTime) < 5000; // 5 seconds\r\n        }\r\n        return false;\r\n      });\r\n\r\n      if (isDuplicate) {\r\n        return;\r\n      }\r\n\r\n      this._recentMessages.push({\r\n        fingerprint,\r\n        receivedAt: now.toISOString()\r\n      });\r\n\r\n      if (this._recentMessages.length > 20) {\r\n        this._recentMessages = this._recentMessages.slice(-20);\r\n      }\r\n\r\n      if (this.messageHandler) {\r\n        this.messageHandler(data);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error parsing message:\", error);\r\n    }\r\n  }\r\n\r\n  private _recentMessages: Array<{fingerprint: string, receivedAt: string}> = [];\r\n\r\n  private handleClose(event: CloseEvent): void {\r\n\r\n    if (this.statusChangeHandler) {\r\n      this.statusChangeHandler('disconnected');\r\n    }\r\n\r\n    if (!event.wasClean && this.channelId && this.token) {\r\n      this.reconnect();\r\n    }\r\n  }\r\n\r\n  private handleError(event: Event): void {\r\n    console.error(\"WebSocket error:\", event);\r\n    if (this.errorHandler) {\r\n      this.errorHandler(event);\r\n    }\r\n  }\r\n\r\n  private reconnect(): void {\r\n    if (this.reconnectAttempts >= this.maxReconnectAttempts) {\r\n      return;\r\n    }\r\n\r\n    this.reconnectAttempts++;\r\n\r\n    if (this.statusChangeHandler) {\r\n      this.statusChangeHandler('reconnecting');\r\n    }\r\n\r\n    this.reconnectTimeoutId = setTimeout(() => {\r\n      if (this.channelId && this.token) {\r\n        this.connect(this.channelId, this.token);\r\n      }\r\n    }, this.reconnectInterval);\r\n  }\r\n\r\n  private getWebSocketBaseUrl(): string {\r\n    const isSecure = window.location.protocol === 'https:';\r\n    const protocol = isSecure ? 'wss:' : 'ws:';\r\n\r\n    let apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';\r\n\r\n    apiBaseUrl = apiBaseUrl.replace(/^https?:\\/\\//, '');\r\n\r\n    return `${protocol}//${apiBaseUrl}`;\r\n  }\r\n}\r\n\r\nconst wsManager = new WebSocketManager();\r\n\r\nlet currentUserInfo: { user_id: number | string } | null = null;\r\n\r\nexport const setWebSocketChatUserInfo = (userInfo: { user_id: number | string }) => {\r\n  currentUserInfo = userInfo;\r\n};\r\n\r\nexport const useWebSocketChat = create<ChatState>((set, get) => ({\r\n  channels: null,\r\n  currentChannel: null,\r\n  messages: null,\r\n  messageIds: new Set<number>(),\r\n  pendingMessages: new Set<number>(),\r\n  unseenMessages: {},\r\n  unseenChannelsCount: 0,\r\n  loading: false,\r\n  error: null,\r\n  connectionStatus: 'disconnected',\r\n\r\n  initWebSocket: () => {\r\n    wsManager.onMessage((data) => {\r\n      const message = data as Message;\r\n      const state = get();\r\n\r\n\r\n      if (currentUserInfo?.user_id && message.sender_id === currentUserInfo.user_id) {\r\n        return;\r\n      }\r\n\r\n      if (state.currentChannel && message.channel_id !== state.currentChannel.channel_id) {\r\n        return;\r\n      }\r\n\r\n      if (message.id && state.messageIds.has(message.id)) {\r\n        return;\r\n      }\r\n\r\n      const messageFingerprint = `${message.sender_id}-${message.created_at}-${message.message.substring(0, 20)}`;\r\n\r\n      const isDuplicate = state.messages?.some(m => {\r\n        const existingFingerprint = `${m.sender_id}-${m.created_at}-${m.message.substring(0, 20)}`;\r\n        return existingFingerprint === messageFingerprint;\r\n      });\r\n\r\n      if (isDuplicate) {\r\n        return;\r\n      }\r\n\r\n      get().addMessage(message);\r\n\r\n      if (state.currentChannel) {\r\n        get().updateLatestSeenMessage(\r\n          state.currentChannel.channel_id,\r\n          message.created_at\r\n        );\r\n      }\r\n    });\r\n\r\n    wsManager.onStatusChange((status) => {\r\n      set({ connectionStatus: status });\r\n    });\r\n\r\n    wsManager.onError((_error) => {\r\n      set({ error: \"WebSocket connection error\" });\r\n    });\r\n  },\r\n\r\n  fetchChannels: async () => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\"/messages/channels\");\r\n      set({ channels: response.data.channels, loading: false });\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.detail || \"Failed to fetch channels\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  fetchMessages: async (channelId: string, skip = 0, limit = 15) => {\r\n    try {\r\n      set({ loading: true, error: null });\r\n      const response = await apiClient.get(\r\n        `/messages/channel/${channelId}?skip=${skip}&limit=${limit}`\r\n      );\r\n      const newMessages = response.data.messages;\r\n\r\n      const messageIds = new Set(get().messageIds);\r\n      newMessages.forEach((msg: Message) => messageIds.add(msg.id));\r\n\r\n      set((state) => ({\r\n        messages:\r\n          skip === 0\r\n            ? newMessages\r\n            : [\r\n                ...(state.messages || []),\r\n                ...newMessages.filter(\r\n                  (msg: Message) =>\r\n                    !state.messages?.some(\r\n                      (existingMsg: Message) => existingMsg.id === msg.id\r\n                    )\r\n                ),\r\n              ],\r\n        messageIds,\r\n        loading: false,\r\n      }));\r\n\r\n      if (limit !== 1 && newMessages.length > 0) {\r\n        get().updateLatestSeenMessage(channelId, newMessages[0].created_at);\r\n      }\r\n    } catch (error: any) {\r\n      set({\r\n        error: error.response?.data?.detail || \"Failed to fetch messages\",\r\n        loading: false,\r\n      });\r\n    }\r\n  },\r\n\r\n  sendMessage: async (payload: SendMessagePayload) => {\r\n    const currentChannel = get().currentChannel;\r\n    if (!currentChannel) return;\r\n\r\n    const state = get();\r\n    const messageFingerprint = `${payload.sender_id}-${dateNowWithTZOffset()}-${payload.message.substring(0, 20)}`;\r\n\r\n    const isDuplicate = state.messages?.some(m => {\r\n      if (m.sender_id !== payload.sender_id) return false;\r\n\r\n      if (m.message !== payload.message) return false;\r\n\r\n      const msgTime = new Date(m.created_at).getTime();\r\n      const now = new Date().getTime();\r\n      return (now - msgTime) < 2000; // 2 seconds\r\n    });\r\n\r\n    if (isDuplicate) {\r\n      return;\r\n    }\r\n\r\n    // Create optimistic message\r\n    const tempId = Math.floor(Math.random() * 1000000);\r\n    const optimisticMessage: Message = {\r\n      id: tempId,\r\n      channel_id: currentChannel.channel_id,\r\n      message: payload.message,\r\n      sender_id: payload.sender_id,\r\n      sender_name: payload.sender_name,\r\n      created_at: dateNowWithTZOffset(),\r\n    };\r\n\r\n    try {\r\n      get().addMessage(optimisticMessage);\r\n      set((state) => ({\r\n        pendingMessages: new Set([...state.pendingMessages, tempId]),\r\n      }));\r\n\r\n      let serverMessage: Message | null = null;\r\n\r\n      if (wsManager.isConnected()) {\r\n        const wsMessage: WebSocketMessage = {\r\n          type: \"message\",\r\n          message: payload.message,\r\n          recipient_id: payload.recipient_id,\r\n          created_at: optimisticMessage.created_at,\r\n        };\r\n\r\n        const sentViaWebSocket = wsManager.sendMessage(wsMessage);\r\n\r\n        if (sentViaWebSocket) {\r\n\r\n\r\n          set((state) => ({\r\n            pendingMessages: new Set([...state.pendingMessages].filter((id) => id !== tempId))\r\n          }));\r\n\r\n          return;\r\n        }\r\n      }\r\n\r\n      const response = await apiClient.post(`/messages`, {\r\n        recipient_id: payload.recipient_id,\r\n        message: payload.message,\r\n        created_at: optimisticMessage.created_at,\r\n      });\r\n\r\n      serverMessage = response.data;\r\n\r\n      if (serverMessage) {\r\n        get().updateLatestSeenMessage(\r\n          currentChannel.channel_id,\r\n          serverMessage.created_at\r\n        );\r\n      }\r\n\r\n      set((state) => {\r\n        const serverMessageExists = serverMessage && state.messages?.some(msg => msg.id === serverMessage.id);\r\n\r\n        const serverMessageFingerprint = serverMessage ?\r\n          `${serverMessage.sender_id}-${serverMessage.created_at}-${serverMessage.message.substring(0, 20)}` : '';\r\n\r\n        const similarMessageExists = serverMessage && state.messages?.some(msg => {\r\n          if (msg.id === tempId) return false; // Skip the optimistic message\r\n          const msgFingerprint = `${msg.sender_id}-${msg.created_at}-${msg.message.substring(0, 20)}`;\r\n          return msgFingerprint === serverMessageFingerprint;\r\n        });\r\n\r\n        if (serverMessageExists || similarMessageExists) {\r\n          return {\r\n            messages: (state.messages || []).filter(msg => msg.id !== tempId),\r\n            messageIds: state.messageIds,\r\n            pendingMessages: new Set([...state.pendingMessages].filter((id) => id !== tempId))\r\n          };\r\n        }\r\n\r\n        if (!serverMessage) {\r\n          return {\r\n            messages: (state.messages || []).filter(msg => msg.id !== tempId),\r\n            messageIds: state.messageIds,\r\n            pendingMessages: new Set([...state.pendingMessages].filter((id) => id !== tempId))\r\n          };\r\n        }\r\n\r\n        return {\r\n          messages: (state.messages || []).map((msg) =>\r\n            msg.id === tempId ? serverMessage : msg\r\n          ),\r\n          messageIds: new Set(\r\n            [...(state.messageIds || [])]\r\n              .filter((id) => id !== tempId)\r\n              .concat(serverMessage.id)\r\n          ),\r\n          pendingMessages: new Set(\r\n            [...state.pendingMessages].filter((id) => id !== tempId)\r\n          ),\r\n        };\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Failed to send message:\", error);\r\n      get().removeMessage(tempId);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  sendInitialMessage: async (payload: InitialMessagePayload) => {\r\n    try {\r\n      await apiClient.post(`/messages`, {\r\n        ...payload,\r\n        created_at: dateNowWithTZOffset(),\r\n      });\r\n    } catch (error) {\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  setCurrentChannel: (channel) => {\r\n    const currentChannelId = get().currentChannel?.channel_id;\r\n\r\n    set({\r\n      messages: null,\r\n      messageIds: new Set<number>()\r\n    });\r\n\r\n    if (currentChannelId) {\r\n      wsManager.disconnect();\r\n    }\r\n\r\n    set({ currentChannel: channel });\r\n\r\n    if (channel) {\r\n\r\n      const token = localStorage.getItem(\"access_token\");\r\n\r\n      if (token) {\r\n        get().initWebSocket();\r\n\r\n        wsManager.connect(channel.channel_id, token);\r\n\r\n        get().fetchMessages(channel.channel_id);\r\n      } else {\r\n        set({ error: \"Authentication token not found\" });\r\n      }\r\n    }\r\n  },\r\n\r\n  addMessage: (message: Message) => {\r\n    set((state) => {\r\n      if (state.currentChannel && message.channel_id !== state.currentChannel.channel_id) {\r\n        return state; // Return state unchanged\r\n      }\r\n\r\n      if (message.id && state.messageIds.has(message.id)) {\r\n        return state; // Return state unchanged\r\n      }\r\n\r\n      const messageFingerprint = `${message.sender_id}-${message.created_at}-${message.message.substring(0, 20)}`;\r\n\r\n      const messageExistsByFingerprint = state.messages?.some(m => {\r\n        const existingFingerprint = `${m.sender_id}-${m.created_at}-${m.message.substring(0, 20)}`;\r\n        return existingFingerprint === messageFingerprint;\r\n      });\r\n\r\n      if (messageExistsByFingerprint) {\r\n        return state; // Return state unchanged\r\n      }\r\n\r\n      return {\r\n        messages: [...(state.messages || []), message],\r\n        messageIds: message.id ? new Set(state.messageIds).add(message.id) : state.messageIds,\r\n      };\r\n    });\r\n  },\r\n\r\n  removeMessage: (messageId: number) => {\r\n    set((state) => ({\r\n      messages: (state.messages || []).filter((msg) => msg.id !== messageId),\r\n      messageIds: new Set(\r\n        [...state.messageIds].filter((id) => id !== messageId)\r\n      ),\r\n      pendingMessages: new Set(\r\n        [...state.pendingMessages].filter((id) => id !== messageId)\r\n      ),\r\n    }));\r\n  },\r\n\r\n  clearError: () => set({ error: null }),\r\n\r\n  updateLatestSeenMessage: (channelId: string, timestamp: string) => {\r\n    try {\r\n      let latestMessages: LatestMessagesStorage = {};\r\n      const storedData = localStorage.getItem(LOCALSTORAGE_KEY);\r\n\r\n      if (storedData) {\r\n        latestMessages = JSON.parse(storedData) as LatestMessagesStorage;\r\n      }\r\n\r\n      if (latestMessages[channelId] === timestamp) return;\r\n\r\n      latestMessages[channelId] = timestamp;\r\n\r\n      localStorage.setItem(LOCALSTORAGE_KEY, JSON.stringify(latestMessages));\r\n\r\n      set((state) => ({\r\n        unseenMessages: {\r\n          ...state.unseenMessages,\r\n          [channelId]: 0,\r\n        },\r\n        unseenChannelsCount: Math.max(0, state.unseenChannelsCount - 1),\r\n      }));\r\n    } catch (error) {\r\n      console.error(\"Failed to update latest seen message:\", error);\r\n    }\r\n  },\r\n\r\n  getUnseenChannelsCount: async (): Promise<number> => {\r\n    try {\r\n      let latestSeenMessages: LatestMessagesStorage = {};\r\n      const storedData = localStorage.getItem(LOCALSTORAGE_KEY);\r\n\r\n      if (storedData) {\r\n        latestSeenMessages = JSON.parse(storedData) as LatestMessagesStorage;\r\n      }\r\n\r\n      if (!get().channels) {\r\n        await get().fetchChannels();\r\n      }\r\n\r\n      const channels = get().channels;\r\n      if (!channels) return 0;\r\n\r\n      let unseenMessages = {} as Record<string, number>;\r\n      let unseenChannelsCount = 0;\r\n\r\n      const promises = channels.map(async (channel) => {\r\n        const response = await apiClient.get(\r\n          `/messages/channel/${channel.channel_id}?skip=0&limit=1`\r\n        );\r\n\r\n        if (response.data.messages && response.data.messages.length > 0) {\r\n          const latestMessage = response.data.messages[0];\r\n          const latestSeenTimestamp = latestSeenMessages[channel.channel_id];\r\n\r\n          if (\r\n            !latestSeenTimestamp ||\r\n            new Date(latestMessage.created_at) > new Date(latestSeenTimestamp)\r\n          ) {\r\n            unseenMessages[channel.channel_id] = 1;\r\n            unseenChannelsCount++;\r\n          }\r\n        }\r\n      });\r\n\r\n      await Promise.all(promises);\r\n      set({\r\n        unseenMessages,\r\n        unseenChannelsCount,\r\n      });\r\n      return unseenChannelsCount;\r\n    } catch (error) {\r\n      console.error(\"Failed to get unseen channels count:\", error);\r\n      return 0;\r\n    }\r\n  },\r\n\r\n  reconnectWebSocket: () => {\r\n    const currentChannel = get().currentChannel;\r\n    if (currentChannel) {\r\n      const token = localStorage.getItem(\"access_token\");\r\n      if (token) {\r\n        wsManager.disconnect();\r\n        wsManager.connect(currentChannel.channel_id, token);\r\n      }\r\n    } else {\r\n      wsManager.disconnect();\r\n    }\r\n  },\r\n\r\n  isWebSocketConnected: () => {\r\n    return wsManager.isConnected();\r\n  },\r\n}));\r\n"], "names": [], "mappings": ";;;;AAGA;AASA;AAgNqB;AA1NrB;AAFA;;;;AAcA,MAAM,mBAAmB;AAMzB,MAAM;IACI,SAA2B,KAAK;IAChC,YAA2B,KAAK;IAChC,QAAuB,KAAK;IAC5B,oBAAoB,EAAE;IACtB,uBAAuB,EAAE;IACzB,oBAAoB,KAAK;IACzB,qBAA4C,KAAK;IACjD,iBAA+C,KAAK;IACpD,sBAA4E,KAAK;IACjF,eAA8C,KAAK;IAE3D,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI;QACrC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;QAC3C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAC7C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;QAC3C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;QACjD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAC7C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI;QAC7C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI;IAC3C;IAEO,QAAQ,SAAiB,EAAE,KAAa,EAAQ;QACrD,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,KAAK,GAAG;QAEb,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,KAAK;QACnB;QAEA,IAAI;YACF,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,GAAG,SAAS,EAAE,UAAU,OAAO,EAAE,OAAO;YACjF,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU;YAE5B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU;YACpC,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa;YAC1C,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW;YACtC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW;YAEtC,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,IAAI,CAAC,mBAAmB,CAAC;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC;YACpB;QACF;IACF;IAEO,aAAmB;QACxB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,KAAK;YACjB,IAAI,CAAC,MAAM,GAAG;QAChB;QAEA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,aAAa,IAAI,CAAC,kBAAkB;YACpC,IAAI,CAAC,kBAAkB,GAAG;QAC5B;QAEA,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,iBAAiB,GAAG;QAEzB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC;QAC3B;IACF;IAEO,YAAY,OAAyB,EAAW;QACrD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,UAAU,IAAI,EAAE;YAC7D,QAAQ,KAAK,CAAC;YACd,OAAO;QACT;QAEA,IAAI;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,SAAS,CAAC;YAChC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,IAAI,CAAC,YAAY,CAAC;YACpB;YACA,OAAO;QACT;IACF;IAEO,UAAU,OAA4B,EAAQ;QACnD,IAAI,CAAC,cAAc,GAAG;IACxB;IAEO,eAAe,OAAoD,EAAQ;QAChF,IAAI,CAAC,mBAAmB,GAAG;IAC7B;IAEO,QAAQ,OAA6B,EAAQ;QAClD,IAAI,CAAC,YAAY,GAAG;IACtB;IAEO,cAAuB;QAC5B,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,UAAU,KAAK,UAAU,IAAI;IAC1E;IAEQ,WAAW,MAAa,EAAQ;QACtC,IAAI,CAAC,iBAAiB,GAAG;QAEzB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC;QAC3B;IACF;IAEQ,cAAc,KAAmB,EAAQ;QAC/C,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC,MAAM,IAAI;YAElC,MAAM,MAAM,IAAI;YAChB,KAAK,WAAW,GAAG,IAAI,WAAW;YAElC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACzB,IAAI,CAAC,eAAe,GAAG,EAAE;YAC3B;YAEA,MAAM,cAAc,GAAG,KAAK,SAAS,IAAI,GAAG,CAAC,EAAE,KAAK,UAAU,IAAI,GAAG,CAAC,EAAE,CAAC,KAAK,OAAO,IAAI,EAAE,EAAE,SAAS,CAAC,GAAG,KAAK;YAE/G,+DAA+D;YAC/D,MAAM,cAAc,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;gBAC5C,+EAA+E;gBAC/E,IAAI,EAAE,WAAW,KAAK,aAAa;oBACjC,MAAM,WAAW,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;oBAC/C,MAAM,cAAc,IAAI,OAAO;oBAC/B,OAAO,AAAC,cAAc,WAAY,MAAM,YAAY;gBACtD;gBACA,OAAO;YACT;YAEA,IAAI,aAAa;gBACf;YACF;YAEA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;gBACxB;gBACA,YAAY,IAAI,WAAW;YAC7B;YAEA,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,IAAI;gBACpC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;YACrD;YAEA,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,cAAc,CAAC;YACtB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEQ,kBAAoE,EAAE,CAAC;IAEvE,YAAY,KAAiB,EAAQ;QAE3C,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC;QAC3B;QAEA,IAAI,CAAC,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE;YACnD,IAAI,CAAC,SAAS;QAChB;IACF;IAEQ,YAAY,KAAY,EAAQ;QACtC,QAAQ,KAAK,CAAC,oBAAoB;QAClC,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAI,CAAC,YAAY,CAAC;QACpB;IACF;IAEQ,YAAkB;QACxB,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YACvD;QACF;QAEA,IAAI,CAAC,iBAAiB;QAEtB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,mBAAmB,CAAC;QAC3B;QAEA,IAAI,CAAC,kBAAkB,GAAG,WAAW;YACnC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,EAAE;gBAChC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK;YACzC;QACF,GAAG,IAAI,CAAC,iBAAiB;IAC3B;IAEQ,sBAA8B;QACpC,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ,KAAK;QAC9C,MAAM,WAAW,WAAW,SAAS;QAErC,IAAI,aAAa,6DAAwC;QAEzD,aAAa,WAAW,OAAO,CAAC,gBAAgB;QAEhD,OAAO,GAAG,SAAS,EAAE,EAAE,YAAY;IACrC;AACF;AAEA,MAAM,YAAY,IAAI;AAEtB,IAAI,kBAAuD;AAEpD,MAAM,2BAA2B,CAAC;IACvC,kBAAkB;AACpB;AAEO,MAAM,mBAAmB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAa,CAAC,KAAK,MAAQ,CAAC;QAC/D,UAAU;QACV,gBAAgB;QAChB,UAAU;QACV,YAAY,IAAI;QAChB,iBAAiB,IAAI;QACrB,gBAAgB,CAAC;QACjB,qBAAqB;QACrB,SAAS;QACT,OAAO;QACP,kBAAkB;QAElB,eAAe;YACb,UAAU,SAAS,CAAC,CAAC;gBACnB,MAAM,UAAU;gBAChB,MAAM,QAAQ;gBAGd,IAAI,iBAAiB,WAAW,QAAQ,SAAS,KAAK,gBAAgB,OAAO,EAAE;oBAC7E;gBACF;gBAEA,IAAI,MAAM,cAAc,IAAI,QAAQ,UAAU,KAAK,MAAM,cAAc,CAAC,UAAU,EAAE;oBAClF;gBACF;gBAEA,IAAI,QAAQ,EAAE,IAAI,MAAM,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG;oBAClD;gBACF;gBAEA,MAAM,qBAAqB,GAAG,QAAQ,SAAS,CAAC,CAAC,EAAE,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;gBAE3G,MAAM,cAAc,MAAM,QAAQ,EAAE,KAAK,CAAA;oBACvC,MAAM,sBAAsB,GAAG,EAAE,SAAS,CAAC,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;oBAC1F,OAAO,wBAAwB;gBACjC;gBAEA,IAAI,aAAa;oBACf;gBACF;gBAEA,MAAM,UAAU,CAAC;gBAEjB,IAAI,MAAM,cAAc,EAAE;oBACxB,MAAM,uBAAuB,CAC3B,MAAM,cAAc,CAAC,UAAU,EAC/B,QAAQ,UAAU;gBAEtB;YACF;YAEA,UAAU,cAAc,CAAC,CAAC;gBACxB,IAAI;oBAAE,kBAAkB;gBAAO;YACjC;YAEA,UAAU,OAAO,CAAC,CAAC;gBACjB,IAAI;oBAAE,OAAO;gBAA6B;YAC5C;QACF;QAEA,eAAe;YACb,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAAC;gBACrC,IAAI;oBAAE,UAAU,SAAS,IAAI,CAAC,QAAQ;oBAAE,SAAS;gBAAM;YACzD,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACvC,SAAS;gBACX;YACF;QACF;QAEA,eAAe,OAAO,WAAmB,OAAO,CAAC,EAAE,QAAQ,EAAE;YAC3D,IAAI;gBACF,IAAI;oBAAE,SAAS;oBAAM,OAAO;gBAAK;gBACjC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,kBAAkB,EAAE,UAAU,MAAM,EAAE,KAAK,OAAO,EAAE,OAAO;gBAE9D,MAAM,cAAc,SAAS,IAAI,CAAC,QAAQ;gBAE1C,MAAM,aAAa,IAAI,IAAI,MAAM,UAAU;gBAC3C,YAAY,OAAO,CAAC,CAAC,MAAiB,WAAW,GAAG,CAAC,IAAI,EAAE;gBAE3D,IAAI,CAAC,QAAU,CAAC;wBACd,UACE,SAAS,IACL,cACA;+BACM,MAAM,QAAQ,IAAI,EAAE;+BACrB,YAAY,MAAM,CACnB,CAAC,MACC,CAAC,MAAM,QAAQ,EAAE,KACf,CAAC,cAAyB,YAAY,EAAE,KAAK,IAAI,EAAE;yBAG1D;wBACP;wBACA,SAAS;oBACX,CAAC;gBAED,IAAI,UAAU,KAAK,YAAY,MAAM,GAAG,GAAG;oBACzC,MAAM,uBAAuB,CAAC,WAAW,WAAW,CAAC,EAAE,CAAC,UAAU;gBACpE;YACF,EAAE,OAAO,OAAY;gBACnB,IAAI;oBACF,OAAO,MAAM,QAAQ,EAAE,MAAM,UAAU;oBACvC,SAAS;gBACX;YACF;QACF;QAEA,aAAa,OAAO;YAClB,MAAM,iBAAiB,MAAM,cAAc;YAC3C,IAAI,CAAC,gBAAgB;YAErB,MAAM,QAAQ;YACd,MAAM,qBAAqB,GAAG,QAAQ,SAAS,CAAC,CAAC,EAAE,CAAA,GAAA,kIAAA,CAAA,sBAAmB,AAAD,IAAI,CAAC,EAAE,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;YAE9G,MAAM,cAAc,MAAM,QAAQ,EAAE,KAAK,CAAA;gBACvC,IAAI,EAAE,SAAS,KAAK,QAAQ,SAAS,EAAE,OAAO;gBAE9C,IAAI,EAAE,OAAO,KAAK,QAAQ,OAAO,EAAE,OAAO;gBAE1C,MAAM,UAAU,IAAI,KAAK,EAAE,UAAU,EAAE,OAAO;gBAC9C,MAAM,MAAM,IAAI,OAAO,OAAO;gBAC9B,OAAO,AAAC,MAAM,UAAW,MAAM,YAAY;YAC7C;YAEA,IAAI,aAAa;gBACf;YACF;YAEA,4BAA4B;YAC5B,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;YAC1C,MAAM,oBAA6B;gBACjC,IAAI;gBACJ,YAAY,eAAe,UAAU;gBACrC,SAAS,QAAQ,OAAO;gBACxB,WAAW,QAAQ,SAAS;gBAC5B,aAAa,QAAQ,WAAW;gBAChC,YAAY,CAAA,GAAA,kIAAA,CAAA,sBAAmB,AAAD;YAChC;YAEA,IAAI;gBACF,MAAM,UAAU,CAAC;gBACjB,IAAI,CAAC,QAAU,CAAC;wBACd,iBAAiB,IAAI,IAAI;+BAAI,MAAM,eAAe;4BAAE;yBAAO;oBAC7D,CAAC;gBAED,IAAI,gBAAgC;gBAEpC,IAAI,UAAU,WAAW,IAAI;oBAC3B,MAAM,YAA8B;wBAClC,MAAM;wBACN,SAAS,QAAQ,OAAO;wBACxB,cAAc,QAAQ,YAAY;wBAClC,YAAY,kBAAkB,UAAU;oBAC1C;oBAEA,MAAM,mBAAmB,UAAU,WAAW,CAAC;oBAE/C,IAAI,kBAAkB;wBAGpB,IAAI,CAAC,QAAU,CAAC;gCACd,iBAAiB,IAAI,IAAI;uCAAI,MAAM,eAAe;iCAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;4BAC5E,CAAC;wBAED;oBACF;gBACF;gBAEA,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE;oBACjD,cAAc,QAAQ,YAAY;oBAClC,SAAS,QAAQ,OAAO;oBACxB,YAAY,kBAAkB,UAAU;gBAC1C;gBAEA,gBAAgB,SAAS,IAAI;gBAE7B,IAAI,eAAe;oBACjB,MAAM,uBAAuB,CAC3B,eAAe,UAAU,EACzB,cAAc,UAAU;gBAE5B;gBAEA,IAAI,CAAC;oBACH,MAAM,sBAAsB,iBAAiB,MAAM,QAAQ,EAAE,KAAK,CAAA,MAAO,IAAI,EAAE,KAAK,cAAc,EAAE;oBAEpG,MAAM,2BAA2B,gBAC/B,GAAG,cAAc,SAAS,CAAC,CAAC,EAAE,cAAc,UAAU,CAAC,CAAC,EAAE,cAAc,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG;oBAEvG,MAAM,uBAAuB,iBAAiB,MAAM,QAAQ,EAAE,KAAK,CAAA;wBACjE,IAAI,IAAI,EAAE,KAAK,QAAQ,OAAO,OAAO,8BAA8B;wBACnE,MAAM,iBAAiB,GAAG,IAAI,SAAS,CAAC,CAAC,EAAE,IAAI,UAAU,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;wBAC3F,OAAO,mBAAmB;oBAC5B;oBAEA,IAAI,uBAAuB,sBAAsB;wBAC/C,OAAO;4BACL,UAAU,CAAC,MAAM,QAAQ,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;4BAC1D,YAAY,MAAM,UAAU;4BAC5B,iBAAiB,IAAI,IAAI;mCAAI,MAAM,eAAe;6BAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;wBAC5E;oBACF;oBAEA,IAAI,CAAC,eAAe;wBAClB,OAAO;4BACL,UAAU,CAAC,MAAM,QAAQ,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;4BAC1D,YAAY,MAAM,UAAU;4BAC5B,iBAAiB,IAAI,IAAI;mCAAI,MAAM,eAAe;6BAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;wBAC5E;oBACF;oBAEA,OAAO;wBACL,UAAU,CAAC,MAAM,QAAQ,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,MACpC,IAAI,EAAE,KAAK,SAAS,gBAAgB;wBAEtC,YAAY,IAAI,IACd;+BAAK,MAAM,UAAU,IAAI,EAAE;yBAAE,CAC1B,MAAM,CAAC,CAAC,KAAO,OAAO,QACtB,MAAM,CAAC,cAAc,EAAE;wBAE5B,iBAAiB,IAAI,IACnB;+BAAI,MAAM,eAAe;yBAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;oBAErD;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,MAAM,aAAa,CAAC;gBACpB,MAAM;YACR;QACF;QAEA,oBAAoB,OAAO;YACzB,IAAI;gBACF,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,EAAE;oBAChC,GAAG,OAAO;oBACV,YAAY,CAAA,GAAA,kIAAA,CAAA,sBAAmB,AAAD;gBAChC;YACF,EAAE,OAAO,OAAO;gBACd,MAAM;YACR;QACF;QAEA,mBAAmB,CAAC;YAClB,MAAM,mBAAmB,MAAM,cAAc,EAAE;YAE/C,IAAI;gBACF,UAAU;gBACV,YAAY,IAAI;YAClB;YAEA,IAAI,kBAAkB;gBACpB,UAAU,UAAU;YACtB;YAEA,IAAI;gBAAE,gBAAgB;YAAQ;YAE9B,IAAI,SAAS;gBAEX,MAAM,QAAQ,aAAa,OAAO,CAAC;gBAEnC,IAAI,OAAO;oBACT,MAAM,aAAa;oBAEnB,UAAU,OAAO,CAAC,QAAQ,UAAU,EAAE;oBAEtC,MAAM,aAAa,CAAC,QAAQ,UAAU;gBACxC,OAAO;oBACL,IAAI;wBAAE,OAAO;oBAAiC;gBAChD;YACF;QACF;QAEA,YAAY,CAAC;YACX,IAAI,CAAC;gBACH,IAAI,MAAM,cAAc,IAAI,QAAQ,UAAU,KAAK,MAAM,cAAc,CAAC,UAAU,EAAE;oBAClF,OAAO,OAAO,yBAAyB;gBACzC;gBAEA,IAAI,QAAQ,EAAE,IAAI,MAAM,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG;oBAClD,OAAO,OAAO,yBAAyB;gBACzC;gBAEA,MAAM,qBAAqB,GAAG,QAAQ,SAAS,CAAC,CAAC,EAAE,QAAQ,UAAU,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;gBAE3G,MAAM,6BAA6B,MAAM,QAAQ,EAAE,KAAK,CAAA;oBACtD,MAAM,sBAAsB,GAAG,EAAE,SAAS,CAAC,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,KAAK;oBAC1F,OAAO,wBAAwB;gBACjC;gBAEA,IAAI,4BAA4B;oBAC9B,OAAO,OAAO,yBAAyB;gBACzC;gBAEA,OAAO;oBACL,UAAU;2BAAK,MAAM,QAAQ,IAAI,EAAE;wBAAG;qBAAQ;oBAC9C,YAAY,QAAQ,EAAE,GAAG,IAAI,IAAI,MAAM,UAAU,EAAE,GAAG,CAAC,QAAQ,EAAE,IAAI,MAAM,UAAU;gBACvF;YACF;QACF;QAEA,eAAe,CAAC;YACd,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,CAAC,MAAM,QAAQ,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;oBAC5D,YAAY,IAAI,IACd;2BAAI,MAAM,UAAU;qBAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;oBAE9C,iBAAiB,IAAI,IACnB;2BAAI,MAAM,eAAe;qBAAC,CAAC,MAAM,CAAC,CAAC,KAAO,OAAO;gBAErD,CAAC;QACH;QAEA,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;QAEpC,yBAAyB,CAAC,WAAmB;YAC3C,IAAI;gBACF,IAAI,iBAAwC,CAAC;gBAC7C,MAAM,aAAa,aAAa,OAAO,CAAC;gBAExC,IAAI,YAAY;oBACd,iBAAiB,KAAK,KAAK,CAAC;gBAC9B;gBAEA,IAAI,cAAc,CAAC,UAAU,KAAK,WAAW;gBAE7C,cAAc,CAAC,UAAU,GAAG;gBAE5B,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;gBAEtD,IAAI,CAAC,QAAU,CAAC;wBACd,gBAAgB;4BACd,GAAG,MAAM,cAAc;4BACvB,CAAC,UAAU,EAAE;wBACf;wBACA,qBAAqB,KAAK,GAAG,CAAC,GAAG,MAAM,mBAAmB,GAAG;oBAC/D,CAAC;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;YACzD;QACF;QAEA,wBAAwB;YACtB,IAAI;gBACF,IAAI,qBAA4C,CAAC;gBACjD,MAAM,aAAa,aAAa,OAAO,CAAC;gBAExC,IAAI,YAAY;oBACd,qBAAqB,KAAK,KAAK,CAAC;gBAClC;gBAEA,IAAI,CAAC,MAAM,QAAQ,EAAE;oBACnB,MAAM,MAAM,aAAa;gBAC3B;gBAEA,MAAM,WAAW,MAAM,QAAQ;gBAC/B,IAAI,CAAC,UAAU,OAAO;gBAEtB,IAAI,iBAAiB,CAAC;gBACtB,IAAI,sBAAsB;gBAE1B,MAAM,WAAW,SAAS,GAAG,CAAC,OAAO;oBACnC,MAAM,WAAW,MAAM,mHAAA,CAAA,UAAS,CAAC,GAAG,CAClC,CAAC,kBAAkB,EAAE,QAAQ,UAAU,CAAC,eAAe,CAAC;oBAG1D,IAAI,SAAS,IAAI,CAAC,QAAQ,IAAI,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG;wBAC/D,MAAM,gBAAgB,SAAS,IAAI,CAAC,QAAQ,CAAC,EAAE;wBAC/C,MAAM,sBAAsB,kBAAkB,CAAC,QAAQ,UAAU,CAAC;wBAElE,IACE,CAAC,uBACD,IAAI,KAAK,cAAc,UAAU,IAAI,IAAI,KAAK,sBAC9C;4BACA,cAAc,CAAC,QAAQ,UAAU,CAAC,GAAG;4BACrC;wBACF;oBACF;gBACF;gBAEA,MAAM,QAAQ,GAAG,CAAC;gBAClB,IAAI;oBACF;oBACA;gBACF;gBACA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,OAAO;YACT;QACF;QAEA,oBAAoB;YAClB,MAAM,iBAAiB,MAAM,cAAc;YAC3C,IAAI,gBAAgB;gBAClB,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,IAAI,OAAO;oBACT,UAAU,UAAU;oBACpB,UAAU,OAAO,CAAC,eAAe,UAAU,EAAE;gBAC/C;YACF,OAAO;gBACL,UAAU,UAAU;YACtB;QACF;QAEA,sBAAsB;YACpB,OAAO,UAAU,WAAW;QAC9B;IACF,CAAC"}}, {"offset": {"line": 1133, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1139, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/student/sessions/send-message-dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport * as z from \"zod\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from \"@components/ui/dialog\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Textarea } from \"@components/ui/textarea\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@components/ui/avatar\";\r\nimport { useWebSocketChat } from \"@/app/hooks/useWebSocketChat\";\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport { toast } from \"react-toastify\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\n\r\nconst messageSchema = z.object({\r\n  message: z\r\n    .string()\r\n    .min(1, \"Message is required\")\r\n    .max(500, \"Message cannot exceed 500 characters\"),\r\n});\r\n\r\ntype MessageFormData = z.infer<typeof messageSchema>;\r\n\r\ninterface CounselorInfo {\r\n  user_id: number;\r\n  first_name: string;\r\n  last_name: string;\r\n  profile_picture_url: string | null;\r\n}\r\n\r\ninterface SendMessageDialogProps {\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  counselorInfo: CounselorInfo;\r\n}\r\n\r\nexport function SendMessageDialog({\r\n  open,\r\n  onOpenChange,\r\n  counselorInfo,\r\n}: SendMessageDialogProps) {\r\n  const [isSending, setIsSending] = useState(false);\r\n  const router = useRouter();\r\n  const { userInfo } = useProfile();\r\n  const { sendInitialMessage } = useWebSocketChat();\r\n\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors },\r\n    reset,\r\n  } = useForm<MessageFormData>({\r\n    resolver: zodResolver(messageSchema),\r\n  });\r\n\r\n  const onSubmit = async (data: MessageFormData) => {\r\n    if (!userInfo) return;\r\n\r\n    setIsSending(true);\r\n    try {\r\n      await sendInitialMessage({\r\n        message: data.message,\r\n        recipient_id: counselorInfo?.user_id,\r\n      });\r\n\r\n      toast.success(\"Message sent successfully!\");\r\n      reset();\r\n      onOpenChange(false);\r\n      router.push(\"/student/dashboard/messages\");\r\n    } catch (error: any) {\r\n      if (\r\n        error?.response?.data?.detail?.includes(\r\n          \"between a counselor and a student\"\r\n        )\r\n      ) {\r\n        toast.error(\"You cannot send any messages to this person.\");\r\n      } else {\r\n        toast.error(\"Failed to send message. Please try again.\");\r\n      }\r\n    } finally {\r\n      setIsSending(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-[425px]\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"text-2xl font-bold text-center\">\r\n            Send message to:\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n        <div className=\"flex items-center space-x-4 mt-4\">\r\n          <Avatar className=\"w-16 h-16\">\r\n            <AvatarImage\r\n              src={\r\n                counselorInfo?.profile_picture_url ||\r\n                generatePlaceholder(\r\n                  counselorInfo?.first_name,\r\n                  counselorInfo?.last_name\r\n                )\r\n              }\r\n              alt={counselorInfo?.first_name || \"\"}\r\n            />\r\n            <AvatarFallback>{counselorInfo?.first_name || \"\"}</AvatarFallback>\r\n          </Avatar>\r\n          <div className=\"flex flex-col\">\r\n            <h3 className=\"text-xl font-semibold\">\r\n              {counselorInfo\r\n                ? `${counselorInfo.first_name} ${counselorInfo.last_name}`\r\n                : \"loading...\"}\r\n            </h3>\r\n          </div>\r\n        </div>\r\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4 mt-4\">\r\n          <div>\r\n            <Textarea\r\n              {...register(\"message\")}\r\n              placeholder=\"Type your message here...\"\r\n              className=\"min-h-[100px]\"\r\n            />\r\n            {errors.message && (\r\n              <p className=\"text-red-500 text-sm mt-1\">\r\n                {errors.message.message}\r\n              </p>\r\n            )}\r\n          </div>\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full bg-blue-800 hover:bg-blue-800/90\"\r\n            disabled={isSending}\r\n          >\r\n            {isSending ? \"Sending...\" : \"Send Message\"}\r\n          </Button>\r\n        </form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA;AAEA;;;AALA;;;;;;;;;;;;;;AAqBA,MAAM,gBAAgB,wIAAE,MAAM,CAAC;IAC7B,SAAS,wIACN,MAAM,GACN,GAAG,CAAC,GAAG,uBACP,GAAG,CAAC,KAAK;AACd;AAiBO,SAAS,kBAAkB,EAChC,IAAI,EACJ,YAAY,EACZ,aAAa,EACU;;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD;IAE9C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACrB,KAAK,EACN,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,UAAU;QAEf,aAAa;QACb,IAAI;YACF,MAAM,mBAAmB;gBACvB,SAAS,KAAK,OAAO;gBACrB,cAAc,eAAe;YAC/B;YAEA,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd;YACA,aAAa;YACb,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAY;YACnB,IACE,OAAO,UAAU,MAAM,QAAQ,SAC7B,sCAEF;gBACA,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,qIAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,qIAAA,CAAA,cAAW;wBAAC,WAAU;kCAAiC;;;;;;;;;;;8BAI1D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BAAC,WAAU;;8CAChB,6LAAC,qIAAA,CAAA,cAAW;oCACV,KACE,eAAe,uBACf,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD,EAChB,eAAe,YACf,eAAe;oCAGnB,KAAK,eAAe,cAAc;;;;;;8CAEpC,6LAAC,qIAAA,CAAA,iBAAc;8CAAE,eAAe,cAAc;;;;;;;;;;;;sCAEhD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CACX,gBACG,GAAG,cAAc,UAAU,CAAC,CAAC,EAAE,cAAc,SAAS,EAAE,GACxD;;;;;;;;;;;;;;;;;8BAIV,6LAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAChD,6LAAC;;8CACC,6LAAC,uIAAA,CAAA,WAAQ;oCACN,GAAG,SAAS,UAAU;oCACvB,aAAY;oCACZ,WAAU;;;;;;gCAEX,OAAO,OAAO,kBACb,6LAAC;oCAAE,WAAU;8CACV,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;sCAI7B,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;sCAET,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAMxC;GAtGgB;;QAMC,qIAAA,CAAA,YAAS;QACH,wIAAA,CAAA,aAAU;QACA,mIAAA,CAAA,mBAAgB;QAO3C,iKAAA,CAAA,UAAO;;;KAfG"}}, {"offset": {"line": 1345, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1351, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/utils/uni-logo.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nconst CLIENT_ID = \"1idDNCRTv8QuIQpUJ7P\";\r\n\r\n// In-memory cache for university logos\r\nconst logoCache = new Map<string, string>();\r\nconst domainCache = new Map<string, string>();\r\n\r\n// Cache expiry time (24 hours)\r\nconst CACHE_EXPIRY = 24 * 60 * 60 * 1000;\r\nconst cacheTimestamps = new Map<string, number>();\r\n\r\n// Fallback logo for when university logo cannot be found\r\nconst FALLBACK_LOGO = \"/images/university-placeholder.png\";\r\n\r\nfunction isCacheValid(key: string): boolean {\r\n  const timestamp = cacheTimestamps.get(key);\r\n  if (!timestamp) return false;\r\n  return Date.now() - timestamp < CACHE_EXPIRY;\r\n}\r\n\r\nfunction setCacheWithTimestamp(key: string, value: string): void {\r\n  logoCache.set(key, value);\r\n  cacheTimestamps.set(key, Date.now());\r\n}\r\n\r\nasync function getUniversityDomain(universityName: string): Promise<string> {\r\n  const cacheKey = `domain_${universityName.toLowerCase()}`;\r\n\r\n  // Check domain cache first\r\n  if (domainCache.has(cacheKey) && isCacheValid(cacheKey)) {\r\n    return domainCache.get(cacheKey)!;\r\n  }\r\n\r\n  try {\r\n    const encodedName = encodeURIComponent(universityName);\r\n    const response = await axios.get(\r\n      `https://universities.hipolabs.com/search?name=${encodedName}`,\r\n      { timeout: 5000 } // 5 second timeout\r\n    );\r\n\r\n    if (response.data.length === 0) {\r\n      throw new Error(\"University not found!\");\r\n    }\r\n\r\n    const domain = response.data[0].domains[0];\r\n    domainCache.set(cacheKey, domain);\r\n    cacheTimestamps.set(cacheKey, Date.now());\r\n\r\n    return domain;\r\n  } catch (error) {\r\n    console.warn(`Failed to fetch domain for ${universityName}:`, error);\r\n    throw error;\r\n  }\r\n}\r\n\r\nexport async function getUniversityLogoUrl(\r\n  universityName: string\r\n): Promise<string> {\r\n  if (!universityName || typeof universityName !== \"string\") {\r\n    return FALLBACK_LOGO;\r\n  }\r\n\r\n  const cacheKey = universityName.toLowerCase().trim();\r\n\r\n  // Check cache first\r\n  if (logoCache.has(cacheKey) && isCacheValid(cacheKey)) {\r\n    return logoCache.get(cacheKey)!;\r\n  }\r\n\r\n  try {\r\n    const domain = await getUniversityDomain(universityName);\r\n    const brandfetchUrl = `https://cdn.brandfetch.io/${domain}/w/200/h/200?c=${CLIENT_ID}`;\r\n\r\n    // Verify the logo URL is accessible\r\n    try {\r\n      await axios.head(brandfetchUrl, { timeout: 3000 });\r\n      setCacheWithTimestamp(cacheKey, brandfetchUrl);\r\n      return brandfetchUrl;\r\n    } catch (logoError) {\r\n      // If brandfetch fails, cache the fallback\r\n      setCacheWithTimestamp(cacheKey, FALLBACK_LOGO);\r\n      return FALLBACK_LOGO;\r\n    }\r\n  } catch (error) {\r\n    console.warn(\r\n      `Error fetching university logo for ${universityName}:`,\r\n      error\r\n    );\r\n    // Cache the fallback to prevent repeated failed requests\r\n    setCacheWithTimestamp(cacheKey, FALLBACK_LOGO);\r\n    return FALLBACK_LOGO;\r\n  }\r\n}\r\n\r\n// Preload university logos for better performance\r\nexport function preloadUniversityLogos(universityNames: string[]): void {\r\n  universityNames.forEach((name) => {\r\n    if (name && !logoCache.has(name.toLowerCase().trim())) {\r\n      // Fire and forget - don't await\r\n      getUniversityLogoUrl(name).catch(() => {\r\n        // Silently handle errors for preloading\r\n      });\r\n    }\r\n  });\r\n}\r\n\r\n// Clear expired cache entries\r\nexport function clearExpiredCache(): void {\r\n  const now = Date.now();\r\n  for (const [key, timestamp] of cacheTimestamps.entries()) {\r\n    if (now - timestamp > CACHE_EXPIRY) {\r\n      logoCache.delete(key);\r\n      domainCache.delete(key);\r\n      cacheTimestamps.delete(key);\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,YAAY;AAElB,uCAAuC;AACvC,MAAM,YAAY,IAAI;AACtB,MAAM,cAAc,IAAI;AAExB,+BAA+B;AAC/B,MAAM,eAAe,KAAK,KAAK,KAAK;AACpC,MAAM,kBAAkB,IAAI;AAE5B,yDAAyD;AACzD,MAAM,gBAAgB;AAEtB,SAAS,aAAa,GAAW;IAC/B,MAAM,YAAY,gBAAgB,GAAG,CAAC;IACtC,IAAI,CAAC,WAAW,OAAO;IACvB,OAAO,KAAK,GAAG,KAAK,YAAY;AAClC;AAEA,SAAS,sBAAsB,GAAW,EAAE,KAAa;IACvD,UAAU,GAAG,CAAC,KAAK;IACnB,gBAAgB,GAAG,CAAC,KAAK,KAAK,GAAG;AACnC;AAEA,eAAe,oBAAoB,cAAsB;IACvD,MAAM,WAAW,CAAC,OAAO,EAAE,eAAe,WAAW,IAAI;IAEzD,2BAA2B;IAC3B,IAAI,YAAY,GAAG,CAAC,aAAa,aAAa,WAAW;QACvD,OAAO,YAAY,GAAG,CAAC;IACzB;IAEA,IAAI;QACF,MAAM,cAAc,mBAAmB;QACvC,MAAM,WAAW,MAAM,wIAAA,CAAA,UAAK,CAAC,GAAG,CAC9B,CAAC,8CAA8C,EAAE,aAAa,EAC9D;YAAE,SAAS;QAAK,EAAE,mBAAmB;;QAGvC,IAAI,SAAS,IAAI,CAAC,MAAM,KAAK,GAAG;YAC9B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,SAAS,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE;QAC1C,YAAY,GAAG,CAAC,UAAU;QAC1B,gBAAgB,GAAG,CAAC,UAAU,KAAK,GAAG;QAEtC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,eAAe,CAAC,CAAC,EAAE;QAC9D,MAAM;IACR;AACF;AAEO,eAAe,qBACpB,cAAsB;IAEtB,IAAI,CAAC,kBAAkB,OAAO,mBAAmB,UAAU;QACzD,OAAO;IACT;IAEA,MAAM,WAAW,eAAe,WAAW,GAAG,IAAI;IAElD,oBAAoB;IACpB,IAAI,UAAU,GAAG,CAAC,aAAa,aAAa,WAAW;QACrD,OAAO,UAAU,GAAG,CAAC;IACvB;IAEA,IAAI;QACF,MAAM,SAAS,MAAM,oBAAoB;QACzC,MAAM,gBAAgB,CAAC,0BAA0B,EAAE,OAAO,eAAe,EAAE,WAAW;QAEtF,oCAAoC;QACpC,IAAI;YACF,MAAM,wIAAA,CAAA,UAAK,CAAC,IAAI,CAAC,eAAe;gBAAE,SAAS;YAAK;YAChD,sBAAsB,UAAU;YAChC,OAAO;QACT,EAAE,OAAO,WAAW;YAClB,0CAA0C;YAC1C,sBAAsB,UAAU;YAChC,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CACV,CAAC,mCAAmC,EAAE,eAAe,CAAC,CAAC,EACvD;QAEF,yDAAyD;QACzD,sBAAsB,UAAU;QAChC,OAAO;IACT;AACF;AAGO,SAAS,uBAAuB,eAAyB;IAC9D,gBAAgB,OAAO,CAAC,CAAC;QACvB,IAAI,QAAQ,CAAC,UAAU,GAAG,CAAC,KAAK,WAAW,GAAG,IAAI,KAAK;YACrD,gCAAgC;YAChC,qBAAqB,MAAM,KAAK,CAAC;YAC/B,wCAAwC;YAC1C;QACF;IACF;AACF;AAGO,SAAS;IACd,MAAM,MAAM,KAAK,GAAG;IACpB,KAAK,MAAM,CAAC,KAAK,UAAU,IAAI,gBAAgB,OAAO,GAAI;QACxD,IAAI,MAAM,YAAY,cAAc;YAClC,UAAU,MAAM,CAAC;YACjB,YAAY,MAAM,CAAC;YACnB,gBAAgB,MAAM,CAAC;QACzB;IACF;AACF"}}, {"offset": {"line": 1454, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1460, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/explore/counselor-card.tsx"], "sourcesContent": ["\"use client\";\r\nimport { MessageCircle, GraduationCap } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport { Button } from \"../../ui/button\";\r\nimport type { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport * as CountryFlags from \"country-flag-icons/react/3x2\";\r\nimport { useEffect, useState } from \"react\";\r\nimport { SendMessageDialog } from \"../../student/sessions/send-message-dialog\";\r\n\r\nimport { useProfile } from \"@/app/hooks/student/useProfile\";\r\nimport { generatePlaceholder } from \"@/app/utils/image\";\r\nimport { getUniversityLogoUrl } from \"@/app/utils/uni-logo\";\r\n\r\nconst getCountryFlag = (countryCode: string | null = \"US\") => {\r\n  const code = (countryCode || \"US\").toUpperCase();\r\n  const FlagComponent = (CountryFlags as any)[code];\r\n  return FlagComponent ? <FlagComponent /> : <CountryFlags.US />;\r\n};\r\n\r\nexport function CounselorCard({\r\n  counselor,\r\n}: {\r\n  counselor: CounselorPublicProfile;\r\n}) {\r\n  const [showMessageDialog, setShowMessageDialog] = useState(false);\r\n  const { userInfo } = useProfile();\r\n  const [universityLogo, setUniversityLogo] = useState(\"\");\r\n\r\n  useEffect(() => {\r\n    const fetchUniversityLogo = async () => {\r\n      if (counselor.education && counselor.education.length > 0) {\r\n        const logoUrl = await getUniversityLogoUrl(\r\n          counselor.education[0].university_name\r\n        );\r\n        setUniversityLogo(logoUrl);\r\n      }\r\n    };\r\n\r\n    fetchUniversityLogo();\r\n  }, [counselor.education]);\r\n\r\n  return (\r\n    <div\r\n      className=\"overflow-hidden rounded-lg flex flex-col bg-white border transition-all duration-300 hover:shadow-xl hover:-translate-y-1 group relative cursor-pointer\"\r\n      onClick={() => window.open(`/profile/${counselor.user_id}`, \"_blank\")}\r\n    >\r\n      <div className=\"relative\">\r\n        <div className=\"h-20 relative overflow-hidden bg-white\">\r\n          <div className=\"h-6 bg-blue-950 w-full transition-colors group-hover:bg-[#9C0E22]\" />\r\n          <div className=\"relative h-full px-4\" />\r\n        </div>\r\n\r\n        {/* Profile Picture */}\r\n        <div className=\"absolute flex items-center justify-between gap-8 -bottom-8 left-4 pr-8 w-full\">\r\n          <div className=\"relative w-24 h-24 rounded-full border-3 border-white overflow-hidden shadow-lg transition-transform group-hover:scale-105\">\r\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity z-10\" />\r\n            <Image\r\n              src={\r\n                counselor.profile_picture_url ||\r\n                generatePlaceholder(counselor.first_name, counselor.last_name)\r\n              }\r\n              alt={`${counselor.first_name} ${counselor.last_name}`}\r\n              fill\r\n              sizes=\"(max-width: 768px) 128px, 128px\"\r\n              quality={95}\r\n              priority\r\n              className=\"object-cover group-hover:opacity-95 transition-opacity\"\r\n            />\r\n          </div>\r\n\r\n          {/* University Logo */}\r\n          {universityLogo && (\r\n            <div className=\"relative\">\r\n              <img\r\n                src={universityLogo}\r\n                alt={counselor.education[0].university_name}\r\n                className=\"w-[4rem] h-[4rem] border rounded-lg overflow-hidden transition-transform group-hover:scale-105\"\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"w-6 h-4 rounded-[2px] overflow-hidden shadow-sm\">\r\n            {getCountryFlag(counselor.country_of_residence)}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"p-4 pt-12 flex-1\">\r\n        <div className=\"flex flex-col justify-between h-full\">\r\n          <div className=\"flex flex-col gap-0.5\">\r\n            <h3 className=\"text-lg font-medium group-hover:text-[#9C0E22] transition-all group-hover:translate-x-0.5\">\r\n              {counselor.first_name} {counselor.last_name}\r\n            </h3>\r\n            <p className=\"mt-1 text-sm text-gray-700 line-clamp-2\">\r\n              {counselor.tagline ||\r\n                counselor.bio ||\r\n                counselor.experience_description}\r\n            </p>\r\n          </div>\r\n          <div>\r\n            {/* Education */}\r\n            {counselor.education && counselor.education.length > 0 && (\r\n              <div className=\"mt-4 flex items-center gap-2 text-sm\">\r\n                <GraduationCap className=\"w-5 h-5 text-gray-700 flex-shrink-0\" />\r\n                <p className=\"font-medium text-base text-blue-950 transition-colors\">\r\n                  {counselor.education[0].university_name}\r\n                </p>\r\n              </div>\r\n            )}\r\n\r\n            {/* Pricing */}\r\n            <div className=\"mt-4\">\r\n              <p className=\"text-sm text-gray-600\">Starts at</p>\r\n              <p className=\"font-[500] text-lg\">${counselor.hourly_rate}/hr</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"px-4 pb-4\">\r\n        <div className=\"flex flex-col sm:grid sm:grid-cols-2 gap-2\">\r\n          <Button\r\n            variant={\"outline\"}\r\n            className=\"rounded-lg text-white bg-blue-950 hover:scale-[102%] hover:bg-[#9C0E22] transition-all z-10 text-sm h-10 w-full flex items-center justify-center shadow-sm hover:shadow\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              if (!userInfo) {\r\n                window.open(\r\n                  `/auth/login?redirect=/profile/${counselor.user_id}`,\r\n                  \"_blank\"\r\n                );\r\n                return;\r\n              }\r\n              window.open(\r\n                `/profile/${counselor.user_id}?booking=true`,\r\n                \"_blank\"\r\n              );\r\n            }}\r\n          >\r\n            Book Session\r\n          </Button>\r\n          <Button\r\n            variant={\"outline\"}\r\n            className=\"rounded-lg text-white bg-blue-950 hover:scale-[102%] hover:bg-[#9C0E22] transition-all z-10 text-sm h-10 w-full flex items-center justify-center gap-2 shadow-sm hover:shadow\"\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              if (!userInfo) {\r\n                window.open(\r\n                  `/auth/login?redirect=/profile/${counselor.user_id}`,\r\n                  \"_blank\"\r\n                );\r\n                return;\r\n              }\r\n              setShowMessageDialog(true);\r\n            }}\r\n          >\r\n            Message\r\n            <MessageCircle className=\"w-4 h-4\" />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      <SendMessageDialog\r\n        open={showMessageDialog}\r\n        onOpenChange={setShowMessageDialog}\r\n        counselorInfo={counselor}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAGA;AACA;AAEA;AACA;AACA;AANA;AAJA;AAAA;;;AADA;;;;;;;;;;AAaA,MAAM,iBAAiB,CAAC,cAA6B,IAAI;IACvD,MAAM,OAAO,CAAC,eAAe,IAAI,EAAE,WAAW;IAC9C,MAAM,gBAAgB,AAAC,oKAAoB,CAAC,KAAK;IACjD,OAAO,8BAAgB,6LAAC;;;;6BAAmB,6LAAC,qKAAa,EAAE;;;;;AAC7D;AAEO,SAAS,cAAc,EAC5B,SAAS,EAGV;;IACC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAC9B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;+DAAsB;oBAC1B,IAAI,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,MAAM,GAAG,GAAG;wBACzD,MAAM,UAAU,MAAM,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD,EACvC,UAAU,SAAS,CAAC,EAAE,CAAC,eAAe;wBAExC,kBAAkB;oBACpB;gBACF;;YAEA;QACF;kCAAG;QAAC,UAAU,SAAS;KAAC;IAExB,qBACE,6LAAC;QACC,WAAU;QACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,OAAO,EAAE,EAAE;;0BAE5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KACE,UAAU,mBAAmB,IAC7B,CAAA,GAAA,wHAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,UAAU,EAAE,UAAU,SAAS;wCAE/D,KAAK,GAAG,UAAU,UAAU,CAAC,CAAC,EAAE,UAAU,SAAS,EAAE;wCACrD,IAAI;wCACJ,OAAM;wCACN,SAAS;wCACT,QAAQ;wCACR,WAAU;;;;;;;;;;;;4BAKb,gCACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,KAAK;oCACL,KAAK,UAAU,SAAS,CAAC,EAAE,CAAC,eAAe;oCAC3C,WAAU;;;;;;;;;;;0CAKhB,6LAAC;gCAAI,WAAU;0CACZ,eAAe,UAAU,oBAAoB;;;;;;;;;;;;;;;;;;0BAIpD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCACX,UAAU,UAAU;wCAAC;wCAAE,UAAU,SAAS;;;;;;;8CAE7C,6LAAC;oCAAE,WAAU;8CACV,UAAU,OAAO,IAChB,UAAU,GAAG,IACb,UAAU,sBAAsB;;;;;;;;;;;;sCAGtC,6LAAC;;gCAEE,UAAU,SAAS,IAAI,UAAU,SAAS,CAAC,MAAM,GAAG,mBACnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;sDACzB,6LAAC;4CAAE,WAAU;sDACV,UAAU,SAAS,CAAC,EAAE,CAAC,eAAe;;;;;;;;;;;;8CAM7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;;gDAAqB;gDAAE,UAAU,WAAW;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKlE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB,IAAI,CAAC,UAAU;oCACb,OAAO,IAAI,CACT,CAAC,8BAA8B,EAAE,UAAU,OAAO,EAAE,EACpD;oCAEF;gCACF;gCACA,OAAO,IAAI,CACT,CAAC,SAAS,EAAE,UAAU,OAAO,CAAC,aAAa,CAAC,EAC5C;4BAEJ;sCACD;;;;;;sCAGD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,eAAe;gCACjB,IAAI,CAAC,UAAU;oCACb,OAAO,IAAI,CACT,CAAC,8BAA8B,EAAE,UAAU,OAAO,EAAE,EACpD;oCAEF;gCACF;gCACA,qBAAqB;4BACvB;;gCACD;8CAEC,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAK/B,6LAAC,yKAAA,CAAA,oBAAiB;gBAChB,MAAM;gBACN,cAAc;gBACd,eAAe;;;;;;;;;;;;AAIvB;GApJgB;;QAMO,wIAAA,CAAA,aAAU;;;KANjB"}}, {"offset": {"line": 1809, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1815, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/logo-loader.tsx"], "sourcesContent": ["import Image from \"next/image\";\r\n\r\ninterface LogoLoaderProps {\r\n  size?: number;\r\n  text?: string;\r\n}\r\n\r\nexport function LogoLoader({ size = 40, text }: LogoLoaderProps) {\r\n  return (\r\n    <div className=\"flex flex-col justify-center items-center h-max gap-y-4\">\r\n      <div className=\"relative inline-flex items-center justify-center p-2\">\r\n        <Image\r\n          src=\"/icons/logo.png\"\r\n          alt=\"logo\"\r\n          width={size}\r\n          height={size}\r\n          className=\"rounded-full\"\r\n        />\r\n        <div\r\n          className=\"absolute rounded-full border-4 border-t-transparent animate-spin\"\r\n          style={{\r\n            width: \"120%\",\r\n            height: \"120%\",\r\n            borderColor: \"rgba(239, 68, 68, 0.2)\",\r\n            borderTopColor: \"rgb(239, 68, 68)\",\r\n          }}\r\n        />\r\n      </div>\r\n      {text && (\r\n        <h2 className=\"text-lg text-center font-medium text-gray-600\">\r\n          {text}\r\n        </h2>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAOO,SAAS,WAAW,EAAE,OAAO,EAAE,EAAE,IAAI,EAAmB;IAC7D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;;kCAEZ,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,QAAQ;4BACR,aAAa;4BACb,gBAAgB;wBAClB;;;;;;;;;;;;YAGH,sBACC,6LAAC;gBAAG,WAAU;0BACX;;;;;;;;;;;;AAKX;KA5BgB"}}, {"offset": {"line": 1880, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1886, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/explore/CounselorCardList.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport { CounselorC<PERSON> } from \"./counselor-card\";\r\nimport { CounselorPublicProfile } from \"@/app/types/counselor\";\r\nimport { LogoLoader } from \"@/app/components/ui/logo-loader\";\r\n\r\ninterface CounselorCardListProps {\r\n  loading: boolean;\r\n  counselors: CounselorPublicProfile[] | null;\r\n}\r\n\r\nconst CounselorCardList: React.FC<CounselorCardListProps> = ({\r\n  loading,\r\n  counselors,\r\n}) => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4\">\r\n      {loading || counselors === null ? (\r\n        // Loading state\r\n        <div className=\"col-span-full flex justify-center items-center py-12\">\r\n          <LogoLoader size={50} text=\"Loading counselors...\" />\r\n        </div>\r\n      ) : counselors.length === 0 ? (\r\n        // No counselors found\r\n        <div className=\"col-span-full text-center py-8\">\r\n          <h2 className=\"text-xl font-medium mb-2\">No counselors found</h2>\r\n          <p className=\"text-gray-600\">\r\n            There are no verified counselors matching your criteria. Try\r\n            adjusting your filters.\r\n          </p>\r\n        </div>\r\n      ) : (\r\n        // Show existing counselors\r\n        counselors.map((counselor) => (\r\n          <CounselorCard key={counselor.user_id} counselor={counselor} />\r\n        ))\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CounselorCardList;\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;;;;AAOA,MAAM,oBAAsD,CAAC,EAC3D,OAAO,EACP,UAAU,EACX;IACC,qBACE,6LAAC;QAAI,WAAU;kBACZ,WAAW,eAAe,OACzB,gBAAgB;sBAChB,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6IAAA,CAAA,aAAU;gBAAC,MAAM;gBAAI,MAAK;;;;;;;;;;mBAE3B,WAAW,MAAM,KAAK,IACxB,sBAAsB;sBACtB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA2B;;;;;;8BACzC,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;mBAM/B,2BAA2B;QAC3B,WAAW,GAAG,CAAC,CAAC,0BACd,6LAAC,+JAAA,CAAA,gBAAa;gBAAyB,WAAW;eAA9B,UAAU,OAAO;;;;;;;;;;AAK/C;KA5BM;uCA8BS"}}, {"offset": {"line": 1959, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1965, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/hooks/student/useDebounce.ts"], "sourcesContent": ["import { useState, useEffect } from \"react\";\r\n\r\nexport function useDebounce<T>(value: T, delay: number): T {\r\n  const [debouncedValue, setDebouncedValue] = useState<T>(value);\r\n\r\n  useEffect(() => {\r\n    const handler = setTimeout(() => {\r\n      setDebouncedValue(value);\r\n    }, delay);\r\n\r\n    return () => {\r\n      clearTimeout(handler);\r\n    };\r\n  }, [value, delay]);\r\n\r\n  return debouncedValue;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;;AAEO,SAAS,YAAe,KAAQ,EAAE,KAAa;;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,UAAU;iDAAW;oBACzB,kBAAkB;gBACpB;gDAAG;YAEH;yCAAO;oBACL,aAAa;gBACf;;QACF;gCAAG;QAAC;QAAO;KAAM;IAEjB,OAAO;AACT;GAdgB"}}, {"offset": {"line": 1997, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2003, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@components/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80\",\r\n        outline: \"text-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface BadgeProps\r\n  extends React.HTMLAttributes<HTMLDivElement>,\r\n    VariantProps<typeof badgeVariants> {}\r\n\r\nfunction Badge({ className, variant, ...props }: BadgeProps) {\r\n  return (\r\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\r\n  )\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS"}}, {"offset": {"line": 2045, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2051, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@components/lib/utils\";\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,oIAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 2081, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2087, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/public/explore/filters-sidebar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useCounselors } from \"@/app/hooks/public/useCounselors\";\r\nimport { useDebounce } from \"@/app/hooks/student/useDebounce\";\r\nimport { Badge } from \"@components/ui/badge\";\r\nimport { Button } from \"@components/ui/button\";\r\nimport { Input } from \"@components/ui/input\";\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON>etHeader,\r\n  SheetT<PERSON>le,\r\n  Sheet<PERSON>rigger,\r\n} from \"@components/ui/sheet\";\r\nimport { ChevronDown, FilterIcon, Search, X } from \"lucide-react\";\r\nimport { useEffect, useMemo, useState, useRef } from \"react\";\r\n\r\n// Country mapping with display name to country code\r\nconst COUNTRY_MAPPING = {\r\n  \"United States\": \"US\",\r\n  \"United Kingdom\": \"GB\",\r\n  Canada: \"CA\",\r\n  Australia: \"AU\",\r\n  India: \"IN\",\r\n  Singapore: \"SG\",\r\n  \"Hong Kong\": \"HK\",\r\n} as const;\r\n\r\n// Reverse mapping for display purposes\r\nconst COUNTRY_CODE_TO_NAME = Object.entries(COUNTRY_MAPPING).reduce(\r\n  (acc, [name, code]) => {\r\n    acc[code] = name;\r\n    return acc;\r\n  },\r\n  {} as Record<string, string>\r\n);\r\n\r\n// Available filters configuration\r\nconst SERVICES = [\r\n  \"Free 15 Minutes Intro Call\",\r\n  \"Personal Statement Guidance\",\r\n  \"Essay Review\",\r\n  \"University Shortlisting\",\r\n  \"Extra Curricular Profile Building\",\r\n  \"Supplementary Essay Guidance\",\r\n  \"Financial Aid Advice\",\r\n  \"Interview Preparation\",\r\n] as const;\r\n\r\n// Use the keys from the mapping for type safety\r\nconst COUNTRIES = Object.keys(COUNTRY_MAPPING) as readonly string[];\r\n\r\nconst UNIVERSITIES = [\r\n  \"University of Oxford\",\r\n  \"University of Cambridge\",\r\n  \"Harvard University\",\r\n  \"Stanford University\",\r\n  \"Yale University\",\r\n  \"University of Pennsylvania\",\r\n  \"University of British Columbia\",\r\n  \"University of Toronto\",\r\n  \"Princeton University\",\r\n  \"Brown University\",\r\n  \"Rice University\",\r\n  \"Bryn Mawr College\",\r\n  \"Colby College\",\r\n  \"Colorado College\",\r\n  \"Imperial College London\",\r\n  \"Yale-NUS\",\r\n  \"NUS\",\r\n] as const;\r\n\r\nexport function FiltersSidebar() {\r\n  const [isOpen, setIsOpen] = useState<Record<string, boolean>>({\r\n    services: true,\r\n    countries: false,\r\n    universities: false,\r\n    pricing: true,\r\n  });\r\n\r\n  const {\r\n    filters: { service_type, country, university, name },\r\n    setFilters,\r\n    fetchAllCounselors,\r\n    setLoading,\r\n  } = useCounselors();\r\n\r\n  // Keep using the ref approach\r\n  const searchInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Create a memoized filters object\r\n  const currentFilters = useMemo(\r\n    () => ({\r\n      service_type,\r\n      country,\r\n      university,\r\n      name,\r\n    }),\r\n    [service_type, country, university, name]\r\n  );\r\n\r\n  // Debounce filter changes\r\n  const debouncedFilters = useDebounce(currentFilters, 1000);\r\n\r\n  // Set initial search value in input field\r\n  useEffect(() => {\r\n    if (name && searchInputRef.current) {\r\n      searchInputRef.current.value = name;\r\n    }\r\n  }, []);\r\n\r\n  // Update filters when debounced values change\r\n  useEffect(() => {\r\n    setFilters({\r\n      ...debouncedFilters,\r\n    });\r\n    fetchAllCounselors();\r\n  }, [debouncedFilters, setFilters, fetchAllCounselors]);\r\n\r\n  // Helper function to toggle an item in an array\r\n  const toggleArrayItem = (\r\n    array: string[] | undefined,\r\n    item: string\r\n  ): string[] => {\r\n    if (!array) return [item];\r\n    return array.includes(item)\r\n      ? array.filter((i) => i !== item)\r\n      : [...array, item];\r\n  };\r\n\r\n  // Convert country display name to code\r\n  const getCountryCode = (countryName: string): string => {\r\n    return (\r\n      COUNTRY_MAPPING[countryName as keyof typeof COUNTRY_MAPPING] ||\r\n      countryName\r\n    );\r\n  };\r\n\r\n  // Update local state immediately without triggering API calls\r\n  const updateFilters = (\r\n    newFilter: Partial<{\r\n      service_type: string[] | undefined;\r\n      country: string[] | undefined;\r\n      university: string[] | undefined;\r\n      min_hourly_rate: number;\r\n      max_hourly_rate: number;\r\n      name: string;\r\n    }>\r\n  ) => {\r\n    setLoading(true);\r\n    const updatedFilters = {\r\n      service_type,\r\n      country,\r\n      university,\r\n      name,\r\n      ...newFilter,\r\n    };\r\n\r\n    // Update global filters state\r\n    setFilters(updatedFilters);\r\n  };\r\n\r\n  // Toggle a service type in the filters\r\n  const toggleServiceType = (serviceItem: string) => {\r\n    updateFilters({\r\n      service_type: toggleArrayItem(service_type as string[], serviceItem),\r\n    });\r\n  };\r\n\r\n  // Toggle a country in the filters, converting display name to code\r\n  const toggleCountry = (countryItem: string) => {\r\n    const countryCode = getCountryCode(countryItem);\r\n    updateFilters({\r\n      country: toggleArrayItem(country as string[], countryCode),\r\n    });\r\n  };\r\n\r\n  // Toggle a university in the filters\r\n  const toggleUniversity = (universityItem: string) => {\r\n    updateFilters({\r\n      university: toggleArrayItem(university as string[], universityItem),\r\n    });\r\n  };\r\n\r\n  // Toggle sections\r\n  const toggleSection = (section: string) => {\r\n    setIsOpen((prev) => ({ ...prev, [section]: !prev[section] }));\r\n  };\r\n\r\n  // Handle search submit\r\n  const handleSearch = () => {\r\n    setLoading(true);\r\n    const searchValue = searchInputRef.current?.value || \"\";\r\n\r\n    // Important: Don't update the filter if it's the same as current name\r\n    // This prevents unnecessary re-renders\r\n    if (searchValue !== name) {\r\n      updateFilters({\r\n        name: searchValue,\r\n      });\r\n    } else {\r\n      // Just fetch counselors if the search value is unchanged\r\n      fetchAllCounselors();\r\n    }\r\n  };\r\n\r\n  // Clear all filters\r\n  const clearFilters = () => {\r\n    // Clear the input field directly\r\n    if (searchInputRef.current) {\r\n      searchInputRef.current.value = \"\";\r\n    }\r\n\r\n    const emptyFilters = {\r\n      service_type: undefined,\r\n      country: undefined,\r\n      university: undefined,\r\n      name: \"\",\r\n    };\r\n    setFilters(emptyFilters);\r\n    fetchAllCounselors();\r\n  };\r\n\r\n  // Count active filters\r\n  const activeFilterCount =\r\n    (service_type?.length || 0) +\r\n    (country?.length || 0) +\r\n    (university?.length || 0) +\r\n    (name ? 1 : 0);\r\n\r\n  const FilterContent = () => (\r\n    <div className=\"space-y-4\">\r\n      {/* Name Search Input with button on right side */}\r\n      <div className=\"relative flex items-center\">\r\n        <Input\r\n          ref={searchInputRef}\r\n          type=\"text\"\r\n          placeholder=\"Search counselors...\"\r\n          className=\"pr-12 w-full\"\r\n          defaultValue={name || \"\"}\r\n          onKeyDown={(e) => {\r\n            if (e.key === \"Enter\") {\r\n              handleSearch();\r\n            }\r\n          }}\r\n        />\r\n        <Button\r\n          type=\"button\"\r\n          variant=\"ghost\"\r\n          size=\"icon\"\r\n          className=\"absolute right-0 top-0 h-full px-3\"\r\n          onClick={handleSearch}\r\n        >\r\n          <Search className=\"h-4 w-4\" />\r\n        </Button>\r\n      </div>\r\n\r\n      {/* Services Section */}\r\n      <div className=\"border-b border-gray-100\">\r\n        <button\r\n          onClick={() => toggleSection(\"services\")}\r\n          className=\"flex w-full items-center justify-between py-4 text-left\"\r\n        >\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-base\">Services</span>\r\n            {service_type && service_type.length > 0 && (\r\n              <Badge variant=\"secondary\" className=\"h-5 px-2\">\r\n                {service_type.length}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n          <ChevronDown\r\n            className={`h-4 w-4 transition-transform ${\r\n              isOpen.services ? \"rotate-180\" : \"\"\r\n            }`}\r\n          />\r\n        </button>\r\n\r\n        {isOpen.services && (\r\n          <div className=\"pb-4\">\r\n            <div className=\"space-y-2\">\r\n              {SERVICES.map((serviceItem) => (\r\n                <label\r\n                  key={serviceItem}\r\n                  className=\"flex items-center gap-2 rounded-lg p-2 hover:bg-gray-50 cursor-pointer\"\r\n                >\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    name=\"service_type\"\r\n                    checked={service_type?.includes(serviceItem)}\r\n                    onChange={() => toggleServiceType(serviceItem)}\r\n                    className=\"h-4 w-4 rounded border-gray-300\"\r\n                  />\r\n                  <span className=\"text-sm\">{serviceItem}</span>\r\n                </label>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Countries Section */}\r\n      <div className=\"border-b border-gray-100\">\r\n        <button\r\n          onClick={() => toggleSection(\"countries\")}\r\n          className=\"flex w-full items-center justify-between py-4 text-left\"\r\n        >\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-base\">Countries</span>\r\n            {country && country.length > 0 && (\r\n              <Badge variant=\"secondary\" className=\"h-5 px-2\">\r\n                {country.length}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n          <ChevronDown\r\n            className={`h-4 w-4 transition-transform ${\r\n              isOpen.countries ? \"rotate-180\" : \"\"\r\n            }`}\r\n          />\r\n        </button>\r\n\r\n        {isOpen.countries && (\r\n          <div className=\"pb-4\">\r\n            <div className=\"space-y-2\">\r\n              {COUNTRIES.map((countryItem) => (\r\n                <label\r\n                  key={countryItem}\r\n                  className=\"flex items-center gap-2 rounded-lg p-2 hover:bg-gray-50 cursor-pointer\"\r\n                >\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={country?.includes(getCountryCode(countryItem))}\r\n                    onChange={() => toggleCountry(countryItem)}\r\n                    className=\"h-4 w-4 rounded border-gray-300\"\r\n                  />\r\n                  <span className=\"text-sm\">{countryItem}</span>\r\n                </label>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Universities Section */}\r\n      <div className=\"border-b border-gray-100\">\r\n        <button\r\n          onClick={() => toggleSection(\"universities\")}\r\n          className=\"flex w-full items-center justify-between py-4 text-left\"\r\n        >\r\n          <div className=\"flex items-center gap-2\">\r\n            <span className=\"text-base\">Universities</span>\r\n            {university && university.length > 0 && (\r\n              <Badge variant=\"secondary\" className=\"h-5 px-2\">\r\n                {university.length}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n          <ChevronDown\r\n            className={`h-4 w-4 transition-transform ${\r\n              isOpen.universities ? \"rotate-180\" : \"\"\r\n            }`}\r\n          />\r\n        </button>\r\n\r\n        {isOpen.universities && (\r\n          <div className=\"pb-4\">\r\n            <div className=\"space-y-2\">\r\n              {UNIVERSITIES.map((universityItem) => (\r\n                <label\r\n                  key={universityItem}\r\n                  className=\"flex items-center gap-2 rounded-lg p-2 hover:bg-gray-50 cursor-pointer\"\r\n                >\r\n                  <input\r\n                    type=\"checkbox\"\r\n                    checked={university?.includes(universityItem)}\r\n                    onChange={() => toggleUniversity(universityItem)}\r\n                    className=\"h-4 w-4 rounded border-gray-300\"\r\n                  />\r\n                  <span className=\"text-sm\">{universityItem}</span>\r\n                </label>\r\n              ))}\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {activeFilterCount > 0 && (\r\n        <div className=\"flex items-center justify-between pt-4\">\r\n          <button\r\n            onClick={clearFilters}\r\n            className=\"flex items-center gap-2 text-sm text-gray-500\"\r\n          >\r\n            <X className=\"h-4 w-4\" />\r\n            Clear filters\r\n          </button>\r\n          <span className=\"text-sm text-gray-500\">\r\n            {activeFilterCount} selected\r\n          </span>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <>\r\n      {/* Desktop sidebar */}\r\n      <div className=\"hidden border-r border-gray-100 lg:block\">\r\n        <div className=\"sticky top-8 w-64 px-4\">\r\n          <FilterContent />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mobile sidebar */}\r\n      <Sheet>\r\n        <SheetTrigger asChild>\r\n          <Button variant=\"outline\" size=\"sm\" className=\"lg:hidden\">\r\n            <FilterIcon className=\"mr-2 h-4 w-4\" />\r\n            Filters\r\n            {activeFilterCount > 0 && (\r\n              <Badge variant=\"secondary\" className=\"ml-2 h-5 px-2\">\r\n                {activeFilterCount}\r\n              </Badge>\r\n            )}\r\n          </Button>\r\n        </SheetTrigger>\r\n        <SheetContent side=\"left\" className=\"overflow-y-auto\">\r\n          <SheetHeader>\r\n            <SheetTitle>Filters</SheetTitle>\r\n          </SheetHeader>\r\n          <div className=\"mt-4\">\r\n            <FilterContent />\r\n          </div>\r\n        </SheetContent>\r\n      </Sheet>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAQA;AADA;AAAA;AAAA;AAAA;;;AAdA;;;;;;;;;AAiBA,oDAAoD;AACpD,MAAM,kBAAkB;IACtB,iBAAiB;IACjB,kBAAkB;IAClB,QAAQ;IACR,WAAW;IACX,OAAO;IACP,WAAW;IACX,aAAa;AACf;AAEA,uCAAuC;AACvC,MAAM,uBAAuB,OAAO,OAAO,CAAC,iBAAiB,MAAM,MACjE,CAAC,KAAK,CAAC,MAAM,KAAK;IAChB,GAAG,CAAC,KAAK,GAAG;IACZ,OAAO;AACT,GACA,CAAC;;AAGH,kCAAkC;AAClC,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,gDAAgD;AAChD,MAAM,YAAY,OAAO,IAAI,CAAC;;AAE9B,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;QAC5D,UAAU;QACV,WAAW;QACX,cAAc;QACd,SAAS;IACX;IAEA,MAAM,EACJ,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,EACpD,UAAU,EACV,kBAAkB,EAClB,UAAU,EACX,GAAG,CAAA,GAAA,2IAAA,CAAA,gBAAa,AAAD;IAEhB,8BAA8B;IAC9B,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAEhD,mCAAmC;IACnC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;kDAC3B,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;YACF,CAAC;iDACD;QAAC;QAAc;QAAS;QAAY;KAAK;IAG3C,0BAA0B;IAC1B,MAAM,mBAAmB,CAAA,GAAA,yIAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB;IAErD,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,QAAQ,eAAe,OAAO,EAAE;gBAClC,eAAe,OAAO,CAAC,KAAK,GAAG;YACjC;QACF;mCAAG,EAAE;IAEL,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,WAAW;gBACT,GAAG,gBAAgB;YACrB;YACA;QACF;mCAAG;QAAC;QAAkB;QAAY;KAAmB;IAErD,gDAAgD;IAChD,MAAM,kBAAkB,CACtB,OACA;QAEA,IAAI,CAAC,OAAO,OAAO;YAAC;SAAK;QACzB,OAAO,MAAM,QAAQ,CAAC,QAClB,MAAM,MAAM,CAAC,CAAC,IAAM,MAAM,QAC1B;eAAI;YAAO;SAAK;IACtB;IAEA,uCAAuC;IACvC,MAAM,iBAAiB,CAAC;QACtB,OACE,eAAe,CAAC,YAA4C,IAC5D;IAEJ;IAEA,8DAA8D;IAC9D,MAAM,gBAAgB,CACpB;QASA,WAAW;QACX,MAAM,iBAAiB;YACrB;YACA;YACA;YACA;YACA,GAAG,SAAS;QACd;QAEA,8BAA8B;QAC9B,WAAW;IACb;IAEA,uCAAuC;IACvC,MAAM,oBAAoB,CAAC;QACzB,cAAc;YACZ,cAAc,gBAAgB,cAA0B;QAC1D;IACF;IAEA,mEAAmE;IACnE,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAc,eAAe;QACnC,cAAc;YACZ,SAAS,gBAAgB,SAAqB;QAChD;IACF;IAEA,qCAAqC;IACrC,MAAM,mBAAmB,CAAC;QACxB,cAAc;YACZ,YAAY,gBAAgB,YAAwB;QACtD;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,CAAC;QACrB,UAAU,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,QAAQ,EAAE,CAAC,IAAI,CAAC,QAAQ;YAAC,CAAC;IAC7D;IAEA,uBAAuB;IACvB,MAAM,eAAe;QACnB,WAAW;QACX,MAAM,cAAc,eAAe,OAAO,EAAE,SAAS;QAErD,sEAAsE;QACtE,uCAAuC;QACvC,IAAI,gBAAgB,MAAM;YACxB,cAAc;gBACZ,MAAM;YACR;QACF,OAAO;YACL,yDAAyD;YACzD;QACF;IACF;IAEA,oBAAoB;IACpB,MAAM,eAAe;QACnB,iCAAiC;QACjC,IAAI,eAAe,OAAO,EAAE;YAC1B,eAAe,OAAO,CAAC,KAAK,GAAG;QACjC;QAEA,MAAM,eAAe;YACnB,cAAc;YACd,SAAS;YACT,YAAY;YACZ,MAAM;QACR;QACA,WAAW;QACX;IACF;IAEA,uBAAuB;IACvB,MAAM,oBACJ,CAAC,cAAc,UAAU,CAAC,IAC1B,CAAC,SAAS,UAAU,CAAC,IACrB,CAAC,YAAY,UAAU,CAAC,IACxB,CAAC,OAAO,IAAI,CAAC;IAEf,MAAM,gBAAgB,kBACpB,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,oIAAA,CAAA,QAAK;4BACJ,KAAK;4BACL,MAAK;4BACL,aAAY;4BACZ,WAAU;4BACV,cAAc,QAAQ;4BACtB,WAAW,CAAC;gCACV,IAAI,EAAE,GAAG,KAAK,SAAS;oCACrB;gCACF;4BACF;;;;;;sCAEF,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;sCAET,cAAA,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKtB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,cAAc;4BAC7B,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAY;;;;;;wCAC3B,gBAAgB,aAAa,MAAM,GAAG,mBACrC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAClC,aAAa,MAAM;;;;;;;;;;;;8CAI1B,6LAAC,uNAAA,CAAA,cAAW;oCACV,WAAW,CAAC,6BAA6B,EACvC,OAAO,QAAQ,GAAG,eAAe,IACjC;;;;;;;;;;;;wBAIL,OAAO,QAAQ,kBACd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,4BACb,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,SAAS,cAAc,SAAS;gDAChC,UAAU,IAAM,kBAAkB;gDAClC,WAAU;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;uCAVtB;;;;;;;;;;;;;;;;;;;;;8BAmBjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,cAAc;4BAC7B,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAY;;;;;;wCAC3B,WAAW,QAAQ,MAAM,GAAG,mBAC3B,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAClC,QAAQ,MAAM;;;;;;;;;;;;8CAIrB,6LAAC,uNAAA,CAAA,cAAW;oCACV,WAAW,CAAC,6BAA6B,EACvC,OAAO,SAAS,GAAG,eAAe,IAClC;;;;;;;;;;;;wBAIL,OAAO,SAAS,kBACf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,4BACd,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDACC,MAAK;gDACL,SAAS,SAAS,SAAS,eAAe;gDAC1C,UAAU,IAAM,cAAc;gDAC9B,WAAU;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;uCATtB;;;;;;;;;;;;;;;;;;;;;8BAkBjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,cAAc;4BAC7B,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAY;;;;;;wCAC3B,cAAc,WAAW,MAAM,GAAG,mBACjC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAClC,WAAW,MAAM;;;;;;;;;;;;8CAIxB,6LAAC,uNAAA,CAAA,cAAW;oCACV,WAAW,CAAC,6BAA6B,EACvC,OAAO,YAAY,GAAG,eAAe,IACrC;;;;;;;;;;;;wBAIL,OAAO,YAAY,kBAClB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,+BACjB,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDACC,MAAK;gDACL,SAAS,YAAY,SAAS;gDAC9B,UAAU,IAAM,iBAAiB;gDACjC,WAAU;;;;;;0DAEZ,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;uCATtB;;;;;;;;;;;;;;;;;;;;;gBAiBhB,oBAAoB,mBACnB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG3B,6LAAC;4BAAK,WAAU;;gCACb;gCAAkB;;;;;;;;;;;;;;;;;;;IAO7B,qBACE;;0BAEE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;;;;;;;;;;;;;;;0BAKL,6LAAC,oIAAA,CAAA,QAAK;;kCACJ,6LAAC,oIAAA,CAAA,eAAY;wBAAC,OAAO;kCACnB,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAK,WAAU;;8CAC5C,6LAAC,6MAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAiB;gCAEtC,oBAAoB,mBACnB,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAClC;;;;;;;;;;;;;;;;;kCAKT,6LAAC,oIAAA,CAAA,eAAY;wBAAC,MAAK;wBAAO,WAAU;;0CAClC,6LAAC,oIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC,oIAAA,CAAA,aAAU;8CAAC;;;;;;;;;;;0CAEd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;;;;;;;;;;;;;;;;;;;;;;;;AAMb;GA7WgB;;QAaV,2IAAA,CAAA,gBAAa;QAiBQ,yIAAA,CAAA,cAAW;;;MA9BtB"}}, {"offset": {"line": 2782, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2788, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/%28landing%29/explore/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useCounselors } from \"@/app/hooks/public/useCounselors\";\r\nimport CounselorCardList from \"@components/public/explore/CounselorCardList\";\r\nimport { FiltersSidebar } from \"@components/public/explore/filters-sidebar\";\r\nimport { useEffect, useRef,  useMemo } from \"react\";\r\n\r\nexport default function ExplorePage() {\r\n  const {\r\n    loading: counselorsLoading,\r\n    fetchAllCounselors,\r\n    counselors,\r\n    loadMore,\r\n    hasMore,\r\n    clearFilters,\r\n  } = useCounselors();\r\n\r\n  const observerTarget = useRef<HTMLDivElement>(null);\r\n  const scrollPositionRef = useRef(0);\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n\r\n  // Memoize the observer callback to prevent unnecessary re-renders\r\n  const handleObserver = useMemo(\r\n    () =>\r\n      function (entries: IntersectionObserverEntry[]) {\r\n        const [entry] = entries;\r\n        if (entry.isIntersecting && hasMore && !counselorsLoading) {\r\n          // Save current scroll position before loading more\r\n          if (contentRef.current) {\r\n            scrollPositionRef.current = window.scrollY;\r\n          }\r\n          loadMore();\r\n        }\r\n      },\r\n    [hasMore, counselorsLoading, loadMore]\r\n  );\r\n\r\n  useEffect(() => {\r\n    const element = observerTarget.current;\r\n    if (!element) return;\r\n\r\n    const observer = new IntersectionObserver(handleObserver, {\r\n      root: null,\r\n      rootMargin: \"200px\", // Increased margin to load earlier\r\n      threshold: 0.1,\r\n    });\r\n\r\n    observer.observe(element);\r\n\r\n    return () => {\r\n      if (element) observer.unobserve(element);\r\n    };\r\n  }, [handleObserver]);\r\n\r\n  // Restore scroll position after new content is loaded\r\n  useEffect(() => {\r\n    if (!counselorsLoading && scrollPositionRef.current > 0) {\r\n      window.scrollTo(0, scrollPositionRef.current);\r\n    }\r\n  }, [counselors, counselorsLoading]);\r\n\r\n  // Track if initial fetch has been done\r\n  const initialFetchRef = useRef(false);\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        if (!initialFetchRef.current) {\r\n          await fetchAllCounselors();\r\n          initialFetchRef.current = true;\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching counselors:\", error);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [fetchAllCounselors]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      clearFilters();\r\n    };\r\n  }, [clearFilters]);\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-white px-4 py-8 sm:px-8 md:px-12 lg:px-20 font-clash-display my-6 sm:my-8 lg:my-10\">\r\n      <h1 className=\"md:mb-12 mb-8 text-center text-3xl md:text-4xl font-[500]\">\r\n        Explore Counselors\r\n      </h1>\r\n      <div className=\"flex flex-col md:flex-row gap-x-8 gap-y-4\">\r\n        <FiltersSidebar />\r\n        <div ref={contentRef} className=\"flex-1 flex flex-col gap-8\">\r\n          <CounselorCardList\r\n            counselors={counselors}\r\n            loading={counselorsLoading}\r\n          />\r\n          {hasMore && (\r\n            <div\r\n              ref={observerTarget}\r\n              className=\"h-10 flex items-center justify-center\"\r\n            >\r\n              {counselorsLoading && (\r\n                <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900\" />\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;AAJA;;;;;AAMe,SAAS;;IACtB,MAAM,EACJ,SAAS,iBAAiB,EAC1B,kBAAkB,EAClB,UAAU,EACV,QAAQ,EACR,OAAO,EACP,YAAY,EACb,GAAG,CAAA,GAAA,2IAAA,CAAA,gBAAa,AAAD;IAEhB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACjC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,kEAAkE;IAClE,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAC3B;uDACE,SAAU,OAAoC;oBAC5C,MAAM,CAAC,MAAM,GAAG;oBAChB,IAAI,MAAM,cAAc,IAAI,WAAW,CAAC,mBAAmB;wBACzD,mDAAmD;wBACnD,IAAI,WAAW,OAAO,EAAE;4BACtB,kBAAkB,OAAO,GAAG,OAAO,OAAO;wBAC5C;wBACA;oBACF;gBACF;;8CACF;QAAC;QAAS;QAAmB;KAAS;IAGxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,UAAU,eAAe,OAAO;YACtC,IAAI,CAAC,SAAS;YAEd,MAAM,WAAW,IAAI,qBAAqB,gBAAgB;gBACxD,MAAM;gBACN,YAAY;gBACZ,WAAW;YACb;YAEA,SAAS,OAAO,CAAC;YAEjB;yCAAO;oBACL,IAAI,SAAS,SAAS,SAAS,CAAC;gBAClC;;QACF;gCAAG;QAAC;KAAe;IAEnB,sDAAsD;IACtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,CAAC,qBAAqB,kBAAkB,OAAO,GAAG,GAAG;gBACvD,OAAO,QAAQ,CAAC,GAAG,kBAAkB,OAAO;YAC9C;QACF;gCAAG;QAAC;QAAY;KAAkB;IAElC,uCAAuC;IACvC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;mDAAY;oBAChB,IAAI;wBACF,IAAI,CAAC,gBAAgB,OAAO,EAAE;4BAC5B,MAAM;4BACN,gBAAgB,OAAO,GAAG;wBAC5B;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,8BAA8B;oBAC9C;gBACF;;YAEA;QACF;gCAAG;QAAC;KAAmB;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;yCAAO;oBACL;gBACF;;QACF;gCAAG;QAAC;KAAa;IAEjB,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA4D;;;;;;0BAG1E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,gKAAA,CAAA,iBAAc;;;;;kCACf,6LAAC;wBAAI,KAAK;wBAAY,WAAU;;0CAC9B,6LAAC,+JAAA,CAAA,UAAiB;gCAChB,YAAY;gCACZ,SAAS;;;;;;4BAEV,yBACC,6LAAC;gCACC,KAAK;gCACL,WAAU;0CAET,mCACC,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;GAxGwB;;QAQlB,2IAAA,CAAA,gBAAa;;;KARK"}}, {"offset": {"line": 2966, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}