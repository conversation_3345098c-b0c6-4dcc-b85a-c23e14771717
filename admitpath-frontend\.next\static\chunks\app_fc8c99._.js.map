{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/common/loadingSpinner/index.tsx"], "sourcesContent": ["export default function LoadingSpinner() {\r\n  return (\r\n    <div role=\"status\" className=\"absolute left-1/2 -translate-x-1/2 pt-4\">\r\n      <svg\r\n        aria-hidden=\"true\"\r\n        className=\"w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600\"\r\n        viewBox=\"0 0 100 101\"\r\n        fill=\"none\"\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n      >\r\n        <path\r\n          d=\"M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z\"\r\n          fill=\"currentColor\"\r\n        />\r\n        <path\r\n          d=\"M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z\"\r\n          fill=\"currentFill\"\r\n        />\r\n      </svg>\r\n      <span className=\"sr-only\">Loading...</span>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,6LAAC;QAAI,MAAK;QAAS,WAAU;;0BAC3B,6LAAC;gBACC,eAAY;gBACZ,WAAU;gBACV,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAEP,6LAAC;wBACC,GAAE;wBACF,MAAK;;;;;;;;;;;;0BAGT,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;KAtBwB"}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/work/admitpath/admitpath-frontend/app/components/counselor/profile/create/FinalCTA.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Image from \"next/image\";\r\nimport { useState } from \"react\";\r\nimport apiClient from \"@/lib/apiClient\";\r\nimport { toast } from \"react-toastify\";\r\nimport LoadingSpinner from \"@components/common/loadingSpinner\";\r\nimport { usePathname, useRouter } from \"next/navigation\";\r\n\r\nconst CompletionMessage = () => (\r\n  <div className=\"min-h-[80vh] flex items-center justify-center p-4 sm:p-6 md:p-8 bg-gray-50\">\r\n    <div className=\"max-w-2xl w-full bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all animate-fadeIn\">\r\n      {/* Top Section with Gradient */}\r\n      <div className=\"relative bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-12 text-center\">\r\n        {/* Decorative top border */}\r\n        <div className=\"absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#2F80ED] to-blue-400\" />\r\n        \r\n        {/* Success Icon and Title */}\r\n        <div className=\"mb-8\">\r\n          <div className=\"relative w-28 h-28 mx-auto mb-8 transform transition-all duration-500 hover:scale-105\">\r\n            <Image\r\n              src=\"/images/011---Approved-Cleaning.png\"\r\n              alt=\"Success\"\r\n              fill\r\n              className=\"object-contain drop-shadow-md\"\r\n              priority\r\n            />\r\n          </div>\r\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-gray-900 mb-3 tracking-tight\">\r\n            Your profile has been{\" \"}\r\n            <span className=\"text-[#2F80ED] relative\">\r\n              submitted\r\n              <span className=\"absolute bottom-0 left-0 w-full h-1 bg-[#2F80ED]/10\"></span>\r\n            </span>\r\n          </h2>\r\n          <p className=\"text-gray-600 text-lg sm:text-xl font-medium\">\r\n            You have successfully completed the application form\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Content Section */}\r\n      <div className=\"px-6 py-10 sm:px-12 sm:py-12\">\r\n        <div className=\"space-y-8 text-center sm:text-left max-w-xl mx-auto\">\r\n          <h4 className=\"text-2xl font-semibold text-gray-900\">\r\n            Thank you for applying to become a counselor\r\n          </h4>\r\n          <div className=\"prose prose-lg prose-blue max-w-none\">\r\n            <p className=\"text-gray-600 leading-relaxed\">\r\n              Our team will review your application and get back to you within{\" \"}\r\n              <span className=\"font-semibold text-gray-900\">1-3 business days</span>.\r\n              {\" \"}If approved, you'll gain access to your dashboard to start mentoring\r\n              students.\r\n            </p>\r\n            <p className=\"text-gray-600 mt-4\">\r\n              Please feel free to reach out in the meantime if you have any questions.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n);\r\n\r\nexport default function FinalCTA({\r\n  selectedMenuIndex = 0,\r\n  onMenuSelect = () => {},\r\n}: {\r\n  selectedMenuIndex?: number;\r\n  onMenuSelect?: (index: number) => void;\r\n}) {\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n\r\n  // If we're on the profile-complete page, show completion message directly\r\n  if (pathname === \"/counselor/profile-complete\") {\r\n    return <CompletionMessage />;\r\n  }\r\n\r\n  const handleSubmit = async () => {\r\n    setIsSubmitting(true);\r\n    try {\r\n      await apiClient.post(\"/counselor/profile/submit\");\r\n      toast.success(\"Profile submitted successfully!\");\r\n      // Immediately redirect to profile-complete page\r\n      router.push(\"/counselor/profile-complete\");\r\n    } catch (error) {\r\n      toast.error(\"Failed to submit profile. Please try again.\");\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex-1 flex items-center justify-center min-h-[80vh] p-4 sm:p-6 md:p-8 bg-gray-50\">\r\n      <div className=\"w-full max-w-3xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden\">\r\n        {/* Top Section with Gradient */}\r\n        <div className=\"relative bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-10\">\r\n          {/* Decorative top border */}\r\n          <div className=\"absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#2F80ED] to-blue-400\" />\r\n          \r\n          <div className=\"text-center max-w-2xl mx-auto\">\r\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\r\n              Ready to Submit Your Profile?\r\n            </h2>\r\n            <p className=\"text-gray-600 text-lg font-medium\">\r\n              Once you submit your profile, our team will process your application and let you know the next steps within 1-3 business days.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Checklist Section */}\r\n        <div className=\"px-6 sm:px-8 py-10 space-y-8\">\r\n          <div className=\"bg-blue-50 border border-blue-100 p-6 sm:p-8 rounded-xl mx-auto\">\r\n            <h3 className=\"font-semibold text-blue-900 mb-6 text-xl text-center\">Final Checklist</h3>\r\n            <ul className=\"space-y-5 max-w-2xl mx-auto\">\r\n              <li className=\"flex items-start space-x-4\">\r\n                <div className=\"flex-shrink-0 w-6 h-6 mt-1\">\r\n                  <svg className=\"w-6 h-6 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                  </svg>\r\n                </div>\r\n                <span className=\"text-gray-700 text-lg\">Your personal information is accurate and complete</span>\r\n              </li>\r\n              <li className=\"flex items-start space-x-4\">\r\n                <div className=\"flex-shrink-0 w-6 h-6 mt-1\">\r\n                  <svg className=\"w-6 h-6 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                  </svg>\r\n                </div>\r\n                <span className=\"text-gray-700 text-lg\">Your educational background details are verified</span>\r\n              </li>\r\n              <li className=\"flex items-start space-x-4\">\r\n                <div className=\"flex-shrink-0 w-6 h-6 mt-1\">\r\n                  <svg className=\"w-6 h-6 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                  </svg>\r\n                </div>\r\n                <span className=\"text-gray-700 text-lg\">You've reviewed all sections for accuracy</span>\r\n              </li>\r\n            </ul>\r\n          </div>\r\n\r\n          {/* Action Buttons */}\r\n          <div className=\"flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto\">\r\n            <button\r\n              onClick={() => onMenuSelect(selectedMenuIndex - 1)}\r\n              className=\"flex-1 px-6 py-4 border-2 border-gray-200 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors duration-200 text-lg\"\r\n            >\r\n              Review Previous Section\r\n            </button>\r\n            <button\r\n              onClick={handleSubmit}\r\n              disabled={isSubmitting}\r\n              className=\"flex-1 bg-[#2F80ED] px-6 py-4 text-white rounded-xl font-medium transition-all duration-200 relative overflow-hidden group hover:shadow-lg disabled:opacity-70 disabled:cursor-not-allowed text-lg\"\r\n            >\r\n              <div className=\"absolute inset-0 bg-black/10 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-200\" />\r\n              <div className=\"relative flex items-center justify-center space-x-3\">\r\n                {isSubmitting ? (\r\n                  <>\r\n                    <LoadingSpinner />\r\n                    <span>Submitting...</span>\r\n                  </>\r\n                ) : (\r\n                  <>\r\n                    <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                    </svg>\r\n                    <span>Submit Profile</span>\r\n                  </>\r\n                )}\r\n              </div>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,oBAAoB,kBACxB,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,QAAQ;;;;;;;;;;;8CAGZ,6LAAC;oCAAG,WAAU;;wCAAmE;wCACzD;sDACtB,6LAAC;4CAAK,WAAU;;gDAA0B;8DAExC,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;;8CAGpB,6LAAC;oCAAE,WAAU;8CAA+C;;;;;;;;;;;;;;;;;;8BAOhE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CAGrD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;;4CAAgC;4CACsB;0DACjE,6LAAC;gDAAK,WAAU;0DAA8B;;;;;;4CAAwB;4CACrE;4CAAI;;;;;;;kDAGP,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA7CxC;AAuDS,SAAS,SAAS,EAC/B,oBAAoB,CAAC,EACrB,eAAe,KAAO,CAAC,EAIxB;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,0EAA0E;IAC1E,IAAI,aAAa,+BAA+B;QAC9C,qBAAO,6LAAC;;;;;IACV;IAEA,MAAM,eAAe;QACnB,gBAAgB;QAChB,IAAI;YACF,MAAM,mHAAA,CAAA,UAAS,CAAC,IAAI,CAAC;YACrB,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,gDAAgD;YAChD,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAoC;;;;;;;;;;;;;;;;;;8BAOrD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuD;;;;;;8CACrE,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAE1C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;sDAE1C,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;sCAM9C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,aAAa,oBAAoB;oCAChD,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;sDACZ,6BACC;;kEACE,6LAAC,0JAAA,CAAA,UAAc;;;;;kEACf,6LAAC;kEAAK;;;;;;;6EAGR;;kEACE,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;kEAEvE,6LAAC;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B;GAlHwB;;QAQL,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;;;MATF"}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}