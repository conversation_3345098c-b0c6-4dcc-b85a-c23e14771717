"use client";

import HeroS<PERSON>tion from "@/app/components/public/for-counselors/hero";
import Mission from "@/app/components/public/for-counselors/mission";
import OnboardingProcess from "@/app/components/public/for-counselors/onboarding-process";
import Opportunity from "@/app/components/public/for-counselors/opportunity";
import Testimonials from "@/app/components/public/for-counselors/testimonials";
import WhyChooseUs from "@/app/components/public/for-counselors/why-choose-us";

export default function ForCounselorsPage() {
  return (
    <div className="flex flex-col font-clash-display">
      <HeroSection />
      <OnboardingProcess />
      <Mission />
      <WhyChooseUs />
      <Testimonials />
      <Opportunity />
    </div>
  );
}
