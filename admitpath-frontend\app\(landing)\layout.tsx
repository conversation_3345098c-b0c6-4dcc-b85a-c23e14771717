import Footer from "../components/public/Footer";
import LandingNavbar from "../components/public/Navbar";

export const metadata = {
  title: "AdmitPath | Find the Right Guidance for Your College Journey",
  description:
    "Search and connect with experienced mentors to get personalized guidance for your university applications.",
};

export default function StudentLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen m-0 p-0 flex flex-col overflow-x-hidden">
      <LandingNavbar />
      <main className="flex-grow pt-[76px]">{children}</main>
      <Footer />
    </div>
  );
}
