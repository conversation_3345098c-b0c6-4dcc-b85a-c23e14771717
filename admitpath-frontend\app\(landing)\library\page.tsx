"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@components/ui/button";
import ResourceCard from "../../components/student/resources/card";
import ResourceSkeleton from "@/app/components/student/resources/skeleton";
import { useResources } from "@/app/hooks/student/useResources";

const categories = [
  "All",
  "Application Guides",
  "Essay Writing",
  "Interview Prep",
  "Test Preparation",
  "College Selection",
  "Financial Aid",
  "Career Planning",
];

export default function LibraryPagee() {
  const { resources, setAudience } = useResources();

  const [selectedCategory, setSelectedCategory] = useState("All");

  useEffect(() => {
    setAudience("public");
  }, []);

  // Filter resources by category
  const filteredResources =
    selectedCategory === "All"
      ? resources
      : resources.filter((resource) => resource.category === selectedCategory);

  return (
    <section className="py-10 md:py-16 bg-white font-clash-display">
      <div className="container mx-auto px-4">
        <div className="text-center space-y-4 mb-12">
          <h2 className="text-4xl md:text-5xl font-medium tracking-tight">
            Explore, Learn, and Grow
          </h2>
          <p className="text-lg md:text-xl text-muted-foreground font-light max-w-3xl mx-auto">
            Access a treasure trove of free resources, insightful blogs,
            engaging podcasts, and the latest news—all curated to help you
            succeed in your academic journey.
          </p>
        </div>

        <div className="flex flex-wrap justify-center gap-3 mb-12">
          {categories.map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? "default" : "outline"}
              onClick={() => setSelectedCategory(category)}
              className="rounded-full"
            >
              {category}
            </Button>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {filteredResources
            ? filteredResources.map((resource) => (
                <ResourceCard key={resource.id} resource={resource} />
              ))
            : [0, 1, 2, 3].map((index) => {
                return <ResourceSkeleton key={index} />;
              })}
        </div>
      </div>
    </section>
  );
}
