"use client";

import BecomeCounselor from "../components/public/landing/BecomeCounselor";
import GetExpertHelpSection from "../components/public/landing/GetExpertHelp";
import Guides from "../components/public/landing/Guides";
import Hero from "../components/public/landing/Hero";
import LogoSlider from "../components/public/landing/LogoSlider";
import { NewsletterForm } from "../components/public/landing/newsletter";
import TestimonialsSection from "../components/public/landing/Testimonials";
import TopCounselors from "../components/public/landing/TopCounselors";
import StickyFindCounselor from "../components/public/landing/StickyFindCounselor";
import FeaturedCategories from "../components/public/landing/FeaturedCategories";

export default function LandingPage() {
  return (
    <div className="flex flex-col bg-white font-clash-display">
      <StickyFindCounselor />
      <Hero />
      <LogoSlider />
      <TopCounselors />
      <GetExpertHelpSection />
      <FeaturedCategories />
      <BecomeCounselor />
      <Guides />
      <TestimonialsSection />
      <NewsletterForm />
    </div>
  );
}
