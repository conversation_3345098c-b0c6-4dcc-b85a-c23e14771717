"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@components/ui/dialog";
import { CounselorPublicProfile } from "@/app/types/counselor";
import { ProfileHeader } from "@components/public/profile/ProfileHeader";
import { Overview } from "@components/public/profile/Overview";
import { Services } from "@/app/components/public/profile/Services";
import PublicPackages from "@/app/components/public/profile/packages";
import { useCounselors } from "@/app/hooks/public/useCounselors";
import { useParams, useRouter } from "next/navigation";
import { useProfile } from "@/app/hooks/student/useProfile";
import Education from "@/app/components/public/profile/Education";
import Experience from "@/app/components/public/profile/Experience";
import BookingForm from "@/app/components/public/profile/booking-form";
import Link from "next/link";
import { Button } from "@/app/components/ui/button";
import { ArrowRight } from "lucide-react";
import ProfileSkeleton from "@/app/components/public/profile/skeletons";
import { RecommendedCounselors } from "@/app/components/public/profile/RecommendedCounselors";
import { CounselorReviews } from "@/app/components/public/profile/Reviews";

export const runtime = "edge";

export default function ProfilePage() {
  const [isBookingOpen, setIsBookingOpen] = useState(false);
  const { id } = useParams();
  const { fetchCounselorProfile, counselors, profileLoading } = useCounselors();
  const [selectedService, setSelectedService] = useState("");
  const { userInfo } = useProfile();
  const router = useRouter();

  const [profileData, setProfileData] = useState<CounselorPublicProfile | null>(
    null
  );
  const [error, setError] = useState(false);

  const handleBookingClick = () => {
    if (!userInfo) {
      router.push(`/auth/login?redirect=/profile/${id}`);
      return;
    }
    setIsBookingOpen(true);
  };

  useEffect(() => {
    let isMounted = true;

    const loadProfile = async () => {
      if (!id) return;

      try {
        setError(false);
        // Always fetch fresh data to ensure we have complete profile
        const data = await fetchCounselorProfile(Number(id));

        // Only update state if component is still mounted
        if (isMounted) {
          setProfileData(data);
        }
      } catch (error) {
        console.error("Failed to load counselor profile:", error);
        if (isMounted) {
          setError(true);
        }
      }
    };

    loadProfile();

    // Cleanup function to prevent state updates on unmounted component
    return () => {
      isMounted = false;
    };
  }, [id, fetchCounselorProfile]);

  // Removed redundant check since we now have more robust error handling below

  // Show loading state while profile is being fetched
  if (profileLoading || !profileData) {
    return (
      <div className="container max-w-7xl mx-auto px-3 sm:px-5 my-12 md:my-20 space-y-4 md:space-y-6 font-clash-display">
        <ProfileSkeleton />
      </div>
    );
  }

  // Show error state if profile could not be loaded
  if (!profileData && error) {
    return (
      <div className="text-center py-12 space-y-8">
        <h1 className="text-2xl md:text-3xl font-medium">No profile found</h1>
        <Link href="/explore">
          <Button className="inline-flex items-center gap-2">
            Explore Counselors <ArrowRight className="w-5 h-5" />
          </Button>
        </Link>
      </div>
    );
  }

  // Safeguard - wait until we have profile data
  if (!profileData) {
    return (
      <div className="container max-w-7xl mx-auto px-3 sm:px-5 my-12 md:my-20 space-y-4 md:space-y-6 font-clash-display">
        <ProfileSkeleton />
      </div>
    );
  }

  return (
    <div className="container max-w-7xl mx-auto px-3 sm:px-5 my-12 md:my-20 space-y-4 md:space-y-6 font-clash-display">
      <ProfileHeader
        counselorUserId={Number(id)}
        profileData={profileData}
        setIsBookingOpen={handleBookingClick}
      />

      <Overview profileData={profileData} />
      <Services
        setIsBookingOpen={handleBookingClick}
        profileData={profileData}
        setSelectedService={setSelectedService}
      />
      <PublicPackages counselor={profileData} />
      <Education profileData={profileData} />
      <Experience profileData={profileData} />
      <CounselorReviews counselorId={Number(profileData.user_id)} />

      <RecommendedCounselors
        currentCounselorId={Number(id)}
        allCounselors={counselors}
      />

      <Dialog open={isBookingOpen} onOpenChange={setIsBookingOpen}>
        <DialogContent className="max-w-2xl p-0 max-h-[90vh] overflow-y-auto">
          <DialogTitle className="text-2xl font-medium p-4">
            Book Service
          </DialogTitle>
          <BookingForm
            counselor={profileData}
            counselor_user_id={Number(id)}
            selectedService={selectedService}
            setSelectedService={setSelectedService}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
