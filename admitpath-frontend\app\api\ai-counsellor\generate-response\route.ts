import { NextRequest, NextResponse } from "next/server";
import { generateCounsellorResponse, validateUserInput } from "@/app/utils/ai";

export const runtime = "edge"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query } = body;

    const validation = validateUserInput(query);
    if (!validation.valid) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    const aiResult = await generateCounsellorResponse(query);

    if (!aiResult.success) {
      return NextResponse.json(
        { error: aiResult.error || "Failed to generate response" },
        { status: 500 }
      );
    }

    console.log("AI Response:", aiResult.response);

    return NextResponse.json({
      success: true,
      response: aiResult.response,
      wordCount: aiResult.wordCount,
      truncated: aiResult.truncated,
    });
  } catch (error: any) {
    console.error("Error in AI counsellor API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
