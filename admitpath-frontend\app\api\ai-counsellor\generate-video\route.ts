import { NextRequest, NextResponse } from "next/server";

export const runtime = "edge"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { text } = body;

    if (!text || typeof text !== "string" || text.trim().length === 0) {
      return NextResponse.json(
        { error: "Text is required and cannot be empty" },
        { status: 400 }
      );
    }

    if (!process.env.HEYGEN_API_KEY) {
      return NextResponse.json(
        { error: "HeyGen API key is not configured" },
        { status: 500 }
      );
    }

    const heygenResponse = await fetch(
      "https://api.heygen.com/v2/video/generate",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": process.env.HEYGEN_API_KEY,
        },
        body: JSON.stringify({
          video_inputs: [
            {
              character: {
                type: "avatar",
                avatar_id: "<PERSON><PERSON>Therapist_public",
              },
              voice: {
                type: "text",
                input_text: text,
                voice_id: "5d8c378ba8c3434586081a52ac368738",
              },
              background: {
                type: "color",
                value: "#ffffff",
              },
            },
          ],
          dimension: {
            width: 1280,
            height: 720,
          },
        }),
      }
    );

    if (!heygenResponse.ok) {
      const errorData = await heygenResponse.json().catch(() => ({}));
      console.error("HeyGen API error:", errorData);
      return NextResponse.json(
        { error: "Failed to generate video", details: errorData },
        { status: 500 }
      );
    }

    const data = await heygenResponse.json();

    if (!data.data?.video_id) {
      return NextResponse.json(
        { error: "Invalid response from video generation service" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      video_id: data.data.video_id,
    });
  } catch (error: any) {
    console.error("Error generating video:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
