import { NextRequest, NextResponse } from 'next/server';

export const runtime = "edge"

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const videoId = searchParams.get('video_id');

    if (!videoId) {
      return NextResponse.json(
        { error: 'video_id parameter is required' },
        { status: 400 }
      );
    }

    if (!process.env.HEYGEN_API_KEY) {
      return NextResponse.json(
        { error: 'HeyGen API key is not configured' },
        { status: 500 }
      );
    }

    const heygenResponse = await fetch(
      `https://api.heygen.com/v1/video_status.get?video_id=${videoId}`,
      {
        method: 'GET',
        headers: {
          'X-Api-Key': process.env.HEYGEN_API_KEY,
        },
      }
    );

    if (!heygenResponse.ok) {
      const errorData = await heygenResponse.json().catch(() => ({}));
      console.error('HeyGen status API error:', errorData);
      return NextResponse.json(
        { error: 'Failed to check video status' },
        { status: 500 }
      );
    }

    const data = await heygenResponse.json();
    
    if (!data.data) {
      return NextResponse.json(
        { error: 'Invalid response from video service' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      status: data.data.status,
      video_url: data.data.video_url || null,
      progress: data.data.progress || null,
    });

  } catch (error: any) {
    console.error('Error checking video status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
