"use client";

import { Suspense, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/app/hooks/useAuth";
import { toast } from "react-toastify";

interface LinkedInLoginResult {
  user_type: "student" | "counselor";
  access_token: string;
  token_type: string;
  is_new_user: boolean;
}

function LinkedInCallbackContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { linkedinAuth } = useAuth();

  useEffect(() => {
    const handleCallback = async () => {
      const code = searchParams.get("code");
      const state = searchParams.get("state") || undefined; // Convert null to undefined
      const error = searchParams.get("error");

      if (error) {
        toast.error("LinkedIn authentication failed");
        router.push("/auth/login");
        return;
      }

      if (!code) {
        toast.error("Invalid callback parameters");
        router.push("/auth/login");
        return;
      }

      try {
        await linked<PERSON><PERSON><PERSON>(code, state as "student" | "counselor" | undefined);
        const redirectPath = state === "counselor" ? "/counselor/dashboard" : "/explore";
        router.push(redirectPath);
      } catch (error: any) {
        if (process.env.NODE_ENV === "development") {
          console.error("LinkedIn callback error:", error);
        }

        if (error.message?.includes("already registered")) {
          toast.error(error.message, { toastId: "auth-error" });
        } else if (error.response?.data?.detail === "user_type is required for registration") {
          toast.error(
            "Please use the signup page to register as either a student or counselor.",
            { toastId: "auth-error" }
          );
        } else {
          toast.error("Failed to complete LinkedIn authentication", {
            toastId: "auth-error",
          });
        }
        router.push("/auth/login");
      }
    };

    handleCallback();
  }, [router, searchParams, linkedinAuth]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h2 className="text-2xl font-semibold mb-4">
          Processing LinkedIn Login...
        </h2>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
      </div>
    </div>
  );
}

export default function LinkedInCallbackPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LinkedInCallbackContent />
    </Suspense>
  );
}
