"use client";

import apiClient from "@/lib/apiClient";
import { LoginForm } from "@components/auth/login";
import { useAuth } from "@hooks/useAuth";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useState } from "react";

function LoginPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (data: { email: string; password: string }) => {
    setIsLoading(true);
    try {
      const loginPromise = login(data.email, data.password);
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Login request timed out")), 15000)
      );

      await Promise.race([loginPromise, timeoutPromise]);

      const redirectParam = searchParams.get("redirect");
      if (redirectParam) {
        router.replace(redirectParam);
      } else {
        const userInfoPromise = apiClient.get(
          `/user/me?timezone=${
            Intl.DateTimeFormat().resolvedOptions().timeZone
          }`
        );
        const userInfoTimeoutPromise = new Promise((_, reject) =>
          setTimeout(
            () => reject(new Error("User info request timed out")),
            10000
          )
        );

        const response = (await Promise.race([
          userInfoPromise,
          userInfoTimeoutPromise,
        ])) as {
          data: {
            userType: string;
            is_verified?: boolean;
            isProfileComplete?: boolean;
          };
        };
        const userInfo = response.data;

        let redirectPath = "";

        if (userInfo.userType === "counselor") {
          redirectPath = userInfo.is_verified
            ? "/counselor/dashboard"
            : userInfo.isProfileComplete
            ? "/counselor/profile-complete"
            : "/counselor/onboarding";
        } else {
          // Students always go to dashboard regardless of profile completion
          redirectPath = "/student/dashboard";
        }

        router.replace(redirectPath);
      }
    } catch (error: any) {
      console.error("Login error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <LoginForm
      handleSubmit={handleSubmit}
      isLoading={isLoading}
      isLogin={true}
    />
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={<></>}>
      <LoginPageContent />
    </Suspense>
  );
}
