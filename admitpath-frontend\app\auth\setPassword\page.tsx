"use client";

import { SetPasswordForm } from "@components/auth/signup/setPassword";
import { useAuth } from "@hooks/useAuth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function SetPasswordPage() {
  const router = useRouter();
  const { completeSignup, loading, signupData } = useAuth();

  useEffect(() => {
    if (!signupData && typeof window !== "undefined") {
      router.push("/auth/signup");
    }
  }, [signupData, router]);

  const handlePasswordSet = async (password: string) => {
    try {
      await completeSignup(password);
      router.push("/auth/verify");
    } catch (error) {
      // Error handled by hook
    }
  };

  if (typeof window === "undefined" || signupData) {
    return (
      <SetPasswordForm
        onSubmit={handlePasswordSet}
        onBack={() => router.push("/auth/signup")}
        isLoading={loading}
      />
    );
  } else {
    return null;
  }
}
