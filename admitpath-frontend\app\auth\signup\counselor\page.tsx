"use client";

import { SignupForm } from "@/app/components/auth/signup";
import { useAuth } from "@/app/hooks/useAuth";
import { useRouter } from "next/navigation";

export default function CounselorSignup() {
  const { initiateSignup, loading } = useAuth();
  const router = useRouter();

  const handleInitialSignup = async (data: {
    firstName: string;
    lastName: string;
    email: string;
  }) => {
    try {
      await initiateSignup({
        first_name: data.firstName,
        last_name: data.lastName,
        email: data.email,
      }, "counselor");
      
      // Store email for verification
      localStorage.setItem("email", data.email);
      router.replace("/auth/verify");
    } catch (error) {
      // Error handled by hook
    }
  };

  return (
    <div className="auth-container">
      <SignupForm
        handleSubmit={handleInitialSignup}
        isLoading={loading}
        isLogin={false}
        userType="counselor"
      />
    </div>
  );
}
