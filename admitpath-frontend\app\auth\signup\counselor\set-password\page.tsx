"use client";

import { SetPasswordForm } from "@components/auth/signup/setPassword";
import { useAuth } from "@hooks/useAuth";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function CounselorSetPassword() {
  const { completeSignup, loading, signupData } = useAuth();
  const router = useRouter();

  const handleSetPassword = async (password: string) => {
    const email = localStorage.getItem("email");
    if (!email) {
      router.push("/auth/signup/counselor");
      return;
    }

    try {
      const userType = await completeSignup(password);
      // Redirect to appropriate page after signup
      if (userType === "counselor") {
        router.push("/counselor/onboarding"); // Counselors still need onboarding
      } else {
        router.push("/explore"); // Students go to explore page instead of onboarding
      }
    } catch (error) {
      // Error handled by hook
    }
  };

  const handleBack = () => {
    router.push("/auth/verify");
  };

  useEffect(() => {
    // Redirect if no email or signup data
    const email = localStorage.getItem("email");
    if (!email || !signupData || signupData.userType !== "counselor") {
      router.push("/auth/signup/counselor");
    }
  }, [signupData, router]);

  return (
    <SetPasswordForm
      onSubmit={handleSetPassword}
      onBack={handleBack}
      isLoading={loading}
      userType="counselor"
    />
  );
}
