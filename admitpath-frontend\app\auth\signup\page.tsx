"use client";

import { SignupForm } from "@components/auth/signup";
import { useAuth } from "@hooks/useAuth";
import { useRouter } from "next/navigation";

type SignupData = {
  firstName: string;
  lastName: string;
  email: string;
};

export default function SignupPage() {
  const router = useRouter();
  const { initiateSignup, loading } = useAuth();
  const userType = "student";

  const handleInitialSignup = async (data: SignupData) => {
    try {
      await initiateSignup(
        {
          first_name: data.firstName,
          last_name: data.lastName,
          email: data.email,
        },
        userType
      );

      // Store email for verification
      localStorage.setItem("email", data.email);
      router.replace("/auth/verify");
    } catch (error) {
      // Error handled by hook
    }
  };

  return (
    <SignupForm
      handleSubmit={handleInitialSignup}
      isLoading={loading}
      isLogin={false}
      userType={userType}
    />
  );
}
