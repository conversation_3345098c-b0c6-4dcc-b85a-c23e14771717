"use client";

import { VerificationForm } from "@components/auth/verification";
import { useAuth } from "@hooks/useAuth";
import { useRouter } from "next/navigation";

export default function VerifyPage() {
  const router = useRouter();
  const { verifySignupCode, loading, signupData } = useAuth();

  const handleVerify = async (data: { email: string; code: string }) => {
    try {
      await verifySignupCode(data.email, data.code);
      // Redirect based on user type
      const redirectPath = signupData?.userType === "counselor" 
        ? "/auth/signup/counselor/set-password"
        : "/auth/signup/set-password";
      router.replace(redirectPath);
    } catch (error) {
      // Error is handled by the hook
    }
  };

  return <VerificationForm onVerify={handleVerify} isLoading={loading} />;
}
