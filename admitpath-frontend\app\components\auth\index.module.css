.layout {
  @apply h-screen flex flex-col lg:flex-row overflow-hidden;
}

.formSection {
  @apply w-full lg:w-1/2 flex flex-col bg-stone-100 overflow-y-auto;
}

.formWrapper {
  @apply flex justify-center items-center md:my-auto md:mx-auto;
}

.mainContent {
  @apply w-max bg-white rounded-xl p-6 sm:p-8 mx-3;
}

.header {
  @apply p-4 sm:p-6;
}

.logo {
  @apply w-[180px];
}

.title {
  @apply text-2xl sm:text-4xl font-medium mb-6;
}

.highlight {
  @apply text-blue-500;
}

.terms {
  @apply text-gray-600 text-sm sm:text-base mb-6;
}

.link {
  @apply text-blue-500 hover:underline;
}

.divider {
  @apply flex items-center gap-4 my-6;
}

.divider hr {
  @apply flex-1 border-gray-200;
}

.divider span {
  @apply text-gray-500 text-xs sm:text-sm whitespace-nowrap;
}

.socialButtons {
  @apply flex flex-col sm:flex-row gap-3 w-full my-6;
}

.form {
  @apply flex flex-col gap-4 sm:gap-6 w-full;
}

.nameFields {
  @apply flex flex-wrap gap-4;
}

.emailField {
  @apply w-full;
}

.formFooter {
  @apply flex flex-col sm:flex-row items-center justify-between gap-4 mt-6;
}

.loginLink {
  @apply flex flex-row items-center gap-2 text-xs sm:text-sm;
}

.loginLink span {
  @apply text-gray-600;
}

.loginLink a {
  @apply text-blue-500 hover:underline whitespace-nowrap;
}

.imageSection {
  @apply hidden lg:block lg:w-1/2 bg-black;
}

.heroImage {
  @apply w-full h-full object-cover;
}
