import type { FC, ReactNode } from "react";
import { SocialButton } from "@components/common/socialButtons";
import { socialButtons } from "@constants/socialButtons";
import Image from "next/image";
import Link from "next/link";

interface LayoutProps {
    header: ReactNode;
    formContent: ReactNode;
    isLogin: boolean;
    userType?: "student" | "counselor";
}

export const Layout: FC<LayoutProps> = ({
    header,
    formContent,
    isLogin,
    userType = "student",
}) => {
    return (
        <div className="min-h-screen flex flex-col lg:flex-row overflow-hidden">
            <div className="w-full lg:w-1/2 flex flex-col bg-stone-100 min-h-screen overflow-y-auto">
                <header className="p-4 sm:py-6 sm:px-8 flex items-center">
                    {/* Logo */}
                    <Link href={"/"}>
                        <Image
                            src="/icons/logo.png"
                            alt="AdmitPath Logo"
                            width={100}
                            height={100}
                            className="w-[4.5rem]"
                            priority
                        />
                    </Link>
                </header>

                <div className="flex-1 flex justify-center items-center py-8 px-4">
                    <div className="w-full max-w-lg bg-white rounded-xl p-6 sm:p-8 mx-auto shadow-sm">
                        {header}
                        <div className="flex flex-col sm:flex-row gap-3 w-full my-6">
                            {socialButtons.map((button) => (
                                <SocialButton
                                    key={button.provider}
                                    type={button.provider}
                                    label={button.label}
                                    altText={button.altText}
                                    userType={userType}
                                    isLogin={isLogin}
                                />
                            ))}
                        </div>

                        <div className="flex items-center gap-4 my-6">
                            <hr className="flex-1 border-gray-200" />
                            <span className="text-gray-500 text-xs sm:text-sm whitespace-nowrap">
                                or continue with email
                            </span>
                            <hr className="flex-1 border-gray-200" />
                        </div>

                        {formContent}

                        {!isLogin && (
                            <div className="mt-6 text-center">
                                {userType === "student" ? (
                                    <Link
                                        href="/auth/signup/counselor"
                                        className="text-blue-600 hover:text-blue-700 font-medium"
                                    >
                                        Sign up as a Counselor
                                    </Link>
                                ) : (
                                    <Link
                                        href="/auth/signup"
                                        className="text-blue-600 hover:text-blue-700 font-medium"
                                    >
                                        Sign up as a Student
                                    </Link>
                                )}
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <div className="hidden lg:block lg:w-1/2 relative">
                <div className="absolute inset-0 bg-black">
                    <img
                        src="/svgs/authCover.svg"
                        alt="Counselor profile"
                        className="w-full h-full object-cover"
                    />
                </div>
            </div>
        </div>
    );
};
