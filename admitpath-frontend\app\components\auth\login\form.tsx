"use client";

import { FC, useState } from "react";
import { InputField } from "@components/common/inputField";
import { Button } from "@components/common/button";
import { FaArrowRight } from "react-icons/fa";
import { PasswordInputField } from "@components/common/passwordInputField";
import { useAuth } from "@/app/hooks/useAuth";

interface FormErrors {
  email?: string;
  password?: string;
}

export interface FormProps {
  handleSubmit: (data: { email: string; password: string }) => Promise<void>;
  isLogin: boolean;
  isLoading: boolean;
}

export const Form: FC<FormProps> = ({ handleSubmit, isLogin, isLoading }) => {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [errors, setErrors] = useState<FormErrors>({});

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await handleSubmit(formData);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <div className="flex flex-col gap-6 w-full">
      <form onSubmit={onSubmit} className="flex flex-col gap-4 sm:gap-6 w-full">
        <div className="w-full">
          <InputField
            label="Email"
            type="email"
            name="email"
            placeholder="Email"
            required
            value={formData.email}
            onChange={handleInputChange}
            error={errors.email}
          />
        </div>
        <div>
          <PasswordInputField
            label="Password"
            name="password"
            placeholder="Enter your password"
            required
            value={formData.password}
            onChange={handleInputChange}
            error={errors.password}
          />
        </div>

        <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="flex flex-col gap-2">
            <a
              href="/auth/forgotPassword"
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              Forgot password?
            </a>

            <a
              href="/auth/signup"
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              Don't have an account?
            </a>
          </div>
          <Button
            variant="primary"
            type="submit"
            icon={<FaArrowRight />}
            disabled={isLoading}
          >
            {isLoading ? "Logging in..." : "Login"}
          </Button>
        </div>
      </form>
    </div>
  );
};
