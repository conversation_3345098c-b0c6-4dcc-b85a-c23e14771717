"use client";

import { FC } from "react";
import { useRouter } from "next/navigation";
import { Layout } from "../layout";
import { Form } from "./form";
import { LoginHeader } from "./header";

interface LoginFormProps {
  handleSubmit: (data: { email: string; password: string }) => Promise<void>;
  isLoading: boolean;
  isLogin: boolean;
}

export const LoginForm: FC<LoginFormProps> = ({
  handleSubmit,
  isLoading,
  isLogin,
}) => {
  const formContent = (
    <Form handleSubmit={handleSubmit} isLoading={isLoading} isLogin={isLogin} />
  );

  return (
    <Layout header={<LoginHeader />} formContent={formContent} isLogin={true} />
  );
};
