"use client";

import React, { useState } from "react";
import { useAuth } from "@hooks/useAuth";
import { useRouter } from "next/navigation";
import { PasswordPagesLayout } from "./layout";
import { VerificationCard } from "./verificationCard";

export const ForgotPassword: React.FC = () => {
  const router = useRouter();
  const { forgotPassword, verifyEmail, loading } = useAuth();
  const [email, setEmail] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
    if (error) setError(null);
  };

  const handleSubmit = async () => {
    if (!email) {
      setError("Email is required");
      return;
    }

    try {
      await forgotPassword(email);
      setIsModalOpen(true);
    } catch (error) {
      // Error handled by hook
    }
  };

  return (
    <PasswordPagesLayout
      title={
        <>
          Enter your <span className="text-blue-600">email</span>
        </>
      }
      buttonText="Send OTP"
      onBackClick={() => window.history.back()}
      onSubmitClick={handleSubmit}
      isLoading={loading}
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-4">
          <input
            type="email"
            placeholder="Enter your email"
            required
            value={email}
            onChange={handleEmailChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {error && <p className="text-red-500 text-sm">{error}</p>}
        </div>
      </form>

      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <VerificationCard
              email={email}
              onVerify={async (code) => {
                try {
                  await verifyEmail(email, code);
                  localStorage.setItem("email", email);
                  localStorage.setItem("code", code);
                  router.push("/auth/resetPassword");
                } catch (error) {
                  // Error handled by hook
                }
              }}
              onCancel={() => setIsModalOpen(false)}
              onResend={async () => {
                try {
                  await forgotPassword(email);
                } catch (error) {
                  // Error handled by hook
                }
              }}
            />
          </div>
        </div>
      )}
    </PasswordPagesLayout>
  );
};
