.container {
  @apply min-h-screen bg-gray-50;
}

.header {
  @apply w-full p-4 bg-white;
}

.headerContent {
  @apply flex justify-between items-center max-w-7xl mx-auto px-4;
}

.main {
  @apply flex items-center justify-center min-h-[calc(100vh-72px)] p-4;
}

.box {
  @apply bg-white rounded-lg shadow-md w-full max-w-[480px] p-6 sm:p-8;
}

.passwordPagesContainer {
  @apply w-full;
}

.passwordPagesContent {
  @apply space-y-6;
}

.passwordPagesTitle {
  @apply text-2xl mb-8 flex items-center gap-2 text-gray-900 font-medium;
}

.passwordPagesTitle span {
  @apply text-blue-600;
}

.formFields {
  @apply space-y-4 mb-8;
}

.buttonGroup {
  @apply flex gap-4 mt-6;
}

/* Input styles */
.formFields input {
  @apply w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500;
}

/* Password requirements list */
.requirementsList {
  @apply mt-4 space-y-2;
}

.requirementItem {
  @apply flex items-center gap-2 text-sm text-gray-600;
}

.requirementItem.checked {
  @apply text-green-600;
}

/* Modal styles for OTP verification */
.modalBackdrop {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modalContent {
  @apply bg-white rounded-lg p-6 w-full max-w-md mx-4;
}

/* OTP input container */
.otpContainer {
  @apply flex gap-2 justify-center my-4;
}

.otpInput {
  @apply w-12 h-12 text-center border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500;
}
