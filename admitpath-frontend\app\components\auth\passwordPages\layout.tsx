"use client";

import React, { ReactNode } from "react";
import { Button } from "@components/common/button";
import { FaArrowRight } from "react-icons/fa";
import { useRouter } from "next/navigation";
import Image from "next/image";

interface PasswordPagesLayoutProps {
  children: ReactNode;
  title: ReactNode;
  buttonText: string;
  onBackClick: () => void;
  onSubmitClick: (e: any) => void;
  isLoading?: boolean;
}

export const PasswordPagesLayout: React.FC<PasswordPagesLayoutProps> = ({
  children,
  title,
  buttonText,
  onBackClick,
  onSubmitClick,
  isLoading = false,
}) => {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="w-full p-4 bg-white">
        <div className="flex justify-between items-center max-w-7xl mx-auto px-4">
          {/* Logo */}
          <Image
            src="/icons/logo.png"
            alt="AdmitPath Logo"
            width={100}
            height={100}
            priority
            className="w-[5rem]"
          />
          <Button
            variant="primary"
            onClick={() => router.push("/auth/login")}
            icon={<FaArrowRight />}
          >
            Login
          </Button>
        </div>
      </header>
      <main className="flex items-center justify-center min-h-[calc(100vh-72px)] p-4">
        <div className="bg-white rounded-lg shadow-md w-full max-w-[480px] p-6 sm:p-8">
          <div className="text-2xl mb-8 flex items-center gap-2 text-gray-900 font-medium">
            {title}
          </div>

          {children}

          <div className="flex justify-between items-center mt-8">
            <Button variant="secondary" onClick={onBackClick}>
              Back
            </Button>
            <Button
              variant="primary"
              onClick={onSubmitClick}
              type="button"
              icon={<FaArrowRight />}
              disabled={isLoading}
            >
              {buttonText}
            </Button>
          </div>
        </div>
      </main>
    </div>
  );
};
