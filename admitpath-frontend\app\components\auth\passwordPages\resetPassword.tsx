"use client";

import React, { useState } from "react";
import { PasswordInputField } from "@components/common/passwordInputField";
import { PasswordData } from "../../../types/auth";
import { PasswordPagesLayout } from "./layout";
import { useAuth } from "@hooks/useAuth";
import { useRouter } from "next/navigation";

export const ResetPassword: React.FC = () => {
  const router = useRouter();
  const { resetPassword, loading } = useAuth();
  const [formData, setFormData] = useState<PasswordData>({
    password: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState<Partial<PasswordData>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<PasswordData> = {};

    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (validateForm()) {
      try {
        const email = localStorage.getItem("email") || "";
        const code = localStorage.getItem("code") || "";
        await resetPassword(email, code, formData.password);
        router.push("/auth/login");
      } catch (error) {
        // Error handled by hook
      }
    }
  };

  return (
    <PasswordPagesLayout
      title={
        <>
          Reset your <span className="text-blue-600">password</span>
        </>
      }
      buttonText="Confirm"
      onBackClick={() => window.history.back()}
      onSubmitClick={handleSubmit}
      isLoading={loading}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-4">
          <PasswordInputField
            label="Password"
            name="password"
            placeholder="Enter your password"
            required
            value={formData.password}
            onChange={(e) =>
              setFormData({ ...formData, password: e.target.value })
            }
            error={errors.password}
          />
          <PasswordInputField
            label="Confirm password"
            name="confirmPassword"
            placeholder="Confirm your password"
            required
            value={formData.confirmPassword}
            onChange={(e) =>
              setFormData({ ...formData, confirmPassword: e.target.value })
            }
            error={errors.confirmPassword}
          />
          <div className="space-y-2 mt-4">
            <div
              className={`flex items-center gap-2 text-sm ${
                formData.password.length >= 8
                  ? "text-green-600"
                  : "text-gray-600"
              }`}
            >
              <span>✓</span> Minimum 8 characters
            </div>
            <div
              className={`flex items-center gap-2 text-sm ${
                /\d/.test(formData.password)
                  ? "text-green-600"
                  : "text-gray-600"
              }`}
            >
              <span>✓</span> At least 1 number
            </div>
            <div
              className={`flex items-center gap-2 text-sm ${
                /[A-Z]/.test(formData.password)
                  ? "text-green-600"
                  : "text-gray-600"
              }`}
            >
              <span>✓</span> At least 1 uppercase letter
            </div>
            <div
              className={`flex items-center gap-2 text-sm ${
                /[!@#$%^&*]/.test(formData.password)
                  ? "text-green-600"
                  : "text-gray-600"
              }`}
            >
              <span>✓</span> At least 1 symbol (!@#$%^&*)
            </div>
          </div>
        </div>
      </form>
    </PasswordPagesLayout>
  );
};
