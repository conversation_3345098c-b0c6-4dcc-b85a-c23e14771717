"use client";

import React, { useState } from "react";
import { useAuth } from "@hooks/useAuth";
import { useRouter } from "next/navigation";
import { PasswordInputField } from "@components/common/passwordInputField";
import { PasswordPagesLayout } from "./layout";
import styles from "./index.module.css";

interface PasswordData {
  password: string;
  confirmPassword: string;
}

export const SetPassword: React.FC = () => {
  const router = useRouter();
  const { resetPassword, loading } = useAuth();
  const [formData, setFormData] = useState<PasswordData>({
    password: "",
    confirmPassword: "",
  });
  const [errors, setErrors] = useState<Partial<PasswordData>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<PasswordData> = {};

    if (!formData.password) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      try {
        const email = localStorage.getItem("email") || "";
        const code = localStorage.getItem("code") || "";
        await resetPassword(email, code, formData.password);
        router.push("/auth/login");
      } catch (error) {
        // Error handled by hook
      }
    }
  };

  return (
    <PasswordPagesLayout
      title={
        <>
          Set up your <span className="highlight">password</span>
        </>
      }
      buttonText="Confirm"
      onBackClick={() => window.history.back()}
      onSubmitClick={handleSubmit}
      isLoading={loading}
    >
      <form onSubmit={handleSubmit}>
        <div className={styles.formFields}>
          <PasswordInputField
            label="Password"
            name="password"
            placeholder="Enter your password"
            required
            value={formData.password}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setFormData({ ...formData, password: e.target.value })
            }
            error={errors.password}
          />
          <PasswordInputField
            label="Confirm password"
            name="confirmPassword"
            placeholder="Confirm your password"
            required
            value={formData.confirmPassword}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setFormData({ ...formData, confirmPassword: e.target.value })
            }
            error={errors.confirmPassword}
          />
          <div className={styles.requirementsList}>
            <div
              className={`${styles.requirementItem} ${
                formData.password.length >= 8 ? styles.checked : ""
              }`}
            >
              ✓ Minimum 8 characters
            </div>
            <div
              className={`${styles.requirementItem} ${
                /\d/.test(formData.password) ? styles.checked : ""
              }`}
            >
              ✓ At least 1 number
            </div>
            <div
              className={`${styles.requirementItem} ${
                /[A-Z]/.test(formData.password) ? styles.checked : ""
              }`}
            >
              ✓ At least 1 uppercase letter
            </div>
            <div
              className={`${styles.requirementItem} ${
                /[!@#$%^&*]/.test(formData.password) ? styles.checked : ""
              }`}
            >
              ✓ At least 1 symbol (!@#$%^&*)
            </div>
          </div>
        </div>
      </form>
    </PasswordPagesLayout>
  );
};
