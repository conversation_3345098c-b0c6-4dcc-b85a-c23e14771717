.verificationContainer {
  @apply flex flex-col p-8 bg-white rounded-2xl w-[480px];
}

.verificationContent {
  @apply flex flex-col gap-6;
}

.verificationHeader {
  @apply flex flex-col gap-2;
}

.verificationTitle {
  @apply text-2xl font-medium text-gray-900;
}

.verificationSubtitle {
  @apply text-sm text-gray-600;
}

.verificationInputContainer {
  @apply flex flex-col items-center gap-4;
}

.verificationInputGrid {
  @apply flex gap-2 justify-center;
}

.verificationInput {
  @apply flex items-center justify-center w-12 h-12 border border-gray-200 rounded-lg bg-white;
}

.verificationInputField {
  @apply w-full h-full text-center text-xl font-medium text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500;
}

.verificationTimer {
  @apply flex gap-1 text-sm;
}

.timerText {
  @apply text-gray-600;
}

.timerValue {
  @apply text-blue-600 cursor-pointer;
}

.verificationActions {
  @apply flex justify-between mt-4;
}

.verificationInputFocused {
  @apply border-blue-500;
}

.verificationInputFilled {
  @apply border-gray-900;
}
