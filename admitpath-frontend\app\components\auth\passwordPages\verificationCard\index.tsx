"use client";

import React, { useState, useRef, useEffect } from "react";
import { Button } from "@/app/components/ui/button";
import { Timer } from "./timer";
import { VerificationInput } from "./verificationInput";

interface VerificationCardProps {
  email: string;
  onVerify: (code: string) => void;
  onCancel: () => void;
  onResend: () => void;
}

export const VerificationCard: React.FC<VerificationCardProps> = ({
  email,
  onVerify,
  onCancel,
  onResend,
}) => {
  const [code, setCode] = useState(["", "", "", "", "", ""]);
  const [activeInput, setActiveInput] = useState(0);
  const [isResendActive, setIsResendActive] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [timerKey, setTimerKey] = useState(0); // Add this to force timer reset
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, 6);
    if (activeInput >= 0 && activeInput < 6) {
      inputRefs.current[activeInput]?.focus();
    }
  }, [activeInput]);

  const handleInputChange = (index: number, value: string) => {
    if (value.length <= 1) {
      const newCode = [...code];
      newCode[index] = value;
      setCode(newCode);

      if (value !== "" && index < 5) {
        setActiveInput(index + 1);
      }
    } else {
      // Handle pasting multiple digits
      const digits = value.replace(/[^0-9]/g, "").split("");
      const newCode = [...code];
      digits.forEach((digit, i) => {
        if (index + i < 6) {
          newCode[index + i] = digit;
        }
      });
      setCode(newCode);
      setActiveInput(Math.min(index + digits.length, 5));
    }
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === "Backspace" && !code[index] && index > 0) {
      setActiveInput(index - 1);
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text");
    const numericValue = pastedData.replace(/[^0-9]/g, "");
    const newCode = [...code];
    for (let i = 0; i < Math.min(numericValue.length, 6); i++) {
      newCode[i] = numericValue[i];
    }
    setCode(newCode);
    setActiveInput(Math.min(numericValue.length, 6) - 1);
  };

  const handleTimerComplete = () => {
    setIsResendActive(true);
  };

  const handleResend = async () => {
    await onResend();
    setIsResendActive(false);
    setTimerKey((prev) => prev + 1); // Force timer to reset
  };

  if (!isMounted) {
    return null;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="bg-white py-8 px-6 shadow-2xl rounded-2xl">
          <div className="flex flex-col items-center gap-8">
            {/* Icon */}
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <svg
                className="w-8 h-8 text-blue-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </div>

            {/* Header */}
            <div className="text-center space-y-2">
              <h2 className="text-3xl font-bold text-gray-900">
                Verify your email
              </h2>
              <p className="text-sm text-gray-500">
                We sent a verification code to
                <br />
                <span className="font-medium text-gray-900">{email}</span>
              </p>
            </div>

            {/* Verification inputs */}
            <div className="flex gap-2" onPaste={handlePaste}>
              {code.map((digit, idx) => (
                <VerificationInput
                  key={idx}
                  value={digit}
                  onChange={(value) => handleInputChange(idx, value)}
                  onKeyDown={(e) => handleKeyDown(e, idx)}
                  isFocused={activeInput === idx}
                  inputRef={(el) => (inputRefs.current[idx] = el)}
                />
              ))}
            </div>

            {/* Timer */}
            <div className="flex items-center gap-2 text-sm text-gray-600">
              {!isResendActive && (
                <Timer
                  key={timerKey}
                  seconds={60}
                  onComplete={handleTimerComplete}
                />
              )}
              {isResendActive && (
                <button
                  onClick={handleResend}
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  Resend code
                </button>
              )}
            </div>

            {/* Buttons */}
            <div className="flex justify-between gap-4 w-full mt-4">
              <Button
                variant="secondary"
                onClick={onCancel}
                className="flex-1 py-2.5"
              >
                Cancel
              </Button>
              <Button
                onClick={() => onVerify(code.join(""))}
                className="flex-1 py-2.5 bg-blue-600 hover:bg-blue-700"
              >
                Verify
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
