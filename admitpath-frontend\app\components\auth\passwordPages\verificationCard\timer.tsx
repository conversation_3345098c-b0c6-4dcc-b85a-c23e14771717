"use client";

import * as React from "react";

export interface TimerProps {
  seconds: number;
  onComplete: () => void;
}

export const Timer: React.FC<TimerProps> = ({ seconds, onComplete }) => {
  const [timeLeft, setTimeLeft] = React.useState(seconds);

  React.useEffect(() => {
    // Reset timer when seconds prop changes
    setTimeLeft(seconds);
  }, [seconds]);

  React.useEffect(() => {
    if (!timeLeft) {
      onComplete();
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 0) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft, onComplete]);

  return (
    <div className="flex gap-1 text-sm">
      <span className="text-gray-600">Resend code in</span>
      <span className="text-blue-600">
        0:{timeLeft.toString().padStart(2, "0")}
      </span>
    </div>
  );
};
