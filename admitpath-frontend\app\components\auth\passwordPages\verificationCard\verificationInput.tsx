import * as React from "react";

export interface VerificationInputProps {
  value: string;
  onChange: (value: string) => void;
  isFocused: boolean;
  onKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  inputRef:
    | React.Ref<HTMLInputElement>
    | ((instance: HTMLInputElement | null) => void);
}

export const VerificationInput: React.FC<VerificationInputProps> = ({
  value,
  onChange,
  isFocused,
  onKeyDown,
  inputRef,
}) => {
  return (
    <div className="flex items-center justify-center w-12 h-12 border border-gray-200 rounded-lg bg-white">
      <input
        ref={inputRef}
        type="text"
        inputMode="numeric"
        pattern="[0-9]*"
        maxLength={1}
        value={value}
        onChange={(e) => {
          const val = e.target.value.replace(/[^0-9]/g, "");
          onChange(val);
        }}
        onKeyDown={onKeyDown}
        className="w-full h-full text-center text-xl font-medium text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500"
        aria-label="Verification code digit"
        autoFocus={isFocused}
      />
    </div>
  );
};
