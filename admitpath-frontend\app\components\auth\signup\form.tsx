"use client";

import { FC, useState } from "react";
import { InputField } from "@components/common/inputField";
import { Button } from "@components/common/button";
import { FaArrowRight } from "react-icons/fa";
import styles from "../index.module.css";
import { FormErrors } from "../../../types/auth";
import { useAuth } from "@/app/hooks/useAuth";

export interface FormProps {
  handleSubmit: (data: {
    firstName: string;
    lastName: string;
    email: string;
  }) => Promise<void>;
  isLogin: boolean;
  isLoading: boolean;
}

export const Form: FC<FormProps> = ({ handleSubmit, isLogin, isLoading }) => {
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
  });
  const [errors, setErrors] = useState<FormErrors>({});

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Reset previous errors
    setErrors({});

    // Validate required fields
    const newErrors: FormErrors = {};
    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
    }
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email";
    }

    // If there are any errors, display them and stop submission
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // If validation passes, submit the form
    await handleSubmit(formData);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };


  return (
    <div className="flex flex-col gap-6">
      <form onSubmit={onSubmit} className={styles.form}>
        <div className={styles.nameFields}>
          <InputField
            label="First name"
            name="firstName"
            placeholder="First name"
            required
            value={formData.firstName}
            onChange={handleInputChange}
            error={errors.firstName}
          />
          <InputField
            label="Last name"
            name="lastName"
            placeholder="Last name"
            required
            value={formData.lastName}
            onChange={handleInputChange}
            error={errors.lastName}
          />
        </div>
        <div className={styles.emailField}>
          <InputField
            label="Email"
            type="email"
            name="email"
            placeholder="Email"
            required
            value={formData.email}
            onChange={handleInputChange}
            error={errors.email}
          />
        </div>
        <div className={styles.formFooter}>
          <div className={styles.loginLink}>
            <span>Already have an account?</span>
            <a href="/auth/login">Login</a>
          </div>
          <Button
            variant="primary"
            type="submit"
            icon={<FaArrowRight />}
            disabled={isLoading}
          >
            {isLoading ? "Creating..." : "Create"}
          </Button>
        </div>
      </form>
    </div>
  );
};
