"use client";

import { FC } from "react";

interface SignupHeaderProps {
  userType: "student" | "counselor";
}

export const SignupHeader: FC<SignupHeaderProps> = ({ userType }) => {
  return (
    <div>
      <h1 className="text-2xl sm:text-4xl font-medium mb-2">
        {userType === "student" ? (
          <>
            Create your <span className="text-[#9C0E22]">Student</span> Profile
          </>
        ) : (
          <>
            Join Our <span className="text-[#9C0E22]">Counselor</span> Network
          </>
        )}
      </h1>
      <p className="text-gray-600 text-lg">
        {userType === "student"
          ? "Get personalized guidance from top counselors"
          : "Share your expertise and grow your counseling practice"}
      </p>
    </div>
  );
};
