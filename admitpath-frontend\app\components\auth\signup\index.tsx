"use client";

import { FC } from "react";
import { Form, FormProps } from "./form";
import { SignupHeader } from "./header";
import { Layout } from "../layout";

interface SignupFormProps extends FormProps {
  userType?: "student" | "counselor";
}

export const SignupForm: FC<SignupFormProps> = ({
  handleSubmit,
  isLoading,
  isLogin,
  userType = "student",
}) => {
  const formContent = (
    <Form handleSubmit={handleSubmit} isLoading={isLoading} isLogin={isLogin} />
  );

  return (
    <Layout
      header={<SignupHeader userType={userType} />}
      formContent={formContent}
      isLogin={false}
      userType={userType}
    />
  );
};
