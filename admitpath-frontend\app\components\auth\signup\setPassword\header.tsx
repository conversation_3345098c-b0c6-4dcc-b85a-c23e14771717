"use client";

import { FC } from "react";
import styles from "../../index.module.css";

interface SetPasswordHeaderProps {
  userType?: "student" | "counselor";
}

export const SetPasswordHeader: FC<SetPasswordHeaderProps> = ({ userType = "student" }) => {
  return (
    <div className={styles.header}>
      <h1 className="text-2xl sm:text-4xl font-medium mb-6">
        {userType === "student" ? (
          "Set Your Password"
        ) : (
          <>
            Set Your <span className="text-blue-500">Counselor</span> Password
          </>
        )}
      </h1>
      <p>Create a secure password for your account</p>
    </div>
  );
};
