"use client";

import { FC } from "react";
import { PasswordInputField } from "@components/common/passwordInputField";
import { PasswordPagesLayout } from "../../passwordPages/layout";
import { useState } from "react";
import {
  PasswordRequirements,
  validatePassword,
} from "@components/common/passwordRequirements";

interface SetPasswordFormProps {
  onSubmit: (password: string) => Promise<void>;
  onBack: () => void;
  isLoading: boolean;
  userType?: "student" | "counselor";
}

export const SetPasswordForm: FC<SetPasswordFormProps> = ({
  onSubmit,
  onBack,
  isLoading,
  userType = "student",
}) => {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState<string | undefined>();
  const [showRequirements, setShowRequirements] = useState(false);

  const handleSubmit = async () => {
    setError(undefined);

    if (!validatePassword(password, confirmPassword)) {
      setError("Please meet all password requirements");
      setShowRequirements(true);
      return;
    }

    await onSubmit(password);
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    setError(undefined);
  };

  const handleConfirmPasswordChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setConfirmPassword(e.target.value);
    setError(undefined);
  };

  return (
    <PasswordPagesLayout
      title={
        userType === "student"
          ? "Set Your Password"
          : "Set Your Counselor Password"
      }
      buttonText="Complete Signup"
      onBackClick={onBack}
      onSubmitClick={handleSubmit}
      isLoading={isLoading}
    >
      <div className="w-full space-y-6">
        <div className="space-y-4">
          <div>
            <PasswordInputField
              label="Password"
              name="password"
              placeholder="Enter your password"
              required
              value={password}
              onChange={handlePasswordChange}
              error={error}
            />
            {showRequirements && (
              <PasswordRequirements
                password={password}
                confirmPassword={confirmPassword}
              />
            )}
          </div>
          <PasswordInputField
            label="Confirm Password"
            name="confirmPassword"
            placeholder="Confirm your password"
            required
            value={confirmPassword}
            onChange={handleConfirmPasswordChange}
            error={error}
          />
        </div>
      </div>
    </PasswordPagesLayout>
  );
};
