"use client";

import { FC } from "react";
import { VerificationCard } from "../passwordPages/verificationCard";
import { useAuth } from "@hooks/useAuth";
import { useRouter } from "next/navigation";

interface VerificationFormProps {
  onVerify: (data: { email: string; code: string }) => Promise<void>;
  isLoading: boolean;
}

export const VerificationForm: FC<VerificationFormProps> = ({
  onVerify,
  isLoading,
}) => {
  const router = useRouter();
  const { resendVerificationCode, signupData } = useAuth();

  const handleVerify = async (code: string) => {
    let email = "";
    if (typeof window !== "undefined") {
      email = localStorage.getItem("email") || "";
    }
    await onVerify({ email, code });
  };

  const handleResend = async () => {
    const email = localStorage.getItem("email") || "";
    if (email) {
      try {
        await resendVerificationCode(email);
      } catch (error) {
        // Error handled by hook
      }
    }
  };

  const handleCancel = () => {
    // Clear any stored signup data
    localStorage.removeItem("email");
    // Reset signup data in auth store
    
    // Redirect based on user type
    const redirectPath = signupData?.userType === "counselor" 
      ? "/auth/signup/counselor"
      : "/auth/signup";
    router.push(redirectPath);
  };

  return (
    <VerificationCard
      email={typeof window !== "undefined" ? localStorage.getItem("email") || "" : ""}
      onVerify={handleVerify}
      onCancel={handleCancel}
      onResend={handleResend}
    />
  );
};
