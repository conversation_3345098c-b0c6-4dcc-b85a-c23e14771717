"use client";

interface TextAreaLimitProps {
  label: string;
  name: string;
  placeholder: string;
  value: string | undefined;
  onChange: (value: string) => void;
  rows?: number;
  charLimit?: number; // Default to 400 if not provided
  disabled?: boolean;
  maxLength?: number;
  required?: boolean;
}

const TextAreaLimit: React.FC<TextAreaLimitProps> = ({
  label,
  name,
  placeholder,
  value,
  onChange,
  rows = 4,
  charLimit = 400,
  disabled,
  maxLength = 0,
  required = false,
}) => {
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const inputValue = e.target.value;

    if (inputValue.length <= charLimit) {
      onChange(inputValue);
    }
  };

  return (
    <div>
      <label className="font-medium mb-2 text-sm flex justify-between">
        {label}{" "}
        <span>
          {value?.length}/{charLimit}
        </span>
      </label>
      <textarea
        name={name}
        placeholder={placeholder}
        value={value}
        onChange={handleInputChange}
        className="w-full border border-gray-300 rounded-md py-2 px-3"
        rows={rows}
        disabled={disabled}
      ></textarea>
    </div>
  );
};

export default TextAreaLimit;
