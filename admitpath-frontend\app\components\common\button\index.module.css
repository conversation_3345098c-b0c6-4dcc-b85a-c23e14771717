.buttonBase {
  @apply flex justify-center items-center px-6 py-2 my-auto text-sm font-medium tracking-wide leading-6 rounded-xl shadow-md;
}

.buttonPrimary {
  @apply bg-black text-white;
}

.buttonSecondary {
  @apply bg-gray-200 text-gray-700;
}

.buttonBase:disabled {
  @apply opacity-50 cursor-not-allowed;
}

.buttonContent {
  @apply flex items-center gap-2 text-nowrap;
}
.icon {
  @apply w-3;
}
