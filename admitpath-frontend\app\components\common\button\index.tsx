"use client";

import React from "react";
import styles from "./index.module.css";

interface ButtonProps {
  variant: "primary" | "secondary";
  children: React.ReactNode;
  onClick?: (e: any) => void;
  type?: "button" | "submit" | "reset";
  disabled?: boolean;
  icon?: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant,
  children,
  onClick,
  type = "button",
  disabled = false,
  icon,
}) => {
  const buttonClasses = `${styles.buttonBase} ${
    variant === "primary" ? styles.buttonPrimary : styles.buttonSecondary
  }`;

  return (
    <button
      type={type}
      onClick={onClick}
      className={buttonClasses}
      disabled={disabled}
    >
      <span className={styles.buttonContent}>
        {children}
        {icon && <span className={styles.icon}>{icon}</span>}
      </span>
    </button>
  );
};
