import type React from "react";
import { useEffect, useRef, useState } from "react";
import { Menu, Send } from "lucide-react";
import { Button } from "@components/ui/button";
import { Input } from "@components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@components/ui/avatar";
import { MessageList } from "./message-list";
import type { UserInfo } from "@/app/types/student/profile";
import type {
  Channel,
  Message,
  WebSocketConnectionStatus,
} from "@/app/types/chat";
import { ChatMessageSkeleton, HeaderSkeleton } from "./skeletons";
import { generatePlaceholder } from "@/app/utils/image";
import { cn } from "@/lib/utils";

type ChatInterfaceProps = {
  userInfo: UserInfo | null;
  currentChannel: Channel | null;
  messages: Message[] | null;
  message: string;
  setMessage: (message: string) => void;
  handleSendMessage: () => void;
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  loading: boolean;
  fetchMoreMessages: (skip: number) => Promise<void>;
  connectionStatus?: WebSocketConnectionStatus;
  onReconnect?: () => void;
  isConnected?: boolean;
};

const ChatInterface = ({
  userInfo,
  currentChannel,
  messages,
  message,
  setMessage,
  handleSendMessage,
  sidebarOpen,
  setSidebarOpen,
  loading,
  fetchMoreMessages,
  connectionStatus, // Kept for functionality but not displayed in UI
  onReconnect, // Kept for functionality but not displayed in UI
  isConnected, // Kept for compatibility but not used to disable input
}: ChatInterfaceProps) => {
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageContainerRef = useRef<HTMLDivElement>(null);
  const previousMessagesLengthRef = useRef(messages?.length || 0);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  const scrollToBottom = () => {
    messageContainerRef.current?.scrollTo({
      top: messageContainerRef.current.scrollHeight,
      behavior: "smooth",
    });
  };

  useEffect(() => {
    if (messages && messages.length > 0 && isInitialLoad) {
      scrollToBottom();
      setIsInitialLoad(false);
    }
  }, [messages, isInitialLoad]);

  useEffect(() => {
    if (!messages || isInitialLoad) return;

    const currentLength = messages.length;
    const previousLength = previousMessagesLengthRef.current;

    if (currentLength === previousLength + 1 && !isLoadingMore) {
      scrollToBottom();
    }

    previousMessagesLengthRef.current = currentLength;
  }, [messages, isLoadingMore, isInitialLoad]);

  const handleLoadMore = async () => {
    if (!isLoadingMore && messages?.length) {
      setIsLoadingMore(true);
      try {
        await fetchMoreMessages(messages.length);
        // Check if we got fewer messages than the limit (15)
        // If so, we've reached the end
        if (messages.length % 15 !== 0) {
          setHasMoreMessages(false);
        }
      } catch (error) {
        console.error("Error loading more messages:", error);
      } finally {
        setIsLoadingMore(false);
      }
    }
  };

  const renderHeader = () => {
    if (!currentChannel?.other_participant) {
      return <HeaderSkeleton />;
    }
    if (currentChannel?.other_participant) {
      const participant = currentChannel.other_participant;
      return (
        <div className="flex items-center justify-between p-2 bg-white border-b ">
          <div className="flex items-center gap-3">
            <button
              className="md:hidden p-2 text-gray-900 focus:outline-none rounded-lg bg-white"
              onClick={() => setSidebarOpen(!sidebarOpen)}
            >
              <Menu className="w-6 h-6" />
            </button>
            <div
              className={cn(
                "flex gap-4 justify-center items-center rounded-lg hover:bg-gray-100 transition-all p-2",
                participant.user_type === "counselor" && "cursor-pointer"
              )}
              onClick={() => {
                participant.user_type === "counselor" &&
                  window.location.replace(`/profile/${participant.user_id}`);
              }}
            >
              <Avatar className="h-10 w-10">
                <AvatarImage
                  src={
                    participant.profile_picture_url ||
                    generatePlaceholder(
                      participant.first_name,
                      participant.last_name
                    )
                  }
                  alt={`${participant.first_name} ${participant.last_name}`}
                />
                <AvatarFallback>{participant.first_name[0]}</AvatarFallback>
              </Avatar>
              <div>
                <h2 className="text-lg font-semibold">{`${participant.first_name} ${participant.last_name}`}</h2>
                <p className="text-sm text-gray-500">{participant.user_type}</p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return <></>;
  };

  return (
    <div className="flex-1 flex flex-col h-full bg-gray-50 ">
      {renderHeader()}

      <div ref={messageContainerRef} className="flex-1 overflow-y-auto">
        {(loading && !isLoadingMore) || !messages ? (
          <>
            <ChatMessageSkeleton />
            <ChatMessageSkeleton />
          </>
        ) : (
          <MessageList
            messages={messages}
            currentUserId={userInfo?.user_id || 0}
            isLoadingMore={isLoadingMore}
            onLoadMore={handleLoadMore}
            hasMoreMessages={hasMoreMessages}
          />
        )}
        <div ref={messagesEndRef} />
      </div>

      {currentChannel && (
        <div className="p-4 bg-white border-t">
          <div className="flex items-center gap-2 max-w-4xl mx-auto">
            {/* <Button variant="ghost" size="icon" className="shrink-0">
              <Smile className="h-6 w-6" />
            </Button> */}
            <Input
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type a message..."
              className="flex-1"
              onKeyDown={(e) => {
                if (e.key === "Enter" && !e.shiftKey) {
                  e.preventDefault();
                  handleSendMessage();
                }
              }}
            />
            <Button
              size="icon"
              className="bg-[#1E2875] text-white hover:bg-[#1E2875]/90 shrink-0"
              onClick={handleSendMessage}
              disabled={!message.trim()}
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatInterface;
