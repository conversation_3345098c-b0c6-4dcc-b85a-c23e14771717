import { useState, useMemo } from "react";
import type { Channel } from "@/app/types/chat";
import { Avatar, AvatarFallback, AvatarImage } from "@components/ui/avatar";
import { Input } from "@components/ui/input";
import { cn } from "@/lib/utils";
import { SidebarSkeleton } from "./skeletons";
import { X } from "lucide-react";
import { Button } from "@components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import { generatePlaceholder } from "@/app/utils/image";

interface ChatSidebarProps {
  channels: Channel[] | null;
  currentChannel: Channel | null;
  onChannelSelect: (channel: Channel) => void;
  isOpen: boolean;
  onClose: () => void;
  loading: boolean;
  unseenMessages?: Record<string, number>;
}

export const ChatSidebar = ({
  channels,
  currentChannel,
  onChannelSelect,
  isOpen,
  onClose,
  loading,
  unseenMessages = {},
}: ChatSidebarProps) => {
  const [searchQuery, setSearchQuery] = useState("");

  const filteredChannels = useMemo(() => {
    if (!channels) return null;
    return channels.filter(
      (channel) =>
        channel.other_participant.first_name
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        channel.other_participant.last_name
          .toLowerCase()
          .includes(searchQuery.toLowerCase())
    );
  }, [channels, searchQuery]);

  return (
    <div
      className={cn(
        "fixed md:relative w-72 border-r h-full bg-white drop-shadow-2xl md:drop-shadow-none ",
        isOpen ? "translate-x-0" : "-translate-x-[120%] md:translate-x-0",
        "transition-transform duration-300 ease-in-out md:z-0 z-20"
      )}
    >
      <div className="p-4 md:p-6 border-b flex justify-between items-center">
        <h1 className="text-xl font-semibold">Messages</h1>
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden"
          onClick={onClose}
        >
          <X className="h-6 w-6" />
        </Button>
      </div>
      <div className="p-4">
        <Input
          type="search"
          placeholder="Search Chat"
          className="w-full pl-4 pr-4 py-2 rounded-full bg-gray-50"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
      </div>
      <div className="overflow-y-auto h-[calc(100%-8rem)]">
        {loading && !filteredChannels ? (
          <SidebarSkeleton />
        ) : filteredChannels && filteredChannels.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            No matching chats found
          </div>
        ) : (
          filteredChannels?.map((channel) => (
            <div
              key={channel.channel_id}
              onClick={() => {
                if (currentChannel?.channel_id !== channel?.channel_id) {
                  onChannelSelect(channel);
                }
                onClose();
              }}
              className={cn(
                "flex items-center gap-3 p-4 hover:bg-gray-50 cursor-pointer",
                channel.channel_id === currentChannel?.channel_id &&
                  "bg-gray-50"
              )}
            >
              <Avatar className="h-10 w-10">
                <AvatarImage
                  src={
                    channel.other_participant.profile_picture_url ||
                    generatePlaceholder(
                      channel.other_participant.first_name,
                      channel.other_participant.last_name
                    )
                  }
                  alt={`${channel.other_participant.first_name} ${channel.other_participant.last_name}`}
                />
                <AvatarFallback>
                  {channel.other_participant.first_name[0]}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {`${channel.other_participant.first_name} ${channel.other_participant.last_name}`}
                </p>
                <p className="text-xs text-gray-500">
                  {channel.other_participant.user_type}
                </p>
              </div>
              <div className="relative">
                {unseenMessages[channel.channel_id] > 0 && (
                  <Badge className="bg-blue-500 ml-2">
                    {unseenMessages[channel.channel_id]}
                  </Badge>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};
