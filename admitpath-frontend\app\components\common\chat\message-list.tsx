import type { Message } from "@/app/types/chat";
import { Avatar, AvatarFallback, AvatarImage } from "@components/ui/avatar";
import { Button } from "@components/ui/button";
import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";
import { generatePlaceholder } from "@/app/utils/image";

interface MessageListProps {
  messages: Message[];
  currentUserId: number;
  isLoadingMore: boolean;
  onLoadMore: () => void;
  hasMoreMessages: boolean;
}

export const MessageList = ({
  messages,
  currentUserId,
  isLoadingMore,
  onLoadMore,
  hasMoreMessages,
}: MessageListProps) => {
  const groupMessagesByDate = (messages: Message[]) => {
    const groups: { [key: string]: Message[] } = {};
    const sortedMessages = [...messages].sort(
      (a, b) =>
        new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    );
    sortedMessages.forEach((message) => {
      const date = new Date(message.created_at);
      // Format as YYYY-MM-DD to ensure consistent grouping
      const dateKey = date.toISOString().split("T")[0];
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(message);
    });
    return groups;
  };

  const getDateLabel = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    // Convert to dates without time for comparison
    const dateWithoutTime = new Date(
      date.getFullYear(),
      date.getMonth(),
      date.getDate()
    );
    const todayWithoutTime = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    const yesterdayWithoutTime = new Date(
      yesterday.getFullYear(),
      yesterday.getMonth(),
      yesterday.getDate()
    );

    if (dateWithoutTime.getTime() === todayWithoutTime.getTime()) {
      return "Today";
    } else if (dateWithoutTime.getTime() === yesterdayWithoutTime.getTime()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString("en-US", {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    }
  };

  if (messages.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-gray-500">
        <p>No messages yet</p>
      </div>
    );
  }

  const groupedMessages = groupMessagesByDate(messages);

  return (
    <div className="flex flex-col p-4 space-y-6">
      {hasMoreMessages && (
        <div className="flex justify-center">
          <Button
            variant="outline"
            size="sm"
            onClick={onLoadMore}
            disabled={isLoadingMore}
            className="mb-4"
          >
            {isLoadingMore ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Loading...
              </>
            ) : (
              "Load More Messages"
            )}
          </Button>
        </div>
      )}

      {Object.entries(groupedMessages).map(([date, msgs]) => (
        <div key={date} className="space-y-4">
          <div className="sticky top-0 flex justify-center">
            <span className="text-xs md:text-sm bg-gray-100 text-gray-500 px-3 md:px-4 py-1 md:py-2 m-2 rounded-full md:drop-shadow-sm drop-shadow-xl">
              {getDateLabel(date)}
            </span>
          </div>
          {msgs.map((msg) => (
            <div
              key={msg.id}
              className={cn(
                "flex gap-3",
                msg.sender_id === currentUserId
                  ? "justify-end"
                  : "justify-start"
              )}
            >
              {msg.sender_id !== currentUserId && (
                <Avatar className="h-8 w-8 shrink-0">
                  <AvatarImage
                    src={
                      msg.sender_profile_picture_url ||
                      generatePlaceholder(
                        msg.sender_name.split(" ")[0],
                        msg.sender_name.split(" ")[1]
                      )
                    }
                    alt={msg.sender_name}
                  />
                  <AvatarFallback>{msg.sender_name[0]}</AvatarFallback>
                </Avatar>
              )}
              <div
                className={cn(
                  "max-w-[70%] rounded-2xl px-4 py-2",
                  msg.sender_id === currentUserId
                    ? "bg-[#3E84F8] text-white"
                    : "bg-white border"
                )}
              >
                <p className="break-words text-sm">{msg.message}</p>
                <p className="text-[10px] mt-1 opacity-70">
                  {new Date(msg.created_at).toLocaleTimeString([], {
                    hour: "2-digit",
                    minute: "2-digit",
                  })}
                </p>
              </div>
            </div>
          ))}
        </div>
      ))}
    </div>
  );
};
