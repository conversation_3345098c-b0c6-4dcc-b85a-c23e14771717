import { Skeleton } from "@components/ui/skeleton";
import { cn } from "@/lib/utils";

export function SidebarSkeleton() {
  return (
    <div className="w-full h-full">
      <div className="p-4 border-b">
        <Skeleton className="h-7 w-24 mb-4" />
        <Skeleton className="h-10 w-full rounded-full" />
      </div>
      <div className="p-2">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex items-center gap-3 p-2">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="flex-1">
              <Skeleton className="h-4 w-24 mb-2" />
              <Skeleton className="h-3 w-32" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export function HeaderSkeleton() {
  return (
    <div className="flex items-center justify-between p-4 bg-white border-b">
      <div className="flex items-center gap-3">
        <Skeleton className="h-10 w-10 rounded-full" />
        <Skeleton className="h-6 w-32" />
      </div>
    </div>
  );
}

export function ChatMessageSkeleton({
  align = "start",
}: {
  align?: "start" | "end";
}) {
  return (
    <div
      className={cn(
        "flex gap-3 my-4 mx-3",
        align === "end" ? "justify-end" : "justify-start"
      )}
    >
      {align === "start" && (
        <Skeleton className="h-8 w-8 rounded-full shrink-0" />
      )}
      <div
        className={cn(
          "flex flex-col gap-2",
          align === "end" ? "items-end" : "items-start"
        )}
      >
        <Skeleton className="h-16 w-[250px] rounded-2xl" />
        <Skeleton className="h-3 w-16" />
      </div>
    </div>
  );
}
