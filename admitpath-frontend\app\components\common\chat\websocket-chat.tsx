"use client";

import { useEffect, useState } from "react";
import {
  useWebSocketChat,
  setWebSocketChatUserInfo,
} from "@/app/hooks/useWebSocketChat";
import { ChatSidebar } from "./chat-sidebar";
import ChatInterface from "./chat-interface";

export default function WebSocketMessages({
  useProfileHook,
}: {
  useProfileHook: any;
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [message, setMessage] = useState("");
  const {
    channels,
    currentChannel,
    messages,
    loading,
    connectionStatus,
    fetchChannels,
    setCurrentChannel,
    sendMessage,
    fetchMessages,
    reconnectWebSocket,
    isWebSocketConnected,
    getUnseenChannelsCount,
    unseenMessages,
  } = useWebSocketChat();

  const { userInfo } = useProfileHook();

  useEffect(() => {
    if (userInfo && userInfo.user_id) {
      setWebSocketChatUserInfo({ user_id: userInfo.user_id });
    }
  }, [userInfo]);

  useEffect(() => {
    if (!localStorage.getItem("access_token")) {
      window.location.href = "/auth/login";
      return;
    }

    const initializeChat = async () => {
      await fetchChannels();
      await getUnseenChannelsCount();
      if (channels && channels.length > 0 && !currentChannel) {
        setCurrentChannel(channels[0]);
      }
    };

    initializeChat();

    return () => {
      if (currentChannel) {
        setCurrentChannel(null);
      }
    };
  }, []);

  useEffect(() => {
    if (channels && channels.length > 0 && !currentChannel) {
      setCurrentChannel(channels[0]);
    }
  }, [channels, currentChannel]);

  const handleSendMessage = async () => {
    if (!message.trim() || !currentChannel || !userInfo) return;

    try {
      await sendMessage({
        channel_id: currentChannel.channel_id,
        recipient_id: currentChannel.other_participant.user_id,
        message: message.trim(),
        sender_id: userInfo.user_id,
        sender_name: `${userInfo.firstName} ${userInfo.lastName}`,
      });
      setMessage("");
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const fetchMoreMessages = async (skip: number) => {
    if (currentChannel) {
      await fetchMessages(currentChannel.channel_id, skip);
    }
  };

  const handleReconnect = () => {
    reconnectWebSocket();
  };

  return (
    <div className="h-[calc(100vh-7rem)] md:h-[calc(100vh-9rem)] shadow-sm">
      <div className="flex h-full rounded-xl overflow-hidden">
        <ChatSidebar
          channels={channels}
          currentChannel={currentChannel}
          onChannelSelect={setCurrentChannel}
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
          loading={loading}
          unseenMessages={unseenMessages}
        />
        <ChatInterface
          loading={loading}
          userInfo={userInfo}
          currentChannel={currentChannel}
          messages={messages}
          message={message}
          setMessage={setMessage}
          handleSendMessage={handleSendMessage}
          sidebarOpen={sidebarOpen}
          setSidebarOpen={setSidebarOpen}
          fetchMoreMessages={fetchMoreMessages}
          connectionStatus={connectionStatus}
          onReconnect={handleReconnect}
          isConnected={isWebSocketConnected()}
        />
      </div>
    </div>
  );
}
