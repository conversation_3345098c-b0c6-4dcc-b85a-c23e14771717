"use client";

import type React from "react";
import { useState, useEffect } from "react";
import { ChevronDown } from "lucide-react";
import { format, parse, isValid } from "date-fns";
import { cn } from "@/lib/utils";
import { Button } from "@components/ui/button";
import { Calendar } from "@components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@components/ui/popover";
import { Input } from "@components/ui/input";
import { Label } from "@components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

interface BirthdayPickerProps {
  value?: string;
  onChange: (date: string) => void;
  required?: boolean;
}

export default function BirthdayPicker({
  value,
  onChange,
  required = false,
}: BirthdayPickerProps) {
  const [date, setDate] = useState<Date | undefined>(
    value ? new Date(value) : undefined
  );
  const [day, setDay] = useState(
    value ? new Date(value).getDate().toString().padStart(2, "0") : ""
  );
  const [month, setMonth] = useState(
    value ? (new Date(value).getMonth() + 1).toString().padStart(2, "0") : ""
  );
  const [year, setYear] = useState(
    value ? new Date(value).getFullYear().toString() : ""
  );
  const [calendarKey, setCalendarKey] = useState(0);

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 100 }, (_, i) => currentYear - i);

  useEffect(() => {
    if (value) {
      const newDate = new Date(value);
      if (isValid(newDate)) {
        setDate(newDate);
        setDay(newDate.getDate().toString().padStart(2, "0"));
        setMonth((newDate.getMonth() + 1).toString().padStart(2, "0"));
        setYear(newDate.getFullYear().toString());
      }
    }
  }, [value]);

  const updateDateFromInputs = () => {
    const dateString = `${year}-${month}-${day}`;
    const newDate = parse(dateString, "yyyy-MM-dd", new Date());
    if (isValid(newDate)) {
      setDate(newDate);
      onChange(format(newDate, "yyyy-MM-dd"));
      setCalendarKey((prev) => prev + 1);
    }
  };

  const handleInputChange = (
    value: string,
    setter: React.Dispatch<React.SetStateAction<string>>
  ) => {
    const numericValue = value.replace(/\D/g, "");
    setter(numericValue);
  };

  const handleYearChange = (selectedYear: string) => {
    setYear(selectedYear);
    const newDate = date ? new Date(date) : new Date();
    newDate.setFullYear(Number.parseInt(selectedYear));
    setDate(newDate);
    onChange(format(newDate, "yyyy-MM-dd"));
    setCalendarKey((prev) => prev + 1);
  };

  return (
    <div className="w-full">
      <Label
        htmlFor="dob-input"
        className="block mb-2 text-sm font-medium text-gray-900"
      >
        Date of Birth {required && <span className="text-red-500">*</span>}
      </Label>
      <div className="flex flex-col md:flex-row lg:flex-col 2xl:flex-row  gap-2">
        <div className="flex gap-2 w-full md:w-[60%] lg:w-full 2xl:w-[60%] ">
          <Input
            id="dob-day"
            value={day}
            onChange={(e) => handleInputChange(e.target.value, setDay)}
            onBlur={updateDateFromInputs}
            placeholder="DD"
            className="h-full p-3 border rounded-lg"
            maxLength={2}
            required={required}
          />
          <Input
            id="dob-month"
            value={month}
            onChange={(e) => handleInputChange(e.target.value, setMonth)}
            onBlur={updateDateFromInputs}
            placeholder="MM"
            className="h-full p-3 border rounded-lg"
            maxLength={2}
            required={required}
          />
          <Input
            id="dob-year"
            value={year}
            onChange={(e) => handleInputChange(e.target.value, setYear)}
            onBlur={updateDateFromInputs}
            placeholder="YYYY"
            className="h-full p-3 border rounded-lg"
            maxLength={4}
            required={required}
          />
        </div>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant={"outline"}
              className={cn(
                "w-full md:w-[40%] lg:w-full 2xl:w-[40%] h-full justify-start text-left font-normal",
                !date && "text-muted-foreground"
              )}
            >
              {date ? format(date, "PPP") : <span>Pick a date</span>}
              <ChevronDown className="ml-auto h-4 w-4 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0 bg-white" align="start">
            <div className="flex flex-col space-y-2 p-2">
              <Select value={year} onValueChange={handleYearChange}>
                <SelectTrigger>
                  <SelectValue placeholder="Select Year" />
                </SelectTrigger>
                <SelectContent className="bg-white">
                  {years.map((y) => (
                    <SelectItem
                      key={y}
                      value={y.toString()}
                      className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                    >
                      {y}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Calendar
                key={calendarKey}
                mode="single"
                selected={date}
                onSelect={(newDate) => {
                  setDate(newDate);
                  if (newDate) {
                    setDay(newDate.getDate().toString().padStart(2, "0"));
                    setMonth(
                      (newDate.getMonth() + 1).toString().padStart(2, "0")
                    );
                    setYear(newDate.getFullYear().toString());
                    onChange(format(newDate, "yyyy-MM-dd"));
                  }
                }}
                defaultMonth={date}
                fromYear={Number.parseInt(year) || currentYear - 100}
                toYear={Number.parseInt(year) || currentYear}
              />
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
}
