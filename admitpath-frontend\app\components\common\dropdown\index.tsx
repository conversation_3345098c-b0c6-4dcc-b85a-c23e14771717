"use client";

import React from "react";

interface DropdownProps {
  label: string;
  name: string;
  options: { value: string | number; label: string }[];
  value: string | number;
  onChange: React.ChangeEventHandler<HTMLSelectElement>;
  required?: boolean;
  className?: string;
  selectStyle?: string;
}

const Dropdown: React.FC<DropdownProps> = ({
  label,
  name,
  options,
  value,
  onChange,
  required = false,
  className = "",
  selectStyle = "",
}) => {
  return (
    <div className={`dropdown relative ${className}`}>
      <label htmlFor={name} className="block font-medium mb-2 text-sm">
        {label} {required && <span>*</span>}
      </label>
      <div className="relative">
        <select
          name={name}
          id={name}
          value={value}
          onChange={onChange}
          required={required}
          className={`appearance-none py-3.5 px-3 pr-10 rounded border border-solid border-slate-200 w-full bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors ${selectStyle}`}
        >
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {/* Custom Down Arrow Icon */}
        <span className="absolute inset-y-0 right-4 flex items-center pointer-events-none">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-5 w-5 text-gray-500"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
              clipRule="evenodd"
            />
          </svg>
        </span>
      </div>
    </div>
  );
};

export default Dropdown;
