.container {
  @apply flex flex-col flex-1 shrink basis-0 min-w-[240px];
}

.label {
  @apply text-sm font-medium leading-6 text-neutral-900;
}

.required {
  @apply text-rose-500;
}

.input {
  @apply w-full px-4 py-3 mt-1 text-base tracking-tight rounded border border-solid border-slate-200 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors;
}

.input[type="password"] {
  @apply pr-12;
}

.input.error {
  @apply border-rose-500 focus:ring-rose-500;
}

.errorMessage {
  @apply text-rose-500 text-sm mt-1;
}