"use client";

import React, { FC, InputHTMLAttributes } from "react";
import styles from "./index.module.css";

interface InputFieldProps extends InputHTMLAttributes<HTMLInputElement> {
  label: string;
  error?: string;
  required?: boolean;
}

export const InputField: FC<InputFieldProps> = ({
  label,
  required = false,
  error,
  ...inputProps
}) => {
  return (
    <div className={styles.container}>
      <label
        htmlFor={inputProps.id || inputProps.name}
        className={styles.label}
      >
        {label} {required && <span className={styles.required}>*</span>}
      </label>

      <input
        {...inputProps}
        className={`${styles.input} ${error ? styles.error : ""}`}
        aria-required={required}
        aria-invalid={!!error}
        aria-describedby={error ? `${inputProps.id}-error` : undefined}
      />

      {error && (
        <span
          id={`${inputProps.id}-error`}
          className={styles.errorMessage}
          role="alert"
        >
          {error}
        </span>
      )}
    </div>
  );
};
