'use client';

import React from 'react';
import styles from './index.module.css';

interface LogoProps {
  text: string;
  className?: string;
}

export const Logo: React.FC<LogoProps> = ({ text, className = '' }) => {
    return (
        <div 
          className={`${styles.logo} ${className}`} 
          role="banner" 
          aria-label='Logo banner'
        >
          {text}
        </div>
      );
    };