"use client";

import React from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { IconProp } from "@fortawesome/fontawesome-svg-core";

interface ButtonProps {
  type?: "submit" | "reset" | "button";
  children: React.ReactNode;
  onClick?: (
    e: React.MouseEvent<HTMLButtonElement> | React.FormEvent<Element>
  ) => void;
  style?: React.CSSProperties;
  className?: string;
  variant?: "primary" | "secondary" | "neutral";
  icon?: IconProp;
  disabled?: boolean;
}

export const MainButton: React.FC<ButtonProps> = ({
  type,
  children,
  onClick,
  style,
  className = "",
  variant = "neutral",
  icon,
  disabled = false,
}) => {
  // Default styles for button variants
  const variantStyles = {
    primary: "bg-mainClr text-white hover:bg-[#152070] focus:ring-blue-300",
    secondary: "bg-[#EB5757] text-white hover:bg-[#EB6767]",
    neutral: "bg-neutral1 text-neutral10 hover:bg-neutral2 focus:ring-neutral3",
  };

  const disabledStyles = "opacity-50 cursor-not-allowed";

  return (
    <button
      type={type}
      className={`py-3 px-6 font-medium rounded-xl text-sm flex items-center justify-center gap-2 
        ${variantStyles[variant]} ${
        disabled ? disabledStyles : ""
      } ${className}`}
      style={style}
      onClick={!disabled ? onClick : undefined}
      disabled={disabled}
    >
      {children}
      {icon && <FontAwesomeIcon icon={icon} />}
    </button>
  );
};
