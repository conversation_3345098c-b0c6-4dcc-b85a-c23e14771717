"use client";

import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrowRight } from "@fortawesome/free-solid-svg-icons";
import { MainButton } from "../mainBtn";

interface NavigationButtonsProps {
  currentIndex: number;
  onNext: () => void;
  onBack: () => void;
  disableBack?: boolean;
  nextLabel?: string;
}

export const NavigationButtons: React.FC<NavigationButtonsProps> = ({
  currentIndex,
  onNext,
  onBack,
  disableBack = false,
  nextLabel,
}) => (
  <div className="flex justify-end gap-3 mt-8">
    {nextLabel === "Next" && (
      <button
        type="button"
        className="border py-1.5 px-6 rounded-lg hover:bg-[#f5eaea]"
        onClick={onBack}
        disabled={disableBack}
      >
        Back
      </button>
    )}

    {/* <button
      type="submit"
      className="bg-mainClr text-white py-1.5 px-6 rounded-lg hover:bg-[#2d2f3b]"
    >
     
    </button> */}
    <MainButton variant="primary">
      {" "}
      {nextLabel}{" "}
      {nextLabel === "Next" && (
        <span>
          <FontAwesomeIcon icon={faArrowRight} />
        </span>
      )}
    </MainButton>
  </div>
);
