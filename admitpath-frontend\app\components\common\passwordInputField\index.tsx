"use client";

import React, { FC, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEye, faEyeSlash } from '@fortawesome/free-solid-svg-icons';
import styles from './index.module.css';
import { InputField } from '../inputField';

interface PasswordInputFieldProps {
  label: string;
  name: string;
  value: string;
  placeholder?: string;
  required?: boolean;
  error?: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  showPasswordTooltip?: boolean;
}

export const PasswordInputField: FC<PasswordInputFieldProps> = ({
  label,
  showPasswordTooltip = false,
  ...props
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);

  const togglePasswordVisibility = () => setShowPassword((prev) => !prev);

  return (
    <div className={styles.passwordContainer}>
      <InputField
        {...props}
        type={showPassword ? 'text' : 'password'}
        label={label}
        onFocus={() => setShowTooltip(true)}
        onBlur={() => setShowTooltip(false)}
      />

      <button
        type="button"
        onClick={togglePasswordVisibility}
        className={styles.inputIcon}
        aria-label={showPassword ? 'Hide password' : 'Show password'}
      >
        <FontAwesomeIcon
          icon={showPassword ? faEyeSlash : faEye}
          className={styles.eyeIcon}
        />
      </button>

      {showPasswordTooltip && showTooltip && (
        <div className={styles.passwordTooltip}>
          <ul>
            <li>Minimum 8 characters</li>
            <li>At least 1 number</li>
            <li>At least 1 uppercase letter</li>
            <li>At least 1 symbol (!@#$%^&*)</li>
          </ul>
        </div>
      )}
    </div>
  );
};
