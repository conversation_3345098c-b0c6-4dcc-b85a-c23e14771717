"use client";

import { FC } from "react";
import { FaCheck, FaTimes, FaRegSquare } from "react-icons/fa";

interface PasswordRequirement {
  label: string;
  validator: (password: string, confirmPassword?: string) => boolean;
}

interface PasswordRequirementsProps {
  password: string;
  confirmPassword?: string;
}

const requirements: PasswordRequirement[] = [
  {
    label: "At least 8 characters long",
    validator: (password) => password.length >= 8,
  },
  {
    label: "Contains at least one uppercase letter",
    validator: (password) => /[A-Z]/.test(password),
  },
  {
    label: "Contains at least one lowercase letter",
    validator: (password) => /[a-z]/.test(password),
  },
  {
    label: "Contains at least one number",
    validator: (password) => /\d/.test(password),
  },
  {
    label: "Contains at least one special character",
    validator: (password) => /[!@#$%^&*(),.?":{}|<>]/.test(password),
  },
  {
    label: "Passwords match",
    validator: (password, confirmPassword) => password === confirmPassword,
  },
];

export const PasswordRequirements: FC<PasswordRequirementsProps> = ({
  password,
  confirmPassword = "",
}) => {
  return (
    <div className="mt-2 space-y-2 text-sm">
      {requirements.map((requirement, index) => {
        const isMet = requirement.validator(password, confirmPassword);
        const shouldShow = password || requirement.label === "Passwords match";

        return (
          <div
            key={index}
            className={`flex items-center space-x-2 ${
              isMet
                ? "text-green-600"
                : shouldShow
                ? "text-red-600"
                : "text-gray-500"
            }`}
          >
            <div className="w-4 h-4 flex items-center justify-center">
              {shouldShow ? (
                isMet ? (
                  <FaCheck className="w-full h-full" />
                ) : (
                  <FaTimes className="w-full h-full" />
                )
              ) : (
                <FaRegSquare className="w-[0.875rem] h-[0.875rem]" />
              )}
            </div>
            <span>{requirement.label}</span>
          </div>
        );
      })}
    </div>
  );
};

export const validatePassword = (
  password: string,
  confirmPassword?: string
): boolean => {
  return requirements.every((requirement) =>
    requirement.validator(password, confirmPassword)
  );
};
