"use client";

import React, { ReactNode } from "react";

interface PopupProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  width?: string;
  height?: string;
  children: ReactNode;
}

const Popup: React.FC<PopupProps> = ({
  isOpen,
  onClose,
  title,
  width,
  height = "80vh",
  children,
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div
        className="bg-white rounded-xl shadow-lg relative overflow-hidden w-[95vw] md:w-[80vw] lg:w-[60vw] max-w-4xl"
        style={{ width }}
      >
        <div className="bg-grayLight p-4 mb-4">
          {title && <h2 className="text-xl font-semibold ">{title}</h2>}
          <button
            onClick={onClose}
            className="absolute top-5 right-4 text-xs text-gray-500 hover:text-black"
            aria-label="Close"
          >
            ✖
          </button>
        </div>
        <div
          className="popupz p-4 pb-6 overflow-y-auto overflow-x-hidden"
          style={{ maxHeight: height }}
        >
          {children}
        </div>
      </div>
    </div>
  );
};

export default Popup;
