"use client";

import { FC } from "react";
import { FaA<PERSON>le, FaLinkedin } from "react-icons/fa";
import { SOCIAL_PROVIDERS } from "@constants/socialButtons";
import { useAuth } from "@/app/hooks/useAuth";
import { useState } from "react";
import { toast } from "react-toastify";

interface SocialButtonProps {
  label: string;
  altText: string;
  type: "google" | "linkedin" | "apple";
  userType?: "student" | "counselor";
  isLogin?: boolean;
}

export const SocialButton: FC<SocialButtonProps> = ({
  label,
  altText,
  type,
  userType,
  isLogin = false,
}) => {
  const { googleAuth, initiateLinkedInAuth } = useAuth();
  const [loading, setLoading] = useState(false);

  const handleClick = async () => {
    setLoading(true);
    try {
      switch (type) {
        case SOCIAL_PROVIDERS.GOOGLE:
          const { google } = window as any;
          const auth2 = google.accounts.oauth2;

          const client = auth2.initTokenClient({
            client_id: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,
            scope: "openid email profile",
            callback: async (response: any) => {
              try {
                // Only pass userType for signup
                await googleAuth(
                  response.access_token,
                  isLogin ? undefined : userType
                );
              } catch (error: any) {
                if (process.env.NODE_ENV === "development") {
                  console.error("Google auth error:", error);
                }

                if (
                  error.response?.data?.detail.includes("already registered")
                ) {
                  toast.error(error.response?.data?.detail, {
                    toastId: "auth-error",
                  });
                } else if (
                  error.response?.data?.detail ===
                  "user_type is required for registration"
                ) {
                  toast.error(
                    "Please use the signup page to register as either a student or counselor.",
                    { toastId: "auth-error" }
                  );
                } else {
                  toast.error(
                    "Failed to sign in with Google. Please try again.",
                    { toastId: "auth-error" }
                  );
                }
              } finally {
                setLoading(false);
              }
            },
          });

          client.requestAccessToken();
          break;

        case SOCIAL_PROVIDERS.LINKEDIN:
          try {
            // Only pass userType for signup
            initiateLinkedInAuth(isLogin ? undefined : userType);
          } catch (error: any) {
            if (process.env.NODE_ENV === "development") {
              console.error("LinkedIn auth error:", error);
            }

            if (error.response?.data?.detail.includes("already registered")) {
              toast.error(error.response?.data?.detail, {
                toastId: "auth-error",
              });
            } else if (
              error.response?.data?.detail ===
              "user_type is required for registration"
            ) {
              toast.error(
                "Please use the signup page to register as either a student or counselor.",
                { toastId: "auth-error" }
              );
            } else {
              toast.error(
                "Failed to sign in with LinkedIn. Please try again.",
                { toastId: "auth-error" }
              );
            }
          }
          setLoading(false);
          break;

        // case SOCIAL_PROVIDERS.APPLE:
        //   if (process.env.NODE_ENV === "development") {
        //     console.log("Apple Sign In not implemented yet");
        //   }
        //   toast.info("Apple Sign In coming soon!", { toastId: "apple-signin" });
        //   setLoading(false);
        //   break;
      }
    } catch (error) {
      if (process.env.NODE_ENV === "development") {
        console.error("Social auth error:", error);
      }
      toast.error("Something went wrong. Please try again.", {
        toastId: "auth-error",
      });
      setLoading(false);
    }
  };

  const getIcon = (value: string) => {
    switch (value) {
      case SOCIAL_PROVIDERS.LINKEDIN:
        return (
          <FaLinkedin className="w-5 h-5 text-blue-600" aria-hidden="true" />
        );
      // case SOCIAL_PROVIDERS.APPLE:
      //   return <FaApple className="w-5 h-5 text-gray-800" aria-hidden="true" />;
      case SOCIAL_PROVIDERS.GOOGLE:
        return (
          <span className="w-5 h-5">
            <img src="/svgs/googleIcon.svg" alt="Icon" />
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <button
      type="button"
      className="flex items-center justify-center w-full px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      aria-label={altText}
      onClick={handleClick}
      disabled={loading}
    >
      <div className="flex items-center gap-2">
        {getIcon(type)}
        <span>{loading ? "Loading..." : label}</span>
      </div>
    </button>
  );
};
