import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { IconDefinition } from "@fortawesome/fontawesome-svg-core";

interface StyledIconProps {
  icon: IconDefinition;
  bgColor?: string;
  color?: string;
}

const StyledIcon: React.FC<StyledIconProps> = ({ icon, bgColor, color }) => (
  <span
    className="p-2 rounded-xl h-9 w-9 flex justify-center items-center"
    style={{ backgroundColor: bgColor, color: color }}
  >
    <FontAwesomeIcon icon={icon} size="sm" />
  </span>
);

export default StyledIcon;
