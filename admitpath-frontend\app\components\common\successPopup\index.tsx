"use client";

import { MainButton } from "../mainBtn";

interface SuccessPopupProps {
  isOpen: string;
  onClose: () => void;
}

export default function SuccessPopup({ isOpen, onClose }: SuccessPopupProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
        {/* Image */}
        <div className="flex justify-center mb-4">
          <img
            src={
              isOpen === "remove" ? "/images/remove.png" : "/images/success.png"
            }
            alt="Success"
            className="w-20 h-20 object-cover rounded-full"
          />
        </div>

        {/* Heading */}
        <h2 className="text-xl font-bold text-center text-gray-800 mb-2">
          Successfully{" "}
          {isOpen === "add"
            ? "Added"
            : isOpen === "edit"
            ? "Edited"
            : "Removed"}
        </h2>

        {/* Paragraph */}
        <p className="text-center text-gray-600 mb-4">
          Lorem ipsum dolor sit amet consectetur. Egestas elit ultrices tellus
          cursus bibendum.
        </p>

        {/* Close Button */}
        <div className="flex justify-center">
          <MainButton
            type="button"
            variant="neutral"
            children="Close"
            onClick={onClose}
          />
        </div>
      </div>
    </div>
  );
}
