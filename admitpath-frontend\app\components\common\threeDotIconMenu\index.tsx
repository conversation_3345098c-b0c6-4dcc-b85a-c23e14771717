import React, { useState, useRef, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faEllipsisV,
  faPencil,
  faTrash,
} from "@fortawesome/free-solid-svg-icons";

interface DropdownProps {
  handleEdit: () => void;
  handleRemove: () => void;
  name?: string;
  isLoading?: boolean;
}

const ThreeDotIconDropdown: React.FC<DropdownProps> = ({
  handleEdit,
  handleRemove,
  name = "Edit",
  isLoading = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const toggleMenu = () => setIsOpen((prev) => !prev);

  const handleClickOutside = (event: MouseEvent) => {
    if (
      dropdownRef.current &&
      !dropdownRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={toggleMenu}
        className={isLoading ? "opacity-50 cursor-wait" : ""}
        disabled={isLoading}
      >
        <FontAwesomeIcon
          icon={faEllipsisV}
          className={`w-4 ${isLoading ? "animate-pulse" : ""}`}
        />
      </button>
      {isOpen && (
        <ul className="absolute top-full right-0 text-sm p-3 bg-white rounded-lg shadow-md w-36">
          <li
            className="mb-2 last:mb-0 cursor-pointer flex items-center text-neutral8"
            onClick={() => {
              handleEdit();
              setIsOpen(false);
            }}
          >
            <FontAwesomeIcon icon={faPencil} className="w-4 mr-2" />
            {name}
          </li>
          <li
            className="cursor-pointer flex items-center text-[#EB5757]"
            onClick={() => {
              handleRemove();
              setIsOpen(false);
            }}
          >
            <FontAwesomeIcon icon={faTrash} className="w-4 mr-2" />
            Remove
          </li>
        </ul>
      )}
    </div>
  );
};

export default ThreeDotIconDropdown;
