import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faBars } from "@fortawesome/free-solid-svg-icons";

export default function HamburgerMenu({
  toggleSidebar,
}: {
  toggleSidebar: () => void;
}) {
  return (
    <button
      className="absolute top-7 left-6 z-20 md:hidden"
      onClick={toggleSidebar}
    >
      <FontAwesomeIcon icon={faBars} className="text-2xl text-black" />
    </button>
  );
}
