"use client";

import Image from "next/image";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faChevronDown } from "@fortawesome/free-solid-svg-icons";
import UserDropdown from "./UserDropdown";
import { generatePlaceholder } from "@/app/utils/image";

export default function Header({
  userInfo,
  isDropdownOpen,
  toggleDropdown,
  dropdownRef,
}: {
  userInfo: {
    firstName: string;
    lastName: string;
    email: string;
    profile_picture_url: string;
  } | null;
  isDropdownOpen: boolean;
  toggleDropdown: () => void;
  dropdownRef: React.RefObject<HTMLDivElement | null>;
}) {
  return (
    <div className="user-container p-5 mb-4 flex gap-3 justify-end items-center relative">
      <Image
        src={
          userInfo?.profile_picture_url ||
          generatePlaceholder(userInfo?.firstName, userInfo?.lastName)
        }
        alt="User"
        width={40}
        height={40}
        className="rounded-full border object-cover object-top w-12 h-12"
      />
      <div className="text-sm hidden lg:block">
        <h3>
          {userInfo?.firstName} {userInfo?.lastName}
        </h3>
        <p>{userInfo?.email}</p>
      </div>
      <button
        onClick={toggleDropdown}
        className="text-black focus:outline-none"
      >
        <FontAwesomeIcon icon={faChevronDown} className="w-3" />
      </button>
      {isDropdownOpen && <UserDropdown dropdownRef={dropdownRef} />}
    </div>
  );
}
