import Link from "next/link";
import Image from "next/image";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faHome,
  faCalendar,
  faUser,
  faBoxOpen,
  faDollarSign,
  faBook,
  faEnvelope,
  faVideo,
  faClose,
} from "@fortawesome/free-solid-svg-icons";
import { useWebSocketChat } from "@/app/hooks/useWebSocketChat";
import { useEffect } from "react";

const navItems = [
  {
    name: "Overview",
    icon: faHome,
    url: "/counselor/dashboard",
    tooltip: "Overview",
  },
  {
    name: "Availability",
    icon: faCalendar,
    url: "/counselor/dashboard/availability",
    tooltip: "Avaialability",
  },
  {
    name: "Profile",
    icon: faUser,
    url: "/counselor/dashboard/profile/view",
    tooltip: "Profile",
  },
  {
    name: "Sessions",
    icon: faVideo,
    url: "/counselor/dashboard/sessions",
    tooltip: "Sessions",
  },
  {
    name: "Services",
    icon: faBox<PERSON><PERSON>,
    url: "/counselor/dashboard/services",
    tooltip: "Services",
  },
  {
    name: "Packages",
    icon: faBoxOpen,
    url: "/counselor/dashboard/packages",
    tooltip: "Packages",
  },
  {
    name: "Payments",
    icon: faDollarSign,
    url: "/counselor/dashboard/payments",
    tooltip: "Payments",
  },
  {
    name: "Resources",
    icon: faBook,
    url: "/counselor/dashboard/resources",
    tooltip: "Resournces",
  },
  {
    name: "Messages",
    icon: faEnvelope,
    url: "/counselor/dashboard/messages",
    tooltip: "Messages",
  },
];

export default function Sidebar({
  isSidebarOpen,
  setIsSidebarOpen,
  isActive,
}: {
  isSidebarOpen: boolean;
  setIsSidebarOpen: (isOpen: boolean) => void;
  isActive: (url: string) => boolean;
}) {
  const { getUnseenChannelsCount, unseenChannelsCount } = useWebSocketChat();

  useEffect(() => {
    const fetchUnseenCount = async () => {
      await getUnseenChannelsCount();
    };

    fetchUnseenCount();
  }, []);
  return (
    <aside
      className={`bg-white p-4 fixed w-64 lg:w-1/6 md:w-[80px] h-screen z-50 transform transition-transform duration-300 ${
        isSidebarOpen ? "translate-x-0" : "translate-x-[-100%] md:translate-x-0"
      }`}
    >
      <ul className="grid gap-3">
        <li className="mb-7 flex md:justify-center justify-between items-center">
          <Link href="/counselor/dashboard">
            <Image
              width={70}
              height={70}
              src="/icons/logo.png"
              alt="Logo"
              className="transition-all duration-300 cursor-pointer"
            />
          </Link>
          <button className="md:hidden" onClick={() => setIsSidebarOpen(false)}>
            <FontAwesomeIcon icon={faClose} className="text-2xl text-black" />
          </button>
        </li>
        {navItems.map(({ name, icon, url, tooltip }) => (
          <Link
            href={url}
            key={name}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg cursor-pointer ${
              isActive(url) ? "bg-mainClr text-white" : "bg-white text-black"
            }`}
            title={tooltip}
          >
            <FontAwesomeIcon icon={icon} className="w-4 lg:w-6" />
            <span className="md:hidden lg:inline">{name}</span>
            {name === "Messages" && unseenChannelsCount > 0 && (
              <span className="ml-2 px-2 py-1 text-xs bg-green-500 text-white rounded-full">
                {unseenChannelsCount}
              </span>
            )}
          </Link>
        ))}
      </ul>
    </aside>
  );
}
