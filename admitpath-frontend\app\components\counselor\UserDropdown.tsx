import { useAuth } from "@/app/hooks/useAuth";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useProfile } from "@/app/hooks/counselor/useProfile";

export default function UserDropdown({
  dropdownRef,
}: {
  dropdownRef: React.RefObject<HTMLDivElement | null>;
}) {
  const router = useRouter();
  const { logout } = useAuth();
  const { userInfo } = useProfile();

  const handleLogout = () => {
    logout();
    router.push("/auth/login");
  };

  return (
    <div
      ref={dropdownRef}
      className="absolute top-full mt-2 right-0 bg-white shadow-lg rounded-lg p-3 w-48 z-10"
    >
      {userInfo?.user_id && (
        <Link
          href={`/profile/${userInfo.user_id}`}
          className="block px-4 py-2 text-sm text-black hover:bg-gray-100 rounded w-full text-left"
          target="_blank"
        >
          View Public Profile
        </Link>
      )}
      <button
        onClick={handleLogout}
        className="block px-4 py-2 text-sm text-black hover:bg-gray-100 rounded w-full text-left"
      >
        Logout
      </button>
    </div>
  );
}
