import { Info } from "lucide-react";
import { Checkbox } from "@components/ui/checkbox";
import { Button } from "@components/ui/button";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@components/ui/select";
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@components/ui/tooltip";

interface AvailabilityControlsProps {
    timezone: string;
    setTimezone: (timezone: string) => void;
    applyToFollowingWeeks: boolean;
    setApplyToFollowingWeeks: (apply: boolean) => void;
    onSave: () => Promise<void>;
    loading: boolean;
    status: string;
    hasSelectedSlots: boolean;
    timezoneOptions: { id?: string; value: string; label: string }[];
}

export function AvailabilityControls({
    timezone,
    setTimezone,
    applyToFollowingWeeks,
    setApplyToFollowingWeeks,
    onSave,
    loading,
    status,
    hasSelectedSlots,
    timezoneOptions,
}: AvailabilityControlsProps) {
    const currentTimezoneLabel =
        timezoneOptions.find((tz) => tz.value === timezone)?.label || timezone;

    return (
        <>
            <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                <div className="flex items-center space-x-2">
                    <Checkbox
                        id="apply-following-weeks"
                        checked={applyToFollowingWeeks}
                        onCheckedChange={(checked) =>
                            setApplyToFollowingWeeks(checked === true)
                        }
                    />
                    <label
                        htmlFor="apply-following-weeks"
                        className="text-sm text-gray-600 cursor-pointer flex items-center"
                    >
                        Apply changes to following 12 weeks
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <Info className="h-4 w-4 ml-1 text-gray-400" />
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>
                                        Sets the same availability pattern for the
                                        next 12 weeks
                                    </p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </label>
                </div>

                <div className="w-full md:w-auto">
                    <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2 md:hidden">
                        <span className="flex items-center">
                            Current timezone: {currentTimezoneLabel}
                            <Info className="h-4 w-4 ml-1 text-gray-400" />
                        </span>
                    </div>
                    <Select value={timezone} onValueChange={setTimezone}>
                        <SelectTrigger className="w-full md:w-[240px]">
                            <SelectValue placeholder="Select timezone" />
                        </SelectTrigger>
                        <SelectContent className="bg-white">
                            {timezoneOptions.map((tz) => (
                                <SelectItem
                                    key={tz.id || `tz-${tz.value}`}
                                    value={tz.value}
                                    className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                                >
                                    {tz.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                </div>
            </div>

            <div className="flex justify-center mt-4">
                <Button
                    onClick={onSave}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-2 rounded-full w-full md:w-auto"
                    disabled={loading || !hasSelectedSlots}
                >
                    {status}
                </Button>
            </div>
        </>
    );
}
