"use client";

import { useEffect, useState } from "react";
import { format, startOfWeek, addWeeks, subWeeks } from "date-fns";
import { toast } from "react-toastify";
import { useAvailability } from "@/app/hooks/counselor/useAvailability";
import type { AvailabilityCreate } from "@/app/types/counselor/availability";
import { WeekNavigation } from "./week-navigation";
import { TimeGrid } from "./time-grid";
import { AvailabilityControls } from "./availability-controls";
import { TIMEZONE_OPTIONS } from "./constants";

export default function AvailabilityManager() {
    const {
        fetchAvailabilities,
        createAvailability,
        updateAvailability,
        deleteAvailability,
        availabilities,
        loading,
        status,
    } = useAvailability();

    // Get user's local timezone
    const [timezone, setTimezone] = useState(() => {
        return Intl.DateTimeFormat().resolvedOptions().timeZone;
    });

    const [currentWeekStart, setCurrentWeekStart] = useState(() => {
        const today = new Date();
        return startOfWeek(today, { weekStartsOn: 1 }); // Start week on Monday
    });

    // Store unsaved selections for each week to persist them when navigating
    const [unsavedSelections, setUnsavedSelections] = useState<
        Record<string, Record<string, string[]>>
    >({});

    const [selectedSlots, setSelectedSlots] = useState<
        Record<string, string[]>
    >({});
    const [applyToFollowingWeeks, setApplyToFollowingWeeks] = useState(false);
    const [currentAvailability, setCurrentAvailability] = useState<
        number | null
    >(null);
    const [isEditing, setIsEditing] = useState(false);
    const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

    // Format the week range for display
    const weekRangeText = `${format(currentWeekStart, "MMM dd")} — ${format(
        addWeeks(currentWeekStart, 0).setDate(currentWeekStart.getDate() + 6),
        "MMM dd, yyyy"
    )}`;

    useEffect(() => {
        const loadAvailabilities = async () => {
            await fetchAvailabilities(timezone);
        };

        loadAvailabilities();
    }, [timezone, fetchAvailabilities]);

    useEffect(() => {
        const currentWeekKey = format(currentWeekStart, "yyyy-MM-dd");

        // Check for unsaved changes for this week
        const hasUnsavedForThisWeek = unsavedSelections[currentWeekKey];

        // Find if there's an existing availability for the current week
        const existingAvailability = availabilities.find(
            (avail) => avail.week_start === currentWeekKey
        );

        if (hasUnsavedForThisWeek) {
            // Prioritize unsaved changes if they exist
            setSelectedSlots(hasUnsavedForThisWeek);
            setCurrentAvailability(existingAvailability?.id || null);
            setIsEditing(!!existingAvailability);
            setHasUnsavedChanges(true);
        } else if (existingAvailability) {
            // Otherwise, use saved availability
            setSelectedSlots(existingAvailability.available_slots);
            setCurrentAvailability(existingAvailability.id);
            setIsEditing(true);
            setHasUnsavedChanges(false);
        } else {
            // No existing data for this week
            setSelectedSlots({});
            setCurrentAvailability(null);
            setIsEditing(false);
            setHasUnsavedChanges(false);
        }
    }, [availabilities, currentWeekStart, unsavedSelections]);

    const saveCurrentSelections = () => {
        // Store current selections before navigating
        const currentWeekKey = format(currentWeekStart, "yyyy-MM-dd");
        if (hasUnsavedChanges) {
            setUnsavedSelections((prev) => ({
                ...prev,
                [currentWeekKey]: { ...selectedSlots },
            }));
        }
    };

    const handlePreviousWeek = () => {
        saveCurrentSelections();
        const newWeekStart = subWeeks(currentWeekStart, 1);
        setCurrentWeekStart(newWeekStart);
    };

    const handleNextWeek = () => {
        saveCurrentSelections();
        const newWeekStart = addWeeks(currentWeekStart, 1);
        setCurrentWeekStart(newWeekStart);
    };

    const handleTodayClick = () => {
        saveCurrentSelections();
        const today = new Date();
        setCurrentWeekStart(startOfWeek(today, { weekStartsOn: 1 }));
    };

    const toggleTimeSlot = (dayIndex: number, timeSlot: string) => {
        const dayKey = dayIndex.toString();

        setSelectedSlots((prev) => {
            const newSlots = { ...prev };

            if (!newSlots[dayKey]) {
                newSlots[dayKey] = [timeSlot];
            } else if (newSlots[dayKey].includes(timeSlot)) {
                newSlots[dayKey] = newSlots[dayKey].filter(
                    (slot) => slot !== timeSlot
                );
                if (newSlots[dayKey].length === 0) {
                    delete newSlots[dayKey];
                }
            } else {
                newSlots[dayKey] = [...newSlots[dayKey], timeSlot].sort();
            }

            return newSlots;
        });
    };

    const handleSaveAvailability = async () => {
        // Log selected slots for debugging
        console.log("Selected slots to save:", JSON.stringify(selectedSlots));

        // Check if there are any time slots beyond 8:00 (looking for any double-digit hour slots)
        const hasLateSlots = Object.values(selectedSlots).some((daySlots) =>
            daySlots.some((slot) => {
                const hour = parseInt(slot.split(":")[0], 10);
                return hour >= 10; // Hours 10 and above
            })
        );

        console.log("Has slots with hours >= 10:", hasLateSlots);

        const newAvailability: AvailabilityCreate = {
            week_start: format(currentWeekStart, "yyyy-MM-dd"),
            available_slots: selectedSlots,
            timezone,
        };

        try {
            // If editing, update the existing availability
            if (isEditing && currentAvailability) {
                console.log(
                    `Updating existing availability ID ${currentAvailability}`
                );
                await updateAvailability(currentAvailability, newAvailability);
            } else {
                console.log("Creating new availability");
                await createAvailability(newAvailability, timezone);
            }

            // If applying to following weeks, create additional availabilities
            if (applyToFollowingWeeks) {
                // Create for the next 12 weeks
                const numOfFollowingWeeks = 12;
                for (let i = 1; i <= numOfFollowingWeeks; i++) {
                    const nextWeekStart = addWeeks(currentWeekStart, i);
                    const nextWeekAvailability: AvailabilityCreate = {
                        week_start: format(nextWeekStart, "yyyy-MM-dd"),
                        available_slots: selectedSlots,
                        timezone,
                    };

                    try {
                        // Attempt to create or update availability for each week
                        const existingAvailability = availabilities.find(
                            (avail) =>
                                avail.week_start ===
                                format(nextWeekStart, "yyyy-MM-dd")
                        );

                        if (existingAvailability) {
                            await updateAvailability(
                                existingAvailability.id,
                                nextWeekAvailability
                            );
                        } else {
                            await createAvailability(
                                nextWeekAvailability,
                                timezone
                            );
                        }
                    } catch (error) {
                        console.error(
                            `Failed to set availability for week ${i}:`,
                            error
                        );
                        // Continue with other weeks even if one fails
                    }
                }

                setApplyToFollowingWeeks(false);
                toast.success(
                    `Availability pattern applied to the next ${numOfFollowingWeeks} weeks`
                );
            }

            toast.success("Availability saved successfully");

            // Clear unsaved changes for this week
            const currentWeekKey = format(currentWeekStart, "yyyy-MM-dd");
            setUnsavedSelections((prev) => {
                const newSelections = { ...prev };
                delete newSelections[currentWeekKey];
                return newSelections;
            });
            setHasUnsavedChanges(false);

            // Explicitly fetch availabilities again to ensure UI is updated
            console.log("Refreshing availabilities from server...");
            await fetchAvailabilities(timezone);

            // Short delay to make sure state updates
            setTimeout(() => {
                console.log("Refreshed availabilities:", availabilities);
            }, 500);
        } catch (error) {
            console.error("Save error:", error);
            toast.error("Failed to save availability");
        }
    };

    const handleDeleteWeek = async () => {
        if (!isEditing || currentAvailability === null) {
            toast.error("No availability to delete for this week");
            return;
        }

        try {
            await deleteAvailability(currentAvailability);
            toast.success("Availability for this week has been deleted");
            setSelectedSlots({});
            setCurrentAvailability(null);
            setIsEditing(false);
            await fetchAvailabilities(timezone);
        } catch (error) {
            toast.error("Failed to delete availability");
        }
    };

    return (
        <div className="bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
            <div className="p-4 md:p-6 space-y-4">
                <div className="flex justify-between items-center">
                    <h2 className="text-xl md:text-2xl font-semibold text-gray-800">
                        Set Your Session Availability
                    </h2>
                    <button className="text-gray-400 hover:text-gray-600">
                        <span className="sr-only">Close</span>✕
                    </button>
                </div>

                {/* <p className="text-gray-600 text-center text-sm md:text-base">
                    Please ensure that your meeting availability is as accurate
                    as possible
                    <br className="hidden md:block" />
                    for faster bookings.
                </p> */}

                <div className="bg-gray-50 rounded-lg p-2 md:p-4">
                    <WeekNavigation
                        weekRangeText={weekRangeText}
                        onPreviousWeek={handlePreviousWeek}
                        onNextWeek={handleNextWeek}
                        onTodayClick={handleTodayClick}
                        onDeleteWeek={handleDeleteWeek}
                        hasAvailability={isEditing}
                    />

                    <div className="overflow-x-auto">
                        <TimeGrid
                            currentWeekStart={currentWeekStart}
                            selectedSlots={selectedSlots}
                            toggleTimeSlot={toggleTimeSlot}
                        />
                    </div>
                </div>

                <AvailabilityControls
                    timezone={timezone}
                    setTimezone={setTimezone}
                    applyToFollowingWeeks={applyToFollowingWeeks}
                    setApplyToFollowingWeeks={setApplyToFollowingWeeks}
                    onSave={handleSaveAvailability}
                    loading={loading}
                    status={status}
                    hasSelectedSlots={Object.keys(selectedSlots).length > 0}
                    timezoneOptions={[
                        // Add current timezone with a unique ID prefix
                        {
                            id: "user-timezone", // Unique ID that won't conflict with other entries
                            value: Intl.DateTimeFormat().resolvedOptions()
                                .timeZone,
                            label: `Your Timezone (${
                                Intl.DateTimeFormat().resolvedOptions().timeZone
                            })`,
                        },
                        // Add each timezone option with a unique ID
                        ...TIMEZONE_OPTIONS.map((tz, index) => ({
                            id: `timezone-${index}`,
                            ...tz,
                        })),
                    ]}
                />
            </div>
        </div>
    );
}
