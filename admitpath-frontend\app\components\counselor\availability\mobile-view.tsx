"use client"

import { useState } from "react"
import { format, addDays } from "date-fns"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { TIME_SLOTS } from "./constants"

interface MobileViewProps {
  currentWeekStart: Date
  selectedSlots: Record<string, string[]>
  toggleTimeSlot: (dayIndex: number, timeSlot: string) => void
}

export function MobileView({ currentWeekStart, selectedSlots, toggleTimeSlot }: MobileViewProps) {
  // Generate days of the week
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(currentWeekStart, i))

  const [currentDayIndex, setCurrentDayIndex] = useState(0)
  const currentDay = weekDays[currentDayIndex]

  const handlePreviousDay = () => {
    setCurrentDayIndex((prev) => (prev > 0 ? prev - 1 : 6))
  }

  const handleNextDay = () => {
    setCurrentDayIndex((prev) => (prev < 6 ? prev + 1 : 0))
  }

  const isSlotSelected = (timeSlot: string) => {
    const dayKey = currentDayIndex.toString()
    return selectedSlots[dayKey]?.includes(timeSlot) || false
  }

  return (
    <div className="md:hidden">
      <div className="flex justify-between items-center mb-4">
        <button onClick={handlePreviousDay} className="p-1 rounded hover:bg-gray-200" aria-label="Previous day">
          <ChevronLeft className="h-5 w-5" />
        </button>
        <div className="text-center">
          <div className="uppercase text-xs font-medium">{format(currentDay, "EEEE")}</div>
          <div className="text-lg font-bold">{format(currentDay, "MMM dd")}</div>
        </div>
        <button onClick={handleNextDay} className="p-1 rounded hover:bg-gray-200" aria-label="Next day">
          <ChevronRight className="h-5 w-5" />
        </button>
      </div>

      <div className="max-h-[400px] overflow-y-auto">
        {TIME_SLOTS.map((time) => (
          <div
            key={time}
            onClick={() => toggleTimeSlot(currentDayIndex, time)}
            className={`p-3 mb-2 rounded-lg flex justify-between items-center cursor-pointer transition-colors ${
              isSlotSelected(time) ? "bg-emerald-500 text-white" : "bg-gray-100 hover:bg-emerald-300"
            }`}
            role="checkbox"
            aria-checked={isSlotSelected(time)}
          >
            <span>{time}</span>
            {isSlotSelected(time) && <span className="text-white">✓</span>}
          </div>
        ))}
      </div>
    </div>
  )
}

