import { MobileView } from "./mobile-view";
import { TimeGrid } from "./time-grid";

interface ResponsiveTimeViewProps {
    currentWeekStart: Date;
    selectedSlots: Record<string, string[]>;
    toggleTimeSlot: (dayIndex: number, timeSlot: string) => void;
}

export function ResponsiveTimeView({
    currentWeekStart,
    selectedSlots,
    toggleTimeSlot,
}: ResponsiveTimeViewProps) {
    return (
        <>
            {/* Mobile view (day by day) */}
            <MobileView
                currentWeekStart={currentWeekStart}
                selectedSlots={selectedSlots}
                toggleTimeSlot={toggleTimeSlot}
            />

            {/* Desktop view (full week grid) */}
            <div className="hidden md:block overflow-x-auto">
                <TimeGrid
                    currentWeekStart={currentWeekStart}
                    selectedSlots={selectedSlots}
                    toggleTimeSlot={toggleTimeSlot}
                />
            </div>
        </>
    );
}
