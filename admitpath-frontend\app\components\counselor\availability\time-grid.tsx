"use client";

import type React from "react";

import { format, addDays } from "date-fns";
import { useRef, useState } from "react";
import { TIME_SLOTS } from "./constants";

// CSS to prevent text selection
const noSelectStyle: React.CSSProperties = {
  WebkitUserSelect: "none",
  MozUserSelect: "none",
  msUserSelect: "none",
  userSelect: "none",
} as React.CSSProperties;

interface TimeGridProps {
  currentWeekStart: Date;
  selectedSlots: Record<string, string[]>;
  toggleTimeSlot: (dayIndex: number, timeSlot: string) => void;
}

export function TimeGrid({
  currentWeekStart,
  selectedSlots,
  toggleTimeSlot,
}: TimeGridProps) {
  // Generate days of the week based on current week start
  const weekDays = Array.from({ length: 7 }, (_, i) =>
    addDays(currentWeekStart, i)
  );

  // Drag selection state
  const [isDragging, setIsDragging] = useState(false);
  const [isSelecting, setIsSelecting] = useState(true);
  const dragStartRef = useRef<{ dayIndex: number; timeSlot: string } | null>(
    null
  );

  const now = new Date();

  // Check if a day is today
  const isToday = (date: Date) => {
    const today = new Date();
    return (
      date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
    );
  };

  // Check if date is in the past (before today)
  const isPastDay = (date: Date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const compareDate = new Date(date);
    compareDate.setHours(0, 0, 0, 0);

    return compareDate < today;
  };

  // Check if a specific time slot is in the past
  const isPastTimeSlot = (date: Date, timeSlot: string) => {
    if (isPastDay(date)) {
      return true;
    }

    if (isToday(date)) {
      const now = new Date();
      const [hours, minutes] = timeSlot.split(":").map(Number);

      // Check if this time is in the past
      return (
        now.getHours() > hours ||
        (now.getHours() === hours && now.getMinutes() > minutes)
      );
    }

    return false;
  };

  const isSlotSelected = (dayIndex: number, timeSlot: string) => {
    const dayKey = dayIndex.toString();
    return selectedSlots[dayKey]?.includes(timeSlot) || false;
  };

  // Handle drag start
  const handleDragStart = (
    dayIndex: number,
    timeSlot: string,
    isDisabled: boolean,
    e: React.MouseEvent | React.TouchEvent
  ) => {
    // Prevent default to avoid text selection
    e.preventDefault();

    if (isDisabled) return;

    setIsDragging(true);
    dragStartRef.current = { dayIndex, timeSlot };

    // Determine if we're selecting or deselecting based on the initial slot
    const initiallySelected = isSlotSelected(dayIndex, timeSlot);
    setIsSelecting(!initiallySelected);

    // Toggle the initial slot
    toggleTimeSlot(dayIndex, timeSlot);
  };

  // Handle drag over
  const handleDragOver = (
    dayIndex: number,
    timeSlot: string,
    isDisabled: boolean,
    e: React.MouseEvent
  ) => {
    // Prevent default to avoid text selection
    e.preventDefault();

    if (!isDragging || isDisabled) return;

    // Only toggle if the current selection state doesn't match our drag operation
    const isSelected = isSlotSelected(dayIndex, timeSlot);
    if (isSelecting !== isSelected) {
      toggleTimeSlot(dayIndex, timeSlot);
    }
  };

  // Handle drag end
  const handleDragEnd = () => {
    setIsDragging(false);
    dragStartRef.current = null;
  };

  return (
    <div
      className="min-w-max select-none"
      onMouseUp={(e) => {
        e.preventDefault();
        handleDragEnd();
      }}
      onMouseLeave={(e) => {
        e.preventDefault();
        handleDragEnd();
      }}
      onTouchEnd={(e) => {
        e.preventDefault();
        handleDragEnd();
      }}
      onTouchCancel={(e) => {
        e.preventDefault();
        handleDragEnd();
      }}
    >
      {/* Day headers */}
      <div className="grid grid-cols-[80px_repeat(7,1fr)] border-b">
        <div className="p-2"></div>
        {weekDays.map((day, index) => (
          <div
            key={index}
            className={`p-2 text-center font-medium ${
              isToday(day) ? "text-blue-600" : ""
            }`}
          >
            <div className="uppercase text-xs">{format(day, "EEE")}</div>
            <div
              className={`text-sm ${
                isToday(day)
                  ? "bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center mx-auto"
                  : ""
              }`}
            >
              {format(day, "dd")}
            </div>
          </div>
        ))}
      </div>

      {/* Time slots grid */}
      <div className="max-h-[300px] md:max-h-[400px] overflow-y-auto">
        {TIME_SLOTS.map((time) => (
          <div
            key={time}
            className="grid grid-cols-[80px_repeat(7,1fr)] border-b"
          >
            <div className="p-2 text-right pr-3 text-gray-500 text-sm border-r">
              {time}
            </div>
            {weekDays.map((day, dayIndex) => {
              const isDisabled = isPastTimeSlot(day, time);
              const isSelected = isSlotSelected(dayIndex, time);

              return (
                <div
                  key={dayIndex}
                  onMouseDown={(e) =>
                    handleDragStart(dayIndex, time, isDisabled, e)
                  }
                  onMouseOver={(e) =>
                    handleDragOver(dayIndex, time, isDisabled, e)
                  }
                  onTouchStart={(e) => {
                    e.preventDefault();
                    handleDragStart(dayIndex, time, isDisabled, e);
                  }}
                  onTouchMove={(e) => {
                    e.preventDefault();

                    // Get the element under the touch point
                    const touch = e.touches[0];
                    const element = document.elementFromPoint(
                      touch.clientX,
                      touch.clientY
                    );

                    // If we have a data attribute for day and time, use it
                    if (
                      element?.getAttribute("data-day-index") &&
                      element?.getAttribute("data-time")
                    ) {
                      const touchDayIndex = Number.parseInt(
                        element.getAttribute("data-day-index") || "0"
                      );
                      const touchTime = element.getAttribute("data-time") || "";
                      const touchDisabled =
                        element.getAttribute("data-disabled") === "true";

                      handleDragOver(
                        touchDayIndex,
                        touchTime,
                        touchDisabled,
                        e as any
                      );
                    }
                  }}
                  className={`border-r p-2 transition-colors ${
                    isDisabled
                      ? "bg-gray-100 text-gray-300 cursor-not-allowed"
                      : isSelected
                      ? "bg-emerald-500 hover:bg-emerald-600 cursor-pointer"
                      : "bg-white hover:bg-emerald-300 cursor-pointer"
                  }`}
                  aria-label={`Time slot ${time} on ${format(day, "EEEE")}`}
                  role="checkbox"
                  aria-checked={isSelected}
                  aria-disabled={isDisabled}
                  data-day-index={dayIndex}
                  data-time={time}
                  data-disabled={isDisabled}
                >
                  &nbsp;
                </div>
              );
            })}
          </div>
        ))}
      </div>

      <div className="mt-4 flex items-center justify-end">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-emerald-500 border border-gray-200"></div>
          <span className="text-sm text-gray-600">Available</span>
        </div>
        <div className="flex items-center space-x-2 ml-4">
          <div className="w-4 h-4 bg-white border border-gray-200"></div>
          <span className="text-sm text-gray-600">Unavailable</span>
        </div>
        <div className="flex items-center space-x-2 ml-4">
          <div className="w-4 h-4 bg-gray-100 border border-gray-200"></div>
          <span className="text-sm text-gray-600">Past</span>
        </div>
      </div>

      <div className="mt-2 text-xs text-gray-500">
        Tip: Click and drag to select multiple time slots at once
      </div>
    </div>
  );
}
