import { ChevronLeft, ChevronRight, MoreVertical } from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@components/ui/dropdown-menu";

interface WeekNavigationProps {
    weekRangeText: string;
    onPreviousWeek: () => void;
    onNextWeek: () => void;
    onTodayClick: () => void;
    onDeleteWeek: () => void;
    hasAvailability: boolean;
}

export function WeekNavigation({
    weekRangeText,
    onPreviousWeek,
    onNextWeek,
    onTodayClick,
    onDeleteWeek,
    hasAvailability,
}: WeekNavigationProps) {
    return (
        <div className="flex justify-between items-center mb-4">
            <div className="flex items-center space-x-2">
                <button
                    onClick={onPreviousWeek}
                    className="p-1 rounded hover:bg-gray-200"
                    aria-label="Previous week"
                >
                    <ChevronLeft className="h-5 w-5" />
                </button>
                <span className="font-medium text-sm md:text-base">
                    {weekRangeText}
                </span>
                <button
                    onClick={onNextWeek}
                    className="p-1 rounded hover:bg-gray-200"
                    aria-label="Next week"
                >
                    <ChevronRight className="h-5 w-5" />
                </button>
            </div>
            <div className="flex items-center space-x-2">
                <button
                    onClick={onTodayClick}
                    className="text-sm text-blue-600 hover:underline"
                >
                    Today
                </button>

                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <button
                            className="p-1 rounded hover:bg-gray-200"
                            aria-label="More options"
                        >
                            <MoreVertical className="h-4 w-4" />
                        </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-white">
                        <DropdownMenuItem
                            onClick={onDeleteWeek}
                            className={
                                !hasAvailability
                                    ? "text-gray-400 cursor-not-allowed"
                                    : "text-red-600 cursor-pointer"
                            }
                            disabled={!hasAvailability}
                        >
                            Delete this week
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </div>
        </div>
    );
}
