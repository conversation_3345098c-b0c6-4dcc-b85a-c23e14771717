"use client";

import React, { useEffect, useState } from "react";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import apiClient from "@/lib/apiClient";
import LoadingSpinner from "@/app/components/common/loadingSpinner";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

type MonthlyData = {
  month: string;
  total: number;
  approved: number;
  pending: number;
};

type EarningsData = {
  total_earning: number;
  approved: number;
  pending: number;
  monthly_data: MonthlyData[];
};

const EarningsSummary: React.FC = () => {
  const [earningsData, setEarningsData] = useState<EarningsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [windowWidth, setWindowWidth] = useState<number>(
    typeof window !== "undefined" ? window.innerWidth : 1200
  );

  useEffect(() => {
    const fetchEarningsData = async () => {
      try {
        const response = await apiClient.get(
          "/counselor/dashboard/earnings-summary"
        );

        const data: EarningsData = response.data;
        setEarningsData(data);
      } catch (err) {
        setError("Earning data is empty");
      } finally {
        setLoading(false);
      }
    };

    fetchEarningsData();

    // Handle window resize for responsiveness
    const handleResize = () => {
      setWindowWidth(window.innerWidth);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Render loading state
  if (loading) {
    return (
      <div className="p-3 sm:p-6 bg-gray-100">
        <div className="bg-white shadow-md rounded-lg p-4 text-center">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="p-3 sm:p-6 bg-gray-100">
        <div className="bg-white shadow-md rounded-lg p-4 text-center">
          <p className="text-red-500">{error}</p>
        </div>
      </div>
    );
  }

  // Render no data state
  if (!earningsData) {
    return (
      <div className="p-3 sm:p-6 bg-gray-100">
        <div className="bg-white shadow-md rounded-lg p-4 text-center">
          <p className="text-gray-500">No data available.</p>
        </div>
      </div>
    );
  }

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // On mobile, abbreviate month names
  const getAdjustedLabels = () => {
    return earningsData.monthly_data.map((data) => {
      if (windowWidth < 640) {
        // Abbreviate month names on small screens
        return data.month.substring(0, 3);
      }
      return data.month;
    });
  };

  const data = {
    labels: getAdjustedLabels(),
    datasets: [
      {
        label: "Total Earnings",
        data: earningsData.monthly_data.map((data) => data.total),
        backgroundColor: "#4CAF50", // Green for total
      },
      {
        label: "Approved",
        data: earningsData.monthly_data.map((data) => data.approved),
        backgroundColor: "#2196F3", // Blue for approved
      },
      {
        label: "Pending",
        data: earningsData.monthly_data.map((data) => data.pending),
        backgroundColor: "#FF9800", // Orange for pending
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: windowWidth < 640 ? ("bottom" as const) : ("top" as const),
        labels: {
          boxWidth: windowWidth < 640 ? 10 : 20,
          padding: windowWidth < 640 ? 10 : 20,
          font: {
            size: windowWidth < 640 ? 10 : 12,
          },
        },
      },
      title: {
        display: true,
        text: "Monthly Earnings Overview",
        font: {
          size: windowWidth < 640 ? 14 : 16,
        },
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            return `${context.dataset.label}: ${formatCurrency(context.raw)}`;
          },
        },
      },
    },
    scales: {
      x: {
        ticks: {
          font: {
            size: windowWidth < 640 ? 10 : 12,
          },
        },
      },
      y: {
        ticks: {
          font: {
            size: windowWidth < 640 ? 10 : 12,
          },
          callback: function (value: any) {
            return formatCurrency(value);
          },
        },
      },
    },
  };

  return (
    <div className="p-0 sm:p-6 bg-gray-100">
      <div className="bg-white shadow-md border rounded-lg p-3 sm:p-4">
        {/* Summary Cards for Quick Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
          <div className="bg-green-50 p-3 rounded-lg border border-green-100 shadow-sm">
            <p className="text-xs sm:text-sm text-green-600 font-medium">
              Total Earnings
            </p>
            <p className="text-lg sm:text-xl font-bold text-green-600">
              {formatCurrency(earningsData.total_earning)}
            </p>
          </div>
          <div className="bg-blue-50 p-3 rounded-lg border border-blue-100 shadow-sm">
            <p className="text-xs sm:text-sm text-blue-600 font-medium">
              Approved
            </p>
            <p className="text-lg sm:text-xl font-bold text-blue-600">
              {formatCurrency(earningsData.approved)}
            </p>
          </div>
          <div className="bg-orange-50 p-3 rounded-lg border border-orange-100 shadow-sm">
            <p className="text-xs sm:text-sm text-orange-600 font-medium">
              Pending
            </p>
            <p className="text-lg sm:text-xl font-bold text-orange-600">
              {formatCurrency(earningsData.pending)}
            </p>
          </div>
        </div>

        {/* Chart Container with Fixed Height for Better Mobile Display */}
        <div className="h-64 sm:h-80 md:h-96">
          <Bar data={data} options={options} />
        </div>
      </div>
    </div>
  );
};

export default EarningsSummary;
