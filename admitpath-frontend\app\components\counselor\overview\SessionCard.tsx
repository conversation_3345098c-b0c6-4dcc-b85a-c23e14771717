import React, { useEffect, useState } from "react";
import LoadingSpinner from "../../common/loadingSpinner";
import apiClient from "@/lib/apiClient";

interface Session {
  id: number;
  event_name: string;
  date: string;
  start_time: string;
  end_time: string;
  meeting_link: string;
  student_name: string;
}

const SessionCards: React.FC = () => {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSessions = async () => {
      try {
        setLoading(true);
        const response = await apiClient.get(
          `/counselor/dashboard/upcoming-sessions?timezone=${
            Intl.DateTimeFormat().resolvedOptions().timeZone
          }&limit=3`
        );
        setSessions(response.data.sessions || []);
        setError(null);
      } catch (err) {
        setError("Failed to fetch data");
      } finally {
        setLoading(false);
      }
    };

    fetchSessions();
  }, []);

  const latestSessions = [...sessions].sort(
    (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()
  );

  return (
    <section>
      {loading ? (
        <div className="text-center text-neutral-500">
          <LoadingSpinner />
        </div>
      ) : error ? (
        <div className="text-center text-red-500">
          <p>{error}</p>
        </div>
      ) : latestSessions.length > 0 ? (
        latestSessions.map((session) => (
          <div
            key={session.id}
            className="border rounded-xl p-4 bg-white hover:shadow-xl transition-shadow mt-4"
          >
            <h3 className="font-semibold text-lg mb-2">{session.event_name}</h3>
            <p className="text-sm text-gray-600">
              <span className="font-bold">Date:</span>{" "}
              {new Date(session.date).toLocaleDateString()}
            </p>
            <p className="text-sm text-gray-600">
              <span className="font-bold">Start Time:</span>{" "}
              {new Date(session.start_time).toLocaleTimeString()} (GMT+05:00)
            </p>
            <p className="text-sm text-gray-600">
              <span className="font-bold">End Time:</span>{" "}
              {new Date(session.end_time).toLocaleTimeString()} (GMT+05:00)
            </p>
            <p className="text-sm text-gray-600">
              <span className="font-bold">Student:</span> {session.student_name}
            </p>
            <a
              href={session.meeting_link}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 underline hover:text-blue-700 text-sm mt-2 inline-block"
            >
              Join Meeting
            </a>
          </div>
        ))
      ) : (
        <div className="text-center text-neutral-500 mt-10">
          <p>No Sessions Available</p>
        </div>
      )}
    </section>
  );
};

export default SessionCards;
