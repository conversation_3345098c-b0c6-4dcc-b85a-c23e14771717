"use client";

import React, { useEffect, useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faArrows } from "@fortawesome/free-solid-svg-icons";
import PendingPayments from "@/app/components/counselor/overview/popup/PendingPayments";
import StudentsFeedback from "@/app/components/counselor/overview/popup/StudentsFeedback";
import EventDetailsModal from "@/app/components/counselor/overview/popup/EventDetailsModal";
import EditEventModal from "@/app/components/counselor/overview/popup/EditEventModal";
import { useProfile } from "@/app/hooks/counselor/useProfile";
import SessionCards from "./SessionCard";
import EarningsSummary from "./EarningsSummary";
import { useSessions } from "@/app/hooks/counselor/useSessions";
import { generatePlaceholder } from "@/app/utils/image";
import { format } from "date-fns";

//////////////////////////// MAIN COMPONENT //////////////////////////
export default function CounselorOverview() {
  const { getUserInfo, userInfo } = useProfile();
  const { getStudentsFeedback, studentsFeedback } = useSessions();

  // Combine all popup states into one object
  const [popups, setPopups] = useState({
    feedback: false,
    pendingPayments: false,
    eventsDetails: false,
    editEvent: false,
  });

  // Generic function to toggle a specific popup by name
  const togglePopup = (popupName: keyof typeof popups) => {
    setPopups((prevPopups) => ({
      ...prevPopups,
      [popupName]: !prevPopups[popupName],
    }));
  };

  useEffect(() => {
    getUserInfo();
  }, []);

  useEffect(() => {
    if (userInfo) {
      getStudentsFeedback(userInfo?.user_id);
    }
  }, [userInfo]);

  ///////////////////////////////// JSX ////////////////////////////////
  return (
    <>
      <PendingPayments
        isPopupOpen={popups.pendingPayments}
        setIsPopupOpen={() => togglePopup("pendingPayments")}
      />

      <StudentsFeedback
        isPopupOpen={popups.feedback}
        setIsPopupOpen={() => togglePopup("feedback")}
        feedbackList={studentsFeedback}
      />

      <EventDetailsModal
        isPopupOpen={popups.eventsDetails}
        setIsPopupOpen={() => togglePopup("eventsDetails")}
      />

      <EditEventModal
        isPopupOpen={popups.editEvent}
        setIsPopupOpen={() => togglePopup("editEvent")}
      />

      <div className="pr-3">
        <h2 className="text-lg font-semibold mb-2">
          Your Space to Mentor, Manage, and Grow
        </h2>
        <p className="mb-4">
          Effortlessly manage your sessions, profile, and earnings while helping
          students achieve their dreams
        </p>

        <div className="split-section flex flex-col-reverse md:flex-row justify-between gap-3">
          <div className="left-side md:w-3/5">
            <div className="earnings  bg-white rounded-lg p-4 mb-3">
              <h5 className="mb-4">Earnings summary</h5>
              <EarningsSummary />
            </div>

            <div className="feedback bg-white rounded-lg p-4">
              <div className="flex justify-between items-center mb-5">
                <h5>
                  Students Feedback{" "}
                  <span className="text-sm text-[#9EA2B3]">
                    ({studentsFeedback?.total_reviews} reviews)
                  </span>
                </h5>
                {studentsFeedback?.reviews.length ? (
                  <button onClick={() => togglePopup("feedback")}>
                    View all <FontAwesomeIcon icon={faArrows} className="w-4" />
                  </button>
                ) : (
                  ""
                )}
              </div>
              <div className="content p-4 border rounded-xl">
                {studentsFeedback?.reviews.length ? (
                  <div className="bg-white rounded-lg hover:scale-[100.7%] transition-all  cursor-pointer flex">
                    {/* Student Avatar */}
                    <div className="mr-4 flex-shrink-0">
                      <img
                        src={
                          studentsFeedback.reviews[0].student_profile_picture ||
                          generatePlaceholder(
                            studentsFeedback.reviews[0].student_name.split(
                              /s+/
                            )[0],
                            studentsFeedback.reviews[0].student_name.split(
                              /s+/
                            )[1]
                          )
                        }
                        alt={studentsFeedback.reviews[0].student_name}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    </div>

                    {/* Review Content */}
                    <div className="flex-1">
                      <div className="font-medium mb-1">
                        {studentsFeedback.reviews[0].student_name}
                      </div>
                      {/* Star Rating */}
                      <div className="flex mb-2">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <svg
                            key={star}
                            className={`w-4 h-4 ${
                              star <= studentsFeedback.reviews[0].rating
                                ? "text-yellow-400"
                                : "text-gray-300"
                            }`}
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>

                      {/* Review Comment */}
                      <p className="text-gray-700 mb-2 line-clamp-3">
                        {studentsFeedback.reviews[0].comment}
                      </p>

                      {/* Review Date */}
                      <p className="text-gray-500 text-sm">
                        {format(
                          new Date(studentsFeedback.reviews[0].created_at),
                          "PPP"
                        )}
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="flex justify-center items-center h-20 text-neutral5">
                    <p>No Reviews Available</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="right-side md:w-2/5">
            <div className="w-full h-full bg-white rounded-xl p-4 relative">
              <div className="flex justify-between items-center gap-5">
                <h4>Upcoming sessions</h4>
              </div>

              <SessionCards />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
