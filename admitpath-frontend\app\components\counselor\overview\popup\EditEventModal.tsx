"use client";

import Popup from "@/app/components/common/popup";
import { PopupProps } from "@/app/types/counselor/profile";

export default function EditEventModal({
  isPopupOpen,
  setIsPopupOpen,
}: PopupProps) {
  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={() => setIsPopupOpen(false)}
      title="Edit Event"
      width="50vw"
    >
      <h2>EDit evnet</h2>
    </Popup>
  );
}
