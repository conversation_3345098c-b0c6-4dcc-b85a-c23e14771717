"use client";

import { MainButton } from "@/app/components/common/mainBtn";
import Popup from "@/app/components/common/popup";
import { PopupProps } from "@/app/types/counselor/profile";
import Image from "next/image";
import Link from "next/link";

export default function EventDetailsModal({
  isPopupOpen,
  setIsPopupOpen,
}: PopupProps) {
  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={() => setIsPopupOpen(false)}
      title="Event Details"
      width="50vw"
    >
      <p className="text-[neutral8] text-md font-medium mb-2">
        Lorem ipsum dolor sit amet, consectetur adipisicing elit. Ad, placeat?
      </p>
      <p className="text-[neutral8] text-sm font-medium mb-4">
        <span>icon</span> Thursday, December 12:15 - 2:00pm
      </p>
      <button className="text-sm bg-[#6FCF9733] py-2 px-4 rounded-lg mb-4">
        Tag here
      </button>

      <hr />

      <p className="mt-8 mb-4 text-sm text-neutral8 font-medium">Guest</p>
      <div className="flex justify-between items-center text-sm text-neutral8 font-medium mb-6">
        <div className="flex items-center gap-4">
          <Image
            src="/images/avatar.png"
            alt="user pic"
            height={60}
            width={60}
          />
          <h3>Student Name</h3>
        </div>
        <MainButton variant="primary">Send Message</MainButton>
      </div>
      <p className="text-sm text-neutral8 font-medium mb-2">Meeting Link</p>
      <div className="flex gap-3 items-center mb-6">
        <Image
          src="/images/avatar.png"
          alt="Zoom link"
          height={40}
          width={40}
        />
        <Link href="/#">
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Quia,
          aspernatur?
        </Link>
      </div>

      <hr />

      <div className="btn-groups flex justify-between items-center mt-6">
        <button className="text-[#EB5757] px-6">Remove</button>

        <div className="flex gap-3">
          <MainButton variant="neutral">Edit</MainButton>
          <MainButton variant="neutral" onClick={() => setIsPopupOpen(false)}>
            Close
          </MainButton>
        </div>
      </div>
    </Popup>
  );
}
