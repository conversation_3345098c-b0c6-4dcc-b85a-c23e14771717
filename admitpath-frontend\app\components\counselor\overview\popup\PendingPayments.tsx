"use client";

import { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowUp,
  faArrowDown,
  faArrowRight,
} from "@fortawesome/free-solid-svg-icons";
import Popup from "@/app/components/common/popup";
import { PopupProps } from "@/app/types/counselor/profile";
import Image from "next/image";
import { MainButton } from "../../../common/mainBtn";

interface Payment {
  id: number;
  studentName: string;
  sessionStatus: string;
  serviceName: string;
  amount: string;
  action: string;
}

const payments: Payment[] = [
  {
    id: 1,
    studentName: "John Doe",
    sessionStatus: "Completed",
    serviceName: "Math Tutoring",
    amount: "$200",
    action: "Request Payment",
  },
  {
    id: 2,
    studentName: "<PERSON>",
    sessionStatus: "Pending",
    serviceName: "Science Tutoring",
    amount: "$150",
    action: "Request Pending",
  },
  {
    id: 3,
    studentName: "<PERSON>",
    sessionStatus: "Completed",
    serviceName: "English Tutoring",
    amount: "$300",
    action: "Payment Received",
  },
  {
    id: 4,
    studentName: "<PERSON> Brown",
    sessionStatus: "Cancelled",
    serviceName: "History Tutoring",
    amount: "$100",
    action: "View Details",
  },
];

const statusClasses: Record<string, string> = {
  Completed: "bg-green-300",
  Pending: "bg-yellow-300",
  Cancelled: "bg-red-400",
  default: "bg-gray-400",
};

const actionClasses: Record<string, string> = {
  "Request Payment": "bg-blue-300",
  "View Details": "bg-green-300",
  "Payment Received": "bg-black",
  "Request Pending": "bg-yellow-400",
};

type PaymentKeys = keyof Payment;

export default function PendingPayments({
  isPopupOpen,
  setIsPopupOpen,
}: PopupProps) {
  const [sortConfig, setSortConfig] = useState<{
    key: PaymentKeys;
    direction: "asc" | "desc";
  }>({ key: "studentName", direction: "asc" });

  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  const sortedPayments = [...payments].sort((a, b) => {
    if (a[sortConfig.key] < b[sortConfig.key]) {
      return sortConfig.direction === "asc" ? -1 : 1;
    }
    if (a[sortConfig.key] > b[sortConfig.key]) {
      return sortConfig.direction === "asc" ? 1 : -1;
    }
    return 0;
  });

  function handleSort(newKey: PaymentKeys) {
    setSortConfig((prevConfig) => ({
      key: newKey,
      direction:
        prevConfig.key === newKey && prevConfig.direction === "asc"
          ? "desc"
          : "asc",
    }));
  }

  const getClass = (item: string, classes: Record<string, string>) =>
    classes[item] || classes.default;

  const renderSortIcon = (key: PaymentKeys) => (
    <FontAwesomeIcon
      icon={
        sortConfig.key === key && sortConfig.direction === "asc"
          ? faArrowUp
          : faArrowDown
      }
      className="ml-2 text-blue-500"
    />
  );

  // Handle individual row selection
  const handleRowSelect = (id: number) => {
    setSelectedRows((prev) =>
      prev.includes(id) ? prev.filter((rowId) => rowId !== id) : [...prev, id]
    );
  };

  // Handle select all toggle
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedRows([]); // Deselect all
    } else {
      setSelectedRows(payments.map((payment) => payment.id)); // Select all
    }
    setSelectAll(!selectAll);
  };

  // Update select all checkbox state when individual checkboxes change
  const isChecked = (id: number) => selectedRows.includes(id);

  return (
    <div className="p-4">
      <Popup
        isOpen={isPopupOpen}
        onClose={() => setIsPopupOpen(false)}
        title="Pending Payments ($2400)"
        width="70vw"
      >
        <div className="content p-3">
          <div className="">
            <table className="table-auto w-full border-collapse">
              <thead className="rounded-xl border overflow-hidden">
                <tr className="border-b bg-gray-100 text-sm font-semibold">
                  <th className="px-4 py-2 text-left">
                    <input
                      type="checkbox"
                      checked={selectAll}
                      onChange={handleSelectAll}
                    />
                  </th>
                  {[
                    { label: "Student Name", key: "studentName" },
                    { label: "Session Status", key: "sessionStatus" },
                    { label: "Service Name", key: "serviceName" },
                    { label: "Amount", key: "amount" },
                  ].map(({ label, key }) => (
                    <th
                      key={key}
                      className="px-4 py-2 cursor-pointer"
                      onClick={() => handleSort(key as PaymentKeys)}
                    >
                      <span className="mr-2">{label}</span>
                      {renderSortIcon(key as PaymentKeys)}
                    </th>
                  ))}
                  <th className="px-4 py-2">Action</th>
                </tr>
              </thead>
              <tbody>
                {sortedPayments.map((payment) => (
                  <tr key={payment.id} className="border-b text-sm">
                    <td className="px-4 py-2">
                      <input
                        type="checkbox"
                        checked={isChecked(payment.id)}
                        onChange={() => handleRowSelect(payment.id)}
                      />
                    </td>
                    <td className="px-4 py-2 flex items-center gap-3">
                      <Image
                        src="/images/avatar.png"
                        alt="User Pic"
                        width={40}
                        height={40}
                      />
                      <div className="text-sm">
                        <span className="block mb-1">
                          {payment.studentName}
                        </span>
                        <span className="block"><EMAIL></span>
                      </div>
                    </td>
                    <td className="px-4 py-2">
                      <button
                        className={`px-4 py-1 rounded-lg text-white text-sm whitespace-nowrap ${getClass(
                          payment.sessionStatus,
                          statusClasses
                        )}`}
                      >
                        {payment.sessionStatus}
                      </button>
                    </td>
                    <td className="px-4 py-2">{payment.serviceName}</td>
                    <td className="px-4 py-2">{payment.amount}</td>
                    <td className="px-4 py-2">
                      <button
                        className={`px-4 py-1 rounded-lg text-white text-sm whitespace-nowrap ${getClass(
                          payment.action,
                          actionClasses
                        )}`}
                      >
                        {payment.action}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="flex justify-end gap-3 ml-auto mt-6">
            <MainButton
              variant="neutral"
              children="Cancel"
              onClick={() => setIsPopupOpen(false)}
            />
            <MainButton
              variant="primary"
              type="submit"
              children="Request all"
              icon={faArrowRight}
            />
          </div>
        </div>
      </Popup>
    </div>
  );
}
