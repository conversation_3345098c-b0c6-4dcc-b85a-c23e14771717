"use client";

import { useState } from "react";
import Popup from "@/app/components/common/popup";
import { PopupProps } from "@/app/types/counselor/profile";
import { FaStar } from "react-icons/fa";
import { MainButton } from "@/app/components/common/mainBtn";
import { Feedback } from "@/app/types/counselor/sessions";
import { generatePlaceholder } from "@/app/utils/image";
import { format } from "date-fns";

interface FeedbackProps extends PopupProps {
  feedbackList: Feedback | null;
}

export default function StudentsFeedback({
  isPopupOpen,
  setIsPopupOpen,
  feedbackList,
}: FeedbackProps) {
  const [selectedButton, setSelectedButton] = useState<string>("All");

  const buttons = ["All", "5", "4+", "3+", "2+", "1+"];

  // Filter feedback based on selected button
  const filteredFeedback =
    selectedButton === "All"
      ? feedbackList?.reviews
      : feedbackList?.reviews.filter((feedback) =>
          selectedButton.includes("+")
            ? feedback.rating >= parseInt(selectedButton)
            : feedback.rating === parseInt(selectedButton)
        );

  return (
    <div className="p-4">
      <Popup
        isOpen={isPopupOpen}
        onClose={() => setIsPopupOpen(false)}
        title={`Students Feedback (${feedbackList?.total_reviews} reviews)`}
      >
        <div className="content p-4">
          <div className="btn-groups flex gap-5">
            {buttons.map((button) => (
              <button
                key={button}
                onClick={() => setSelectedButton(button)}
                className={`py-2 px-5 rounded-3xl border flex items-center gap-2 ${
                  selectedButton === button ? "bg-mainClr text-white" : ""
                }`}
              >
                {button === "All" ? (
                  button
                ) : (
                  <>
                    <FaStar className="text-yellow-500" /> {button}
                  </>
                )}
              </button>
            ))}
          </div>

          <ul className="feedback-container mt-7 grid gap-3">
            {filteredFeedback && filteredFeedback.length > 0 ? (
              filteredFeedback.map((feedback, index) => (
                <li
                  key={feedback.id}
                  className="bg-white border rounded-lg hover:scale-[100.7%] transition-all  cursor-pointer p-4 flex"
                >
                  {/* Student Avatar */}
                  <div className="mr-4 flex-shrink-0">
                    <img
                      src={
                        feedback.student_profile_picture ||
                        generatePlaceholder(
                          feedback.student_name.split(/s+/)[0],
                          feedback.student_name.split(/s+/)[1]
                        )
                      }
                      alt={feedback.student_name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  </div>

                  {/* Review Content */}
                  <div className="flex-1">
                    <div className="font-medium mb-1">
                      {feedback.student_name}
                    </div>
                    {/* Star Rating */}
                    <div className="flex mb-2">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <svg
                          key={star}
                          className={`w-4 h-4 ${
                            star <= feedback.rating
                              ? "text-yellow-400"
                              : "text-gray-300"
                          }`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>

                    {/* Review Comment */}
                    <p className="text-gray-700 mb-2 line-clamp-3">
                      {feedback.comment}
                    </p>

                    {/* Review Date */}
                    <p className="text-gray-500 text-sm">
                      {format(new Date(feedback.created_at), "PPP")}
                    </p>
                  </div>
                </li>
              ))
            ) : (
              <p className="text-center text-gray-500">
                No feedback available for this filter.
              </p>
            )}
          </ul>

          <div className="mt-6 flex justify-end">
            <MainButton
              variant="neutral"
              children="Close"
              onClick={() => setIsPopupOpen(false)}
            />
          </div>
        </div>
      </Popup>
    </div>
  );
}
