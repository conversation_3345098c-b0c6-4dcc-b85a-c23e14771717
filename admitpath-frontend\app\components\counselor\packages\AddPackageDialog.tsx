"use client";

import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { InputField } from "@/app/components/common/inputField";
import { Textarea } from "@/app/components/common/textarea";
import { MainButton } from "@/app/components/common/mainBtn";
import { faTrash, faPlus } from "@fortawesome/free-solid-svg-icons";
import { usePackages } from "@/app/hooks/counselor/usePackage";
import { useServices } from "@/app/hooks/counselor/useService";
import { useProfile } from "@/app/hooks/counselor/useProfile";
import Dropdown from "@/app/components/common/dropdown";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/app/components/ui/dialog";
import { Button } from "@/app/components/ui/button";
import { Spinner } from "@/app/components/ui/spinner";

interface AddPackageDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

interface InitialStateProps {
  title: string;
  description: string;
  total_price: number;
  items: { service_name: string; custom_type: string; hours: number }[];
}

const initialState = {
  title: "",
  description: "",
  total_price: 0,
  items: [{ service_name: "", custom_type: "", hours: 0 }],
};

export default function AddPackageDialog({
  isOpen,
  onClose,
}: AddPackageDialogProps) {
  const { addPackage, fetchPackages } = usePackages();
  const { services, fetchServices } = useServices();
  const { userInfo } = useProfile();

  const [formData, setFormData] = useState<InitialStateProps>(initialState);
  const [counselorServices, setCounselorServices] = useState<
    { value: string; label: string }[]
  >([]);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch counselor services when the dialog opens
  useEffect(() => {
    if (isOpen && userInfo?.counselor_id) {
      fetchServices(userInfo.counselor_id);
    }
  }, [isOpen, userInfo, fetchServices]);

  // Convert services to dropdown options
  useEffect(() => {
    if (services.length > 0) {
      const serviceOptions = [
        { value: "", label: "Please select" },
        ...services.map((service) => ({
          value: service.custom_type || service.service_type,
          label: service.custom_type || service.service_type,
        })),
      ];
      setCounselorServices(serviceOptions);
    }
  }, [services]);

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.title.trim()) {
      newErrors.title = "Package title is required.";
    }
    if (!formData.description.trim()) {
      newErrors.description = "Description is required.";
    }
    if (!formData.total_price || formData.total_price <= 0) {
      newErrors.total_price = "Price must be a positive number.";
    }

    // Validate services - must have at least one valid service
    if (formData.items.length === 0) {
      newErrors.services = "At least one service is required.";
    } else {
      // Check if all provided services have valid data
      const hasEmptyServices = formData.items.some(
        (item) => !item.service_name.trim() || item.hours <= 0
      );
      if (hasEmptyServices) {
        newErrors.services =
          "All service fields must be filled with valid values.";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleServiceChange = (index: number, field: string, value: string) => {
    const updatedItems = [...formData.items];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value,
    };
    setFormData({ ...formData, items: updatedItems });
  };

  const handleServiceHoursChange = (
    index: number,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.valueAsNumber || 0;
    const updatedItems = [...formData.items];
    updatedItems[index] = {
      ...updatedItems[index],
      hours: value,
    };
    setFormData({ ...formData, items: updatedItems });
  };

  const handleAddService = (
    e: React.MouseEvent<HTMLButtonElement> | React.FormEvent<Element>
  ) => {
    // Prevent form submission
    e.preventDefault();

    setFormData({
      ...formData,
      items: [
        ...formData.items,
        { service_name: "", custom_type: "", hours: 0 },
      ],
    });
  };

  const handleRemoveService = (index: number) => {
    if (formData.items.length === 1) {
      return; // Don't remove the last service
    }
    const updatedItems = [...formData.items];
    updatedItems.splice(index, 1);
    setFormData({
      ...formData,
      items: updatedItems,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitted(true);

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    toast.info("Creating package...");

    const formattedData = {
      ...formData,
      items: formData.items.map((item) => ({
        service_name: item.service_name,
        hours: item.hours,
      })),
    };

    try {
      await addPackage(formattedData);
      toast.success("Package created successfully!");
      onClose();
      setFormData(initialState);
      fetchPackages();
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message || "Error adding package";
      toast.error(errorMessage);
      console.error("Error adding package:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get the list of selected services
  const selectedServices = formData.items.map((item) => item.service_name);

  // Filter out already selected services from the dropdown options
  const getFilteredOptions = (index: number) => {
    // Filter the counselor's services
    const filteredOptions = counselorServices.filter(
      (item) =>
        item.value === "" || // Always include the placeholder
        !selectedServices.includes(item.value) ||
        formData.items[index].service_name === item.value
    );

    return filteredOptions;
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          setIsSubmitted(false);
          onClose();
        }
      }}
    >
      <DialogContent
        className="max-w-4xl max-h-[90vh] overflow-y-auto"
        onInteractOutside={() => {
          setIsSubmitted(false);
          onClose();
        }}
      >
        <DialogHeader>
          <DialogTitle>Add Package</DialogTitle>
          <p className="text-sm text-gray-500 mt-2">
            Fields marked with * are required
          </p>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {formData.items.map((item, index) => (
            <div key={index} className="mb-6 p-4 bg-gray-50 rounded-lg">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-medium">Service {index + 1} *</h3>
                <MainButton
                  variant="neutral"
                  icon={faTrash}
                  onClick={() => handleRemoveService(index)}
                  disabled={formData.items.length === 1}
                >
                  Remove
                </MainButton>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Dropdown
                    label="Service Type *"
                    name={`service_${index}`}
                    options={getFilteredOptions(index)}
                    value={item.service_name}
                    onChange={(e) =>
                      handleServiceChange(index, "service_name", e.target.value)
                    }
                  />
                </div>

                <div>
                  <InputField
                    label="Hours *"
                    type="number"
                    placeholder="Enter hours"
                    value={item.hours.toString()}
                    min="0"
                    onChange={(e) => handleServiceHoursChange(index, e)}
                  />
                </div>
              </div>
            </div>
          ))}

          <div className="flex justify-end">
            <MainButton
              variant="neutral"
              icon={faPlus}
              onClick={(e) => handleAddService(e)}
            >
              Add Service
            </MainButton>
          </div>

          <InputField
            label="Package Title *"
            placeholder="Enter package title"
            name="title"
            value={formData.title}
            onChange={handleChange}
          />
          {isSubmitted && errors.title && (
            <p className="text-red-500 text-sm">{errors.title}</p>
          )}

          <Textarea
            label="Description *"
            placeholder="Enter package description"
            name="description"
            value={formData.description}
            onChange={handleChange}
          />
          {isSubmitted && errors.description && (
            <p className="text-red-500 text-sm">{errors.description}</p>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <InputField
                label="Price ($) *"
                type="number"
                placeholder="Enter price"
                name="total_price"
                value={formData.total_price || ""}
                min="0"
                onChange={(e) => {
                  const value = e.target.valueAsNumber;
                  setFormData({ ...formData, total_price: value });
                  if (value > 0) {
                    setErrors((prev) => ({ ...prev, total_price: "" }));
                  }
                }}
              />
              {isSubmitted && errors.total_price && (
                <p className="text-red-500 text-sm">{errors.total_price}</p>
              )}
            </div>
          </div>

          {isSubmitted && errors.services && (
            <p className="text-red-500 text-sm">{errors.services}</p>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsSubmitted(false);
                onClose();
              }}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <div className="flex items-center justify-center">
                  <Spinner size="sm" className="mr-2" /> Creating...
                </div>
              ) : (
                "Add Package"
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
