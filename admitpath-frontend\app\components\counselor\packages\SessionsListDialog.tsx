"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/app/components/ui/dialog";
import { PackageSubscription } from "@/app/types/counselor/package";
import { Badge } from "@/app/components/ui/badge";
import {
  Calendar,
  Clock,
  User,
  FileText,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { Button } from "@/app/components/ui/button";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/app/components/ui/tabs";
import { motion } from "framer-motion";

interface SessionsListDialogProps {
  isOpen: boolean;
  onClose: () => void;
  subscription: PackageSubscription | null;
}

export default function SessionsListDialog({
  isOpen,
  onClose,
  subscription,
}: SessionsListDialogProps) {
  const [activeTab, setActiveTab] = useState("all");

  if (!subscription) return null;

  // Filter sessions based on active tab
  const filteredSessions = subscription.sessions.filter((session) => {
    if (activeTab === "all") return true;
    return session.status === activeTab;
  });

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "upcoming":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4 mx-2 my-1 text-green-600" />;
      case "upcoming":
        return <Clock className="w-4 h-4 mx-2 my-1 text-blue-600" />;
      case "cancelled":
        return <XCircle className="w-4 h-4 mx-2 my-1 text-red-600" />;
      default:
        return <FileText className="w-4 h-4 mx-2 my-1 text-gray-600" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold flex items-center gap-2">
            <User className="w-5 h-5" />
            {subscription.student.name}'s Sessions
          </DialogTitle>
          <DialogDescription>
            Package:{" "}
            <span className="font-medium">{subscription.package.title}</span>
            <span className="mx-2">•</span>
            <span
              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                subscription.status
              )}`}
            >
              {subscription.status.charAt(0).toUpperCase() +
                subscription.status.slice(1)}
            </span>
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {Object.entries(subscription.service_hours).map(
              ([service, hours]) => (
                <div key={service} className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm text-gray-500 mb-1">Service Hours</h4>
                  <p className="font-medium">{service}</p>
                  <div className="mt-2 flex items-center gap-2">
                    <div className="bg-gray-200 h-2 flex-1 rounded-full overflow-hidden">
                      <div
                        className="bg-blue-500 h-full"
                        style={{
                          width: `${(hours.used / hours.total) * 100}%`,
                        }}
                      ></div>
                    </div>
                    <span className="text-xs font-medium whitespace-nowrap">
                      {hours.used}/{hours.total} hrs
                    </span>
                  </div>
                </div>
              )
            )}
          </div>

          <Tabs
            defaultValue="all"
            className="w-full"
            onValueChange={setActiveTab}
          >
            <TabsList className="mb-4">
              <TabsTrigger value="all">All Sessions</TabsTrigger>
              <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
              <TabsTrigger value="completed">Completed</TabsTrigger>
              <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
            </TabsList>

            <TabsContent value={activeTab} className="mt-0">
              {filteredSessions.length === 0 ? (
                <div className="text-center py-12 bg-gray-50 rounded-lg">
                  <h3 className="text-lg font-medium text-gray-700 mb-2">
                    No {activeTab !== "all" ? activeTab : ""} sessions found
                  </h3>
                  <p className="text-gray-500">
                    {activeTab === "upcoming"
                      ? "No upcoming sessions scheduled yet"
                      : activeTab === "completed"
                      ? "No completed sessions yet"
                      : activeTab === "cancelled"
                      ? "No cancelled sessions"
                      : "No sessions have been scheduled yet"}
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredSessions.map((session, index) => (
                    <motion.div
                      key={session.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.2, delay: index * 0.05 }}
                      className="border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow"
                    >
                      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-medium text-lg">
                              {session.service_name}
                            </h3>
                          </div>

                          <div className="flex flex-col sm:flex-row sm:items-center gap-x-4 gap-y-1 text-sm text-gray-600 mt-2">
                            <div className="flex items-center gap-1">
                              <Calendar className="w-4 h-4" />
                              <span>
                                {new Date(session.date).toLocaleDateString()}
                              </span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Clock className="w-4 h-4" />
                              <span>
                                {new Date(
                                  session.start_time
                                ).toLocaleTimeString([], {
                                  hour: "2-digit",
                                  minute: "2-digit",
                                })}
                                {" - "}
                                {new Date(session.end_time).toLocaleTimeString(
                                  [],
                                  {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                  }
                                )}
                              </span>
                            </div>
                          </div>
                        </div>
                        <Badge
                          variant="outline"
                          className={`ml-auto ${getStatusColor(
                            session.status
                          )}`}
                        >
                          {getStatusIcon(session.status)}
                          {session.status}
                        </Badge>
                      </div>
                    </motion.div>
                  ))}
                </div>
              )}
            </TabsContent>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
}
