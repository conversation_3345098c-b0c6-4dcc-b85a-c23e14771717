"use client";

import { useState, useEffect, use<PERSON>allback } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCheck,
  faCalendarAlt,
  faUsers,
} from "@fortawesome/free-solid-svg-icons";
import { toast } from "react-toastify";
import { MainButton } from "../../common/mainBtn";
import AddPackageDialog from "@/app/components/counselor/packages/AddPackageDialog";
import EditPackagePopup from "@/app/components/counselor/profile/view/popup/EditPackagePopup";
import DeletePackagePopup from "@/app/components/counselor/profile/view/popup/DeletePackagePopup";
import SessionsListDialog from "@/app/components/counselor/packages/SessionsListDialog";
import { PackageSubscription } from "@/app/types/counselor/package";
import { usePackages } from "@/app/hooks/counselor/usePackage";
import ThreeDotIconDropdown from "../../common/threeDotIconMenu";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger,
} from "@/app/components/ui/tabs";
import { PackagesSkeletonList, SubscriptionsSkeletonList } from "./skeletons";
import { generatePlaceholder } from "@/app/utils/image";

///////////////////////////////// MAIN COMPONENT //////////////////////////////
export default function CounselorPackages() {
  const {
    fetchSinglePackage,
    deletePackage,
    selectedPackage,
    fetchPackages,
    fetchSubscriptions,
    packages,
    subscriptions,
    completedSubscriptions,
    loading,
  } = usePackages();

  // State for sessions dialog
  const [sessionsDialogOpen, setSessionsDialogOpen] = useState(false);
  const [selectedSubscription, setSelectedSubscription] =
    useState<PackageSubscription | null>(null);

  const [addPackagePopup, setAddPackagePopup] = useState(false);
  const [editPackagePopup, setEditPackagePopup] = useState(false);
  const [deletePackagePopup, setDeletePackagePopup] = useState(false);
  const [activeTab, setActiveTab] = useState("packages");
  const [editLoading, setEditLoading] = useState(false);

  useEffect(() => {
    fetchPackages();
    if (activeTab === "subscriptions") {
      fetchSubscriptions("active");
    } else if (activeTab === "completed-subscriptions") {
      fetchSubscriptions("completed");
    }
  }, [activeTab, fetchPackages, fetchSubscriptions]);

  ////////////// function handles update package items
  const handleEditPackage = useCallback(
    async (packageId: number | undefined) => {
      if (packageId) {
        try {
          setEditLoading(true);
          await fetchSinglePackage(packageId);
          setEditPackagePopup(true);
        } catch (error) {
          console.error("Error fetching package:", error);
          toast.error("Failed to load package details. Please try again.");
        } finally {
          setEditLoading(false);
        }
      }
    },
    [fetchSinglePackage]
  );

  ////////////// function handles delete package items
  const fetchDeletePackage = useCallback(
    async (packageId: number | undefined) => {
      if (packageId) {
        await fetchSinglePackage(packageId);
        setDeletePackagePopup(true);
      }
    },
    [fetchSinglePackage]
  );

  /////////////////////////////////////// JSX //////////////////////////////////
  return (
    <section className="bg-white rounded-xl px-5 py-9">
      <AddPackageDialog
        isOpen={addPackagePopup}
        onClose={() => setAddPackagePopup(false)}
      />
      <EditPackagePopup
        isPopupOpen={editPackagePopup}
        setIsPopupOpen={setEditPackagePopup}
        packageData={selectedPackage}
      />
      <DeletePackagePopup
        isPopupOpen={deletePackagePopup}
        setIsPopupOpen={setDeletePackagePopup}
        packageData={selectedPackage}
        onSave={async () => {
          if (selectedPackage?.id) {
            await deletePackage(selectedPackage?.id);
            fetchPackages();
          }
        }}
      />

      <div className="flex justify-between items-center mb-8">
        <h1 className="text-neutral8 text-2xl font-bold">Package Management</h1>
        <MainButton
          variant="primary"
          onClick={() => {
            console.log("Add Package button clicked");
            setAddPackagePopup(true);
            console.log("addPackagePopup state after click:", true);
          }}
        >
          Add Package +
        </MainButton>
      </div>

      <Tabs
        defaultValue="packages"
        className="w-full"
        onValueChange={setActiveTab}
      >
        <TabsList className="mb-6">
          <TabsTrigger value="packages" className="px-6">
            <FontAwesomeIcon icon={faCalendarAlt} className="mr-2" />
            My Packages
          </TabsTrigger>
          <TabsTrigger value="subscriptions" className="px-6">
            <FontAwesomeIcon icon={faUsers} className="mr-2" />
            Student Subscriptions
          </TabsTrigger>
          <TabsTrigger value="completed-subscriptions" className="px-6">
            <FontAwesomeIcon icon={faCheck} className="mr-2" />
            Completed Packages
          </TabsTrigger>
        </TabsList>

        <TabsContent value="packages" className="mt-0">
          {loading ? (
            <PackagesSkeletonList />
          ) : packages.items.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-medium text-gray-700 mb-2">
                No packages created yet
              </h3>
              <p className="text-gray-500 mb-6">
                Create your first package to offer to students
              </p>
              <MainButton
                variant="primary"
                onClick={() => setAddPackagePopup(true)}
              >
                Create Package
              </MainButton>
            </div>
          ) : (
            <ul className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {packages.items.map((pkg) => (
                <li
                  key={pkg.id}
                  className="bg-neutral1 shadow-sm hover:shadow-md transition-shadow duration-200 rounded-lg p-5 flex flex-col"
                >
                  <div className="flex justify-between">
                    <h2 className="text-neutral9 text-lg font-semibold h-12">
                      {pkg.title}
                    </h2>

                    <ThreeDotIconDropdown
                      handleEdit={() => handleEditPackage(pkg.id)}
                      handleRemove={() => fetchDeletePackage(pkg.id)}
                      isLoading={editLoading}
                    />
                  </div>

                  <div className="my-4">
                    {pkg.package_items.slice(0, 2).map((service) => (
                      <div
                        key={service.id}
                        className="bg-[#6FCF9733] px-3 py-2 rounded-lg mb-2 text-neutral8 font-medium flex justify-between gap-4"
                      >
                        <span>{service.service_name}</span>
                        <span className="whitespace-nowrap">
                          {service.hours} hours
                        </span>
                      </div>
                    ))}
                    {pkg.package_items.length > 2 && (
                      <div className="text-sm text-blue-600 font-medium px-3 py-2">
                        + {pkg.package_items.length - 2} more service
                        {pkg.package_items.length - 2 > 1 ? "s" : ""}
                      </div>
                    )}
                  </div>

                  <p className="text-sm mb-4 text-neutral7 line-clamp-2">
                    {pkg.description}
                  </p>

                  <div className="flex justify-between items-end mt-auto pt-4 border-t">
                    <div>
                      <p className="text-sm text-neutral5 mb-1">Price</p>
                      <p className="text-2xl font-bold text-neutral8">
                        ${pkg.total_price}
                      </p>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </TabsContent>

        <TabsContent value="subscriptions" className="mt-0">
          {loading ? (
            <SubscriptionsSkeletonList />
          ) : subscriptions.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-medium text-gray-700 mb-2">
                No active subscriptions
              </h3>
              <p className="text-gray-500">
                Students haven't purchased any of your packages yet
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {subscriptions.map((subscription) => (
                <div
                  key={subscription.id}
                  className="border rounded-lg p-5 bg-white shadow-sm"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <img
                        src={
                          subscription.student.profile_picture ||
                          generatePlaceholder(
                            subscription.student.name.split(" ")[0],
                            subscription.student.name.split(" ")[1]
                          )
                        }
                        alt={subscription.student.name}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                      <div>
                        <h3 className="font-semibold text-lg">
                          {subscription.student.name}
                        </h3>
                        <p className="text-sm text-gray-500">
                          Package:{" "}
                          <span className="font-medium text-gray-700">
                            {subscription.package.title}
                          </span>
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <span
                        className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                          subscription.status === "active"
                            ? "bg-green-100 text-green-800"
                            : subscription.status === "pending"
                            ? "bg-yellow-100 text-yellow-800"
                            : subscription.status === "completed"
                            ? "bg-blue-100 text-blue-800"
                            : subscription.status === "cancelled"
                            ? "bg-red-100 text-red-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {subscription.status.charAt(0).toUpperCase() +
                          subscription.status.slice(1)}
                      </span>
                      <p className="text-sm text-gray-500 mt-1">
                        {subscription.end_date ? (
                          <>
                            {new Date(
                              subscription.start_date
                            ).toLocaleDateString()}{" "}
                            -{" "}
                            {new Date(
                              subscription.end_date
                            ).toLocaleDateString()}
                          </>
                        ) : (
                          <>
                            Starting Date:{" "}
                            {new Date(
                              subscription.start_date
                            ).toLocaleDateString()}
                          </>
                        )}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="border rounded-md p-3">
                      <h4 className="font-medium mb-2">Service Hours</h4>
                      <div className="space-y-2">
                        {Object.entries(subscription.service_hours).map(
                          ([service, hours]) => (
                            <div
                              key={service}
                              className="flex justify-between items-center"
                            >
                              <span className="text-sm">{service}</span>
                              <div className="flex items-center gap-2">
                                <div className="bg-gray-200 h-2 w-24 rounded-full overflow-hidden">
                                  <div
                                    className="bg-blue-500 h-full"
                                    style={{
                                      width: `${
                                        (hours.used / hours.total) * 100
                                      }%`,
                                    }}
                                  ></div>
                                </div>
                                <span className="text-xs font-medium">
                                  {hours.used}/{hours.total} hrs
                                </span>
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>

                    <div className="border rounded-md p-3">
                      <h4 className="font-medium mb-2">Recent Sessions</h4>
                      {subscription.sessions.length > 0 ? (
                        <div className="space-y-2 max-h-32 overflow-y-auto">
                          {subscription.sessions.slice(0, 3).map((session) => (
                            <div
                              key={session.id}
                              className="flex justify-between items-center text-sm"
                            >
                              <div>
                                <span className="font-medium">
                                  {session.service_name}
                                </span>
                                <p className="text-xs text-gray-500">
                                  {new Date(session.date).toLocaleDateString()},
                                  {new Date(
                                    session.start_time
                                  ).toLocaleTimeString([], {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                  })}
                                </p>
                              </div>
                              <span
                                className={`text-xs px-2 py-0.5 rounded-full ${
                                  session.status === "completed"
                                    ? "bg-green-100 text-green-800"
                                    : session.status === "upcoming"
                                    ? "bg-blue-100 text-blue-800"
                                    : session.status === "cancelled"
                                    ? "bg-red-100 text-red-800"
                                    : "bg-gray-100 text-gray-800"
                                }`}
                              >
                                {session.status}
                              </span>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500">
                          No sessions scheduled yet
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end gap-3 mt-4">
                    <MainButton
                      variant="primary"
                      onClick={() => {
                        setSelectedSubscription(subscription);
                        setSessionsDialogOpen(true);
                      }}
                    >
                      View Details
                    </MainButton>
                  </div>
                </div>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="completed-subscriptions" className="mt-0">
          {loading ? (
            <SubscriptionsSkeletonList />
          ) : completedSubscriptions.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <h3 className="text-lg font-medium text-gray-700 mb-2">
                No completed packages
              </h3>
              <p className="text-gray-500">
                Completed package subscriptions will appear here
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {completedSubscriptions.map((subscription) => (
                <div
                  key={subscription.id}
                  className="border rounded-lg p-5 bg-white shadow-sm"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <img
                        src={
                          subscription.student.profile_picture ||
                          generatePlaceholder(
                            subscription.student.name.split(" ")[0],
                            subscription.student.name.split(" ")[1]
                          )
                        }
                        alt={subscription.student.name}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                      <div>
                        <h3 className="font-semibold text-lg">
                          {subscription.student.name}
                        </h3>
                        <p className="text-sm text-gray-500">
                          Package:{" "}
                          <span className="font-medium text-gray-700">
                            {subscription.package.title}
                          </span>
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <span className="inline-block px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                        Completed
                      </span>
                      <p className="text-sm text-gray-500 mt-1">
                        {subscription.end_date ? (
                          <>
                            {new Date(
                              subscription.start_date
                            ).toLocaleDateString()}{" "}
                            -{" "}
                            {new Date(
                              subscription.end_date
                            ).toLocaleDateString()}
                          </>
                        ) : (
                          <>
                            Starting Date:{" "}
                            {new Date(
                              subscription.start_date
                            ).toLocaleDateString()}
                          </>
                        )}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="border rounded-md p-3">
                      <h4 className="font-medium mb-2">Service Hours</h4>
                      <div className="space-y-2">
                        {Object.entries(subscription.service_hours).map(
                          ([service, hours]) => (
                            <div
                              key={service}
                              className="flex justify-between items-center"
                            >
                              <span className="text-sm">{service}</span>
                              <div className="flex items-center gap-2">
                                <div className="bg-gray-200 h-2 w-24 rounded-full overflow-hidden">
                                  <div
                                    className="bg-blue-500 h-full"
                                    style={{
                                      width: `${
                                        (hours.used / hours.total) * 100
                                      }%`,
                                    }}
                                  ></div>
                                </div>
                                <span className="text-xs font-medium">
                                  {hours.used}/{hours.total} hrs
                                </span>
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>

                    <div className="border rounded-md p-3">
                      <h4 className="font-medium mb-2">Sessions</h4>
                      {subscription.sessions.length > 0 ? (
                        <div className="space-y-2 max-h-32 overflow-y-auto">
                          {subscription.sessions.slice(0, 3).map((session) => (
                            <div
                              key={session.id}
                              className="flex justify-between items-center text-sm"
                            >
                              <div>
                                <span className="font-medium">
                                  {session.service_name}
                                </span>
                                <p className="text-xs text-gray-500">
                                  {new Date(session.date).toLocaleDateString()},
                                  {new Date(
                                    session.start_time
                                  ).toLocaleTimeString([], {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                  })}
                                </p>
                              </div>
                              <span className="text-xs px-2 py-0.5 rounded-full bg-green-100 text-green-800">
                                {session.status}
                              </span>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500">
                          No sessions recorded
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end gap-3 mt-4">
                    <MainButton
                      variant="primary"
                      onClick={() => {
                        setSelectedSubscription(subscription);
                        setSessionsDialogOpen(true);
                      }}
                    >
                      View Details
                    </MainButton>
                  </div>
                </div>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      <SessionsListDialog
        isOpen={sessionsDialogOpen}
        onClose={() => setSessionsDialogOpen(false)}
        subscription={selectedSubscription}
      />
    </section>
  );
}
