"use client";

import { Skeleton } from "@/app/components/ui/skeleton";

export function PackageSkeleton() {
  return (
    <div className="border rounded-lg p-5 bg-white shadow-sm">
      <div className="flex justify-between items-start mb-4">
        <Skeleton className="h-7 w-48" />
        <Skeleton className="h-8 w-8 rounded-full" />
      </div>
      
      <div className="space-y-2 mb-4">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>
      
      <div className="grid grid-cols-2 gap-3 mb-4">
        <Skeleton className="h-16 rounded-md" />
        <Skeleton className="h-16 rounded-md" />
      </div>
      
      <div className="space-y-2 mb-4">
        <Skeleton className="h-4 w-32" />
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4 rounded-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-4 rounded-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        </div>
      </div>
      
      <div className="flex justify-between items-end pt-4 border-t">
        <div>
          <Skeleton className="h-3 w-12 mb-1" />
          <Skeleton className="h-7 w-24" />
        </div>
        <div>
          <Skeleton className="h-3 w-16 mb-1" />
          <Skeleton className="h-6 w-20" />
        </div>
      </div>
    </div>
  );
}

export function SubscriptionSkeleton() {
  return (
    <div className="border rounded-lg p-5 bg-white shadow-sm">
      <div className="flex items-start gap-3 mb-4">
        <Skeleton className="h-12 w-12 rounded-full" />
        <div className="flex-1">
          <Skeleton className="h-5 w-40 mb-1" />
          <Skeleton className="h-4 w-32" />
        </div>
        <Skeleton className="h-6 w-24 rounded-full" />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="border rounded-md p-3">
          <Skeleton className="h-5 w-32 mb-3" />
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <Skeleton className="h-4 w-24" />
              <div className="flex items-center gap-2">
                <Skeleton className="h-2 w-24 rounded-full" />
                <Skeleton className="h-4 w-16" />
              </div>
            </div>
            <div className="flex justify-between items-center">
              <Skeleton className="h-4 w-32" />
              <div className="flex items-center gap-2">
                <Skeleton className="h-2 w-24 rounded-full" />
                <Skeleton className="h-4 w-16" />
              </div>
            </div>
          </div>
        </div>
        
        <div className="border rounded-md p-3">
          <Skeleton className="h-5 w-32 mb-3" />
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-16" />
            </div>
            <div className="flex justify-between items-center">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-16" />
            </div>
          </div>
        </div>
      </div>
      
      <div className="flex justify-end">
        <Skeleton className="h-9 w-32" />
      </div>
    </div>
  );
}

export function PackagesSkeletonList() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array(3).fill(0).map((_, i) => (
        <PackageSkeleton key={i} />
      ))}
    </div>
  );
}

export function SubscriptionsSkeletonList() {
  return (
    <div className="space-y-6">
      {Array(2).fill(0).map((_, i) => (
        <SubscriptionSkeleton key={i} />
      ))}
    </div>
  );
}
