"use client";

import { Building } from "lucide-react";
import { MainButton } from "../../common/mainBtn";

interface BankInfo {
  primary: {
    bankName: string;
    accountNumber: string;
  };
  secondary?: {
    bankName: string;
    accountNumber: string;
  };
}

interface AccountInfoProps {
  bankInfo: BankInfo;
  onClick: () => void;
  setUpdateAccModal: () => void;
}

export const AccountInfo = ({
  bankInfo,
  onClick,
  setUpdateAccModal,
}: AccountInfoProps) => {
  return (
    <div className="rounded-2xl border bg-white mb-4">
      <div className="p-4 md:p-6 flex items-center justify-between border-b">
        <div className="flex items-center gap-3">
          <span className="p-3 bg-gray-100 rounded-xl border ">
            <Building className="w-5 h-5" />
          </span>
          <h2 className="text-xl font-medium">Account Information</h2>
        </div>
        <MainButton variant="neutral" onClick={setUpdateAccModal}>
          Update
        </MainButton>
      </div>

      <div className="p-4 md:p-6 grid md:grid-cols-2 gap-6">
        <div className="space-y-1 bg-gray-100 border rounded-xl p-4">
          <h3 className="text-gray-700/80 font-medium text-md">
            Primary Bank Information
          </h3>
          <div className="space-y-1">
            <p className="font-medium text-lg">{bankInfo.primary.bankName}</p>
            <p className="text-gray-900 font-medium text-md">
              **** **** **** {bankInfo.primary.accountNumber}
            </p>
          </div>
        </div>

        <div className="space-y-4 bg-gray-100 border rounded-xl p-4">
          <h3 className="text-gray-700/80 font-medium text-m">
            Secondary Bank Information
          </h3>
          {bankInfo.secondary ? (
            <div className="space-y-1">
              <p className="font-medium text-lg">
                {bankInfo.secondary.bankName}
              </p>
              <p className="text-gray-900 font-medium text-md">
                **** **** **** {bankInfo.secondary.accountNumber}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="font-medium text-black text-xl">--</p>
              <button
                className="text-black font-medium text-xl hover:underline"
                onClick={onClick}
              >
                Add now
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
