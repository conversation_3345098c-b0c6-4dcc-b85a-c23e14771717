import { BankDetail } from "@/app/types/counselor/payments";
import { Pencil, Trash2 } from "lucide-react";
import { But<PERSON> } from "@components/ui/button";

interface AccountCardProps {
  bank: BankDetail;
  onEdit: (bank: BankDetail) => void;
  onDelete: (id: number) => void;
}

export const AccountCard = ({ bank, onEdit, onDelete }: AccountCardProps) => (
  <div className="relative bg-white border rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow">
    <Button
      variant="ghost"
      size="icon"
      className="absolute top-2 right-2 text-gray-400 hover:text-blue-500 transition-colors"
      onClick={() => onEdit(bank)}
    >
      <Pencil className="h-4 w-4" />
      <span className="sr-only">Edit bank account</span>
    </Button>

    <h3 className="text-sm font-medium text-gray-500 mb-2">
      {bank.is_primary
        ? "Primary Bank Information"
        : "Secondary Bank Information"}
    </h3>

    <div className="space-y-2">
      <p className="font-semibold text-lg text-gray-900">{bank.bank_name}</p>
      <p className="text-gray-700 font-medium">
        **** **** **** {bank.account_number.slice(-4)}
      </p>
      <p className="text-gray-600 text-sm">SWIFT: {bank.swift_code}</p>
    </div>

    <Button
      variant="ghost"
      size="sm"
      className="absolute bottom-2 right-2 text-red-500 hover:text-red-700 hover:bg-red-50 transition-colors"
      onClick={() => onDelete(bank.id!)}
    >
      <Trash2 className="h-4 w-4 mr-1" />
      <span className="text-xs">Delete</span>
    </Button>
  </div>
);
