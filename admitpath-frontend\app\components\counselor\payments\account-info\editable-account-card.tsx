"use client";

import { BankDetail } from "@/app/types/counselor/payments";
import { useState } from "react";
import { Input } from "@components/ui/input";
import { Button } from "@components/ui/button";
import { Label } from "@components/ui/label";
import { Checkbox } from "@components/ui/checkbox";

interface EditableAccountCardProps {
  initialData: BankDetail | null;
  onSave: (data: BankDetail) => void;
  onCancel: () => void;
}

export const EditableAccountCard = ({
  initialData,
  onSave,
  onCancel,
}: EditableAccountCardProps) => {
  const [formData, setFormData] = useState<BankDetail>(
    initialData || {
      id: 0,
      bank_name: "",
      account_title: "",
      account_number: "",
      swift_code: "",
      is_primary: true,
    }
  );

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const validateFields = () => {
    const newErrors: { [key: string]: string } = {};
    if (!formData.bank_name) {
      newErrors.bank_name = "Bank Name is required.";
    }
    if (!formData.account_title) {
      newErrors.account_title = "Account Title is required.";
    }
    if (!formData.account_number) {
      newErrors.account_number = "Account Number is required.";
    } else if (!/^[0-9]+$/.test(formData.account_number)) {
      newErrors.account_number = "Account Number must be numeric.";
    }
    if (!formData.swift_code) {
      newErrors.swift_code = "SWIFT Code is required.";
    } else if (formData.swift_code.length !== 8) {
      newErrors.swift_code = "SWIFT Code must be exactly 8 characters.";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (validateFields()) {
      onSave(formData);
    }
  };

  return (
    <div className="bg-white rounded-xl p-3 sm:p-6 border">
      {/* Header */}
      <div className="flex items-center gap-2 mb-6">
        <h3 className="font-medium">
          {initialData ? "Edit Bank Account" : "Add Bank Account"}
        </h3>
      </div>

      <div className="space-y-6">
        {/* Bank Information Section */}
        <div className="space-y-4 bg-gray-50 rounded-lg p-4">
          <div className="text-sm text-gray-500">
            {formData.is_primary ? "Primary" : "Secondary"} Bank Information
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="bank_name">Bank Name</Label>
              <Input
                id="bank_name"
                name="bank_name"
                value={formData.bank_name}
                onChange={handleChange}
                placeholder="Enter bank name"
                className="bg-white"
              />
              {errors.bank_name && (
                <p className="text-red-600 text-sm">{errors.bank_name}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="account_title">Account Title</Label>
              <Input
                id="account_title"
                name="account_title"
                value={formData.account_title}
                onChange={handleChange}
                placeholder="Enter account title"
                className="bg-white"
              />
              {errors.account_title && (
                <p className="text-red-600 text-sm">{errors.account_title}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="account_number">Account Number</Label>
              <Input
                id="account_number"
                name="account_number"
                value={formData.account_number}
                onChange={handleChange}
                placeholder="Enter account number"
                className="bg-white"
              />
              {errors.account_number && (
                <p className="text-red-600 text-sm">{errors.account_number}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="swift_code">SWIFT Code</Label>
              <Input
                id="swift_code"
                name="swift_code"
                value={formData.swift_code}
                onChange={handleChange}
                placeholder="Enter SWIFT code"
                className="bg-white"
              />
              {errors.swift_code && (
                <p className="text-red-600 text-sm">{errors.swift_code}</p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="is_primary"
              checked={formData.is_primary}
              onCheckedChange={(checked) =>
                setFormData((prev) => ({
                  ...prev,
                  is_primary: checked as boolean,
                }))
              }
            />
            <Label htmlFor="is_primary" className="text-sm font-normal">
              Set as primary account
            </Label>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-end gap-2">
          <Button variant="outline" onClick={onCancel} className="px-6">
            Cancel
          </Button>
          <Button onClick={handleSave} className="px-6">
            Save Changes
          </Button>
        </div>
      </div>
    </div>
  );
};
