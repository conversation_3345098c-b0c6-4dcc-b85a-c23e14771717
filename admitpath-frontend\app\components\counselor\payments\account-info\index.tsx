import { usePayments } from "@/app/hooks/counselor/usePayments";
import { BankDetail } from "@/app/types/counselor/payments";
import { Building } from "lucide-react";
import { useEffect, useState } from "react";
import { EditableAccountCard } from "./editable-account-card";
import { AccountCard } from "./account-card";
import { BankInfoSkeleton } from "../skeleton";

interface AccountInfoProps {}

export default function AccountInfo({}: AccountInfoProps) {
  const {
    loading,
    bankAccounts,
    createBankAccount,
    updateBankAccount,
    deleteBankAccount,
    fetchBankAccounts,
  } = usePayments();
  const [editMode, setEditMode] = useState(false);
  const [currentEdit, setCurrentEdit] = useState<BankDetail | null>(null);

  const handleEdit = (bank: BankDetail) => {
    setCurrentEdit(bank);
    setEditMode(true);
  };

  const handleDelete = (id: number) => {
    deleteBankAccount(id);
  };

  const handleSave = (data: BankDetail) => {
    if (!data.id || data.id === 0) {
      createBankAccount(data);
    } else {
      updateBankAccount(data.id, data);
    }
    setEditMode(false);
    setCurrentEdit(null);
  };

  useEffect(() => {
    fetchBankAccounts();
  }, [fetchBankAccounts]);

  return !bankAccounts || loading ? (
    <BankInfoSkeleton />
  ) : (
    <div className="rounded-2xl bg-white p-3 sm:p-6 space-y-5 sm:space-y-8">
      <div className="flex md:flex-row flex-col md:items-center items-start gap-y-4 justify-between">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-gray-100 rounded-xl border ">
            <Building className="w-5 h-5" />
          </div>
          <h2 className="text-xl font-medium">Account Information</h2>
        </div>
        <div className="flex gap-2  mx-auto md:mx-0">
          <button
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors"
            onClick={() => {
              setEditMode(true);
              setCurrentEdit(null); // Clear edit state for new account
            }}
          >
            Add Account
          </button>
        </div>
      </div>

      {editMode ? (
        <EditableAccountCard
          initialData={currentEdit}
          onSave={handleSave}
          onCancel={() => {
            setEditMode(false);
            setCurrentEdit(null);
          }}
        />
      ) : (
        bankAccounts &&
        (bankAccounts.length === 0 ? (
          <div className="text-center text-gray-500 mx-auto pb-3 sm:pb-6">
            No account information available.
            <button
              className="md:inline-block hidden mx-2 px-4 py-2 text-sm font-medium text-gray-700  rounded-xl hover:bg-gray-100 transition-all"
              onClick={() => {
                setEditMode(true);
                setCurrentEdit(null); // Clear edit state for new account
              }}
            >
              Add Now
            </button>
          </div>
        ) : (
          <div className="grid lg:grid-cols-2 grid-cols-1 gap-6">
            {bankAccounts
              .sort((a, b) => (a.is_primary ? -1 : 1))
              .map((bank) => (
                <AccountCard
                  key={bank.id}
                  bank={bank}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                />
              ))}
          </div>
        ))
      )}
    </div>
  );
}
