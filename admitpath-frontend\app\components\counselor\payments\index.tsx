"use client";

import { StatsCards } from "@components/counselor/payments/stats-cards";
import AccountInfo from "@components/counselor/payments/account-info/index";
import { SessionsTable } from "@components/counselor/payments/sessions-table";
import { paymentsData } from "@constants/dummy-student-data";

export default function Payments() {
  return (
    <div className="space-y-6">
      {/* <StatsCards stats={paymentsData.stats} /> */}
      <AccountInfo />
      {/* <SessionsTable sessions={paymentsData.sessions} /> */}
    </div>
  );
}
