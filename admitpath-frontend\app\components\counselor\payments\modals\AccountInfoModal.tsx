import React, { useState } from "react";
import { PopupProps } from "@/app/types/counselor/profile";
import Popup from "@/app/components/common/popup";
import Dropdown from "@/app/components/common/dropdown";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCheck,
  faBuildingColumns,
  faWallet,
} from "@fortawesome/free-solid-svg-icons";
import { toast } from "react-toastify";
import { MainButton } from "@/app/components/common/mainBtn";

type CountryOption = {
  value: string;
  label: string;
};

type PaymentOptionProps = {
  icon: any;
  title: string;
  description: string;
  checked: boolean;
  onClick: () => void;
};

interface AddAccountModalProps extends PopupProps {
  setAddAccModal: () => void;
}

const countryOptions: CountryOption[] = [
  { value: "", label: "Select Country" },
  { value: "pak", label: "Pakistan" },
  { value: "usa", label: "United States" },
];

const PaymentOption: React.FC<PaymentOptionProps> = ({
  icon,
  title,
  description,
  checked,
  onClick,
}) => (
  <div
    className={`border rounded-lg p-3 mt-5 cursor-pointer ${
      checked ? "bg-gray-100" : "bg-transparent"
    }`}
    onClick={onClick}
  >
    <div className="flex justify-between items-center">
      <div className="flex items-center">
        <div className="grid gap-2">
          <span className="border rounded-md p-2 mr-3 flex justify-center items-center w-min">
            <FontAwesomeIcon icon={icon} className="text-primary" />
          </span>
          <h3 className="font-medium text-lg text-neutral8">{title}</h3>
          <p className="text-sm text-neutral-500">{description}</p>
        </div>
      </div>
      <span
        className={`border p-1 rounded-full h-7 w-7 flex justify-center items-center ${
          checked ? "bg-black text-white" : "bg-transparent text-neutral-300"
        }`}
      >
        <FontAwesomeIcon icon={faCheck} className="w-3 h-3" />
      </span>
    </div>
  </div>
);

const AccountInfoModal: React.FC<AddAccountModalProps> = ({
  isPopupOpen,
  setIsPopupOpen,
  setAddAccModal,
}) => {
  const [country, setCountry] = useState<string>("");
  const [selectedPayment, setSelectedPayment] =
    useState<string>("bank-transfer");

  function handleNext() {
    if (!country || !selectedPayment) {
      toast.error("Country and Payment option are reuired!");
      return;
    }

    setAddAccModal();
    setIsPopupOpen(false);
  }

  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={() => setIsPopupOpen(false)}
      title="Account Information"
      width="50vw"
    >
      <Dropdown
        label="Select Country"
        options={countryOptions}
        value={country}
        name="country"
        onChange={(e) => setCountry(e.target.value)}
        required={true}
        selectStyle="w-full border p-3"
      />

      <PaymentOption
        icon={faBuildingColumns}
        title="Bank Transfer"
        description="Powered by Stripe"
        checked={selectedPayment === "bank-transfer"}
        onClick={() => setSelectedPayment("bank-transfer")}
      />

      <PaymentOption
        icon={faWallet}
        title="Connect Payoneer Account"
        description="Powered by Stripe"
        checked={selectedPayment === "payoneer"}
        onClick={() => setSelectedPayment("payoneer")}
      />

      <div className="flex justify-end mt-6 gap-4">
        <MainButton variant="neutral" onClick={() => setIsPopupOpen(false)}>
          Cancel
        </MainButton>

        <MainButton variant="primary" onClick={() => handleNext()}>
          Next
        </MainButton>
      </div>
    </Popup>
  );
};

export default AccountInfoModal;
