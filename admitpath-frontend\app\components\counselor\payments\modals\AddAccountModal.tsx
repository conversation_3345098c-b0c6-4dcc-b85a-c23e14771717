"use client";

import React from "react";
import { InputField } from "@/app/components/common/inputField";
import Popup from "@/app/components/common/popup";
import useFormState from "@/app/hooks/useFormState";
import { toast } from "react-toastify";
import { MainButton } from "@/app/components/common/mainBtn";

const AddAccountModal: React.FC<{
  isPopupOpen: boolean;
  setIsPopupOpen: (open: boolean) => void;
}> = ({ isPopupOpen, setIsPopupOpen }) => {
  const { formData, handleChange } = useFormState({
    bankName: "",
    accountTitle: "",
    iban: "",
    swiftCode: "",
  });

  const handleSubmit = () => {
    if (Object.values(formData).some((value) => !value)) {
      toast.error("All fields are required.");
      return;
    }
    console.log("Form Submitted", formData);
    setIsPopupOpen(false);
  };

  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={() => setIsPopupOpen(false)}
      title="Account Information"
      width="50vw"
    >
      <form className="space-y-4" onSubmit={(e) => e.preventDefault()}>
        <InputField
          label="Bank Name"
          name="bankName"
          value={formData.bankName}
          onChange={handleChange}
          required
        />

        <InputField
          label="Account Title"
          name="accountTitle"
          value={formData.accountTitle}
          onChange={handleChange}
          required
        />

        <InputField
          label="IBAN/Account No"
          name="iban"
          value={formData.iban}
          onChange={handleChange}
          required
        />

        <InputField
          label="Swift Code"
          name="swiftCode"
          value={formData.swiftCode}
          onChange={handleChange}
          required
        />

        <div className="flex justify-end mt-6 gap-4">
          <MainButton variant="neutral" onClick={() => setIsPopupOpen(false)}>
            Cancel
          </MainButton>
          <MainButton variant="primary" onClick={() => handleSubmit()}>
            Add
          </MainButton>
        </div>
      </form>
    </Popup>
  );
};

export default AddAccountModal;
