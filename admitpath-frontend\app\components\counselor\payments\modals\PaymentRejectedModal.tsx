import React from "react";
import { PopupProps } from "@/app/types/counselor/profile";
import Popup from "@/app/components/common/popup";
import useFormState from "@/app/hooks/useFormState";
import { MainButton } from "@/app/components/common/mainBtn";

type FormData = {
  service: string;
  feedback: string;
};

const initialState: FormData = {
  service: "yes",
  feedback: "",
};

const PaymentRejectedModal: React.FC<PopupProps> = ({
  isPopupOpen,
  setIsPopupOpen,
}) => {
  const { formData, handleChange } = useFormState<FormData>(initialState);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Example: Add validation or API call here
    console.log("Submitted Data:", formData);

    setIsPopupOpen(false); // Close the popup after submission
  };

  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={() => setIsPopupOpen(false)}
      title="Payment rejected"
      width="50vw"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Service Radio Options */}
        <div className="bg-[#EB575733] p-4 rounded-lg border text-[#F2994A]">
          <h3 className="font-medium">
            Student feedback has not been submitted!
          </h3>
          <p className="text-sm">Lorem ipsom hctq eybc i</p>
        </div>

        {/* Feedback Textarea */}
        <div>
          <label
            htmlFor="feedback"
            className="block font-medium mb-2 text-neutral8"
          >
            Do you have any feedback? (optional)
          </label>
          <textarea
            id="feedback"
            name="feedback"
            placeholder="Enter here..."
            value={formData.feedback}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded-md py-2 px-3"
            rows={4}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-4">
          <MainButton variant="neutral" onClick={() => setIsPopupOpen(false)}>
            Cancel
          </MainButton>
          <MainButton variant="primary">Submit</MainButton>
        </div>
      </form>
    </Popup>
  );
};

export default PaymentRejectedModal;
