import React from "react";
import { PopupProps } from "@/app/types/counselor/profile";
import Popup from "@/app/components/common/popup";
import useFormState from "@/app/hooks/useFormState";
import { MainButton } from "@/app/components/common/mainBtn";

type FormData = {
  service: string;
  feedback: string;
};

const initialState: FormData = {
  service: "yes",
  feedback: "",
};

const ProofSessionModal: React.FC<PopupProps> = ({
  isPopupOpen,
  setIsPopupOpen,
}) => {
  const { formData, handleChange } = useFormState<FormData>(initialState);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Example: Add validation or API call here
    console.log("Submitted Data:", formData);

    setIsPopupOpen(false); // Close the popup after submission
  };

  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={() => setIsPopupOpen(false)}
      title="Submit proof for the session"
      width="50vw"
    >
      <h3 className="font-semibold text-neutral8">Submit the form below</h3>
      <p className="text-neutral5 text-sm">
        Please fill out the form as proof of session.
      </p>

      <form onSubmit={handleSubmit} className="mt-6 space-y-6">
        {/* Service Radio Options */}
        <div>
          <label className="block font-medium mb-2 text-neutral8">
            Did you provide the service(s) as expected? *
          </label>
          <div className="flex gap-4">
            {["yes", "no"].map((option) => (
              <label key={option} className="inline-flex items-center">
                <input
                  type="radio"
                  name="service"
                  value={option}
                  checked={formData.service === option}
                  onChange={handleChange}
                  className="mr-2"
                  required
                />
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </label>
            ))}
          </div>
        </div>

        {/* Feedback Textarea */}
        <div>
          <label
            htmlFor="feedback"
            className="block font-medium mb-2 text-neutral8"
          >
            Do you have any feedback? (optional)
          </label>
          <textarea
            id="feedback"
            name="feedback"
            placeholder="Enter here..."
            value={formData.feedback}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded-md py-2 px-3"
            rows={4}
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-4">
          <MainButton variant="neutral" onClick={() => setIsPopupOpen(false)}>
            Cancel
          </MainButton>
          <MainButton variant="primary">Submit</MainButton>
        </div>
      </form>
    </Popup>
  );
};

export default ProofSessionModal;
