"use client";

import React, { useState } from "react";
import { InputField } from "@/app/components/common/inputField";
import Popup from "@/app/components/common/popup";
import useFormState from "@/app/hooks/useFormState";
import { toast } from "react-toastify";
import { MainButton } from "@/app/components/common/mainBtn";

const UpdateAccountModal: React.FC<{
  isPopupOpen: boolean;
  setIsPopupOpen: (open: boolean) => void;
}> = ({ isPopupOpen, setIsPopupOpen }) => {
  // State to track active tab
  const [activeTab, setActiveTab] = useState<"primary" | "secondary">(
    "primary"
  );

  // Form state for both tabs
  const primaryFormState = useFormState({
    bankName: "",
    accountTitle: "",
    iban: "",
    swiftCode: "",
  });

  const secondaryFormState = useFormState({
    bankName: "",
    accountTitle: "",
    iban: "",
    swiftCode: "",
  });

  // Determine the active form state
  const currentFormState =
    activeTab === "primary" ? primaryFormState : secondaryFormState;

  const handleSubmit = () => {
    if (Object.values(currentFormState.formData).some((value) => !value)) {
      toast.error("All fields are required.");
      return;
    }
    console.log(
      `Form Submitted for ${activeTab} tab`,
      currentFormState.formData
    );
    setIsPopupOpen(false);
  };

  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={() => setIsPopupOpen(false)}
      title="Update Account"
      width="50vw"
    >
      <div>
        {/* Tab Navigation */}
        <div className="flex border-b mb-4">
          <button
            className={`px-4 py-2 ${
              activeTab === "primary"
                ? "border-b-2 border-primary font-semibold"
                : "text-gray-500"
            }`}
            onClick={() => setActiveTab("primary")}
          >
            Primary
          </button>
          <button
            className={`px-4 py-2 ${
              activeTab === "secondary"
                ? "border-b-2 border-primary font-semibold"
                : "text-gray-500"
            }`}
            onClick={() => setActiveTab("secondary")}
          >
            Secondary
          </button>
        </div>

        {/* Form */}
        <form className="space-y-4" onSubmit={(e) => e.preventDefault()}>
          <InputField
            label="Bank Name"
            name="bankName"
            value={currentFormState.formData.bankName}
            onChange={currentFormState.handleChange}
            required
          />

          <InputField
            label="Account Title"
            name="accountTitle"
            value={currentFormState.formData.accountTitle}
            onChange={currentFormState.handleChange}
            required
          />

          <InputField
            label="IBAN/Account No"
            name="iban"
            value={currentFormState.formData.iban}
            onChange={currentFormState.handleChange}
            required
          />

          <InputField
            label="Swift Code"
            name="swiftCode"
            value={currentFormState.formData.swiftCode}
            onChange={currentFormState.handleChange}
            required
          />

          {/* Buttons */}
          <div className="flex justify-end mt-6 gap-4">
            <MainButton variant="neutral" onClick={() => setIsPopupOpen(false)}>
              Cancel
            </MainButton>
            <MainButton variant="primary" onClick={() => handleSubmit()}>
              Save
            </MainButton>
          </div>
        </form>
      </div>
    </Popup>
  );
};

export default UpdateAccountModal;
