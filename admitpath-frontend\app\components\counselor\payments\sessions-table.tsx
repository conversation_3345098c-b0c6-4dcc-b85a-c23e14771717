import {
  Search,
  DollarSign,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  Filter,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table";
import Image from "next/image";
import { useState } from "react";
import { SessionPayment } from "@constants/dummy-student-data";

interface SessionsTableProps {
  sessions: SessionPayment[];
}

export const SessionsTable = ({ sessions }: SessionsTableProps) => {
  const [activeTab, setActiveTab] = useState<"all" | "requested" | "completed">(
    "all"
  );
  const [searchQuery, setSearchQuery] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 10;
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "asc" | "desc" | null;
  }>({ key: "", direction: null });

  const sortData = (data: SessionPayment[]) => {
    if (!sortConfig.key || !sortConfig.direction) return data;

    return [...data].sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortConfig.key) {
        case "student":
          aValue = a.counsellor.name.toLowerCase();
          bValue = b.counsellor.name.toLowerCase();
          break;
        case "date":
          aValue = new Date(a.date).getTime();
          bValue = new Date(b.date).getTime();
          break;
        case "status":
          aValue = a.status;
          bValue = b.status;
          break;
        case "service":
          aValue = a.serviceName.toLowerCase();
          bValue = b.serviceName.toLowerCase();
          break;
        case "amount":
          aValue = a.amount;
          bValue = b.amount;
          break;
        default:
          return 0;
      }

      if (sortConfig.direction === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
  };

  const filteredSessions = sortData(
    sessions
      .filter((session) => {
        if (activeTab === "requested") return session.status === "pending";
        if (activeTab === "completed") return session.status === "completed";
        return true;
      })
      .filter(
        (session) =>
          session.counsellor.name
            .toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          session.serviceName.toLowerCase().includes(searchQuery.toLowerCase())
      )
  );

  const totalPages = Math.ceil(filteredSessions.length / rowsPerPage);
  const paginatedSessions = filteredSessions.slice(
    (currentPage - 1) * rowsPerPage,
    currentPage * rowsPerPage
  );

  const handleSort = (key: string) => {
    let direction: "asc" | "desc" | null = "asc";

    if (sortConfig.key === key) {
      if (sortConfig.direction === "asc") direction = "desc";
      else if (sortConfig.direction === "desc") direction = null;
    }

    setSortConfig({ key, direction });
  };

  return (
    <div className="rounded-2xl border bg-white">
      <div className="p-4 md:p-6 border-b">
        <div className="flex items-center justify-between gap-4 flex-wrap">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gray-100 rounded-lg">
              <DollarSign className="w-5 h-5" />
            </div>
            <h2 className="text-xl font-medium">Sessions Over Time</h2>
          </div>
          <div className="flex items-center gap-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2.5 border border-gray-200 rounded-xl w-[280px] text-sm focus:outline-none focus:ring-1 focus:ring-gray-200 bg-gray-50"
              />
            </div>
            <button className="p-2.5 text-sm font-medium text-gray-700 bg-gray-50 rounded-xl border border-gray-200 hover:bg-gray-100 transition-colors">
              <Filter className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="flex gap-8 mt-8">
          {["All", "Requested", "Completed"].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab.toLowerCase() as any)}
              className={`pb-3 relative ${
                activeTab === tab.toLowerCase()
                  ? "text-gray-900"
                  : "text-gray-500"
              }`}
            >
              {tab}
              {activeTab === tab.toLowerCase() && (
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gray-900" />
              )}
            </button>
          ))}
        </div>
      </div>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 hover:bg-gray-50">
              <TableHead className="pl-6 pr-4 py-3 w-[50px]">
                <input
                  type="checkbox"
                  className="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </TableHead>
              {[
                { key: "student", label: "Student Name" },
                { key: "date", label: "Date" },
                { key: "status", label: "Session Status" },
                { key: "service", label: "Service Name" },
                { key: "amount", label: "Amount" },
                { key: "action", label: "Action" },
              ].map(({ key, label }) => (
                <TableHead
                  key={key}
                  className="px-4 py-3 cursor-pointer"
                  onClick={() => key !== "action" && handleSort(key)}
                >
                  <div className="flex items-center gap-1 text-base font-medium text-gray-500">
                    {label}
                    {key !== "action" && (
                      <ChevronDown
                        className={`w-5 h-5 transition-transform ${
                          sortConfig.key === key &&
                          sortConfig.direction === "desc"
                            ? "transform rotate-180"
                            : ""
                        }`}
                      />
                    )}
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedSessions.map((session) => (
              <TableRow key={session.id} className="hover:bg-gray-50">
                <TableCell className="pl-6 pr-4 py-4">
                  <input
                    type="checkbox"
                    className="w-4 h-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </TableCell>
                <TableCell className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <Image
                        src={session.counsellor.avatar}
                        alt={session.counsellor.name}
                        width={40}
                        height={40}
                        className="rounded-full"
                      />
                      <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
                    </div>
                    <div>
                      <p className="text-base font-medium text-gray-900">
                        {session.counsellor.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {session.counsellor.email}
                      </p>
                    </div>
                  </div>
                </TableCell>
                <TableCell className="p-4 text-base text-gray-600">
                  {session.date}
                </TableCell>
                <TableCell className="p-4">
                  <span
                    className={`inline-flex items-center px-4 py-1.5 rounded-full text-base font-medium ${
                      session.status === "pending"
                        ? "bg-yellow-100 text-yellow-700"
                        : session.status === "completed"
                        ? "bg-green-100 text-green-700"
                        : "bg-red-100 text-red-700"
                    }`}
                  >
                    <span
                      className={`w-2 h-2 rounded-full mr-2 ${
                        session.status === "pending"
                          ? "bg-yellow-700"
                          : session.status === "completed"
                          ? "bg-green-700"
                          : "bg-red-700"
                      }`}
                    />
                    {session.status.charAt(0).toUpperCase() +
                      session.status.slice(1)}
                  </span>
                </TableCell>
                <TableCell className="p-4">
                  <span className="inline-flex items-center px-4 py-1.5 rounded-full text-base font-medium bg-purple-100 text-purple-700">
                    <span className="w-2 h-2 rounded-full mr-2 bg-purple-600" />
                    {session.serviceName}
                  </span>
                </TableCell>
                <TableCell className="p-4 text-lg font-medium text-gray-900">
                  ${session.amount}
                </TableCell>
                <TableCell className="p-4">
                  <button className="px-5 py-2.5 text-base font-medium text-gray-700 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors">
                    Dispute payment
                  </button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="p-4 border-t flex items-center justify-between">
        <button
          onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
          disabled={currentPage === 1}
          className="p-2 border rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft className="w-4 h-4" />
        </button>
        <div className="flex items-center gap-2">
          <span className="font-medium">{currentPage}</span>
          <span className="text-gray-500">of</span>
          <span className="font-medium">{totalPages}</span>
        </div>
        <button
          onClick={() =>
            setCurrentPage((prev) => Math.min(prev + 1, totalPages))
          }
          disabled={currentPage === totalPages}
          className="p-2 border rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>
    </div>
  );
};
