import { Skeleton } from "@components/ui/skeleton";
import {
  Building,
  Target,
  Calendar,
  Clock,
  Search,
  SlidersHorizontal,
} from "lucide-react";

export function BankInfoSkeleton() {
  return (
    <div className="rounded-2xl bg-white p-3 sm:p-6 space-y-5 sm:space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-gray-100 rounded-xl border">
            <Building className="w-5 h-5" />
          </div>
          <h2 className="text-xl font-medium">Account Information</h2>
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-10 w-24 rounded-xl" /> {/* Update button */}
          <Skeleton className="h-10 w-[110px] rounded-xl" />{" "}
          {/* Add Account button */}
        </div>
      </div>

      {/* Content Grid */}
      <div className="grid lg:grid-cols-2 grid-cols-1 gap-6">
        {/* Primary Bank Card */}
        <div className="space-y-4 p-4 bg-gray-50 rounded-xl border">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-[140px]" />{" "}
              {/* Primary Bank Info text */}
            </div>
            <div className="flex gap-2">
              <Skeleton className="h-8 w-8 rounded-lg" /> {/* Edit button */}
              <Skeleton className="h-8 w-8 rounded-lg" /> {/* Delete button */}
            </div>
          </div>

          <div className="space-y-3">
            <Skeleton className="h-6 w-[180px]" /> {/* Bank name */}
            <Skeleton className="h-5 w-[200px]" /> {/* Account number */}
          </div>
        </div>

        {/* Secondary Bank Card */}
        <div className="space-y-4 p-4 bg-gray-50 rounded-xl border">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-[160px]" />{" "}
              {/* Secondary Bank Info text */}
            </div>
            <div className="flex gap-2">
              <Skeleton className="h-8 w-8 rounded-lg" /> {/* Edit button */}
              <Skeleton className="h-8 w-8 rounded-lg" /> {/* Delete button */}
            </div>
          </div>

          <div className="space-y-3">
            <Skeleton className="h-6 w-[180px]" /> {/* Bank name */}
            <Skeleton className="h-5 w-[200px]" /> {/* Account number */}
          </div>
        </div>
      </div>
    </div>
  );
}

export function PaymentsTableSkeleton() {
  return (
    <div className="bg-white rounded-xl p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <Clock className="w-5 h-5" />
          <span className="font-medium">Payments Over Time</span>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Skeleton className="h-10 w-64 pl-10" />
          </div>
          <SlidersHorizontal className="w-5 h-5 text-gray-500" />
        </div>
      </div>

      {/* Tabs */}
      <div className="flex gap-4 mb-6">
        {["All", "Requested", "Completed"].map((tab) => (
          <div key={tab} className="pb-2 px-2">
            <span className="text-sm">{tab}</span>
          </div>
        ))}
      </div>

      {/* Table */}
      <div className="w-full">
        {/* Table Header */}
        <div className="grid grid-cols-6 gap-4 py-3 px-4 border-b">
          <span className="text-sm text-gray-500">Counsellor Name</span>
          <span className="text-sm text-gray-500">Date</span>
          <span className="text-sm text-gray-500">Session Status</span>
          <span className="text-sm text-gray-500">Service Name</span>
          <span className="text-sm text-gray-500">Amount</span>
          <span className="text-sm text-gray-500">Action</span>
        </div>

        {/* Table Rows */}
        {[1, 2, 3, 4, 5].map((row) => (
          <div key={row} className="grid grid-cols-6 gap-4 py-4 px-4 border-b">
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8 rounded-full" />
              <Skeleton className="h-4 w-24" />
            </div>
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-6 w-24 rounded-full" />
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-8 w-32" />
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-end gap-2 mt-4">
        <Skeleton className="h-8 w-8" /> {/* Previous */}
        <Skeleton className="h-8 w-8" /> {/* Page number */}
        <span>of</span>
        <Skeleton className="h-8 w-8" /> {/* Total pages */}
        <Skeleton className="h-8 w-8" /> {/* Next */}
      </div>
    </div>
  );
}

export function StatsCardsSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full">
      {/* Amount Card */}
      <div className="bg-white rounded-xl p-4 flex flex-col gap-1">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-gray-100 rounded-full">
            <Target className="w-4 h-4 text-gray-500" />
          </div>
          <Skeleton className="h-7 w-24" />
        </div>
        <span className="text-sm text-gray-500">Total Amount Spent</span>
      </div>

      {/* Sessions Card */}
      <div className="bg-white rounded-xl p-4 flex flex-col gap-1">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-gray-100 rounded-full">
            <Calendar className="w-4 h-4 text-gray-500" />
          </div>
          <Skeleton className="h-7 w-16" />
        </div>
        <span className="text-sm text-gray-500">Total Sessions</span>
      </div>

      {/* Hours Card */}
      <div className="bg-white rounded-xl p-4 flex flex-col gap-1">
        <div className="flex items-center gap-2">
          <div className="p-2 bg-gray-100 rounded-full">
            <Clock className="w-4 h-4 text-gray-500" />
          </div>
          <Skeleton className="h-7 w-20" />
        </div>
        <span className="text-sm text-gray-500">Total Hours Spent</span>
      </div>
    </div>
  );
}
