import { DollarSign, Clock, Video } from "lucide-react";
import { PaymentStats } from "@constants/dummy-student-data";

interface StatsCardsProps {
  stats: PaymentStats;
}

export const StatsCards = ({ stats }: StatsCardsProps) => {
  const cards = [
    {
      icon: <DollarSign className="w-6 h-6 text-gray-800" />,
      value: `$${stats.totalAmount}`,
      label: "Total Amount Spent",
      bgColor: "bg-gray-100",
    },
    {
      icon: <Video className="w-6 h-6 text-gray-800" />,
      value: stats.totalSessions,
      label: "Total Sessions",
      bgColor: "bg-gray-100",
    },
    {
      icon: <Clock className="w-6 h-6 text-gray-800" />,
      value: `${stats.totalHours} hrs`,
      label: "Total Hours Spent",
      bgColor: "bg-gray-100",
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {cards.map((card, index) => (
        <div
          key={index}
          className="flex items-center gap-4 p-4 md:p-6 rounded-2xl border bg-white hover:shadow-sm transition-all duration-200"
        >
          <div className={`p-3 ${card.bgColor} rounded-xl`}>{card.icon}</div>
          <div>
            <h3 className="text-2xl md:text-3xl font-medium">{card.value}</h3>
            <p className="text-sm md:text-base text-gray-600">{card.label}</p>
          </div>
        </div>
      ))}
    </div>
  );
};
