"use client";

import { useEffect, useState } from "react";

import apiClient from "@/lib/apiClient";
import Dropdown from "@components/common/dropdown";
import { NavigationButtons } from "@components/common/navigationBtns";
import useFormState from "@hooks/useFormState";
import { toast } from "react-toastify";
import LoadingSpinner from "@components/common/loadingSpinner";
import { ProfileCreateProps } from "@/app/types/counselor/profile";
import { InputField } from "@/app/components/common/inputField";
import { MainButton } from "@/app/components/common/mainBtn";

const hoursPerWeekOptions = [
  { label: "Please select", value: "" },
  { label: "1-5 hours", value: "1-5" },
  { label: "6-10 hours", value: "6-10" },
  { label: "11-20 hours", value: "11-20" },
  { label: "21-30 hours", value: "21-30" },
  { label: "31-40 hours", value: "31-40" },
  { label: "40+ hours", value: "40+" },
];

const pricePerHourOptions = [
  { label: "Please select", value: "" },
  { label: "30", value: 30 },
  { label: "40", value: 40 },
  { label: "50", value: 50 },
  { label: "60", value: 60 },
];

interface CommitmentData {
  hours_per_week: string;
  hourly_rate: number;
  counselor_id: number;
}

const initialState: CommitmentData = {
  hours_per_week: "",
  hourly_rate: 0,
  counselor_id: 0,
};

export default function CommitmentPricing({
  selectedMenuIndex = 0, // Default to 0
  onMenuSelect = () => {}, // Default to a no-op function
  onSectionComplete = () => {},
  isCompleted = false,
  edit = false,
}: ProfileCreateProps & {
  onSectionComplete?: () => void;
  isCompleted?: boolean;
}) {
  const [formData, setFormData] = useState<CommitmentData>(initialState);
  const [isLoading, setIsLoading] = useState(true);
  const [isDirty, setIsDirty] = useState(false);

  const apiURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/counselor/profile/commitment`;

  useEffect(() => {
    const fetchCommitmentData = async () => {
      setIsLoading(true);

      try {
        const { data } = await apiClient.get(apiURL);

        setFormData({
          hours_per_week: data.hours_per_week || "",
          hourly_rate: data.hourly_rate || 0,
          counselor_id: data.counselor_id || 0,
        });
      } catch (error: any) {
        console.log(error.response.data.detail);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCommitmentData();
  }, []);

  const handleFieldChange = (
    field: keyof CommitmentData,
    value: string | number
  ) => {
    setIsDirty(true);
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateForm = () => {
    if (!formData.hours_per_week || !formData.hourly_rate) {
      toast.error("Please fill in all required fields");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // If form hasn't changed and section is completed, just navigate
    if (!isDirty && isCompleted && !edit) {
      onMenuSelect(selectedMenuIndex + 1);
      return;
    }

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    const payload = {
      hours_per_week: formData.hours_per_week,
      hourly_rate: formData.hourly_rate,
    };

    try {
      // Check for counselor_id to determine POST or PUT
      if (formData.counselor_id) {
        await apiClient.put(apiURL, payload);
      } else {
        await apiClient.post(apiURL, payload);
      }

      toast.success("Commitment details saved successfully!");
      setIsDirty(false);
      if (!edit) {
        onSectionComplete();
        onMenuSelect(selectedMenuIndex + 1);
      }
    } catch (error: any) {
      toast.error(error.response?.data?.detail || "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative">
      {isLoading ? (
        <LoadingSpinner />
      ) : (
        <form className="mt-12" onSubmit={handleSubmit}>
          <div className="flex items-center justify-between mb-6">
            <h3 className="font-black">Commitment & Pricing</h3>
            {!edit && (
              <MainButton
                variant="secondary"
                type="button"
                onClick={() => onMenuSelect(selectedMenuIndex + 1)}
              >
                Skip this step →
              </MainButton>
            )}
          </div>

          <div className="mt-4">
            <Dropdown
              label="How many hours can you commit per week?"
              name="hours_per_week"
              options={hoursPerWeekOptions}
              value={formData.hours_per_week}
              onChange={(e) =>
                handleFieldChange("hours_per_week", e.target.value)
              }
              selectStyle="w-full border p-3"
            />
          </div>

          <div className="mt-4">
            <InputField
              label="Expected price per hour ($)"
              type="number"
              placeholder="Enter your hourly rate"
              name="hourly_rate"
              value={formData.hourly_rate === 0 ? "" : formData.hourly_rate}
              onChange={(e) =>
                handleFieldChange("hourly_rate", e.target.valueAsNumber)
              }
              min={1}
              step={1}
            />
            <p className="text-sm text-gray-500 mt-1">
              Enter a whole number greater than 0
            </p>
          </div>

          <NavigationButtons
            currentIndex={selectedMenuIndex || 0}
            onBack={() =>
              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex - 1 : 0)
            }
            onNext={() =>
              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex + 1 : 1)
            }
            disableBack={!selectedMenuIndex || selectedMenuIndex < 1}
            nextLabel={edit ? "Save Changes" : "Next"}
          />
        </form>
      )}
    </div>
  );
}
