"use client";

import { useEffect, useState } from "react";
import apiClient from "@/lib/apiClient";
import { NavigationButtons } from "@components/common/navigationBtns";
import useFormState from "@hooks/useFormState";
import { toast } from "react-toastify";
import LoadingSpinner from "@components/common/loadingSpinner";
import { ProfileCreateProps } from "@/app/types/counselor/profile";
import TextAreaLimit from "@/app/components/common/TeaxAreaLimit";
import { MainButton } from "@/app/components/common/mainBtn";
import { RadioGroup } from "@headlessui/react";

const initialState = {
  mentor: "",
  expDesc: "",
  counselorId: 0,
};

function CheckIcon(props: any) {
  return (
    <svg viewBox="0 0 24 24" fill="none" {...props}>
      <circle cx={12} cy={12} r={12} fill="#fff" opacity="0.2" />
      <path
        d="M7 13l3 3 7-7"
        stroke="currentColor"
        strokeWidth={1.5}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

export default function CounsellingExperience({
  selectedMenuIndex = 0,
  onMenuSelect = () => {},
  onSectionComplete = () => {},
  isCompleted = false,
  edit = false,
}: ProfileCreateProps & {
  onSectionComplete?: () => void;
  isCompleted?: boolean;
}) {
  const { formData, setFormData, handleChange } = useFormState(initialState);
  const [isLoading, setIsLoading] = useState(true);
  const [isDirty, setIsDirty] = useState(false);

  const apiURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/counselor/profile/counseling-experience`;

  useEffect(() => {
    const fetchCounselingExperience = async () => {
      setIsLoading(true);

      try {
        const response = await apiClient.get(apiURL);
        const data = response.data;

        if (data) {
          setFormData({
            mentor: data.has_mentored_before ? "yes" : "no",
            expDesc: data.experience_description || "",
            counselorId: data.counselor_id,
          });
        }
      } catch (error: any) {
        // If no data exists, keep the empty initial state
        console.log(error?.response?.data?.detail);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCounselingExperience();
  }, [setFormData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // If form hasn't changed and section is completed, just navigate
    if (!isDirty && isCompleted && !edit) {
      onMenuSelect(selectedMenuIndex + 1);
      return;
    }

    // Validate that an option is selected
    if (!formData.mentor) {
      toast.error("Please select whether you have mentoring experience");
      return;
    }

    // Validate description if "Yes" is selected
    if (formData.mentor === "yes" && !formData.expDesc.trim()) {
      toast.error("Please describe your mentoring experience");
      return;
    }

    setIsLoading(true);

    const payload = {
      has_mentored_before: formData.mentor === "yes",
      experience_description: formData.mentor === "yes" ? formData.expDesc : "",
    };

    try {
      if (formData.counselorId) {
        await apiClient.put(apiURL, payload);
      } else {
        await apiClient.post(apiURL, payload);
      }

      toast.success("Counselling experience saved successfully!");
      setIsDirty(false);
      if (!edit) {
        onSectionComplete();
        onMenuSelect(selectedMenuIndex + 1);
      }
    } catch (error) {
      toast.error("Failed to save counseling experience.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleMentorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleChange(e);
    setIsDirty(true);
    if (e.target.value === "no") {
      setFormData((prev) => ({ ...prev, expDesc: "" }));
    }
  };

  const handleExpDescChange = (value: string) => {
    setFormData((prev) => ({ ...prev, expDesc: value }));
    setIsDirty(true);
  };

  return (
    <div className="relative">
      {isLoading ? (
        <LoadingSpinner />
      ) : (
        <form className="mt-12" onSubmit={handleSubmit}>
          <div className="flex items-center justify-between mb-6">
            <h3 className="font-black">Counselling Experience</h3>
            {!edit && (
              <MainButton
                variant="secondary"
                type="button"
                onClick={() => onMenuSelect(selectedMenuIndex + 1)}
              >
                Skip this step →
              </MainButton>
            )}
          </div>

          <div className="mt-4">
            <label className="block font-medium mb-2 text-sm">
              Have you mentored students for college applications before?
            </label>
            <RadioGroup
              value={formData.mentor}
              onChange={(value) => {
                handleMentorChange({
                  target: { name: "mentor", value },
                } as React.ChangeEvent<HTMLInputElement>);
              }}
              className="mt-2"
            >
              <div className="flex gap-4">
                <RadioGroup.Option
                  value="yes"
                  className={({ checked }) =>
                    `relative flex cursor-pointer rounded-lg px-5 py-3 border ${
                      checked
                        ? "bg-blue-50 border-blue-500"
                        : "border-gray-300 hover:bg-gray-50"
                    }`
                  }
                >
                  {({ checked }) => (
                    <div className="flex w-full items-center justify-between">
                      <div className="flex items-center">
                        <div className="text-sm">
                          <RadioGroup.Label
                            as="p"
                            className={`font-medium ${checked ? "text-blue-900" : "text-gray-900"}`}
                          >
                            Yes
                          </RadioGroup.Label>
                        </div>
                      </div>
                      {checked && (
                        <div className="shrink-0 text-blue-500">
                          <CheckIcon className="h-6 w-6" />
                        </div>
                      )}
                    </div>
                  )}
                </RadioGroup.Option>

                <RadioGroup.Option
                  value="no"
                  className={({ checked }) =>
                    `relative flex cursor-pointer rounded-lg px-5 py-3 border ${
                      checked
                        ? "bg-blue-50 border-blue-500"
                        : "border-gray-300 hover:bg-gray-50"
                    }`
                  }
                >
                  {({ checked }) => (
                    <div className="flex w-full items-center justify-between">
                      <div className="flex items-center">
                        <div className="text-sm">
                          <RadioGroup.Label
                            as="p"
                            className={`font-medium ${checked ? "text-blue-900" : "text-gray-900"}`}
                          >
                            No
                          </RadioGroup.Label>
                        </div>
                      </div>
                      {checked && (
                        <div className="shrink-0 text-blue-500">
                          <CheckIcon className="h-6 w-6" />
                        </div>
                      )}
                    </div>
                  )}
                </RadioGroup.Option>
              </div>
            </RadioGroup>
          </div>

          {formData.mentor === "yes" && (
            <div className="mt-4">
              <TextAreaLimit
                label="Briefly describe your experience"
                name="expDesc"
                placeholder="Share your experience with college applications mentoring"
                value={formData.expDesc}
                rows={8}
                onChange={handleExpDescChange}
                maxLength={400}
                required
              />
            </div>
          )}

          <NavigationButtons
            currentIndex={selectedMenuIndex || 0}
            onBack={() =>
              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex - 1 : 0)
            }
            onNext={() =>
              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex + 1 : 1)
            }
            disableBack={!selectedMenuIndex || selectedMenuIndex < 1}
            nextLabel={edit ? "Save Changes" : "Next"}
          />
        </form>
      )}
    </div>
  );
}
