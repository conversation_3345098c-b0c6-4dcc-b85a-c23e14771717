"use client";

import { useState, useEffect, useMemo } from "react";
import { Dialog } from "@headlessui/react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";
import apiClient from "@/lib/apiClient";
import { InputField } from "@components/common/inputField";
import { NavigationButtons } from "@components/common/navigationBtns";
import { toast } from "react-toastify";
import { formatNormalDate, formatDateISO } from "@/app/utils/time-helpers";
import LoadingSpinner from "@components/common/loadingSpinner";
import TextAreaLimit from "@components/common/TeaxAreaLimit";
import { ProfileCreateProps } from "@/app/types/counselor/profile";
import { MainButton } from "@/app/components/common/mainBtn";
import Dropdown from "@components/common/dropdown";

interface EducationEntry {
  id?: string;
  university_name: string;
  degree: string;
  major: string;
  start_date: string;
  end_date: string;
  grade: string;
  accomplishments: string;
  education_level: string;
  specific_degree: string;
}

interface University {
  name: string;
  country: string;
}

const searchUniversities = async (query: string): Promise<University[]> => {
  if (!query || query.length < 2) return [];

  try {
    const response = await fetch(
      `http://universities.hipolabs.com/search?name=${encodeURIComponent(
        query
      )}`
    );
    const data = await response.json();
    return data.map((uni: any) => ({
      name: uni.name,
      country: uni.country,
    }));
  } catch (error) {
    console.error("Error fetching universities:", error);
    return [];
  }
};

const initialEntry: EducationEntry = {
  university_name: "",
  degree: "",
  major: "",
  start_date: "",
  end_date: "",
  grade: "",
  accomplishments: "",
  education_level: "",
  specific_degree: "",
};

type EducationLevelOption = {
  value: string;
  label: string;
};

export default function EducationalInfo({
  selectedMenuIndex = 0,
  onMenuSelect = () => {},
  onSectionComplete = () => {},
  isCompleted = false,
  edit = false,
}: ProfileCreateProps & {
  onSectionComplete?: () => void;
  isCompleted?: boolean;
}) {
  const [educationEntries, setEducationEntries] = useState<EducationEntry[]>([
    initialEntry,
  ]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDirty, setIsDirty] = useState(false);

  const apiURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/counselor/profile/education`;

  const educationLevels: EducationLevelOption[] = [
    { value: "", label: "Select Education Level" },
    { value: "high_school", label: "High School" },
    { value: "associate", label: "Associate Degree" },
    { value: "bachelors", label: "Bachelor's Degree" },
    { value: "masters", label: "Master's Degree" },
    { value: "doctorate", label: "Doctorate/PhD" },
    { value: "certification", label: "Professional Certification" },
    { value: "other", label: "Other" },
  ];

  useEffect(() => {
    const fetchEducationData = async () => {
      setIsLoading(true);

      try {
        const response = await apiClient.get(apiURL);

        if (response.status === 200 && response.data) {
          const formattedData = response.data.map((entry: EducationEntry) => ({
            ...entry,
            start_date: formatNormalDate(entry.start_date),
            end_date: formatNormalDate(entry.end_date),
            education_level: entry.degree,
            specific_degree: !educationLevels.find(
              (level) => level.value === entry.degree
            )
              ? entry.degree
              : "",
          }));
          setEducationEntries(
            formattedData.length > 0 ? formattedData : [initialEntry]
          );
        }
      } catch (error) {
        console.error("Error fetching education data:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchEducationData();
  }, []);

  const handleFieldChange = (
    index: number,
    fieldName: keyof EducationEntry,
    value: string
  ) => {
    setIsDirty(true);
    setEducationEntries((prevEntries) => {
      return prevEntries.map((entry, i) => {
        if (i !== index) return entry;

        const updatedEntry = { ...entry };

        if (fieldName === "education_level") {
          updatedEntry.education_level = value;
          updatedEntry.degree =
            value === "other" ? entry.specific_degree : value;
          updatedEntry.specific_degree =
            value === "other" ? entry.specific_degree : "";
        } else if (fieldName === "specific_degree") {
          updatedEntry.specific_degree = value;
          updatedEntry.degree =
            entry.education_level === "other" ? value : entry.degree;
        } else {
          updatedEntry[fieldName] = value;
        }

        return updatedEntry;
      });
    });
  };

  const handleAddMoreEducation = () => {
    setIsDirty(true);
    const allValid = educationEntries.every((entry) => validateEntry(entry));

    if (allValid) {
      setEducationEntries((prevEntries) => [
        ...prevEntries,
        { ...initialEntry },
      ]);
    } else {
      toast.error(
        "Please fill in all the required fields in previous entries before adding more."
      );
    }
  };

  const handleRemoveEducation = async (index: number) => {
    setIsDirty(true);
    const experienceToRemove = educationEntries[index];

    if (!experienceToRemove.id) {
      setEducationEntries((prevEntries) =>
        prevEntries.filter((_, i) => i !== index)
      );
      return;
    }

    try {
      setIsLoading(true);
      const deleteUrl = `${apiURL}/${experienceToRemove.id}`;
      const response = await apiClient.delete(deleteUrl);

      if (response.status === 200) {
        toast.success("Education removed successfully.");
        setEducationEntries((prevEntries) =>
          prevEntries.filter((_, i) => i !== index)
        );
      } else {
        throw new Error(
          response.data?.message || "Failed to delete experience."
        );
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Something went wrong while removing experience.";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const validateEntry = (entry: EducationEntry): boolean => {
    return Boolean(
      entry.university_name &&
        entry.education_level &&
        entry.major &&
        entry.start_date &&
        entry.end_date
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // If form hasn't changed and section is completed, just navigate
    if (!isDirty && isCompleted && !edit) {
      onMenuSelect(selectedMenuIndex + 1);
      return;
    }

    setIsLoading(true);

    try {
      for (const entry of educationEntries) {
        if (!validateEntry(entry)) {
          throw new Error(
            "All fields (institution name, education level, major, start date, and end date) are required."
          );
        }

        if (new Date(entry.start_date) > new Date(entry.end_date)) {
          throw new Error("Start date cannot be after end date.");
        }

        const payload = {
          ...entry,
          start_date: formatDateISO(entry.start_date),
          end_date: formatDateISO(entry.end_date),
          degree:
            entry.education_level === "other"
              ? entry.specific_degree
              : entry.education_level,
        };

        const { education_level, specific_degree, ...finalPayload } = payload;

        const url = entry.id ? `${apiURL}/${entry.id}` : apiURL;
        const method = entry.id ? "put" : "post";
        const response = await apiClient[method](url, finalPayload);

        if (response.status !== 200) {
          throw new Error(response.data?.message || "Something went wrong");
        }
      }

      setIsDirty(false);
      if (!edit) {
        onSectionComplete();
        onMenuSelect(selectedMenuIndex + 1);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to submit form";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const [isInstitutionModalOpen, setIsInstitutionModalOpen] = useState(false);
  const [institutionSearchTerm, setInstitutionSearchTerm] = useState("");
  const [selectedEntryIndex, setSelectedEntryIndex] = useState<number | null>(
    null
  );
  const [searchResults, setSearchResults] = useState<University[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const handleOpenInstitutionModal = (index: number) => {
    setSelectedEntryIndex(index);
    setInstitutionSearchTerm("");
    setIsInstitutionModalOpen(true);
  };

  const handleInstitutionSelect = (institutionName: string) => {
    if (selectedEntryIndex !== null) {
      handleFieldChange(selectedEntryIndex, "university_name", institutionName);
      setIsInstitutionModalOpen(false);
    }
  };

  useEffect(() => {
    const delayDebounce = setTimeout(async () => {
      if (institutionSearchTerm.length >= 2) {
        setIsSearching(true);
        const results = await searchUniversities(institutionSearchTerm);
        setSearchResults(results);
        setIsSearching(false);
      } else {
        setSearchResults([]);
      }
    }, 300);

    return () => clearTimeout(delayDebounce);
  }, [institutionSearchTerm]);

  return (
    <div className="relative">
      {isLoading ? (
        <LoadingSpinner />
      ) : (
        <form onSubmit={handleSubmit}>
          {educationEntries.map((entry, index) => (
            <div key={index} className="mb-4 rounded-md relative">
              <h3 className="font-black mb-4 mt-12">
                {index === 0 ? "Educational Background" : "Add More Education"}
              </h3>

              <div className="mt-4">
                <label className="block mb-2 text-sm font-medium text-gray-900">
                  Education Level<span className="text-red-500">*</span>
                </label>
                <Select
                  onValueChange={(value) =>
                    handleFieldChange(index, "education_level", value)
                  }
                  value={entry.education_level}
                >
                  <SelectTrigger className="h-[42px] shadow-none bg-white">
                    <SelectValue placeholder="Select Education Level" />
                  </SelectTrigger>
                  <SelectContent className="bg-white">
                    {educationLevels.map(
                      (level) =>
                        level.value && (
                          <SelectItem
                            key={level.value}
                            className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                            value={level.value}
                          >
                            {level.label}
                          </SelectItem>
                        )
                    )}
                  </SelectContent>
                </Select>
              </div>

              {entry.education_level === "other" && (
                <div className="mt-4">
                  <InputField
                    label="Specify Degree/Certification"
                    type="text"
                    placeholder="Enter your specific degree or certification"
                    name="specific_degree"
                    required={true}
                    value={entry.specific_degree}
                    onChange={(e) =>
                      handleFieldChange(
                        index,
                        "specific_degree",
                        e.target.value
                      )
                    }
                  />
                </div>
              )}

              <div className="mt-4">
                <label className="block mb-2 text-sm font-medium text-gray-900">
                  Institution Name<span className="text-red-500">*</span>
                </label>
                <button
                  type="button"
                  onClick={() => handleOpenInstitutionModal(index)}
                  className="w-full p-3 text-left border rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white"
                >
                  {entry.university_name || "Select Institution"}
                </button>
              </div>

              <div className="grid lg:grid-cols-2 gap-4 mt-4">
                <InputField
                  label="Major/Field of Study"
                  type="text"
                  placeholder="Your field of study"
                  name="major"
                  required={true}
                  value={entry.major}
                  onChange={(e) =>
                    handleFieldChange(index, "major", e.target.value)
                  }
                />
                <InputField
                  label="GPA/Grade"
                  type="text"
                  placeholder="e.g. 3.7/4.0"
                  name="grade"
                  value={entry.grade}
                  onChange={(e) =>
                    handleFieldChange(index, "grade", e.target.value)
                  }
                />
              </div>

              <div className="grid lg:grid-cols-2 gap-4 mt-4">
                <InputField
                  label="Start Date"
                  type="date"
                  name="start_date"
                  required={true}
                  value={entry.start_date}
                  onChange={(e) =>
                    handleFieldChange(index, "start_date", e.target.value)
                  }
                />
                <InputField
                  label="End Date"
                  type="date"
                  name="end_date"
                  required={true}
                  value={entry.end_date}
                  onChange={(e) =>
                    handleFieldChange(index, "end_date", e.target.value)
                  }
                />
              </div>

              <div className="mt-4">
                <TextAreaLimit
                  label="Accomplishments (Optional)"
                  name={`accomplishments-${index}`}
                  placeholder="Share your relevant accomplishments, awards, or notable achievements"
                  value={entry.accomplishments}
                  onChange={(value) =>
                    handleFieldChange(index, "accomplishments", value)
                  }
                />
              </div>

              {index > 0 && (
                <MainButton
                  variant="secondary"
                  type="button"
                  onClick={() => handleRemoveEducation(index)}
                >
                  Remove Education
                </MainButton>
              )}
            </div>
          ))}

          <MainButton
            variant="primary"
            type="button"
            onClick={handleAddMoreEducation}
          >
            Add More Education +
          </MainButton>

          <NavigationButtons
            currentIndex={selectedMenuIndex}
            onBack={() =>
              onMenuSelect(selectedMenuIndex ? selectedMenuIndex - 1 : 0)
            }
            onNext={() =>
              onMenuSelect(selectedMenuIndex ? selectedMenuIndex + 1 : 1)
            }
            disableBack={!selectedMenuIndex || selectedMenuIndex < 1}
            nextLabel={edit ? "Save Changes" : "Next"}
          />
        </form>
      )}

      {/* Institution Selection Modal */}
      <Dialog
        open={isInstitutionModalOpen}
        onClose={() => setIsInstitutionModalOpen(false)}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
            <Dialog.Title className="text-lg font-medium leading-6 text-gray-900 mb-4">
              Select Institution
            </Dialog.Title>

            {/* Search Input */}
            <div className="mb-4">
              <input
                type="text"
                placeholder="Search institutions or enter new one..."
                value={institutionSearchTerm}
                onChange={(e) => setInstitutionSearchTerm(e.target.value)}
                className="w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500"
                autoFocus
              />
            </div>

            {/* Institutions List */}
            <div className="mt-2 max-h-[60vh] overflow-y-auto">
              {isSearching ? (
                <div className="text-center py-4 text-gray-500">
                  Searching...
                </div>
              ) : institutionSearchTerm.length < 2 ? (
                <div className="text-center py-4 text-gray-500">
                  Type at least 2 characters to search
                </div>
              ) : searchResults.length > 0 ? (
                searchResults.map((institution) => (
                  <button
                    key={`${institution.name}-${institution.country}`}
                    type="button"
                    className="w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 rounded-lg"
                    onClick={() => handleInstitutionSelect(institution.name)}
                  >
                    <div>{institution.name}</div>
                    <div className="text-sm text-gray-500">
                      {institution.country}
                    </div>
                  </button>
                ))
              ) : (
                <button
                  type="button"
                  className="w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 rounded-lg text-blue-600"
                  onClick={() => handleInstitutionSelect(institutionSearchTerm)}
                >
                  Add "{institutionSearchTerm}" as new institution
                </button>
              )}
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>
    </div>
  );
}
