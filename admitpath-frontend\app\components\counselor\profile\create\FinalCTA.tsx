"use client";

import Image from "next/image";
import { useState } from "react";
import apiClient from "@/lib/apiClient";
import { toast } from "react-toastify";
import LoadingSpinner from "@components/common/loadingSpinner";
import { usePathname, useRouter } from "next/navigation";

const CompletionMessage = () => (
  <div className="min-h-[80vh] flex items-center justify-center p-4 sm:p-6 md:p-8 bg-gray-50">
    <div className="max-w-2xl w-full bg-white rounded-2xl shadow-lg overflow-hidden transform transition-all animate-fadeIn">
      {/* Top Section with Gradient */}
      <div className="relative bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-12 text-center">
        {/* Decorative top border */}
        <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#2F80ED] to-blue-400" />
        
        {/* Success Icon and Title */}
        <div className="mb-8">
          <div className="relative w-28 h-28 mx-auto mb-8 transform transition-all duration-500 hover:scale-105">
            <Image
              src="/images/011---Approved-Cleaning.png"
              alt="Success"
              fill
              className="object-contain drop-shadow-md"
              priority
            />
          </div>
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-3 tracking-tight">
            Your profile has been{" "}
            <span className="text-[#2F80ED] relative">
              submitted
              <span className="absolute bottom-0 left-0 w-full h-1 bg-[#2F80ED]/10"></span>
            </span>
          </h2>
          <p className="text-gray-600 text-lg sm:text-xl font-medium">
            You have successfully completed the application form
          </p>
        </div>
      </div>

      {/* Content Section */}
      <div className="px-6 py-10 sm:px-12 sm:py-12">
        <div className="space-y-8 text-center sm:text-left max-w-xl mx-auto">
          <h4 className="text-2xl font-semibold text-gray-900">
            Thank you for applying to become a counselor
          </h4>
          <div className="prose prose-lg prose-blue max-w-none">
            <p className="text-gray-600 leading-relaxed">
              Our team will review your application and get back to you within{" "}
              <span className="font-semibold text-gray-900">1-3 business days</span>.
              {" "}If approved, you'll gain access to your dashboard to start mentoring
              students.
            </p>
            <p className="text-gray-600 mt-4">
              Please feel free to reach out in the meantime if you have any questions.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default function FinalCTA({
  selectedMenuIndex = 0,
  onMenuSelect = () => {},
}: {
  selectedMenuIndex?: number;
  onMenuSelect?: (index: number) => void;
}) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const pathname = usePathname();
  const router = useRouter();

  // If we're on the profile-complete page, show completion message directly
  if (pathname === "/counselor/profile-complete") {
    return <CompletionMessage />;
  }

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await apiClient.post("/counselor/profile/submit");
      toast.success("Profile submitted successfully!");
      // Immediately redirect to profile-complete page
      router.push("/counselor/profile-complete");
    } catch (error) {
      toast.error("Failed to submit profile. Please try again.");
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex-1 flex items-center justify-center min-h-[80vh] p-4 sm:p-6 md:p-8 bg-gray-50">
      <div className="w-full max-w-3xl mx-auto bg-white rounded-2xl shadow-lg overflow-hidden">
        {/* Top Section with Gradient */}
        <div className="relative bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-10">
          {/* Decorative top border */}
          <div className="absolute top-0 left-0 w-full h-1.5 bg-gradient-to-r from-[#2F80ED] to-blue-400" />
          
          <div className="text-center max-w-2xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Ready to Submit Your Profile?
            </h2>
            <p className="text-gray-600 text-lg font-medium">
              Once you submit your profile, our team will process your application and let you know the next steps within 1-3 business days.
            </p>
          </div>
        </div>

        {/* Checklist Section */}
        <div className="px-6 sm:px-8 py-10 space-y-8">
          <div className="bg-blue-50 border border-blue-100 p-6 sm:p-8 rounded-xl mx-auto">
            <h3 className="font-semibold text-blue-900 mb-6 text-xl text-center">Final Checklist</h3>
            <ul className="space-y-5 max-w-2xl mx-auto">
              <li className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-6 h-6 mt-1">
                  <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <span className="text-gray-700 text-lg">Your personal information is accurate and complete</span>
              </li>
              <li className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-6 h-6 mt-1">
                  <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <span className="text-gray-700 text-lg">Your educational background details are verified</span>
              </li>
              <li className="flex items-start space-x-4">
                <div className="flex-shrink-0 w-6 h-6 mt-1">
                  <svg className="w-6 h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <span className="text-gray-700 text-lg">You've reviewed all sections for accuracy</span>
              </li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 max-w-2xl mx-auto">
            <button
              onClick={() => onMenuSelect(selectedMenuIndex - 1)}
              className="flex-1 px-6 py-4 border-2 border-gray-200 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors duration-200 text-lg"
            >
              Review Previous Section
            </button>
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="flex-1 bg-[#2F80ED] px-6 py-4 text-white rounded-xl font-medium transition-all duration-200 relative overflow-hidden group hover:shadow-lg disabled:opacity-70 disabled:cursor-not-allowed text-lg"
            >
              <div className="absolute inset-0 bg-black/10 transform -translate-x-full group-hover:translate-x-0 transition-transform duration-200" />
              <div className="relative flex items-center justify-center space-x-3">
                {isSubmitting ? (
                  <>
                    <LoadingSpinner />
                    <span>Submitting...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>Submit Profile</span>
                  </>
                )}
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
