"use client";

import { useState, useEffect } from "react";

import apiClient from "@/lib/apiClient";
import LoadingSpinner from "@components/common/loadingSpinner";
import { InputField } from "@components/common/inputField";
import { countries } from "@/lib/countries";
import { NavigationButtons } from "@components/common/navigationBtns";
import useFormState from "@hooks/useFormState";
import { toast } from "react-toastify";
import { formatNormalDate, formatDateISO } from "@/app/utils/time-helpers";
import { ProfileCreateProps } from "@/app/types/counselor/profile";
import { useProfile } from "@/app/hooks/counselor/useProfile";
import { Dialog } from "@headlessui/react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

export default function PersonalInfo({
  selectedMenuIndex = 0, // Default to 0
  onMenuSelect = () => {}, // Default to a no-op function
  onSectionComplete = () => {},
  isCompleted = false,
  edit = false,
}: ProfileCreateProps & {
  onSectionComplete?: () => void;
  isCompleted?: boolean;
}) {
  const { getUserInfo, userInfo } = useProfile();

  const [isLoading, setIsLoading] = useState(true);
  const [personalInfoId, setPersonalInfoId] = useState<string | null>(null);
  const [isDirty, setIsDirty] = useState(false);
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);

  const { formData, setFormData } = useFormState({
    firstName: "",
    lastName: "",
    nationality: "",
    countryOfResidence: "",
    gender: "",
    dateOfBirth: "",
    bio: "",
    tagline: "",
  });

  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string>("");

  const apiURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/counselor/profile/personal-info`;

  useEffect(() => {
    getUserInfo();
  }, []);

  // Fetch existing personal info
  useEffect(() => {
    const fetchPersonalInfo = async () => {
      setIsLoading(true);

      try {
        const response = await apiClient.get(apiURL);

        if (response.status === 200) {
          const data = response.data;

          setFormData({
            firstName: formData.firstName || "",
            lastName: formData.lastName || "",
            nationality: data.nationality || "",
            countryOfResidence: data.country_of_residence || "",
            gender: data.gender || "",
            dateOfBirth: formatNormalDate(data.date_of_birth) || "",
            bio: data.bio || "",
            tagline: data.tagline || "",
          });
          setPersonalInfoId(data.id);
          if (data.profile_image_url) {
            setPreviewUrl(data.profile_image_url);
          }
        }
      } catch (error: any) {
        console.log(error?.response?.data?.detail);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPersonalInfo();
  }, [setFormData]);

  // Handle image selection
  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setIsDirty(true);
      setProfileImage(file);
      // Create a preview URL
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  // Update handleChange to track form changes
  const handleFormChange = (name: string, value: string) => {
    setIsDirty(true);
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Validation function
  const validateForm = () => {
    if (
      !formData.nationality ||
      !formData.countryOfResidence ||
      !formData.gender ||
      !formData.dateOfBirth
    ) {
      toast.error("All fields are required.");
      return false;
    }

    // Check if the date of birth is greater than today
    const enteredDate = new Date(formData.dateOfBirth);
    const today = new Date();

    if (enteredDate > today) {
      toast.error("Date of birth cannot be in the future.");
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setHasAttemptedSubmit(true);

    if (!validateForm()) return;

    // If form hasn't changed and section is completed, just navigate
    if (!isDirty && isCompleted && !edit) {
      onMenuSelect(selectedMenuIndex + 1);
      return;
    }

    setIsLoading(true);

    try {
      // Create FormData object
      const formDataToSend = new FormData();
      formDataToSend.append("nationality", formData.nationality);
      formDataToSend.append(
        "country_of_residence",
        formData.countryOfResidence
      );
      formDataToSend.append("gender", formData.gender);
      formDataToSend.append(
        "date_of_birth",
        formatDateISO(formData.dateOfBirth)
      );
      if (formData.bio) formDataToSend.append("bio", formData.bio);
      if (formData.tagline) formDataToSend.append("tagline", formData.tagline);
      if (profileImage) formDataToSend.append("profile_image", profileImage);

      let response;
      if (personalInfoId) {
        // Update existing record
        response = await apiClient.put(
          `${apiURL}/${personalInfoId}`,
          formDataToSend,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );
      } else {
        // Create new record
        response = await apiClient.post(apiURL, formDataToSend, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        });
      }

      if (response.status === 200 || response.status === 201) {
        toast.success("Personal information saved successfully!");
        setIsDirty(false);
        if (!edit) {
          onSectionComplete();
          onMenuSelect(selectedMenuIndex + 1);
        }
      }
    } catch (error: any) {
      console.error("Error:", error);
      toast.error(
        error?.response?.data?.detail ||
          "An error occurred while saving personal information."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedField, setSelectedField] = useState<
    "nationality" | "countryOfResidence" | null
  >(null);

  const handleOpenModal = (field: "nationality" | "countryOfResidence") => {
    setSelectedField(field);
    setSearchTerm("");
    setIsModalOpen(true);
  };

  const handleCountrySelect = (code: string) => {
    if (selectedField) {
      handleFormChange(selectedField, code);
      setIsModalOpen(false);
    }
  };

  const getFieldLabel = (field: string) => {
    const country = countries.find((c) => c.code === field);
    return country ? country.name : "Please select";
  };

  const filteredCountries = countries.filter((country) =>
    country.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="relative">
      {isLoading ? (
        <LoadingSpinner />
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Profile Image Upload */}
          <div className="flex flex-col items-center space-y-4 p-6 bg-white rounded-lg shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900">
              Profile Picture
            </h3>

            <div className="relative">
              {previewUrl ? (
                <div className="group relative w-32 h-32 rounded-full overflow-hidden border-4 border-blue-100">
                  <img
                    src={previewUrl}
                    alt="Profile preview"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300">
                    <button
                      type="button"
                      onClick={async () => {
                        if (personalInfoId) {
                          setIsLoading(true);
                          try {
                            const formDataToSend = new FormData();
                            formDataToSend.append(
                              "nationality",
                              formData.nationality
                            );
                            formDataToSend.append(
                              "country_of_residence",
                              formData.countryOfResidence
                            );
                            formDataToSend.append("gender", formData.gender);
                            formDataToSend.append(
                              "date_of_birth",
                              formatDateISO(formData.dateOfBirth)
                            );
                            if (formData.bio)
                              formDataToSend.append("bio", formData.bio);
                            if (formData.tagline)
                              formDataToSend.append(
                                "tagline",
                                formData.tagline
                              );
                            formDataToSend.append("delete_image", "true");

                            const response = await apiClient.put(
                              `${apiURL}/${personalInfoId}`,
                              formDataToSend,
                              {
                                headers: {
                                  "Content-Type": "multipart/form-data",
                                },
                              }
                            );

                            if (response.status === 200) {
                              setPreviewUrl("");
                              setProfileImage(null);
                              setIsDirty(true);
                              toast.success(
                                "Profile picture removed successfully!"
                              );
                            }
                          } catch (error: any) {
                            toast.error(
                              error?.response?.data?.detail ||
                                "Failed to remove profile picture"
                            );
                          } finally {
                            setIsLoading(false);
                          }
                        } else {
                          setPreviewUrl("");
                          setProfileImage(null);
                          setIsDirty(true);
                        }
                      }}
                      className="opacity-0 group-hover:opacity-100 text-white hover:scale-110 transition-all duration-300"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-8 w-8"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              ) : (
                <div className="w-32 h-32 rounded-full border-4 border-dashed border-gray-200 flex items-center justify-center bg-gray-50">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-12 w-12 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                </div>
              )}
            </div>

            <div className="flex flex-col items-center space-y-2">
              <label
                htmlFor="profile-image"
                className="px-4 py-2 bg-blue-50 text-blue-700 rounded-full cursor-pointer hover:bg-blue-100 transition-colors font-medium text-sm"
              >
                {previewUrl ? "Change Photo" : "Upload Photo"}
                <input
                  id="profile-image"
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="hidden"
                />
              </label>
              <p className="text-sm text-gray-500">JPG or PNG</p>
            </div>
          </div>

          <h3 className="font-black mb-4">Personal Information</h3>

          {/* First Name and Last Name Fields */}
          <div className="grid lg:grid-cols-2 gap-4 mt-4">
            <InputField
              label="First Name"
              type="text"
              placeholder="First Name"
              name="firstName"
              value={formData.firstName || userInfo?.firstName}
              onChange={(e) => handleFormChange(e.target.name, e.target.value)}
              disabled
              style={{ backgroundColor: "#d5d5d540" }}
              required={true}
            />
            <InputField
              label="Last Name"
              type="text"
              placeholder="Last Name"
              name="lastName"
              value={formData.lastName || userInfo?.lastName}
              onChange={(e) => handleFormChange(e.target.name, e.target.value)}
              disabled
              style={{ backgroundColor: "#d5d5d540" }}
              required={true}
            />
          </div>

          {/* Nationality and Country of Residence */}
          <div className="grid lg:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Nationality<span className="text-red-500">*</span>
              </label>
              <button
                type="button"
                onClick={() => handleOpenModal("nationality")}
                className="w-full p-3 text-left border rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white"
              >
                {getFieldLabel(formData.nationality)}
              </button>
              {hasAttemptedSubmit && !formData.nationality && (
                <p className="mt-1 text-sm text-red-600">
                  Nationality is required
                </p>
              )}
            </div>

            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Country of Residence<span className="text-red-500">*</span>
              </label>
              <button
                type="button"
                onClick={() => handleOpenModal("countryOfResidence")}
                className="w-full p-3 text-left border rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white"
              >
                {getFieldLabel(formData.countryOfResidence)}
              </button>
              {hasAttemptedSubmit && !formData.countryOfResidence && (
                <p className="mt-1 text-sm text-red-600">
                  Country of residence is required
                </p>
              )}
            </div>
          </div>

          {/* Gender and Date of Birth */}
          <div className="grid lg:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Gender<span className="text-red-500">*</span>
              </label>
              <Select
                onValueChange={(value) => handleFormChange("gender", value)}
                value={formData.gender}
              >
                <SelectTrigger
                  className={`h-[42px] shadow-none bg-white ${
                    hasAttemptedSubmit && !formData.gender
                      ? "border-red-500"
                      : ""
                  }`}
                >
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
                <SelectContent className="bg-white">
                  <SelectItem
                    className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                    value="male"
                  >
                    Male
                  </SelectItem>
                  <SelectItem
                    className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                    value="female"
                  >
                    Female
                  </SelectItem>
                  <SelectItem
                    className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                    value="other"
                  >
                    Other
                  </SelectItem>
                </SelectContent>
              </Select>
              {hasAttemptedSubmit && !formData.gender && (
                <p className="mt-1 text-sm text-red-500">Gender is required</p>
              )}
            </div>
            <div>
              <label className="block mb-2 text-sm font-medium text-gray-900">
                Date of Birth<span className="text-red-500">*</span>
              </label>
              <input
                type="date"
                name="dateOfBirth"
                value={formData.dateOfBirth}
                onChange={(e) =>
                  handleFormChange(e.target.name, e.target.value)
                }
                required={true}
                className="w-full h-[42px] p-3 border rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white"
              />
            </div>
          </div>

          {/* Tagline */}
          <div className="mt-4">
            <label className="block mb-2 text-sm font-medium text-gray-900">
              Tagline (Optional)
            </label>
            <textarea
              name="tagline"
              value={formData.tagline}
              onChange={(e) => handleFormChange(e.target.name, e.target.value)}
              className="w-full p-3 border rounded-lg focus:ring-blue-500 focus:border-blue-500"
              rows={4}
              placeholder="Your tagline"
              maxLength={100}
            />
            <p className="mt-1 text-sm text-gray-500">
              {formData.tagline?.length || 0}/100 characters
            </p>
          </div>
          {/* Bio */}
          <div className="mt-4">
            <label className="block mb-2 text-sm font-medium text-gray-900">
              Bio (Optional)
            </label>
            <textarea
              name="bio"
              value={formData.bio}
              onChange={(e) => handleFormChange(e.target.name, e.target.value)}
              className="w-full p-3 border rounded-lg focus:ring-blue-500 focus:border-blue-500"
              rows={4}
              placeholder="Tell us about yourself..."
              maxLength={1000}
            />
            <p className="mt-1 text-sm text-gray-500">
              {formData.bio?.length || 0}/1000 characters
            </p>
          </div>

          <NavigationButtons
            currentIndex={selectedMenuIndex || 0} // Fallback to 0 if undefined
            onBack={() =>
              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex - 1 : 0)
            }
            onNext={() =>
              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex + 1 : 1)
            }
            disableBack={!selectedMenuIndex || selectedMenuIndex < 1}
            nextLabel={edit ? "Save Changes" : "Next"}
          />
        </form>
      )}

      {/* Country Selection Modal */}
      <Dialog
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
            <Dialog.Title className="text-lg font-medium leading-6 text-gray-900 mb-4">
              Select{" "}
              {selectedField === "nationality"
                ? "Nationality"
                : "Country of Residence"}
            </Dialog.Title>

            {/* Search Input */}
            <div className="mb-4">
              <input
                type="text"
                placeholder="Search countries..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                autoFocus={true}
                className="w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Countries List */}
            <div className="mt-2 max-h-[60vh] overflow-y-auto">
              {filteredCountries.map((country) => (
                <button
                  key={country.code}
                  type="button"
                  className="w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 rounded-lg"
                  onClick={() => handleCountrySelect(country.code)}
                >
                  {country.name}
                </button>
              ))}
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>
    </div>
  );
}
