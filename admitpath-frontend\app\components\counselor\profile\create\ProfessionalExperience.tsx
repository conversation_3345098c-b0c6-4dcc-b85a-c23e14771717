"use client";

import React, { useState, useEffect } from "react";

import apiClient from "@/lib/apiClient";
import { InputField } from "@components/common/inputField";
import { NavigationButtons } from "@components/common/navigationBtns";
import { toast } from "react-toastify";
import { formatNormalDate, formatDateISO } from "@/app/utils/time-helpers";
import LoadingSpinner from "@components/common/loadingSpinner";
import TextAreaLimit from "@components/common/TeaxAreaLimit";
import { ProfileCreateProps } from "@/app/types/counselor/profile";
import { MainButton } from "@/app/components/common/mainBtn";

interface ProfessionalExperienceEntry {
  role?: string;
  company_name?: string;
  start_date?: string;
  end_date?: string;
  experience_description?: string;
  id?: string;
}

interface SocialLinks {
  id?: number;
  counselor_id?: number;
  linkedin_url?: string;
  portfolio_url?: string;
  website_url?: string;
}

const initialState = [
  {
    role: "",
    company_name: "",
    start_date: "",
    end_date: "",
    experience_description: "",
  },
];

export default function ProfessionalExperience({
  selectedMenuIndex = 0,
  onMenuSelect = () => {},
  onSectionComplete = () => {},
  isCompleted = false,
  edit = false,
}: ProfileCreateProps & {
  onSectionComplete?: () => void;
  isCompleted?: boolean;
}) {
  const [experiences, setExperiences] =
    useState<ProfessionalExperienceEntry[]>(initialState);
  const [socialLinks, setSocialLinks] = useState<SocialLinks>({
    linkedin_url: "",
    portfolio_url: "",
    website_url: "",
  });
  const [isLoading, setIsLoading] = useState<Boolean>(true);
  const [isDirty, setIsDirty] = useState(false);

  const apiURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/counselor/profile/professional-experience`;
  const socialLinkURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/counselor/profile/social-links`;

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);

      try {
        // Fetch Professional Experiences
        const experienceResponse = await apiClient.get(apiURL);

        if (experienceResponse.status === 200 && experienceResponse.data) {
          const dataReceived = experienceResponse.data.map((entry: any) => {
            return {
              ...entry,
              start_date: entry.start_date
                ? formatNormalDate(entry.start_date)
                : "",
              end_date: entry.end_date ? formatNormalDate(entry.end_date) : "",
            };
          });

          setExperiences(dataReceived.length > 0 ? dataReceived : initialState);
        }

        // Fetch Social Links
        const socialLinksResponse = await apiClient.get(socialLinkURL);

        if (socialLinksResponse.status === 200 && socialLinksResponse.data) {
          setSocialLinks({
            id: socialLinksResponse.data.id,
            counselor_id: socialLinksResponse.data.counselor_id,
            linkedin_url: socialLinksResponse.data.linkedin_url || "",
            portfolio_url: socialLinksResponse.data.portfolio_url || "",
            website_url: socialLinksResponse.data.website_url || "",
          });
        }
      } catch (error: any) {
        console.log(error?.response?.data?.detail);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleExperienceChange = (
    index: number,
    field: keyof ProfessionalExperienceEntry,
    value: string
  ) => {
    setIsDirty(true);
    const updatedExperiences = [...experiences];
    updatedExperiences[index][field] = value;
    setExperiences(updatedExperiences);
  };

  const handleSocialLinksChange = (field: keyof SocialLinks, value: string) => {
    setIsDirty(true);
    setSocialLinks((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleAddExperience = () => {
    setIsDirty(true);
    setExperiences([
      ...experiences,
      {
        role: "",
        company_name: "",
        start_date: "",
        end_date: "",
        experience_description: "",
      },
    ]);
  };

  const handleRemoveExperience = async (index: number) => {
    setIsDirty(true);
    const experienceToRemove = experiences[index];

    if (!experienceToRemove.id) {
      // If the experience doesn't have an ID, it's a new entry and hasn't been saved to the server
      setExperiences((prevExperiences) =>
        prevExperiences.filter((_, i) => i !== index)
      );
      return;
    }

    try {
      setIsLoading(true);
      const deleteUrl = `${apiURL}/${experienceToRemove.id}`;
      const response = await apiClient.delete(deleteUrl);

      if (response.status === 200) {
        toast.success("Experience removed successfully.");
        setExperiences((prevExperiences) =>
          prevExperiences.filter((_, i) => i !== index)
        );
      } else {
        throw new Error(
          response.data?.message || "Failed to delete experience."
        );
      }
    } catch (error: any) {
      toast.error(
        error.message || "Something went wrong while removing experience."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // If form hasn't changed and section is completed, just navigate
    if (!isDirty && isCompleted && !edit) {
      onMenuSelect(selectedMenuIndex + 1);
      return;
    }

    setIsLoading(true);
    try {
      // Handle Professional Experiences
      const nonEmptyExperiences = experiences.filter(
        (exp) =>
          exp.role ||
          exp.company_name ||
          exp.start_date ||
          exp.end_date ||
          exp.experience_description
      );

      for (const entry of nonEmptyExperiences) {
        if (
          entry.start_date &&
          entry.end_date &&
          new Date(entry.start_date) > new Date(entry.end_date)
        ) {
          throw new Error("Start date cannot be after end date.");
        }

        if (
          entry.start_date ||
          entry.end_date ||
          entry.role ||
          entry.company_name ||
          entry.experience_description
        ) {
          const payload = {
            ...entry,
            start_date: entry.start_date
              ? formatDateISO(entry.start_date)
              : null,
            end_date: entry.end_date ? formatDateISO(entry.end_date) : null,
          };

          const url = entry.id ? `${apiURL}/${entry.id}` : apiURL;
          const method = entry.id ? "put" : "post";
          const response = await apiClient[method](url, payload);

          if (response.status !== 200) {
            throw new Error(response.data.message || "Something went wrong");
          }
        }
      }

      // Handle Social Links
      const socialLinksPayload = {
        linkedin_url: socialLinks.linkedin_url || "",
        portfolio_url: socialLinks.portfolio_url || "",
        website_url: socialLinks.website_url || "",
      };

      const socialLinksMethod = socialLinks.id ? "put" : "post";
      const socialLinksResponse = await apiClient[socialLinksMethod](
        socialLinkURL,
        socialLinksPayload
      );

      if (socialLinksResponse.status !== 200) {
        throw new Error(
          socialLinksResponse.data.message || "Failed to save social links"
        );
      }

      if (!edit) {
        onSectionComplete();
        onMenuSelect(selectedMenuIndex + 1);
      } else {
        toast.success("Changes saved successfully");
      }
      setIsDirty(false);
    } catch (error: any) {
      toast.error(error.message || "Failed to submit form");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="relative">
      {isLoading ? (
        <LoadingSpinner />
      ) : (
        <form className="mt-12" onSubmit={handleSubmit}>
          {/* Professional Experience Section */}
          {experiences.map((experience, index) => (
            <div key={index}>
              {index === 0 && (
                <>
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="font-black">Professional Experience</h3>
                    {!edit && (
                      <MainButton
                        variant="secondary"
                        type="button"
                        onClick={() => onMenuSelect(selectedMenuIndex + 1)}
                      >
                        Skip this step →
                      </MainButton>
                    )}
                  </div>

                  <div className="mb-6">
                    <InputField
                      label="LinkedIn URL"
                      type="text"
                      placeholder="Enter your LinkedIn Profile URL"
                      name="linkedin_url"
                      value={socialLinks.linkedin_url}
                      onChange={(e) =>
                        handleSocialLinksChange("linkedin_url", e.target.value)
                      }
                    />
                  </div>
                </>
              )}

              {index > 0 && (
                <h3 className="font-black mb-4">Add more experience</h3>
              )}

              <div key={index} className="mb-6 border-b pb-4 space-y-4">
                <InputField
                  label="Company Name"
                  type="text"
                  placeholder="Enter your company name"
                  name={`company_name-${index}`}
                  value={experience.company_name}
                  onChange={(e) =>
                    handleExperienceChange(
                      index,
                      "company_name",
                      e.target.value
                    )
                  }
                />
                <div className="">
                  <InputField
                    label="Role"
                    type="text"
                    placeholder="Product Designer"
                    name={`role-${index}`}
                    value={experience.role}
                    onChange={(e) =>
                      handleExperienceChange(index, "role", e.target.value)
                    }
                  />
                </div>

                <div className="grid lg:grid-cols-2 gap-4 mt-4 mb-4">
                  <InputField
                    label="Start Date"
                    type="date"
                    name={`start_date-${index}`}
                    value={experience.start_date}
                    onChange={(e) =>
                      handleExperienceChange(
                        index,
                        "start_date",
                        e.target.value
                      )
                    }
                  />
                  <InputField
                    label="End Date"
                    type="date"
                    name={`end_date-${index}`}
                    value={experience.end_date}
                    onChange={(e) =>
                      handleExperienceChange(index, "end_date", e.target.value)
                    }
                  />
                </div>

                <TextAreaLimit
                  label="Briefly describe your experience"
                  name={`experience_description-${index}`}
                  placeholder="Share your experience"
                  value={experience.experience_description}
                  onChange={(value) =>
                    handleExperienceChange(
                      index,
                      "experience_description",
                      value
                    )
                  }
                />

                {index > 0 && (
                  <MainButton
                    variant="secondary"
                    type="button"
                    onClick={() => handleRemoveExperience(index)}
                  >
                    Remove Experience
                  </MainButton>
                )}
              </div>
            </div>
          ))}

          <MainButton
            variant="primary"
            type="button"
            onClick={handleAddExperience}
          >
            Add More Experience +
          </MainButton>

          <NavigationButtons
            currentIndex={selectedMenuIndex || 0}
            onBack={() =>
              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex - 1 : 0)
            }
            onNext={() =>
              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex + 1 : 1)
            }
            disableBack={!selectedMenuIndex || selectedMenuIndex < 1}
            nextLabel={edit ? "Save Changes" : "Next"}
          />
        </form>
      )}
    </div>
  );
}
