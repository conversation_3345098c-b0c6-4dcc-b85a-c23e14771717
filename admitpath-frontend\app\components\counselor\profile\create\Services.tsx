"use client";

import { useEffect, useState } from "react";

import apiClient from "@/lib/apiClient";
import { InputField } from "@components/common/inputField";
import { NavigationButtons } from "@components/common/navigationBtns";
import useFormState from "@hooks/useFormState";
import { toast } from "react-toastify";
import LoadingSpinner from "@components/common/loadingSpinner";
import { ProfileCreateProps } from "@/app/types/counselor/profile";

export default function Services({
  selectedMenuIndex = 0, // Default to 0
  onMenuSelect = () => {}, // Default to a no-op function
  onSectionComplete = () => {}, // Default to a no-op function
  isCompleted = false,
  edit = false,
}: ProfileCreateProps & {
  onSectionComplete?: () => void;
  isCompleted?: boolean;
}) {
  const apiURL = `${process.env.NEXT_PUBLIC_API_BASE_URL}/counselor/profile/services`;

  const { formData, setFormData, handleChange } = useFormState<{
    services: string[];
    pleaseSpecify: string;
  }>({
    services: [],
    pleaseSpecify: "",
  });

  const [isLoading, setIsLoading] = useState(true);
  const [isDirty, setIsDirty] = useState(false);

  useEffect(() => {
    // Fetch existing data on mount
    const fetchServices = async () => {
      try {
        setIsLoading(true);
        const response = await apiClient.get(apiURL);
        const data = response.data;

        // Separate "Specify Other" values from services
        const services = data || [];
        const otherValue = services.filter((service: string) =>
          service.startsWith("Specify:")
        );
        const other = otherValue.length
          ? otherValue[0].replace("Specify: ", "")
          : "";

        setFormData({
          services: services.filter(
            (service: string) => !service.startsWith("Specify:")
          ),
          pleaseSpecify: other,
        });
      } catch (error: any) {
        console.log(error?.response?.data?.detail);
      } finally {
        setIsLoading(false);
      }
    };

    fetchServices();
  }, [setFormData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // If form hasn't changed and section is completed, just navigate
    if (!isDirty && isCompleted && !edit) {
      onMenuSelect(selectedMenuIndex + 1);
      return;
    }

    if (!formData.services.length) {
      toast.error("Please select atleast one service");
      return;
    }

    // Combine services and "Specify Other" field value
    const payload = {
      services: [
        ...formData.services,
        ...(formData.pleaseSpecify
          ? [`Specify: ${formData.pleaseSpecify}`]
          : []),
      ],
    };

    setIsLoading(true);
    try {
      await apiClient.post(apiURL, payload); // Adjust with PUT logic if necessary
      if (!edit) {
        onSectionComplete();
        onMenuSelect(selectedMenuIndex + 1);
      }
      setIsDirty(false);
    } catch (error) {
      toast.error("Failed to save services.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleServiceSelect = (item: string) => {
    setIsDirty(true);
    setFormData((prevState) => {
      const isSelected = prevState.services.includes(item);
      const updatedServices = isSelected
        ? prevState.services.filter((service) => service !== item) // Remove if already selected
        : [...prevState.services, item]; // Add if not selected

      return {
        ...prevState,
        services: updatedServices,
        // Clear "pleaseSpecify" only when "Other" is deselected
        pleaseSpecify:
          item === "Other" && isSelected ? "" : prevState.pleaseSpecify,
      };
    });
  };

  const serviceItems = [
    "Essay Review",
    "Personal Statement Guidance",
    "University Shortlisting",
    "Entire Curricular Profile Building",
    "Supplementary Essay Guidance",
    "Financial Aid Advice",
    "Interview Preparation",
    "Other",
  ];

  return (
    <div className="relative">
      {isLoading ? (
        <LoadingSpinner />
      ) : (
        <form className="mt-12" onSubmit={handleSubmit}>
          <h3 className="font-black mb-4">Services</h3>

          <h5 className="text-md font-large text-gray-900">Select services <span className="text-red-500">*</span></h5>

          <ul className="mt-4 flex flex-wrap gap-2">
            {serviceItems.map((item) => (
              <li
                key={item}
                onClick={() => handleServiceSelect(item)}
                className={`border px-2 py-3 rounded cursor-pointer ${
                  formData.services.includes(item)
                    ? "text-blue-500"
                    : "text-black"
                }`}
              >
                {item}
              </li>
            ))}
          </ul>

          {/* Show InputField only if "Other" is clicked */}
          {formData.services.includes("Other") && (
            <div className="mt-4">
              <InputField
                label="Specify Others"
                required={false}
                type="text"
                placeholder="Enter here"
                name="pleaseSpecify"
                value={formData.pleaseSpecify}
                onChange={(e) => {
                  setIsDirty(true);
                  handleChange(e);
                }}
              />
            </div>
          )}

          {/* Navigation buttons */}
          <NavigationButtons
            currentIndex={selectedMenuIndex || 0} // Fallback to 0 if undefined
            onBack={() =>
              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex - 1 : 0)
            }
            onNext={() =>
              onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex + 1 : 1)
            }
            disableBack={!selectedMenuIndex || selectedMenuIndex < 1}
            nextLabel={edit ? "Save Changes" : "Next"}
          />
        </form>
      )}
    </div>
  );
}
