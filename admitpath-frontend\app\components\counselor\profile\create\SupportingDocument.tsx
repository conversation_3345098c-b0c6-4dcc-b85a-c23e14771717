"use client";

import { useState, useRef, useEffect } from "react";

import apiClient from "@/lib/apiClient";
import axios from "axios";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faTrash,
  faUpload,
  faDownload,
  faEye,
} from "@fortawesome/free-solid-svg-icons";
import LoadingSpinner from "@components/common/loadingSpinner";
import { NavigationButtons } from "@components/common/navigationBtns";
import { MainButton } from "@/app/components/common/mainBtn";
import useFormState from "@hooks/useFormState";
import { toast } from "react-toastify";
import { ProfileCreateProps } from "@/app/types/counselor/profile";
import { cn } from "@/lib/utils";

const acceptedFileTypes = [
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
];

//////////////////////////////// MAIN COMPONENT /////////////////////////////////
export default function SupportingDocuments({
  selectedMenuIndex = 0, // Default to 0
  onMenuSelect = () => {}, // Default to a no-op function
  onSectionComplete = () => {}, // Default to a no-op function
  isCompleted = false, // Default to false
  edit = false,
}: ProfileCreateProps & {
  onSectionComplete?: () => void;
  isCompleted?: boolean;
}) {
  const { formData, setFormData } = useFormState({
    files: [] as File[],
  });

  const [dragging, setDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isDirty, setIsDirty] = useState(false);

  const baseURL = process.env.NEXT_PUBLIC_API_BASE_URL;
  const token = localStorage.getItem("access_token");

  // Fetch existing files on mount
  useEffect(() => {
    const fetchFiles = async () => {
      setIsLoading(true);

      try {
        getLatestFiles();
      } catch (error: any) {
        console.log(error?.response?.data?.detail);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFiles();
  }, [setFormData]);

  async function getLatestFiles() {
    const response = await apiClient.get(
      `${baseURL}/counselor/profile/documents`
    );

    setFormData({ files: response.data || [] });
  }

  const handleFileUpload = async (files: FileList | File[]) => {
    const validFiles = Array.from(files).filter((file) =>
      acceptedFileTypes.includes(file.type)
    );

    const invalidFiles = Array.from(files).filter(
      (file) => !acceptedFileTypes.includes(file.type)
    );

    if (invalidFiles.length > 0) {
      toast.error(
        "Some files are invalid. Please upload PDF, Word, or Excel files only."
      );
    }

    setIsLoading(true);
    setIsDirty(true);

    // Upload each valid file separately
    for (const file of validFiles) {
      try {
        const formData = new FormData();
        formData.append("file", file);

        await axios.post(
          `${baseURL}/counselor/profile/documents/upload`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        toast.success(`${file.name} uploaded successfully`);
      } catch (error: any) {
        toast.error(
          `Failed to upload ${file.name}: ${
            error?.response?.data?.detail || "Unknown error"
          }`
        );
      } finally {
        setIsLoading(false);
      }
    }

    // Fetch updated file list after all uploads
    getLatestFiles();
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setDragging(false);

    const files = e.dataTransfer.files;
    if (files) {
      await handleFileUpload(files);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;

    if (files) {
      await handleFileUpload(files);
    }
  };

  const handleDeleteFile = async (fileId: string) => {
    setIsDirty(true);
    try {
      await apiClient.delete(
        `${baseURL}/counselor/profile/documents/${fileId}`
      );

      // Fetch updated file list
      getLatestFiles();
    } catch (error) {
      toast.error("Failed to delete the file. Please try again.");
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragging(true);
  };

  const handleDragLeave = () => {
    setDragging(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // If form hasn't changed and section is completed, just navigate
    if (!isDirty && isCompleted && !edit) {
      onMenuSelect(selectedMenuIndex + 1);
      return;
    }

    setIsLoading(true);
    try {
      // Since documents are uploaded immediately, we just need to verify and proceed
      setIsDirty(false);
      if (!edit) {
        onSectionComplete();
        onMenuSelect(selectedMenuIndex + 1);
      }
    } catch (error) {
      toast.error("Failed to submit");
    } finally {
      setIsLoading(false);
    }
  };

  const handleClickUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleDownloadFile = (file: any) => {
    if (file?.document_url) {
      const anchor = document.createElement("a");
      anchor.href = file.file_url;
      anchor.download = file.document_name || "download";
      anchor.click();
    } else {
      toast.error("File URL not available for download.");
    }
  };

  ///////////////////////////////////// JSX /////////////////////////////
  return (
    <div className="relative">
      {isLoading ? (
        <LoadingSpinner />
      ) : (
        <form className="mt-12" onSubmit={handleSubmit}>
          <div className="flex items-center justify-between mb-6">
            <h3 className="font-black">Supporting Documents</h3>
            {!edit && (
              <MainButton
                variant="secondary"
                type="button"
                onClick={() => onMenuSelect(selectedMenuIndex + 1)}
              >
                Skip this step →
              </MainButton>
            )}
          </div>
          <p className="text-gray-600 mb-6">
            Feel free to add your resume or any other documents to support your
            case
          </p>

          <div
            className={`border-2 border-dashed p-6 rounded-md text-center bg-[#EFF0F7] ${
              dragging ? "border-blue-500" : "border-gray-300"
            } transition-all`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={handleClickUpload}
          >
            <FontAwesomeIcon
              icon={faUpload}
              className="text-blue-500 text-3xl mb-2"
            />
            <p className="text-gray-500">
              Click to upload or drag and drop files here
            </p>
            <p className="text-sm text-gray-400">
              Accepted file types: PDF, Word, Excel
            </p>
            <input
              ref={fileInputRef}
              type="file"
              accept=".pdf,.doc,.docx,.xls,.xlsx"
              className="hidden"
              multiple
              onChange={handleFileChange}
            />
          </div>

          <div className="mt-6">
            <h5>Selected Files:</h5>
            <ul className="mt-3 grid lg:grid-cols-2 gap-2">
              {formData.files.length === 0 ? (
                <li>No files selected</li>
              ) : (
                formData.files.map((file: any, index) => (
                  <li
                    key={index}
                    className="flex items-center justify-between gap-4 bg-[#EFF0F7] p-3"
                  >
                    <span className="text-gray-700">{file.document_name}</span>
                    <div className="whitespace-nowrap">
                      <button
                        title="View"
                        type="button"
                        onClick={() => {
                          window.open(file.document_url, "_blank"); // Open document in a new tab
                        }}
                        className="text-blue-500 hover:text-blue-700 mr-3"
                      >
                        <FontAwesomeIcon icon={faEye} />{" "}
                      </button>
                      <button
                        title="Download"
                        type="button"
                        onClick={() => handleDownloadFile(file)}
                        className="text-blue-500 hover:text-blue-700 mr-3"
                      >
                        <FontAwesomeIcon icon={faDownload} />
                      </button>
                      <button
                        title="Remove"
                        type="button"
                        onClick={() => handleDeleteFile(file.id)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </button>
                    </div>
                  </li>
                ))
              )}
            </ul>
          </div>

          <div className={cn("flex justify-end mt-8", edit && "hidden")}>
            <NavigationButtons
              currentIndex={selectedMenuIndex || 0}
              onBack={() =>
                onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex - 1 : 0)
              }
              onNext={() =>
                onMenuSelect?.(selectedMenuIndex ? selectedMenuIndex + 1 : 1)
              }
              disableBack={!selectedMenuIndex || selectedMenuIndex < 1}
              nextLabel={edit ? "Save Changes" : "Next"}
            />
          </div>
        </form>
      )}
    </div>
  );
}
