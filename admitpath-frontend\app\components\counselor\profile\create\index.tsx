"use client";

import React, { FC, useState } from "react";
import Image from "next/image";
import PersonalInfo from "./PersonalInfo";
import EducationalBackground from "./EducationalBackground";
import ProfessionalExperience from "./ProfessionalExperience";
import CounsellingExperience from "./CounsellingExperience";
import Services from "./Services";
import Commitment from "./Commitment";
import SupportingDocument from "./SupportingDocument";
import FinalCTA from "./FinalCTA";

export const OnboardingPage: FC = () => {
  const [selectedMenuIndex, setSelectedMenuIndex] = useState(0);
  const [completedSections, setCompletedSections] = useState<number[]>([]);

  const handleSectionComplete = (index: number) => {
    if (!completedSections.includes(index)) {
      setCompletedSections(prev => [...prev, index].sort((a, b) => a - b));
    }
  };

  function menuItemObj(CompName: any) {
    return (
      <CompName
        onMenuSelect={setSelectedMenuIndex}
        selectedMenuIndex={selectedMenuIndex}
        onSectionComplete={() => handleSectionComplete(selectedMenuIndex)}
        isCompleted={completedSections.includes(selectedMenuIndex)}
      />
    );
  }

  // Define the mapping between menu items and components
  const menuItems = [
    {
      label: "Personal Information",
      component: menuItemObj(PersonalInfo),
    },
    {
      label: "Educational Background",
      component: menuItemObj(EducationalBackground),
    },
    {
      label: "Professional Experience",
      component: menuItemObj(ProfessionalExperience),
    },
    {
      label: "Counselling Experience",
      component: menuItemObj(CounsellingExperience),
    },
    {
      label: "Services you can offer",
      component: menuItemObj(Services),
    },
    {
      label: "Commitment & Pricing",
      component: menuItemObj(Commitment),
    },
    {
      label: "Supporting Documents",
      component: menuItemObj(SupportingDocument),
    },
  ];

  return (
    <div className="onboarding-container bg-white px-3 pb-8">
      <div className="overflow-hidden">
        <figure className="top-bg absolute -top-16 left-0 h-80 w-full">
          <Image
            src="/images/counselor-bg-onboarding.jpg"
            alt="banner"
            layout="fill"
          />

          <Image
            width={100}
            height={100}
            src="/images/white-logo.png"
            alt="Logo"
            className="transition-all duration-300 absolute top-24 left-7"
          />
        </figure>
      </div>

      <div className="container relative z-10 mx-auto mt-32">
        <div className="content flex gap-6 justify-between">
          <div className="hidden md:block left-side shadow-md rounded-xl overflow-hidden max-h-min">
            <ul className="bg-white grid gap-8 py-10 pl-5 pr-8 lg:pr-24">
              {menuItems.map((item, index) => (
                <li
                  key={index}
                  onClick={() => completedSections.includes(index) && setSelectedMenuIndex(index)}
                  className={`flex gap-3 items-center ${
                    completedSections.includes(index) ? "cursor-pointer hover:opacity-80" : "cursor-not-allowed"
                  } ${
                    index <= selectedMenuIndex
                      ? "text-[#141519]"
                      : "text-[#9EA2B3]"
                  }`}
                >
                  <span
                    className={`relative rounded-full border h-8 w-8 text-sm flex justify-center items-center ${
                      completedSections.includes(index)
                        ? "bg-mainClr text-white"
                        : index === selectedMenuIndex
                        ? "border-[#141519]"
                        : "border-[#9EA2B3]"
                    }`}
                  >
                    {completedSections.includes(index) ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 00-1.414 0L8 12.586 4.707 9.293a1 1 0 10-1.414 1.414l4 4a1 1 0 001.414 0l8-8a1 1 0 000-1.414z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : (
                      (index + 1).toString().padStart(2, "0")
                    )}
                    {menuItems.length > index + 1 && (
                      <span
                        className={`absolute top-full ml-2.5 mt-2 flex justify-center rotate-90 ${
                          completedSections.includes(index)
                            ? "text-[#141519]"
                            : "text-[#9EA2B3]"
                        }`}
                      >
                        ...... {/* Visual separator */}
                      </span>
                    )}
                  </span>
                  <h5 className="transition-all duration-200">{item.label}</h5>
                </li>
              ))}
            </ul>
          </div>

          {selectedMenuIndex < menuItems.length ? (
            <div className="right-side bg-white shadow-md rounded-lg px-5 py-10 lg:px-10 flex-1">
              <h2 className="text-2xl mb-5">
                Tell us about <span className="text-[#2F80ED]">yourself</span>
              </h2>
              <p className="mb-6 text-[#141519]">
                Please complete the following details to let us know more about
                you. It will take less than 5 minutes!
              </p>
              <hr />

              {menuItems[selectedMenuIndex]?.component || "No Component"}
            </div>
          ) : (
            <FinalCTA
              selectedMenuIndex={selectedMenuIndex}
              onMenuSelect={setSelectedMenuIndex}
            />
          )}
        </div>
      </div>
    </div>
  );
};
