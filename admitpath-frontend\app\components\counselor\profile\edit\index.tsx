"use client";

import React, { useState } from "react";
import PersonalInfo from "../create/PersonalInfo";
import EducationalInfo from "../create/EducationalBackground";
import ProfessionalExperience from "../create/ProfessionalExperience";
import CommitmentPricing from "../create/Commitment";
import Services from "../view/Services";
import { useProfile } from "@/app/hooks/counselor/useProfile";
import SupportingDocuments from "../create/SupportingDocument";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import { generatePlaceholder } from "@/app/utils/image";
import Image from "next/image";

// Define the sidebar items
const sidebarItems = [
  { name: "Personal Information", value: "personalInformation" },
  { name: "Educational Background", value: "educationalBackground" },
  { name: "Professional Experience", value: "professionalExperience" },
  { name: "Services You Can Offer", value: "services" },
  { name: "Commitment & Pricing", value: "commitmentAndPricing" },
  { name: "Supporting Documents", value: "documents" },
];

// Define the sections content
const sections: Record<string, React.JSX.Element> = {
  personalInformation: <PersonalInfo edit={true} />,
  educationalBackground: <EducationalInfo edit={true} />,
  professionalExperience: <ProfessionalExperience edit={true} />,
  services: <Services />,
  commitmentAndPricing: <CommitmentPricing edit={true} />,
  documents: <SupportingDocuments edit={true} />,
};

///////////////////////////////////// MAIN COMPONENT /////////////////////////////////////
export default function EditProfile() {
  const { profilePicture, userInfo } = useProfile();

  // Set activeTab type to match keys of 'sections' for type safety
  const [activeTab, setActiveTab] = useState<keyof typeof sections>(
    "personalInformation"
  );

  return (
    <div className="bg-white rounded-xl px-5 py-9">
      <div className="flex justify-between">
        <h3 className="text-lg md:mb-10 mb-6 font-semibold">Edit Profile</h3>
        <Link
          href={"/counselor/dashboard/profile/view"}
          className="flex items-center"
        >
          <ArrowLeft className="size-5" /> Back
        </Link>
      </div>
      <div className="container flex lg:flex-row flex-col gap-y-6">
        <aside className="left-side lg:w-1/4 w-full rounded-lg">
          <ul className="space-y-4">
            {sidebarItems.map((item) => (
              <li
                key={item.value}
                className={`cursor-pointer py-2 rounded-lg transition-colors ${
                  activeTab === item.value ? "text-neutral8" : "text-neutral4"
                }`}
                onClick={() =>
                  setActiveTab(item.value as keyof typeof sections)
                } // Cast to a valid key
              >
                {item.name}
              </li>
            ))}
          </ul>
        </aside>

        <section className="right-side lg:w-3/4 w-full md:p-6 p-4 bg-gray-100 rounded-xl">
          <div className="flex gap-6 items-center pb-6">
            <Image
              src={
                profilePicture ||
                generatePlaceholder(userInfo?.firstName, userInfo?.lastName)
              }
              alt="User pic"
              width={80}
              height={80}
              className="rounded-full"
            />

            <div>
              <h3 className="mb-1 text-lg font-semibold">
                {userInfo?.firstName} {userInfo?.lastName}
              </h3>
              <p>Edit Profile / Personal Information</p>
            </div>
          </div>

          <hr />

          {sections[activeTab]}
        </section>
      </div>
    </div>
  );
}
