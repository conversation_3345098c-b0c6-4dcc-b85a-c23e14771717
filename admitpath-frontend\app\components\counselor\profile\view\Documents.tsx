"use client";

import { useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPencil, faFileAlt } from "@fortawesome/free-solid-svg-icons";
import Link from "next/link";
import { useProfile } from "@/app/hooks/counselor/useProfile";

//////////////////////// MAIN COMPONENT /////////////////////////////
export default function Documents() {
  const { getDocumentInfo, documentInfo } = useProfile();

  useEffect(() => {
    getDocumentInfo();
  }, []);

  return (
    <section className="border p-4 rounded-xl">
      <div className="flex justify-between items-center mb-8">
        <h3 className="font-semibold text-lg">Documents</h3>
        <Link
          href="/counselor/dashboard/profile/edit#documents"
          className="text-neutral-500 hover:text-neutral-700 transition-colors"
        >
          <FontAwesomeIcon icon={faPencil} className="w-4 h-4" />
        </Link>
      </div>

      <ul className="grid md:grid-cols-2 gap-4">
        {documentInfo?.map((doc) => (
          <li
            key={doc.id}
            className="flex gap-4 items-center bg-neutral-100 p-3 rounded-md shadow-sm"
          >
            <FontAwesomeIcon
              icon={faFileAlt}
              className="text-neutral-500 cursor-pointer"
            />
            <div>
              <h4 className="text-gray-700 font-medium">{doc.document_name}</h4>
              {/* <p className="text-gray-500 text-sm">{doc.size}</p> */}
            </div>
          </li>
        ))}
      </ul>
    </section>
  );
}
