"use client";

import { useEffect } from "react";
import StyledIcon from "@/app/components/common/styledIcon";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPencil, faUniversity } from "@fortawesome/free-solid-svg-icons";
import Link from "next/link";
import { formatToLongDateString } from "@/app/utils/time-helpers";
import { useProfile } from "@/app/hooks/counselor/useProfile";

export default function Education() {
  const { getEducationInfo, educationInfo } = useProfile();

  useEffect(() => {
    getEducationInfo();
  }, []);

  return (
    <section className="border p-4 rounded-xl">
      <div className="flex justify-between items-center mb-8">
        <h3 className="font-semibold text-lg">Education</h3>
        <Link
          href="/counselor/dashboard/profile/edit#education"
          className="text-neutral-500 hover:text-neutral-700 transition-colors"
        >
          <FontAwesomeIcon icon={faPencil} className="w-4 h-4" />
        </Link>
      </div>

      <ul className="space-y-4">
        {educationInfo?.map((education) => (
          <li
            key={education.id}
            className="flex items-start gap-4 border-b pb-4 last:border-b-0"
          >
            <p className="h-8 w-8 rounded-full bg-[#F8F8F8] flex justify-center items-center">
              <StyledIcon icon={faUniversity} />
            </p>
            <div>
              <h3 className="text-neutral-900 font-semibold mb-1">
                {education.university_name}
              </h3>
              <p className="text-sm mb-1">{education.degree}</p>
              <p className="text-sm mb-1 text-neutral-500">
                {formatToLongDateString(education.start_date)} -{" "}
                {formatToLongDateString(education.end_date)}
              </p>
            </div>
          </li>
        ))}
      </ul>
    </section>
  );
}
