"use client";

import { useEffect } from "react";
import StyledIcon from "@/app/components/common/styledIcon";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPencil, faUniversity } from "@fortawesome/free-solid-svg-icons";
import Link from "next/link";
import { formatToLongDateString } from "@/app/utils/time-helpers";
import { useProfile } from "@/app/hooks/counselor/useProfile";

export default function Eexperience() {
  const { getExperienceInfo, experienceInfo } = useProfile();

  useEffect(() => {
    getExperienceInfo();
  }, []);

  return (
    <section className="border p-4 rounded-xl">
      <div className="flex justify-between items-center mb-8">
        <h3 className="font-semibold text-lg">Experience</h3>
        <Link
          href="/counselor/dashboard/profile/edit#experience"
          className="text-neutral-500 hover:text-neutral-700 transition-colors"
        >
          <FontAwesomeIcon icon={faPencil} className="w-4 h-4" />
        </Link>
      </div>

      <ul className="space-y-4">
        {experienceInfo?.map((experience) => (
          <li
            key={experience.id}
            className="flex items-start gap-4 border-b pb-4 last:border-b-0"
          >
            <p className="h-8 w-8 rounded-full bg-[#F8F8F8] flex justify-center items-center">
              <StyledIcon icon={faUniversity} />
            </p>
            <div>
              <h3 className="text-neutral-900 font-semibold mb-1">
                {experience.role}
              </h3>
              <p className="text-sm mb-1">
                {experience.experience_description}
              </p>
              <p className="text-sm mb-1 text-neutral-500">
                {formatToLongDateString(experience.start_date)} -{" "}
                {formatToLongDateString(experience.end_date)}
              </p>
            </div>
          </li>
        ))}
      </ul>
    </section>
  );
}
