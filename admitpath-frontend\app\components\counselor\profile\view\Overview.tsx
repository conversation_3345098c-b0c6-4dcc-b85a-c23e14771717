"use client";

import { useEffect, useState } from "react";
import apiClient from "@/lib/apiClient";
import {
  faChalkboardTeacher,
  faCalendarAlt,
} from "@fortawesome/free-solid-svg-icons";
import StyledIcon from "@/app/components/common/styledIcon";
import { toast } from "react-toastify";
import { PersonalInfo } from "@/app/types/counselor/profile";
import { MainButton } from "@/app/components/common/mainBtn";
import { SessionsResponse } from "@/app/types/counselor/sessions";

interface OverviewProps {
  personalInfo: PersonalInfo | null;
  sessions: SessionsResponse | null;
}

export default function Overview({ personalInfo, sessions }: OverviewProps) {
  const [bio, setBio] = useState<string | undefined>("");
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setBio(personalInfo?.bio);
  }, [personalInfo]);

  async function updateBio() {
    const payload = { ...personalInfo, bio };

    try {
      setLoading(true);

      const response = await apiClient.put(
        "/counselor/profile/personal-info",
        payload
      );
      setBio(response.data.bio);
      toast.success("Bio updated successfully!");
      setIsEditing(false);
    } catch (err) {
      toast.error("Failed to update bio. Please try again.");
    } finally {
      setLoading(false);
    }
  }

  return (
    <section>
      {/* Stats Section */}
      <ul className="grid md:grid-cols-3 gap-4 mb-4">
        <li className="flex flex-col items-start border rounded-xl p-3 gap-3">
          <StyledIcon
            icon={faChalkboardTeacher}
            bgColor="#9B51E033"
            color="#9B51E0"
          />
          <span className="font-bold text-lg">
            {
              sessions?.items.filter((item) => item.status === "completed")
                .length
            }
          </span>
          <span className="text-neutral5 text-sm">Sessions conducted</span>
        </li>

        <li className="flex flex-col items-start border rounded-xl p-3 gap-3">
          <StyledIcon icon={faCalendarAlt} bgColor="#F2994A33" color="Orange" />
          <span className="font-bold text-lg">
            {
              sessions?.items.filter((item) => item.status === "upcoming")
                .length
            }
          </span>
          <span className="text-neutral5 text-sm">Upcoming sessions</span>
        </li>
      </ul>

      {/* Bio Section */}
      <div className="rounded-xl border p-4">
        <div className="flex justify-between gap-3">
          <h3 className="font-semibold text-lg mb-3">Bio</h3>
          {!isEditing && (
            <MainButton onClick={() => setIsEditing(true)} variant="primary">
              Update Bio
            </MainButton>
          )}
        </div>

        {isEditing ? (
          <div className="flex flex-col gap-3">
            <textarea
              value={bio}
              onChange={(e) => setBio(e.target.value)}
              rows={5}
              className="border rounded p-2 w-full"
              placeholder="Write your bio here..."
            />
            <div className="flex justify-end gap-2">
              <MainButton variant="neutral" onClick={() => setIsEditing(false)}>
                Cancel
              </MainButton>
              <MainButton variant="primary" onClick={() => updateBio()}>
                {loading ? "Saving..." : "Save"}
              </MainButton>
            </div>
          </div>
        ) : (
          <div className="text-neutral7 flex justify-between gap-3 mb-3">
            <p>{bio || "No bio available."}</p>
          </div>
        )}
      </div>
    </section>
  );
}
