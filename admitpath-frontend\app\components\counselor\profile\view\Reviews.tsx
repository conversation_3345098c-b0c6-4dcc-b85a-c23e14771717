"use client";

import { useSessions } from "@/app/hooks/counselor/useSessions";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faPencil } from "@fortawesome/free-solid-svg-icons";
import Link from "next/link";
import { format } from "date-fns";
import { generatePlaceholder } from "@/app/utils/image";

export default function Reviews() {
  const { studentsFeedback } = useSessions();

  return (
    <section className="feedback bg-white rounded-lg p-4">
      <div className="flex justify-between items-center mb-8">
        <h3 className="font-semibold text-lg">Reviews</h3>
        <Link
          href="/counselor/dashboard/profile/edit#reviews"
          className="text-neutral-500 hover:text-neutral-700 transition-colors"
        >
          <FontAwesomeIcon icon={faPencil} className="w-4 h-4" />
        </Link>
      </div>

      {studentsFeedback?.reviews.length ? (
        <ul className="grid md:grid-cols-2 gap-4">
          {studentsFeedback.reviews.map((feedback) => (
            <li
              key={feedback.id}
              className="bg-white border rounded-lg hover:scale-[100.7%] transition-all  cursor-pointer p-4 flex"
            >
              {/* Student Avatar */}
              <div className="mr-4 flex-shrink-0">
                <img
                  src={
                    feedback.student_profile_picture ||
                    generatePlaceholder(
                      feedback.student_name.split(/s+/)[0],
                      feedback.student_name.split(/s+/)[1]
                    )
                  }
                  alt={feedback.student_name}
                  className="w-12 h-12 rounded-full object-cover"
                />
              </div>

              {/* Review Content */}
              <div className="flex-1">
                <div className="font-medium mb-1">{feedback.student_name}</div>
                {/* Star Rating */}
                <div className="flex mb-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <svg
                      key={star}
                      className={`w-4 h-4 ${
                        star <= feedback.rating
                          ? "text-yellow-400"
                          : "text-gray-300"
                      }`}
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>

                {/* Review Comment */}
                <p className="text-gray-700 mb-2 line-clamp-3">
                  {feedback.comment}
                </p>

                {/* Review Date */}
                <p className="text-gray-500 text-sm">
                  {format(new Date(feedback.created_at), "PPP")}
                </p>
              </div>
            </li>
          ))}
        </ul>
      ) : (
        <div className="flex justify-center items-center h-20 text-neutral-500">
          <p>No Reviews Available</p>
        </div>
      )}
    </section>
  );
}
