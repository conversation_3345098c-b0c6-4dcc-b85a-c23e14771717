"use client";

import { useEffect, useState } from "react";
import { faBoxOpen } from "@fortawesome/free-solid-svg-icons";
import AddServicePopup from "./popup/AddServicePopup";
import EditServicePopup from "./popup/EditServicePopup";
import DeleteServicePopup from "./popup/DeleteServicePopup";
import { MainButton } from "@/app/components/common/mainBtn";
import StyledIcon from "@/app/components/common/styledIcon";
import AddPackagePopup from "./popup/AddPackagePopup";
import { useServices } from "@/app/hooks/counselor/useService";
import ThreeDotIconDropdown from "@/app/components/common/threeDotIconMenu";
import { useProfile } from "@/app/hooks/counselor/useProfile";

////////////////////////////////// MAIN COMPONENT ///////////////////////////
export default function Services() {
  const {
    services,
    fetchServices,
    fetchSingleService,
    deleteService,
    selectedService,
  } = useServices();
  const { userInfo } = useProfile();

  const [addServicePopup, setAddServicePopup] = useState(false);
  const [editServicePopup, setEditServicePopup] = useState(false);
  const [deleteServicePopup, setDeleteServicePopup] = useState(false);
  const [addPackagePopup, setAddPackagePopup] = useState(false);

  useEffect(() => {
    fetchServices(userInfo?.counselor_id);
  }, []);

  const currServiceItems =
    services?.map(
      ({ id, service_type }: { id: number; service_type: string }) => ({
        value: id,
        label: service_type,
      })
    ) || [];
  currServiceItems.unshift({ value: 0, label: "-- select --" });

  ////////////// function handles update service items
  const handleEditService = async (serviceId: number) => {
    await fetchSingleService(serviceId);
    setEditServicePopup(true);
  };

  ////////////// function handles delete service items
  const handleDeleteService = async (serviceId: number) => {
    await fetchSingleService(serviceId);
    setDeleteServicePopup(true);
  };

  // ////////////////////////////////// JSX ///////////////////////////
  return (
    <>
      <AddServicePopup
        isPopupOpen={addServicePopup}
        setIsPopupOpen={setAddServicePopup}
      />
      <EditServicePopup
        isPopupOpen={editServicePopup}
        setIsPopupOpen={setEditServicePopup}
        serviceData={selectedService}
      />
      <DeleteServicePopup
        isPopupOpen={deleteServicePopup}
        setIsPopupOpen={setDeleteServicePopup}
        serviceData={selectedService}
        onSave={async () => {
          if (selectedService) {
            await deleteService(selectedService?.id);
            setDeleteServicePopup(false);
            fetchServices(userInfo?.counselor_id);
          }
        }}
      />
      <AddPackagePopup
        isPopupOpen={addPackagePopup}
        setIsPopupOpen={setAddPackagePopup}
        services={currServiceItems}
      />

      <section className="border p-4 rounded-xl">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-semibold text-lg">Services</h3>
          <MainButton
            variant="neutral"
            onClick={() => setAddServicePopup(true)}
          >
            Add service
          </MainButton>
        </div>

        {/* Services Cards */}
        <ul className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {services.map((service) => (
            <li
              key={service.id}
              className="border p-4 rounded-xl flex flex-col justify-between bg-white"
            >
              <div>
                <div className="flex justify-between items-center mb-2">
                  <StyledIcon
                    icon={faBoxOpen}
                    bgColor="#F2C94C33"
                    color="#F2C94C"
                  />

                  <ThreeDotIconDropdown
                    handleEdit={() => handleEditService(service.id)}
                    handleRemove={() => handleDeleteService(service.id)}
                  />
                </div>
                <h3 className="text-neutral8 font-semibold mb-2">
                  {service.service_type}
                </h3>
                <p className="text-neutral5 text-sm mb-2">
                  {service.description}
                </p>
              </div>
              <div>
                <p className="text-sm text-neutral5 mb-1">Price</p>
                <p className="text-lg font-bold mb-4 text-neutral8">
                  ${service.price}
                </p>
              </div>
            </li>
          ))}
        </ul>
      </section>
    </>
  );
}
