"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { IconDefinition } from "@fortawesome/fontawesome-svg-core";
import {
  faEnvelope,
  faBirthdayCake,
  faMars,
  faPencil,
  faMapMarkedAlt,
  faTrash,
  faPlus,
} from "@fortawesome/free-solid-svg-icons";
import { useProfile } from "@/app/hooks/counselor/useProfile";
import { formatToLongDateString } from "@/app/utils/time-helpers";

// Custom components
import Overview from "./Overview";
import Services from "./Services";
import Packages from "./Packages";
import Education from "./Education";
import Experience from "./Experience";
import Documents from "./Documents";
import Reviews from "./Reviews";
import Image from "next/image";
import { useSessions } from "@/app/hooks/counselor/useSessions";
import { generatePlaceholder } from "@/app/utils/image";

interface InfoItemProps {
  icon: IconDefinition;
  value: string | undefined;
  label: string;
}

// Reusable Icon Component
const InfoItem = ({ icon, value, label }: InfoItemProps) => (
  <li className="flex gap-4 items-center">
    <span className="h-8 w-8 flex justify-center items-center bg-grayLight rounded-md">
      <FontAwesomeIcon icon={icon} />
    </span>
    <div>
      <span
        className={`block mb-1 ${label !== "Email Address" && "capitalize"}`}
      >
        {value || "N/A"}
      </span>
      <span className="block text-sm text-textLight">{label}</span>
    </div>
  </li>
);

////////////////////////////// MAIN COMPONENT ////////////////////////////
export default function ProfileView() {
  const {
    getPersonalInfo,
    personalInfo,
    userInfo,
    uploadProfilePicture,
    deleteProfilePicture,
    profilePicture,
    getUserInfo,
  } = useProfile();

  const { fetchSessions, sessions } = useSessions();

  const [activeTab, setActiveTab] = useState("overview");

  // Fetch all data on component mount
  useEffect(() => {
    getPersonalInfo();
  }, [userInfo]);

  useEffect(() => {
    getUserInfo();
  }, [profilePicture]);

  useEffect(() => {
    fetchSessions(Intl.DateTimeFormat().resolvedOptions().timeZone);
  }, []);

  const tabs = [
    "Overview",
    "Services",
    // "Packages",  // Temporarily disabled
    "Education",
    "Experience",
    "Documents",
    "Reviews",
  ];

  // Content rendering for active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case "overview":
        return <Overview personalInfo={personalInfo} sessions={sessions} />;
      case "services":
        return <Services />;
      // case "packages":
      //   return <Packages />;
      case "education":
        return <Education />;
      case "experience":
        return <Experience />;
      case "documents":
        return <Documents />;
      case "reviews":
        return <Reviews />;
      default:
        return <Overview personalInfo={personalInfo} sessions={sessions} />;
    }
  };

  ////////////////////////////////////// JSX /////////////////////////////////
  return (
    <div className="bg-white rounded-xl px-5 py-9">
      {/* Profile Header */}
      <div className="flex flex-col md:flex-row justify-between md:items-start mb-8">
        <div className="flex flex-col text-center md:text-left md:flex-row gap-4 items-center">
          <figure className="profile-picture mb-5 md:mb-0 relative h-20 w-20 lg:w-32 lg:h-32">
            <Image
              src={
                userInfo?.profile_picture_url ||
                generatePlaceholder(userInfo?.firstName, userInfo?.lastName)
              }
              alt="User Pic"
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              className="rounded-full mx-auto border border-mainClr h-full w-full object-cover object-top"
            />

            {!userInfo?.profile_picture_url ? (
              <>
                <label
                  htmlFor="file-input"
                  className="cursor-pointer bg-neutral1 absolute bottom-3 right-2 h-5 w-5 rounded-full flex justify-center items-center border border-mainClr"
                  aria-label="Upload Profile Picture"
                >
                  <FontAwesomeIcon icon={faPlus} className="w-2" />
                </label>
                <input
                  id="file-input"
                  type="file"
                  accept="image/png, image/jpeg, image/jpg, image/webp"
                  onChange={uploadProfilePicture}
                  className="hidden"
                />
              </>
            ) : (
              <button
                onClick={deleteProfilePicture}
                className="delete-btn absolute bottom-3 -right-2 lg:right-2 h-5 w-5 rounded-full bg-neutral1 flex justify-center items-center border"
                aria-label="Delete Profile Picture"
              >
                <FontAwesomeIcon icon={faTrash} className="w-2 text-red-400" />
              </button>
            )}
          </figure>

          <div>
            <h3 className="name text-xl flex items-center justify-center md:justify-start gap-5 mb-2">
              {personalInfo?.first_name} {personalInfo?.last_name}
            </h3>
            {/* <p className="mb-2">
              Our team will review your application and get back to you.
            </p> */}
            {/* <p className="text-sm text-textLight">Product Designer @ XYZ</p> */}
          </div>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 mx-auto md:mx-0 mt-6">
          {userInfo?.user_id && (
            <Link
              href={`/profile/${userInfo.user_id}`}
              className="bg-primary text-white p-3 px-8 rounded-lg hover:bg-primary/90 focus:ring-2 focus:ring-primary/20 whitespace-nowrap text-center"
              target="_blank"
              aria-label="View Public Profile"
            >
              View Public Profile
            </Link>
          )}
          <Link
            href="/counselor/dashboard/profile/edit"
            className="bg-neutral1 text-neutral10 p-3 px-8 rounded-lg hover:bg-neutral2 focus:ring-neutral3 whitespace-nowrap text-center"
            aria-label="Edit Profile"
          >
            Edit Profile
            <FontAwesomeIcon icon={faPencil} className="ml-2" />
          </Link>
        </div>
      </div>

      {/* User Data */}
      <ul className="user-data grid sm:grid-cols-2 lg:grid-cols-4 gap-6 p-5 border rounded-xl">
        <InfoItem
          icon={faEnvelope}
          value={userInfo?.email}
          label="Email Address"
        />
        <InfoItem
          icon={faMapMarkedAlt}
          value={personalInfo?.country_of_residence}
          label="Located in"
        />
        <InfoItem
          icon={faBirthdayCake}
          value={formatToLongDateString(personalInfo?.date_of_birth || "")}
          label="Date of Birth"
        />
        <InfoItem icon={faMars} value={personalInfo?.gender} label="Gender" />
      </ul>

      {/* Tab Navigation */}
      <div className="tabs mt-8">
        <div className="flex gap-6 border-b-2 mb-10 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab}
              className={`py-2 px-4 text-sm font-medium ${
                activeTab === tab.toLowerCase()
                  ? "border-b-2 border-blue-500 text-blue-500"
                  : "text-gray-500"
              }`}
              onClick={() => setActiveTab(tab.toLowerCase())}
              aria-label={`Go to ${tab} tab`}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="tab-content">{renderTabContent()}</div>
      </div>
    </div>
  );
}
