"use client";

import { useState } from "react";
import { toast } from "react-toastify";
import { InputField } from "@/app/components/common/inputField";
import { Textarea } from "@/app/components/common/textarea";
import Popup from "@/app/components/common/popup";
import useFormState from "@/app/hooks/useFormState";
import { PopupProps } from "@/app/types/counselor/profile";
import { MainButton } from "@/app/components/common/mainBtn";
import {
  faCheck,
  faClose,
  faTrash,
  faPlus,
} from "@fortawesome/free-solid-svg-icons";
import { usePackages } from "@/app/hooks/counselor/usePackage";
import Dropdown from "@/app/components/common/dropdown";
import { serviceItems } from "@/app/constants/counselor";
import { Input } from "@/app/components/ui/input";

interface InitialStateProps {
  title: string;
  description: string;
  total_price: number;
  items: { service_name: string; custom_type: string; hours: number }[];
}

const initialState = {
  title: "",
  description: "",
  total_price: 0,
  items: [{ service_name: "", custom_type: "", hours: 0 }],
};

export default function AddPackagePopup({
  isPopupOpen,
  setIsPopupOpen,
}: PopupProps) {
  console.log("AddPackagePopup rendered with isPopupOpen:", isPopupOpen);
  const { addPackage, fetchPackages } = usePackages();

  const {
    formData,
    setFormData,
    handleChange: baseHandleChange,
  } = useFormState<InitialStateProps>(initialState);

  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.title.trim()) {
      newErrors.title = "Package title is required.";
    }
    if (!formData.description.trim()) {
      newErrors.description = "Description is required.";
    }
    if (!formData.total_price || formData.total_price <= 0) {
      newErrors.total_price = "Price must be a positive number.";
    }

    // Validate all services
    const hasEmptyServices = formData.items.some(
      (item) => !item.service_name.trim() || item.hours <= 0
    );
    if (hasEmptyServices) {
      newErrors.services =
        "All service fields must be filled with valid values.";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name } = e.target;

    if (errors[name as keyof typeof errors]) {
      const newErrors = { ...errors, [name]: "" };
      setErrors(newErrors);
    }

    baseHandleChange(e);
  };

  const handleServiceChange = (
    index: number,
    field: "service_name" | "custom_type" | "hours",
    value: string | number
  ) => {
    const updatedServices = [...formData.items];
    updatedServices[index] = {
      ...updatedServices[index],
      [field]: value,
    };

    setFormData((prev) => ({
      ...prev,
      items: updatedServices,
    }));

    // Clear service-related errors
    if (errors.services) {
      setErrors((prev) => ({ ...prev, services: "" }));
    }
  };

  const addService = () => {
    // Check if all existing services are filled
    const hasEmptyFields = formData.items.some(
      (item) => !item.service_name.trim() || item.hours <= 0
    );

    if (hasEmptyFields) {
      toast.error("Please fill all existing service fields first");
      return;
    }

    setFormData((prev) => ({
      ...prev,
      items: [...prev.items, { service_name: "", custom_type: "", hours: 0 }],
    }));
  };

  const removeService = (index: number) => {
    if (formData.items.length === 1) {
      toast.error("At least one service is required");
      return;
    }

    const updateditems = formData.items.filter((_, i) => i !== index);
    setFormData((prev) => ({
      ...prev,
      items: updateditems,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const formattedData = {
      ...formData,
      items: formData.items.map((item) => ({
        service_name:
          item.service_name === "Other" ? item.custom_type : item.service_name,
        hours: item.hours,
      })),
    };

    await addPackage(formattedData);

    setIsPopupOpen(false);
    setFormData(initialState);
    fetchPackages();
  };

  // Get the list of selected services
  const selectedServices = formData.items.map((item) => item.service_name);

  // Filter out already selected services from the dropdown options
  const getFilteredOptions = (index: number) => {
    return serviceItems.filter(
      (item) =>
        !selectedServices.includes(item.value) ||
        formData.items[index].service_name === item.value
    );
  };

  // Log the props before rendering
  console.log("Before rendering Popup - isPopupOpen:", isPopupOpen);

  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={() => {
        console.log("Popup close button clicked");
        setIsPopupOpen(false);
      }}
      title="Add Package"
    >
      <form onSubmit={handleSubmit} className="max-w-full overflow-x-hidden">
        {formData.items.map((item, index) => (
          <div key={index} className="mb-6 p-4 bg-gray-50 rounded-lg">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 mb-4">
              <h3 className="text-lg font-medium">Service {index + 1}</h3>
              {formData.items.length > 1 && (
                <MainButton
                  type="button"
                  variant="secondary"
                  icon={faTrash}
                  onClick={() => removeService(index)}
                  className="px-2 py-1"
                  children="Remove"
                />
              )}
            </div>

            <div className="space-y-4">
              <Dropdown
                label="Service Name *"
                name="service_name"
                options={getFilteredOptions(index)}
                value={item.service_name}
                onChange={(e) =>
                  handleServiceChange(index, "service_name", e.target.value)
                }
                selectStyle="w-full border p-3 bg-neutral1"
              />

              {item.service_name === "Other" && (
                <InputField
                  label="Specify Other"
                  name="custom_type"
                  value={item.custom_type}
                  onChange={(e) =>
                    handleServiceChange(index, "custom_type", e.target.value)
                  }
                />
              )}

              <InputField
                label="Hours *"
                type="number"
                min="1"
                placeholder="Enter hours"
                value={item.hours === 0 ? "" : item.hours}
                onChange={(e) =>
                  handleServiceChange(
                    index,
                    "hours",
                    parseInt(e.target.value) || 0
                  )
                }
              />
            </div>
          </div>
        ))}

        {errors.services && (
          <p className="text-red-500 text-sm mb-4">{errors.services}</p>
        )}

        <MainButton
          type="button"
          variant="primary"
          icon={faPlus}
          onClick={addService}
          className="mb-6"
          children="Add Another Service"
        />

        <InputField
          label="Package Title *"
          type="text"
          placeholder="Eg. Essay review session for $200"
          name="title"
          value={formData.title}
          onChange={handleChange}
        />
        {errors.title && <p className="text-red-500 text-sm">{errors.title}</p>}

        <div className="my-4">
          <Textarea
            label="Description *"
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Enter here.."
            rows={5}
          />
          {errors.description && (
            <p className="text-red-500 text-sm">{errors.description}</p>
          )}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <InputField
              label="Price ($) *"
              type="number"
              placeholder="Enter price"
              name="total_price"
              value={formData.total_price || ""}
              min="0"
              onChange={(e) => {
                const value = e.target.valueAsNumber;
                setFormData({ ...formData, total_price: value });
                if (value > 0) {
                  setErrors((prev) => ({ ...prev, total_price: "" }));
                }
              }}
            />
            {errors.total_price && (
              <p className="text-red-500 text-sm">{errors.total_price}</p>
            )}
          </div>
        </div>

        <div className="flex justify-end space-x-4 mt-6">
          <MainButton
            type="button"
            variant="neutral"
            children="Cancel"
            icon={faClose}
            onClick={() => {
              setIsPopupOpen(false);
              setFormData(initialState);
              setErrors({});
            }}
          />
          <MainButton
            type="submit"
            variant="primary"
            children="Add"
            icon={faCheck}
          />
        </div>
      </form>
    </Popup>
  );
}
