"use client";

import Dropdown from "@/app/components/common/dropdown";
import { InputField } from "@/app/components/common/inputField";
import { Textarea } from "@/app/components/common/textarea";
import Popup from "@/app/components/common/popup";
import useFormState from "@/app/hooks/useFormState";
import { PopupProps } from "@/app/types/counselor/profile";
import { MainButton } from "@/app/components/common/mainBtn";
import { faCheck, faClose } from "@fortawesome/free-solid-svg-icons";
import { useServices } from "@/app/hooks/counselor/useService";
import { useProfile } from "@/app/hooks/counselor/useProfile";
import { toast } from "react-toastify";
import { useState } from "react";
import { serviceItems } from "@/app/constants/counselor";
import { serviceDescriptions } from "@/app/constants/serviceDescriptions";

const initialState = {
  service_type: "",
  custom_type: "",
  description: "",
  price: 0,
  offers_intro_call: false,
};

const initialErrorState = {
  service_type: "",
  custom_type: "",
  description: "",
  price: "",
};

export default function AddServicePopup({
  isPopupOpen,
  setIsPopupOpen,
}: PopupProps) {
  const { addService, fetchServices } = useServices();
  const { userInfo } = useProfile();

  const {
    formData,
    setFormData,
    handleChange: baseHandleChange,
  } = useFormState<{
    service_type: string;
    custom_type?: string;
    description: string;
    price: number | string;
    offers_intro_call: boolean;
  }>(initialState);

  const [errors, setErrors] = useState(initialErrorState);
  const [loading, setLoading] = useState(false);

  const validateForm = () => {
    const newErrors = { ...initialErrorState };
    let isValid = true;

    if (!formData.service_type) {
      newErrors.service_type = "Please select a service type.";
      isValid = false;
    }

    if (formData.service_type === "Other" && !formData.custom_type) {
      newErrors.custom_type = "Please enter a custom service type.";
      isValid = false;
    }

    if (!formData.description) {
      newErrors.description = "Description is required.";
      isValid = false;
    }

    if (
      formData.service_type !== "Free 15 Minutes Intro Call" &&
      typeof formData.price === "number" &&
      formData.price <= 0
    ) {
      newErrors.price = "Price must be greater than 0.";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;

    // Clear the specific error when the input becomes valid
    if (errors[name as keyof typeof errors]) {
      const newErrors = { ...errors, [name]: "" };
      setErrors(newErrors);
    }

    // If service_type changes, update the description and handle price
    if (name === "service_type") {
      const description =
        serviceDescriptions[value as keyof typeof serviceDescriptions] || "";

      if (value === "Free 15 Minutes Intro Call") {
        setFormData((prevState) => ({
          ...prevState,
          service_type: value,
          description: description,
          price: "Free",
        }));
      } else {
        setFormData((prevState) => ({
          ...prevState,
          service_type: value,
          description: description,
          price: prevState.price === "Free" ? 0 : prevState.price,
        }));
      }
    } else {
      baseHandleChange(e); // Call the original handleChange logic to update formData
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (loading) return;
    setLoading(true);
    if (!validateForm()) return;

    const requestData = {
      service_type: formData.service_type,
      description: formData.description,
      price:
        formData.service_type === "Free 15 Minutes Intro Call"
          ? 0
          : formData.price, // Set price to 0 if service_type is "Free 15 Minutes Intro Call"
      offers_intro_call: formData.offers_intro_call,
      ...(formData.service_type === "Other" && formData.custom_type
        ? { custom_type: formData.custom_type }
        : {}),
    };

    try {
      await addService(requestData);
      setFormData(initialState);
      setIsPopupOpen(false);
      fetchServices(userInfo?.counselor_id);
    } catch (error) {
      toast.error("Failed to add service. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  function handleCancel() {
    setIsPopupOpen(false);
    setFormData(initialState);
  }

  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={() => setIsPopupOpen(false)}
      title="Add Service"
    >
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <Dropdown
            label="Select Service *"
            name="service_type"
            options={serviceItems}
            value={formData.service_type}
            onChange={handleChange}
            selectStyle="w-full border p-3 bg-neutral1"
          />
          {errors.service_type && (
            <p className="text-red-500 text-sm mt-1">{errors.service_type}</p>
          )}
        </div>

        {formData.service_type === "Other" && (
          <InputField
            label="Specify Service Type *"
            type="text"
            placeholder="Enter custom service type"
            name="custom_type"
            value={formData.custom_type || ""}
            onChange={handleChange}
            className="mb-4"
          />
        )}
        {errors.custom_type && (
          <p className="text-red-500 text-sm mt-1">{errors.custom_type}</p>
        )}

        <div className="my-4">
          <Textarea
            label="Description *"
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Enter service description"
            rows={5}
          />
          {errors.description && (
            <p className="text-red-500 text-sm mt-1">{errors.description}</p>
          )}
        </div>

        <div className="mb-6">
          <InputField
            label="Price per hour ($) *"
            type={
              formData.service_type === "Free 15 Minutes Intro Call"
                ? "text"
                : "number"
            } // Change input type to "text" if service_type is "Free 15 Minutes Intro Call"
            placeholder={
              formData.service_type === "Free 15 Minutes Intro Call"
                ? "Free"
                : "Enter price"
            }
            name="price"
            value={
              formData.service_type === "Free 15 Minutes Intro Call"
                ? "Free"
                : formData.price || ""
            }
            onChange={handleChange}
            min="0"
            disabled={formData.service_type === "Free 15 Minutes Intro Call"} // Disable the input if service_type is "Free 15 Minutes Intro Call"
            className={
              formData.service_type === "Free 15 Minutes Intro Call"
                ? "bg-red-500"
                : ""
            }
          />
          {errors.price && (
            <p className="text-red-500 text-sm mt-1">{errors.price}</p>
          )}
        </div>

        <div className="flex justify-end space-x-4 mt-6">
          <MainButton
            type="button"
            variant="neutral"
            children="Cancel"
            icon={faClose}
            onClick={handleCancel}
          />

          <MainButton
            type="submit"
            variant="primary"
            children={loading ? "Adding..." : "Add"}
            icon={faCheck}
            disabled={loading}
          />
        </div>
      </form>
    </Popup>
  );
}
