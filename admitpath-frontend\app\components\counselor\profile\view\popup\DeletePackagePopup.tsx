import { MainButton } from "@/app/components/common/mainBtn";
import Popup from "@/app/components/common/popup";
import { PackageData } from "@/app/types/counselor/package";
import { PopupProps } from "@/app/types/counselor/profile";
import { faCheck, faClose } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

interface DeleteProps extends Omit<PopupProps, "packageData"> {
  packageData: PackageData | null;
}

export default function DeletePackagePopup({
  isPopupOpen,
  setIsPopupOpen,
  packageData,
  onSave,
}: DeleteProps) {
  const handleRemove = async () => {
    if (onSave) {
      await onSave();
      setIsPopupOpen(false);
    }
  };

  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={() => setIsPopupOpen(false)}
      title="Remove Package"
      height="auto"
    >
      <div className="mx-4 bg-neutral1 py-2 px-4 rounded-md mb-4">
        <h3 className="text-neutral8">Do you want to remove this package</h3>

        <p className="text-neutral5 text-sm">
          This service will be permanently removed from your profile.
        </p>
      </div>

      <hr />

      <div className="p-4">
        {/* <h2 className="mb-3 text-lg">{packageData.service_type}</h2> */}
        <p className="text-neutral8">{packageData?.title}</p>
        <p className="text-sm mb-3">{packageData?.description}</p>
        <p className="mb-1 text-neutral5">Price</p>
        <p className="text-3xl">${packageData?.total_price}</p>
      </div>

      <hr />

      <div className="flex justify-end gap-3 p-4">
        <MainButton variant="neutral" onClick={() => setIsPopupOpen(false)}>
          Cancel <FontAwesomeIcon icon={faClose} />
        </MainButton>

        <MainButton variant="secondary" onClick={handleRemove}>
          Remove <FontAwesomeIcon icon={faCheck} />
        </MainButton>
      </div>
    </Popup>
  );
}
