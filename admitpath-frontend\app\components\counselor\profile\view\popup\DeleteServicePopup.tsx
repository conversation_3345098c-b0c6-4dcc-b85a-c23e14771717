import { MainButton } from "@/app/components/common/mainBtn";
import Popup from "@/app/components/common/popup";
import { PopupProps } from "@/app/types/counselor/profile";
import { faCheck, faClose } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

export default function DeleteServicePopup({
  isPopupOpen,
  setIsPopupOpen,
  setSuccessPopup,
  serviceData,
  onSave,
}: PopupProps) {
  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={() => setIsPopupOpen(false)}
      title="Remove Service"
      height="auto"
    >
      <div className="mx-4 bg-neutral1 py-2 px-4 rounded-md mb-4">
        <h3 className="text-neutral8">Do you want to remove this service</h3>
        <p className="text-neutral5 text-sm">
          This service will be permanently removed from your profile.
        </p>
      </div>

      <hr />

      <div className="p-4">
        <h2 className="mb-3">
          {" "}
          {serviceData?.custom_type || serviceData?.service_type}
        </h2>
        <p className="text-sm mb-3"> {serviceData?.description}</p>
        <p className="mb-1 text-neutral5">Price</p>
        <p className="text-3xl">${serviceData?.price}</p>
      </div>

      <hr />

      <div className="flex justify-end gap-3 p-4">
        <MainButton variant="neutral" onClick={() => setIsPopupOpen(false)}>
          Cancel <FontAwesomeIcon icon={faClose} />
        </MainButton>

        <MainButton variant="secondary" onClick={onSave}>
          Remove <FontAwesomeIcon icon={faCheck} />
        </MainButton>
      </div>
    </Popup>
  );
}
