"use client";

import { useState, useEffect } from "react";
import { InputField } from "@/app/components/common/inputField";
import { Textarea } from "@/app/components/common/textarea";
import Popup from "@/app/components/common/popup";
import useFormState from "@/app/hooks/useFormState";
import { PopupProps } from "@/app/types/counselor/profile";
import { MainButton } from "@/app/components/common/mainBtn";
import { faClose } from "@fortawesome/free-solid-svg-icons";
import { usePackages } from "@/app/hooks/counselor/usePackage";
import { PackageData } from "@/app/types/counselor/package";
import Dropdown from "@/app/components/common/dropdown";
import { useServices } from "@/app/hooks/counselor/useService";
import { useProfile } from "@/app/hooks/counselor/useProfile";
import { toast } from "react-toastify";
import { Spinner } from "@/app/components/ui/spinner";

interface InitialStateProps {
  title: string;
  description: string;
  total_price: number;
  items: { service_name: string; custom_type: string; hours: number }[];
}

const initialState = {
  title: "",
  description: "",
  total_price: 0,
  items: [{ service_name: "", custom_type: "", hours: 0 }],
};

interface EditProps extends Omit<PopupProps, "packageData"> {
  packageData: PackageData | null;
}

export default function EditPackagePopup({
  isPopupOpen,
  setIsPopupOpen,
  packageData,
}: EditProps) {
  const { updatePackage, fetchPackages } = usePackages();
  const { services, fetchServices } = useServices();
  const { userInfo } = useProfile();

  const {
    formData,
    setFormData,
    handleChange: baseHandleChange,
  } = useFormState<InitialStateProps>(initialState);

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [counselorServices, setCounselorServices] = useState<
    { value: string; label: string }[]
  >([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch counselor services when the dialog opens
  useEffect(() => {
    if (isPopupOpen && userInfo?.counselor_id) {
      fetchServices(userInfo.counselor_id);
    }
  }, [isPopupOpen, userInfo, fetchServices]);

  // Convert services to dropdown options
  useEffect(() => {
    if (services.length > 0) {
      const serviceOptions = [
        { value: "", label: "Please select" },
        ...services.map((service) => ({
          value: service.custom_type || service.service_type,
          label: service.custom_type || service.service_type,
        })),
      ];
      setCounselorServices(serviceOptions);
    }
  }, [services]);

  useEffect(() => {
    if (packageData) {
      setFormData({
        title: packageData.title,
        description: packageData.description,
        total_price: packageData.total_price,
        items: packageData.package_items.map((item) => ({
          service_name: item.service_name,
          custom_type: "", // This field is not returned from the API
          hours: item.hours,
        })),
      });
    }
  }, [packageData, setFormData]);

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.title.trim()) {
      newErrors.title = "Package title is required.";
    }
    if (!formData.description.trim()) {
      newErrors.description = "Description is required.";
    }
    if (!formData.total_price || formData.total_price <= 0) {
      newErrors.total_price = "Price must be a positive number.";
    }

    // Validate services - must have at least one valid service
    if (formData.items.length === 0) {
      newErrors.services = "At least one service is required.";
    } else {
      // Check if all provided services have valid data
      const hasEmptyServices = formData.items.some(
        (item) => !item.service_name.trim() || item.hours <= 0
      );
      if (hasEmptyServices) {
        newErrors.services =
          "All service fields must be filled with valid values.";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name } = e.target;

    if (errors[name as keyof typeof errors]) {
      const newErrors = { ...errors, [name]: "" };
      setErrors(newErrors);
    }

    baseHandleChange(e);
  };

  const handleServiceChange = (
    index: number,
    field: "service_name" | "custom_type" | "hours",
    value: string | number
  ) => {
    const updatedServices = [...formData.items];
    updatedServices[index] = {
      ...updatedServices[index],
      [field]: value,
    };

    setFormData((prev) => ({
      ...prev,
      items: updatedServices,
    }));

    // Clear service-related errors
    if (errors.items) {
      setErrors((prev) => ({ ...prev, items: "" }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    toast.info("Updating package...");

    try {
      const formattedData = {
        ...formData,
        items: formData.items.map((item) => ({
          service_name: item.service_name,
          hours: item.hours,
        })),
      };

      await updatePackage(packageData?.id, formattedData);
      setIsPopupOpen(false);
      setFormData(initialState);
      fetchPackages();
    } catch (error) {
      console.error("Error updating package:", error);
      toast.error("Failed to update package. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get the list of selected services
  const selectedServices = formData.items.map((item) => item.service_name);

  // Filter out already selected services from the dropdown options
  const getFilteredOptions = (index: number) => {
    return counselorServices.filter(
      (item: { value: string; label: string }) =>
        item.value === "" || // Always include the placeholder
        !selectedServices.includes(item.value) ||
        formData.items[index].service_name === item.value
    );
  };

  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={() => setIsPopupOpen(false)}
      title="Update Package"
    >
      <form onSubmit={handleSubmit} className="max-w-full overflow-x-hidden">
        <p className="text-sm text-gray-500 mb-4">
          Fields marked with * are required
        </p>
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Services *</h3>
            <button
              type="button"
              className="px-3 py-1 bg-blue-50 text-blue-600 rounded-md text-sm font-medium hover:bg-blue-100 transition-colors"
              onClick={() => {
                setFormData((prev) => ({
                  ...prev,
                  items: [
                    ...prev.items,
                    { service_name: "", custom_type: "", hours: 0 },
                  ],
                }));
              }}
            >
              + Add Service
            </button>
          </div>

          {formData.items.map((item, index) => (
            <div
              key={index}
              className="mb-6 p-4 bg-gray-50 rounded-lg relative"
            >
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 mb-4">
                <h3 className="text-lg font-medium">Service {index + 1}</h3>
                {formData.items.length > 1 && (
                  <button
                    type="button"
                    className="text-red-500 hover:text-red-700 transition-colors mt-2 sm:mt-0"
                    onClick={() => {
                      const updatedItems = [...formData.items];
                      updatedItems.splice(index, 1);
                      setFormData((prev) => ({ ...prev, items: updatedItems }));
                    }}
                  >
                    Remove
                  </button>
                )}
              </div>

              <div className="space-y-4">
                <Dropdown
                  label="Service Name *"
                  name="service_name"
                  options={getFilteredOptions(index)}
                  value={item.service_name}
                  onChange={(e) =>
                    handleServiceChange(index, "service_name", e.target.value)
                  }
                  selectStyle="w-full border p-3 bg-neutral1"
                />

                {item.service_name === "Other" && (
                  <InputField
                    label="Specify Other"
                    name="custom_type"
                    value={item.custom_type}
                    onChange={(e) =>
                      handleServiceChange(index, "custom_type", e.target.value)
                    }
                  />
                )}

                <InputField
                  label="Hours *"
                  type="number"
                  min="1"
                  placeholder="Enter hours"
                  value={item.hours === 0 ? "" : item.hours}
                  onChange={(e) =>
                    handleServiceChange(
                      index,
                      "hours",
                      parseInt(e.target.value) || 0
                    )
                  }
                />
              </div>
            </div>
          ))}
        </div>

        {errors.services && (
          <p className="text-red-500 text-sm mb-4">{errors.services}</p>
        )}

        <InputField
          label="Package Title *"
          type="text"
          placeholder="Eg. Essay review session for $200"
          name="title"
          value={formData.title}
          onChange={handleChange}
        />
        {errors.title && <p className="text-red-500 text-sm">{errors.title}</p>}

        <div className="my-4">
          <Textarea
            label="Description *"
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Enter here.."
            rows={5}
          />
          {errors.description && (
            <p className="text-red-500 text-sm">{errors.description}</p>
          )}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <InputField
              label="Price ($) *"
              type="number"
              placeholder="Enter price"
              name="total_price"
              value={formData.total_price.toString()}
              min="0"
              onChange={(e) => {
                const value = parseFloat(e.target.value);
                setFormData({ ...formData, total_price: value });
                if (value > 0) {
                  setErrors((prev) => ({ ...prev, total_price: "" }));
                }
              }}
            />
            {errors.total_price && (
              <p className="text-red-500 text-sm">{errors.total_price}</p>
            )}
          </div>
        </div>

        <div className="flex justify-end space-x-4 mt-6">
          <MainButton
            type="button"
            variant="neutral"
            children="Cancel"
            icon={faClose}
            onClick={() => {
              setIsPopupOpen(false);
              setFormData(initialState);
              setErrors({});
            }}
            disabled={isSubmitting}
          />
          <MainButton
            type="submit"
            variant="primary"
            disabled={isSubmitting}
            className="min-w-[100px]"
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <Spinner size="sm" className="mr-2" /> Updating...
              </div>
            ) : (
              "Update"
            )}
          </MainButton>
        </div>
      </form>
    </Popup>
  );
}
