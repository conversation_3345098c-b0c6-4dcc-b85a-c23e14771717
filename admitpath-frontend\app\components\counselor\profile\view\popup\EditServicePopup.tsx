import { useState, useEffect } from "react";

import Dropdown from "@/app/components/common/dropdown";
import { InputField } from "@/app/components/common/inputField";
import { Textarea } from "@/app/components/common/textarea";
import Popup from "@/app/components/common/popup";
import useFormState from "@/app/hooks/useFormState";
import { PopupProps } from "@/app/types/counselor/profile";
import { MainButton } from "@/app/components/common/mainBtn";
import { faCheck, faClose } from "@fortawesome/free-solid-svg-icons";
import { useServices } from "@/app/hooks/counselor/useService";
import { serviceItems } from "@/app/constants/counselor";

const initialErrorState = {
  service_type: "",
  custom_type: "",
  description: "",
  price: "",
};

/////////////////////////// MAIN COMPONENT //////////////////////
export default function EditServicePopup({
  isPopupOpen,
  setIsPopupOpen,
  serviceData,
}: PopupProps) {
  const { updateService } = useServices();

  const {
    formData,
    setFormData,
    handleChange: baseHandleChange,
  } = useFormState<{
    service_type: string;
    custom_type?: string | null;
    description: string;
    price: number | string;
    offers_intro_call?: boolean;
  }>({
    service_type: "",
    custom_type: "",
    description: "",
    price: 0,
    offers_intro_call: false,
  });

  const [errors, setErrors] = useState(initialErrorState);

  useEffect(() => {
    if (serviceData) {
      setFormData({
        service_type: serviceData.service_type,
        description: serviceData.description,
        price: serviceData.price,
        custom_type: serviceData.custom_type || null,
        offers_intro_call: serviceData.offers_intro_call,
      });
    }
  }, [serviceData]);

  const validateForm = () => {
    const newErrors = { ...initialErrorState };
    let isValid = true;

    if (!formData.service_type) {
      newErrors.service_type = "Please select a service type.";
      isValid = false;
    }

    if (formData.service_type === "Other" && !formData.custom_type) {
      newErrors.custom_type = "Please enter a custom service type.";
      isValid = false;
    }

    if (!formData.description) {
      newErrors.description = "Description is required.";
      isValid = false;
    }

    if (
      formData.service_type !== "Free 15 Minutes Intro Call" &&
      typeof formData.price === "number" &&
      formData.price <= 0
    ) {
      newErrors.price = "Price must be greater than 0.";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;

    // Clear the specific error when the input becomes valid
    if (errors[name as keyof typeof errors]) {
      const newErrors = { ...errors, [name]: "" };
      setErrors(newErrors);
    }

    // Handle switching between service_type and custom_type
    if (name === "service_type") {
      // If the user selects "Free 15 Minutes Intro Call", set price to "Free"
      if (value === "Free 15 Minutes Intro Call") {
        setFormData((prev) => ({
          ...prev,
          service_type: value,
          price: "Free", // Set price to "Free"
        }));
      } else {
        // If the user selects a predefined service type, clear the custom_type
        setFormData((prev) => ({
          ...prev,
          service_type: value,
          custom_type: value === "Other" ? prev.custom_type : null, // Clear custom_type if not "Other"
          price: prev.price === "Free" ? 0 : prev.price, // Reset price if service_type changes
        }));
      }
    } else if (name === "custom_type") {
      // If the user enters a custom type
      setFormData((prev) => ({
        ...prev,
        custom_type: value,
        service_type: "Other", // Set service_type to "Other"
      }));
    } else {
      // For all other fields, use the base handleChange logic
      baseHandleChange(e);
    }
  };

  // Handle form submission
  const handleUpdate = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!serviceData) return; // If serviceData is undefined, exit early
    if (!validateForm()) return;

    // Convert price to number before sending the request
    const updatedData = {
      ...formData,
      price:
        formData.service_type === "Free 15 Minutes Intro Call"
          ? 0
          : formData.price,
    };

    await updateService(serviceData.id, updatedData);
    setIsPopupOpen(false);
  };

  //////////////////////////// JSX /////////////////////////////
  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={() => setIsPopupOpen(false)}
      title="Edit Service"
      height="80vh"
    >
      <form>
        <div className="mb-4">
          <Dropdown
            label="Select Service"
            name="service_type"
            options={serviceItems} // Corrected to match the expected format
            value={formData.service_type}
            onChange={handleChange}
            required={true}
            selectStyle="w-full border p-3 bg-neutral1"
          />
          {errors.service_type && (
            <p className="text-red-500 text-sm mt-1">{errors.service_type}</p>
          )}
        </div>

        {/* Conditional input for custom type */}
        {formData.service_type === "Other" && (
          <InputField
            label="Update Service Type"
            required={true}
            type="text"
            placeholder="Enter custom service type"
            name="custom_type"
            value={formData.custom_type || ""}
            onChange={handleChange}
          />
        )}
        {errors.custom_type && (
          <p className="text-red-500 text-sm mt-1">{errors.custom_type}</p>
        )}

        <div className="my-4">
          <Textarea
            label="Description"
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Enter service description"
            required={true}
            rows={5}
          />
          {errors.description && (
            <p className="text-red-500 text-sm mt-1">{errors.description}</p>
          )}
        </div>

        <div className="mb-4">
          <InputField
            label="Price per hour ($)"
            required={true}
            type={
              formData.service_type === "Free 15 Minutes Intro Call"
                ? "text"
                : "number"
            } // Change input type to "text" if service_type is "Free 15 Minutes Intro Call"
            placeholder={
              formData.service_type === "Free 15 Minutes Intro Call"
                ? "Free"
                : "Enter price"
            }
            name="price"
            value={
              formData.service_type === "Free 15 Minutes Intro Call"
                ? "Free"
                : formData.price
            }
            onChange={handleChange}
            min="0"
            disabled={formData.service_type === "Free 15 Minutes Intro Call"} // Disable the input if service_type is "Free 15 Minutes Intro Call"
          />
          {errors.price && (
            <p className="text-red-500 text-sm mt-1">{errors.price}</p>
          )}
        </div>

        <div className="flex justify-end space-x-4 mt-6">
          <MainButton
            type="button"
            variant="neutral"
            children="Discard"
            icon={faClose}
            onClick={() => setIsPopupOpen(false)}
          />

          <MainButton
            type="submit"
            variant="primary"
            children="Save"
            icon={faCheck}
            onClick={handleUpdate}
          />
        </div>
      </form>
    </Popup>
  );
}
