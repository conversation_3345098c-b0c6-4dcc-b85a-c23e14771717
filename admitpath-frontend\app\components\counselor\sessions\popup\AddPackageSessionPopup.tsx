"use client";

import { useState, useEffect } from "react";
import { InputField } from "@/app/components/common/inputField";
import Popup from "@/app/components/common/popup";
import { PopupProps } from "@/app/types/counselor/profile";
import { MainButton } from "@/app/components/common/mainBtn";
import { faCheck, faClose } from "@fortawesome/free-solid-svg-icons";
import { formatDateISO, formatTimeToISO } from "@/app/utils/time-helpers";
import { usePackages } from "@/app/hooks/counselor/usePackage";
import { useServices } from "@/app/hooks/counselor/useService";
import { useProfile } from "@/app/hooks/counselor/useProfile";
import { toast } from "react-toastify";
import {
  PackageSessionPayload,
  PackageSubscription,
} from "@/app/types/counselor/package";

const initialState: PackageSessionPayload = {
  service_name: "",
  date: "",
  start_time: "",
  end_time: "",
};

const initialErrorState = {
  subscription_id: "",
  service_name: "",
  date: "",
  start_time: "",
  end_time: "",
};

export default function AddPackageSessionPopup({
  isPopupOpen,
  setIsPopupOpen,
}: PopupProps) {
  const { fetchSubscriptions, subscriptions, createPackageSession, loading } =
    usePackages();
  const { services, fetchServices } = useServices();
  const { userInfo } = useProfile();

  const [formData, setFormData] = useState(initialState);
  const [errors, setErrors] = useState(initialErrorState);
  const [selectedSubscription, setSelectedSubscription] = useState<string>("");
  const [activeSubscriptions, setActiveSubscriptions] = useState<
    PackageSubscription[]
  >([]);
  const [availableServices, setAvailableServices] = useState<string[]>([]);

  useEffect(() => {
    if (isPopupOpen && userInfo?.counselor_id) {
      fetchSubscriptions();
      fetchServices(userInfo.counselor_id);
    }
  }, [isPopupOpen, userInfo, fetchSubscriptions, fetchServices]);

  useEffect(() => {
    if (subscriptions.length > 0) {
      console.log(subscriptions);
      const active = subscriptions.filter((sub) => sub.status === "active");
      setActiveSubscriptions(active);
    }
  }, [subscriptions]);

  useEffect(() => {
    updateAvailableServices();
  }, [selectedSubscription, subscriptions, services]);

  const updateAvailableServices = () => {
    if (!selectedSubscription) {
      setAvailableServices([]);
      return;
    }

    const subscription = subscriptions.find(
      (sub) => String(sub.id) === selectedSubscription
    );

    if (!subscription) {
      setAvailableServices([]);
      return;
    }

    if (
      !subscription.service_hours ||
      Object.keys(subscription.service_hours).length === 0
    ) {
      const serviceList = services.map(
        (service) => service.custom_type || service.service_type
      );
      setAvailableServices(serviceList);
      return;
    }

    const serviceList = Object.entries(subscription.service_hours)
      .filter(([_, hours]) => hours.total > hours.used)
      .map(([serviceName]) => serviceName);

    setAvailableServices(serviceList);
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;

    if (name === "subscription_id") {
      setSelectedSubscription(value);
      setFormData((prev) => ({ ...prev, service_name: "" }));
      setErrors((prev) => ({
        ...prev,
        subscription_id: "",
        service_name: "",
      }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }

    if (name === "start_time" && value) {
      try {
        const startTime = new Date(`1970-01-01T${value}:00`);
        startTime.setHours(startTime.getHours() + 1);
        const endTime = startTime.toTimeString().slice(0, 5);
        setFormData((prev) => ({ ...prev, end_time: endTime }));
      } catch (error) {}
    }
  };

  const validateForm = () => {
    const newErrors = { ...initialErrorState };
    let isValid = true;

    if (!selectedSubscription) {
      newErrors.subscription_id = "Please select a package subscription";
      isValid = false;
    }

    if (!formData.service_name) {
      newErrors.service_name = "Please select a service";
      isValid = false;
    }

    if (!formData.date) {
      newErrors.date = "Please select a date";
      isValid = false;
    } else {
      const selectedDate = new Date(formData.date);
      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0);

      if (selectedDate < currentDate) {
        newErrors.date = "Date cannot be in the past";
        isValid = false;
      }
    }

    if (!formData.start_time) {
      newErrors.start_time = "Please select a start time";
      isValid = false;
    }

    if (!formData.end_time) {
      newErrors.end_time = "Please select an end time";
      isValid = false;
    }

    if (
      formData.start_time &&
      formData.end_time &&
      formData.start_time >= formData.end_time
    ) {
      newErrors.end_time = "End time must be after start time";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !selectedSubscription) {
      return;
    }

    try {
      const subscriptionId = parseInt(selectedSubscription, 10);

      if (isNaN(subscriptionId)) {
        toast.error("Invalid subscription ID");
        return;
      }

      const formattedDate = formatDateISO(formData.date);

      const startTimeISO = formatTimeToISO(formData.date, formData.start_time);
      const endTimeISO = formatTimeToISO(formData.date, formData.end_time);

      console.log("Formatted times:", {
        date: formattedDate,
        start_time: startTimeISO,
        end_time: endTimeISO,
      });

      const sessionData = {
        service_name: formData.service_name,
        date: formattedDate,
        start_time: startTimeISO,
        end_time: endTimeISO,
      };

      await createPackageSession(subscriptionId, sessionData);
      handleClose();
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message || "Failed to create package session";
      toast.error(errorMessage);
      console.error("Error creating package session:", error);
    }
  };

  const handleClose = () => {
    setFormData(initialState);
    setErrors(initialErrorState);
    setSelectedSubscription("");
    setAvailableServices([]);
    setIsPopupOpen(false);
  };

  const getTodayFormatted = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const day = String(today.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  const renderError = (error: string) =>
    error && <p className="text-red-500 text-sm mt-1">{error}</p>;

  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={handleClose}
      title="Add Package Session"
    >
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">
            Package Subscription <span className="text-red-500">*</span>
          </label>
          <select
            name="subscription_id"
            value={selectedSubscription}
            onChange={handleChange}
            className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            aria-label="Select package subscription"
          >
            <option value="">Select a package subscription</option>
            {activeSubscriptions.length > 0 ? (
              activeSubscriptions.map((subscription) => (
                <option
                  key={String(subscription.id)}
                  value={String(subscription.id)}
                >
                  {subscription.student.name} - {subscription.package.title}
                </option>
              ))
            ) : (
              <option value="" disabled>
                No active subscriptions available
              </option>
            )}
          </select>
          {renderError(errors.subscription_id)}
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">
            Service <span className="text-red-500">*</span>
          </label>
          <select
            name="service_name"
            value={formData.service_name}
            onChange={handleChange}
            className="w-full p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={!selectedSubscription}
            aria-label="Select service"
          >
            <option value="">Select a service</option>
            {availableServices.length > 0 ? (
              availableServices.map((service) => (
                <option key={service} value={service}>
                  {service}
                </option>
              ))
            ) : (
              <option value="" disabled>
                {selectedSubscription
                  ? "No available services for this subscription"
                  : "Select a subscription first"}
              </option>
            )}
          </select>
          {renderError(errors.service_name)}
        </div>

        <div className="mb-4">
          <InputField
            label="Date *"
            type="date"
            name="date"
            value={formData.date}
            onChange={handleChange}
            min={getTodayFormatted()}
            aria-label="Select session date"
          />
          {renderError(errors.date)}
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <InputField
              label="Start Time *"
              type="time"
              name="start_time"
              value={formData.start_time}
              onChange={handleChange}
              aria-label="Select start time"
            />
            {renderError(errors.start_time)}
          </div>
          <div>
            <InputField
              label="End Time *"
              type="time"
              name="end_time"
              value={formData.end_time}
              onChange={handleChange}
              aria-label="Select end time"
            />
            {renderError(errors.end_time)}
          </div>
        </div>

        <div className="flex justify-end space-x-4 pt-4">
          <MainButton
            type="button"
            variant="neutral"
            icon={faClose}
            onClick={handleClose}
            aria-label="Cancel"
          >
            Cancel
          </MainButton>
          <MainButton
            type="submit"
            variant="primary"
            icon={faCheck}
            disabled={loading}
            aria-label="Create session"
          >
            {loading ? "Creating..." : "Create Session"}
          </MainButton>
        </div>
      </form>
    </Popup>
  );
}
