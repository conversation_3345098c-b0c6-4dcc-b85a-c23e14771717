"use client";

import { MainButton } from "@/app/components/common/mainBtn";
import Popup from "@/app/components/common/popup";
import { PopupProps } from "@/app/types/counselor/profile";
import { Session } from "@/app/types/counselor/sessions";
import { formatTimeForInput } from "@/app/utils/time-helpers";
import { faCheck, faClose } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { Calendar, Clock, Mail } from "lucide-react";

interface CancelProps extends PopupProps {
  sessionData: Session | null;
}

/////////////////////////////////// MAIN COMPONENT /////////////////////////////////
export default function CancelSessionPopup({
  isPopupOpen,
  setIsPopupOpen,
  sessionData,
  onSave,
}: CancelProps) {
  const formatTime = (start: string, end: string) => {
    const startTime = new Date(start).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
    const endTime = new Date(end).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
    return `${startTime} - ${endTime}`;
  };

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric",
    });
  };

  ///////////////////////////////// JSX //////////////////////////////////////
  return (
    <Popup
      isOpen={isPopupOpen}
      onClose={() => setIsPopupOpen(false)}
      title="Cancel Session"
      height="auto"
    >
      <div className="mx-4 bg-neutral1 py-2 px-4 rounded-md mb-4">
        <h2 className="text-neutral8 mb-2">
          Do you want to cancel this session
        </h2>
        <p className="text-neutral5 text-sm">
          This session will be permanently cancelled.
        </p>
      </div>

      <hr />

      <div className="p-4">
        <h2 className="mb-3"> {sessionData?.event_name}</h2>
      </div>

      {sessionData && (
        <>
          <div className="flex md:flex-row flex-col md:items-center items-start gap-6 sm:px-6 p-4 sm:py-5 gap-y-2 border-b-2 text-gray-600 text-md font-medium">
            <div className="flex items-center gap-2">
              <Calendar className="w-6 h-6" />
              <span>{formatDate(sessionData.date)}</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-6 h-6" />
              <span>
                {formatTime(sessionData.start_time, sessionData.end_time)}{" "}
                (GMT+05:00)
              </span>
            </div>
          </div>
       
        </>
      )}

      <hr />

      <div className="flex justify-end gap-3 p-4">
        <MainButton variant="neutral" onClick={() => setIsPopupOpen(false)}>
          Close <FontAwesomeIcon icon={faClose} />
        </MainButton>

        <MainButton variant="secondary" onClick={onSave}>
          Cancel <FontAwesomeIcon icon={faCheck} />
        </MainButton>
      </div>
    </Popup>
  );
}
