"use client";

import { InputField } from "@/app/components/common/inputField";
import Popup from "@/app/components/common/popup";
import { PopupProps } from "@/app/types/counselor/profile";
import { MainButton } from "@/app/components/common/mainBtn";
import { faCheck, faClose } from "@fortawesome/free-solid-svg-icons";
import { useEffect, useState } from "react";
import {
  formatDateISO,
  formatNormalDate,
  formatTimeForInput,
  formatTimeToISO,
} from "@/app/utils/time-helpers";
import { useSessions } from "@/app/hooks/counselor/useSessions";
import {
  Session,
  SessionNotesData,
  UpdateSession,
} from "@/app/types/counselor/sessions";
import TextAreaLimit from "@/app/components/common/TeaxAreaLimit";

const initialState = {
  date: "",
  start_time: "",
  end_time: "",
};

const initialErrorState = {
  date: "",
  start_time: "",
  end_time: "",
};

interface UpdateProps extends PopupProps {
  sessionData: Session | null;
  currentSessionNotes: SessionNotesData | null;
}

export default function UpdateSessionPopup({
  isPopupOpen,
  setIsPopupOpen,
  sessionData,
  currentSessionNotes,
}: UpdateProps) {
  const { updateSession, updateSessionNotes } = useSessions();
  const [formData, setFormData] = useState<UpdateSession>(initialState);
  const [errors, setErrors] = useState(initialErrorState);
  const [activeTab, setActiveTab] = useState<"details" | "notes">("details");
  const [sessionNotes, setSessionNotes] = useState("");
  const [isSaving, setIsSaving] = useState(false);

  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    if (sessionData) {
      setIsInitializing(true);

      setTimeout(() => {
        setFormData({
          date: formatNormalDate(sessionData.date),
          start_time: formatTimeForInput(sessionData.start_time),
          end_time: formatTimeForInput(sessionData.end_time),
        });

        if (
          currentSessionNotes &&
          currentSessionNotes.session_id === sessionData.id
        ) {
          setSessionNotes(currentSessionNotes.notes || "");
        } else {
          setSessionNotes("");
        }

        setIsInitializing(false);
      }, 100);
    }
  }, [sessionData, currentSessionNotes]);

  const handleTabSwitch = (tab: "details" | "notes") => {
    setActiveTab(tab);
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;

    const key = name as keyof typeof errors;

    setFormData((prevState) => ({
      ...prevState,
      [key]: value,
    }));

    if (errors[key]) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [key]: "",
      }));
    }
  };

  const validateForm = () => {
    const newErrors = { ...initialErrorState };
    let isValid = true;

    // Check required fields
    if (!formData.date) {
      newErrors.date = "Date is required.";
      isValid = false;
    }

    if (!formData.start_time) {
      newErrors.start_time = "Start time is required.";
      isValid = false;
    }

    if (!formData.end_time) {
      newErrors.end_time = "End time is required.";
      isValid = false;
    }

    if (formData.date) {
      const selectedDate = new Date(formData.date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (selectedDate < today) {
        newErrors.date = "Date cannot be in the past.";
        isValid = false;
      }
    }

    if (formData.start_time && formData.end_time) {
      if (formData.start_time >= formData.end_time) {
        newErrors.end_time = "End time must be after start time.";
        isValid = false;
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      setIsSaving(true);

      const updatedData = {
        ...formData,
        // Preserve the original event_name
        event_name: sessionData?.event_name || "",
        date: formatDateISO(formData.date),
        start_time: formatTimeToISO(formData.date, formData.start_time),
        end_time: formatTimeToISO(formData.date, formData.end_time),
      };

      if (sessionData) {
        await updateSession(
          sessionData.id,
          updatedData,
          Intl.DateTimeFormat().resolvedOptions().timeZone
        );
      }

      if (currentSessionNotes && sessionData) {
        if (sessionNotes !== currentSessionNotes.notes) {
          await updateSessionNotes(sessionData.id, { notes: sessionNotes });
        }
      }

      setIsPopupOpen(false);
    } catch (error) {
      console.error("Error updating session:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setSessionNotes("");
    setErrors(initialErrorState);
    setIsPopupOpen(false);
  };

  return (
    <Popup isOpen={isPopupOpen} onClose={handleCancel} title="Update Session">
      {isInitializing ? (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
          <p className="text-gray-600">Loading session data...</p>
        </div>
      ) : (
        <>
          <div className="border-b flex mb-6">
            <button
              className={`px-4 py-2 ${
                activeTab === "details"
                  ? "border-b-2 border-blue-500 font-bold"
                  : "text-gray-500"
              }`}
              onClick={() => handleTabSwitch("details")}
            >
              Details
            </button>
            <button
              className={`px-4 py-2 ${
                activeTab === "notes"
                  ? "border-b-2 border-blue-500 font-bold"
                  : "text-gray-500"
              }`}
              onClick={() => handleTabSwitch("notes")}
            >
              Session Notes
            </button>
          </div>

          {activeTab === "details" ? (
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <InputField
                  label="Date *"
                  type="date"
                  name="date"
                  value={formData.date}
                  onChange={handleChange}
                />
                {errors.date && (
                  <p className="text-red-500 text-sm mt-1">{errors.date}</p>
                )}
              </div>

              <div className="grid lg:grid-cols-2 gap-4 mb-4">
                <InputField
                  label="Start Time *"
                  type="time"
                  name="start_time"
                  value={formData.start_time}
                  onChange={handleChange}
                />
                <InputField
                  label="End Time *"
                  type="time"
                  name="end_time"
                  value={formData.end_time}
                  onChange={handleChange}
                />
              </div>
              {errors.start_time && (
                <p className="text-red-500 text-sm mt-1">{errors.start_time}</p>
              )}
              {errors.end_time && (
                <p className="text-red-500 text-sm mt-1">{errors.end_time}</p>
              )}
            </form>
          ) : (
            <div className="mt-4">
              <TextAreaLimit
                label=""
                name=""
                rows={8}
                placeholder="Enter session notes"
                value={sessionNotes}
                onChange={(value) => setSessionNotes(value)}
              />
            </div>
          )}

          <div className="flex justify-end space-x-4 mt-6">
            <MainButton
              type="button"
              variant="neutral"
              children="Cancel"
              icon={faClose}
              onClick={handleCancel}
            />
            <MainButton
              type="button"
              variant="primary"
              children={isSaving ? "Saving..." : "Save"}
              icon={faCheck}
              onClick={handleSubmit}
              disabled={isSaving}
            />
          </div>
          {/* Close the JSX fragment */}
        </>
      )}
    </Popup>
  );
}
