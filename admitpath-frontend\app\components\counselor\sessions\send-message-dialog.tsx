"use client";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@components/ui/dialog";
import { Button } from "@components/ui/button";
import { Textarea } from "@components/ui/textarea";
import { useWebSocketChat } from "@/app/hooks/useWebSocketChat";
import { toast } from "react-toastify";
import { useProfile } from "@/app/hooks/counselor/useProfile";
import { useStudent } from "@/app/hooks/counselor/useStudent";

const messageSchema = z.object({
  message: z
    .string()
    .min(1, "Message is required")
    .max(500, "Message cannot exceed 500 characters"),
});

type MessageFormData = z.infer<typeof messageSchema>;

interface SendMessageDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  student_id: number;
  student_name: string;
}

export function SendMessageDialog({
  open,
  onOpenChange,
  student_id,
  student_name,
}: SendMessageDialogProps) {
  const [isSending, setIsSending] = useState(false);
  const { userInfo } = useProfile();
  const { sendInitialMessage } = useWebSocketChat();
  const { getStudentInfo } = useStudent();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<MessageFormData>({
    resolver: zodResolver(messageSchema),
  });

  const onSubmit = async (data: MessageFormData) => {
    if (!userInfo) return;

    setIsSending(true);
    try {
      await sendInitialMessage({
        message: data.message,
        recipient_id: student_id,
      });

      toast.success("Message sent successfully!");
      reset();
      onOpenChange(false);
    } catch (error: any) {
      if (
        error?.response?.data?.detail?.includes(
          "between a counselor and a student"
        )
      ) {
        toast.error("You cannot send any messages to this person.");
      } else {
        toast.error("Failed to send message. Please try again.");
      }
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center">
            Send message:
          </DialogTitle>
          <DialogDescription className="text-center font-medium text-md">
            Send a message to {student_name}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 mt-4">
          <div>
            <Textarea
              {...register("message")}
              placeholder="Type your message here..."
              className="min-h-[100px]"
            />
            {errors.message && (
              <p className="text-red-500 text-sm mt-1">
                {errors.message.message}
              </p>
            )}
          </div>
          <Button
            type="submit"
            className="w-full bg-blue-800 hover:bg-blue-800/90"
            disabled={isSending}
          >
            {isSending ? "Sending..." : "Send Message"}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
}
