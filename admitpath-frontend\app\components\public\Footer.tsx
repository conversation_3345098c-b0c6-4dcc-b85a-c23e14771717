import Link from "next/link";
import { Facebook, Instagram, Linkedin } from "lucide-react";
import Image from "next/image";
import StarBackground from "../ui/StarBackground";

export default function Footer() {
  return (
    <footer className="relative bg-gradient-to-br from-blue-950 to-black/90 text-white overflow-hidden font-clash-display">
      <div className="absolute inset-0">
        <StarBackground />
      </div>
      <div className="container mx-auto px-4 py-12 relative z-10">
        <div className="flex flex-col md:flex-row justify-between items-center gap-y-4 mb-12">
          <Link
            href="/"
            className="flex items-center space-x-2 text-2xl font-bold"
          >
            <Image
              src="/icons/logo-white.png"
              alt="Admit Path"
              width={100}
              height={100}
            />
          </Link>
          <nav className="flex flex-wrap gap-6 md:gap-12 text-md md:text-lg font-semibold">
            <Link
              href="/"
              className="text-zinc-100 hover:text-gray-300 transition-colors font-medium"
            >
              Home
            </Link>
            <Link
              href="/library"
              className="text-zinc-100 hover:text-gray-300 transition-colors font-medium"
            >
              Library
            </Link>
            <Link
              href="/faq"
              className="text-zinc-100 hover:text-gray-300 transition-colors font-medium"
            >
              FAQ
            </Link>
            <Link
              href="/contact"
              className="text-zinc-100 hover:text-gray-300 transition-colors font-medium"
            >
              Contact Us
            </Link>
          </nav>
          <div className="flex gap-4">
            <Link
              target="_blank"
              href="https://www.linkedin.com/company/admit-path"
              className="p-2 hover:text-gray-300 transition-colors"
              rel="noopener noreferrer"
            >
              <Linkedin className="w-6 h-6" />
            </Link>

            <Link
              target="_blank"
              href="https://www.instagram.com/admitpath/"
              className="p-2 hover:text-gray-300 transition-colors"
              rel="noopener noreferrer"
            >
              <Instagram className="w-6 h-6" />
            </Link>
          </div>
        </div>
        <div className="flex flex-col md:flex-row gap-y-4 justify-between items-center pt-8 border-t border-white/10 text-md text-zinc-50 font-medium">
          <p>© 2024-2025 AdmitPath. All Rights Reserved.</p>
          <div className="flex gap-6">
            <Link href="/terms" className="hover:text-gray-300 hover:underline">
              Terms & Conditions
            </Link>
            <Link
              href="/privacy"
              className="hover:text-gray-300 hover:underline"
            >
              Privacy Policy
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
