"use client";
import { Sheet, SheetContent, SheetTitle } from "@components/ui/sheet";

import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import { UserButton } from "./UserButton";
import { useEffect, useState } from "react";
import { useProfile } from "@/app/hooks/student/useProfile";

export default function LandingNavbar() {
  const { userInfo } = useProfile();
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 20;
      setScrolled(isScrolled);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <div
      className={`fixed top-0 left-0 right-0 bg-white z-50 transition-all duration-300 ${
        scrolled ? "shadow-md" : "shadow-none"
      } border-b`}
    >
      <div className="container mx-auto px-4 md:px-8 py-2 z-50">
        <div className="flex items-center justify-between">
          {/* Left side - Logo */}
          <div className="lg:w-1/4">
            <Link
              href="/"
              className="flex items-center space-x-2 transition-transform duration-300 hover:scale-105"
            >
              <Image src="/icons/logo.png" alt="logo" width={70} height={70} />
            </Link>
          </div>

          {/* Middle - Navigation */}
          <div className="hidden md:flex justify-center gap-8 lg:w-2/4 font-clash-display">
            <Link
              href="/explore"
              className="text-lg font-medium relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#9C0E22] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
            >
              Explore
            </Link>
            <Link
              href="/for-counselors"
              className="text-lg font-medium relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#9C0E22] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
            >
              For Counselors
            </Link>
            <Link
              href="/library"
              className="text-lg font-medium relative after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-[#9C0E22] after:left-0 after:-bottom-1 after:transition-all after:duration-300 hover:after:w-full"
            >
              Library
            </Link>
          </div>

          {/* Right side - User button and menu */}
          <div className="flex items-center justify-end space-x-4 lg:w-1/4">
            <UserButton />

            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden flex flex-col gap-1.5 w-8"
            >
              <span
                className={`block w-full h-[0.25rem] rounded-full bg-gray-800 transition-all duration-300 ${
                  isMenuOpen ? "rotate-45 translate-y-2" : ""
                }`}
              />
              <span
                className={`block w-full h-[0.25rem] rounded-full bg-gray-800 transition-all duration-300 ${
                  isMenuOpen ? "opacity-0" : ""
                }`}
              />
              <span
                className={`block w-full h-[0.25rem] rounded-full bg-gray-800 transition-all duration-300 ${
                  isMenuOpen ? "-rotate-45 -translate-y-2" : ""
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
        <SheetContent side="right" className="w-[300px] sm:w-[400px]">
          <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
          <nav className="flex flex-col gap-4  font-clash-display">
            <Link
              onClick={() => setIsMenuOpen(false)}
              href="/explore"
              className="text-lg font-medium p-2 hover:bg-gray-50 rounded-lg transition-all duration-300 hover:pl-4"
            >
              Explore
            </Link>
            <Link
              onClick={() => setIsMenuOpen(false)}
              href="/for-counselors"
              className="text-lg font-medium p-2 hover:bg-gray-50 rounded-lg transition-all duration-300 hover:pl-4"
            >
              For Counselors
            </Link>
            <Link
              onClick={() => setIsMenuOpen(false)}
              href="/library"
              className="text-lg font-medium p-2 hover:bg-gray-50 rounded-lg transition-all duration-300 hover:pl-4"
            >
              Library
            </Link>
            <Link
              onClick={() => setIsMenuOpen(false)}
              href="/auth/login"
              className={`inline-flex text-lg font-medium px-6 py-2 mt-2 bg-[#9C0E22] hover:bg-[#9C0E22]/90 text-white rounded-lg transition-all duration-300 hover:translate-y-[-2px] hover:shadow-md w-fit ${
                userInfo ? "hidden" : ""
              }`}
            >
              Login
            </Link>
          </nav>
        </SheetContent>
      </Sheet>
    </div>
  );
}
