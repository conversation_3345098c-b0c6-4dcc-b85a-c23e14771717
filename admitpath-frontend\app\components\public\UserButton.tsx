"use client";

import { ChevronDown, ArrowRight, LogOut } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/app/components/ui/dropdown-menu";
import { useEffect, useState } from "react";
import Image from "next/image";
import { useProfile } from "@/app/hooks/student/useProfile";
import { Skeleton } from "../ui/skeleton";
import Link from "next/link";
import { Button } from "../ui/button";
import { useAuth } from "@/app/hooks/useAuth";
import { useRouter } from "next/navigation";
import { generatePlaceholder } from "@/app/utils/image";

export const UserButton = ({}) => {
  const { fetchUserInfo, userInfo, loading } = useProfile();
  const [isOpen, setIsOpen] = useState(false);
  const { logout } = useAuth();
  const [windowLoaded, setWindowLoaded] = useState(false);
  const router = useRouter();

  useEffect(() => {
    setWindowLoaded(true);
    if (localStorage.getItem("access_token")) {
      fetchUserInfo();
    }
  }, []);

  const handleLogout = async () => {
    try {
      logout();
      router.push("/auth/login");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  return (
    <>
      {!userInfo && !loading && (
        <div className="flex items-center gap-4">
          <Link href="/auth/login" className="md:inline-block hidden">
            <Button className="bg-[#9C0E22] hover:bg-[#9C0E22]/90 text-white px-6">
              Login
            </Button>
          </Link>
          <Link
            href={
              windowLoaded && window.location.pathname === "/for-counselors"
                ? "/auth/signup/counselor"
                : "/auth/signup"
            }
          >
            <Button className="bg-blue-950 hover:bg-blue-950/90 rounded-xl px-5 py-2 text-white">
              Signup
              <ArrowRight className="h-6 w-6 flex-shrink-0" />
            </Button>
          </Link>
        </div>
      )}
      {!userInfo && loading && localStorage.getItem("access_token") ? (
        <Skeleton className="flex items-center gap-4 rounded-full p-5 border"></Skeleton>
      ) : (
        userInfo && (
          <div className="flex gap-4 items-center justify-center">
            <Link
              href={
                userInfo.userType === "counselor"
                  ? "/counselor/dashboard"
                  : "/student/dashboard"
              }
            >
              <Button className="bg-blue-950 hover:bg-blue-950/90 rounded-xl md:inline-block hidden px-5 py-2 text-white">
                Dashboard
                <ArrowRight className="h-6 w-6" />
              </Button>
            </Link>
            <div className="flex items-center gap-2 rounded-xl p-2 border">
              <Image
                className="rounded-full object-cover w-[40px] h-[40px]"
                src={
                  userInfo?.profile_picture_url ||
                  generatePlaceholder(userInfo?.firstName, userInfo?.lastName)
                }
                alt={`${userInfo?.firstName}`}
                width={40}
                height={40}
              />

              <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
                <DropdownMenuTrigger asChild>
                  <button className="outline-none hover:opacity-80">
                    <ChevronDown
                      className={`w-6 h-6 transform transition-transform duration-200 ${
                        isOpen ? "rotate-180" : "rotate-0"
                      }`}
                    />
                  </button>
                </DropdownMenuTrigger>

                <DropdownMenuContent
                  align="end"
                  className="p-2 flex flex-col items-start bg-white"
                >
                  <DropdownMenuItem className="cursor-pointer w-full mb-2">
                    <Link
                      href={
                        userInfo.userType === "counselor"
                          ? "/counselor/dashboard"
                          : "/student/dashboard"
                      }
                      className="w-full"
                    >
                      <Button className="bg-blue-950 hover:bg-blue-950/90 rounded-xl md:hidden w-full px-5 py-2 text-white">
                        Dashboard
                        <ArrowRight className="h-6 w-6 flex-shrink-0" />
                      </Button>
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="cursor-pointer w-full"
                    onClick={() => handleLogout()}
                  >
                    <Button className="w-full flex items-center justify-center gap-2 text-white bg-red-500 hover:bg-red-600/90">
                      Logout
                      <LogOut className="h-4 w-4" />
                    </Button>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        )
      )}
    </>
  );
};
