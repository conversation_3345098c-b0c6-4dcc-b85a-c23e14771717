"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@components/ui/button";
import { Input } from "@components/ui/input";
import { Textarea } from "@components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";
import { ArrowRight } from "lucide-react";
import { useContact } from "@hooks/public/useContact";

const contactSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  service: z.string().min(1, "Please select a service"),
  message: z
    .string()
    .min(10, "Message must be at least 10 characters")
    .max(400, "Message cannot exceed 400 characters"),
});

type ContactFormData = z.infer<typeof contactSchema>;

export function ContactForm() {
  const [charCount, setCharCount] = useState(0);
  const { submitContact, loading } = useContact();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
    reset,
  } = useForm<ContactFormData>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      email: "",
      service: "",
      message: "",
    },
  });

  const onSubmit = async (data: ContactFormData) => {
    try {
      await submitContact(data);
      reset();
      setCharCount(0);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="bg-white rounded-xl md:rounded-2xl p-5 md:p-8 shadow-lg h-full">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Email<span className="text-red-500">*</span>
            </label>
            <Input
              {...register("email")}
              placeholder="eg. <EMAIL>"
              type="email"
              className={`w-full shadow-none bg-gray-50 ${
                errors.email ? "border-red-500" : ""
              }`}
            />
            {errors.email && (
              <p className="text-red-500 text-sm">{errors.email.message}</p>
            )}
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">
              Service<span className="text-red-500">*</span>
            </label>
            <Select
              onValueChange={(value) => setValue("service", value)}
              value={watch("service")}
            >
              <SelectTrigger
                className={`shadow-none bg-gray-50 ${
                  errors.service ? "border-red-500" : ""
                }`}
              >
                <SelectValue placeholder="eg. Session" />
              </SelectTrigger>
              <SelectContent className="bg-white">
                <SelectItem
                  className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                  value="session"
                >
                  Session
                </SelectItem>
                <SelectItem
                  className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                  value="counseling"
                >
                  Counseling
                </SelectItem>
                <SelectItem
                  className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                  value="guidance"
                >
                  Guidance
                </SelectItem>
                <SelectItem
                  className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                  value="other"
                >
                  Other
                </SelectItem>
              </SelectContent>
            </Select>
            {errors.service && (
              <p className="text-red-500 text-sm">{errors.service.message}</p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Message<span className="text-red-500">*</span>
          </label>
          <Textarea
            {...register("message")}
            placeholder="Write your message here..."
            className={`w-full min-h-[120px] shadow-none bg-gray-50 resize-none ${
              errors.message ? "border-red-500" : ""
            }`}
            onChange={(e) => setCharCount(e.target.value.length)}
          />
          <div className="flex justify-between items-center">
            <div>
              {errors.message && (
                <p className="text-red-500 text-sm">{errors.message.message}</p>
              )}
            </div>
            <p className="text-sm text-gray-500">{charCount}/400</p>
          </div>
        </div>

        <Button
          type="submit"
          disabled={loading}
          className="w-full max-w-[10rem] ml-auto bg-blue-800 hover:bg-blue-800/90 text-white flex items-center justify-center gap-2"
        >
          Send Message
          <ArrowRight className="h-4 w-4" />
        </Button>
      </form>
    </div>
  );
}
