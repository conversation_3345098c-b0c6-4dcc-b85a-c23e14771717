import React from "react";
import { Counselor<PERSON><PERSON> } from "./counselor-card";
import { CounselorPublicProfile } from "@/app/types/counselor";
import { LogoLoader } from "@/app/components/ui/logo-loader";
import { CounselorCardSkeletonGrid } from "@/app/components/ui/counselor-card-skeleton";

interface CounselorCardListProps {
  loading: boolean;
  counselors: CounselorPublicProfile[] | null;
}

const CounselorCardList: React.FC<CounselorCardListProps> = ({
  loading,
  counselors,
}) => {
  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
      {loading && counselors === null ? (
        // Initial loading state with skeletons
        <CounselorCardSkeletonGrid count={8} variant="explore" />
      ) : counselors === null ? (
        // Fallback loading state
        <div className="col-span-full flex justify-center items-center py-12">
          <LogoLoader size={50} text="Loading counselors..." />
        </div>
      ) : counselors.length === 0 ? (
        // No counselors found
        <div className="col-span-full text-center py-8">
          <h2 className="text-xl font-medium mb-2">No counselors found</h2>
          <p className="text-gray-600">
            There are no verified counselors matching your criteria. Try
            adjusting your filters.
          </p>
        </div>
      ) : (
        // Show existing counselors
        counselors.map((counselor, index) => (
          <CounselorCard
            key={counselor.user_id}
            counselor={counselor}
            priority={index < 4} // Prioritize first 4 images
            preload={index < 8} // Preload first 8 images
          />
        ))
      )}
    </div>
  );
};

export default CounselorCardList;
