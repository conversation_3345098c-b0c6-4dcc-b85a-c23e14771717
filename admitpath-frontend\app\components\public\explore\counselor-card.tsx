"use client";
import { MessageCircle, GraduationCap } from "lucide-react";
import Image from "next/image";
import { Button } from "../../ui/button";
import type { CounselorPublicProfile } from "@/app/types/counselor";
import * as CountryFlags from "country-flag-icons/react/3x2";
import { useEffect, useState } from "react";
import { SendMessageDialog } from "../../student/sessions/send-message-dialog";

import { useProfile } from "@/app/hooks/student/useProfile";
import { generatePlaceholder } from "@/app/utils/image";
import { getUniversityLogoUrl } from "@/app/utils/uni-logo";

const getCountryFlag = (countryCode: string | null = "US") => {
  const code = (countryCode || "US").toUpperCase();
  const FlagComponent = (CountryFlags as any)[code];
  return FlagComponent ? <FlagComponent /> : <CountryFlags.US />;
};

export function CounselorCard({
  counselor,
}: {
  counselor: CounselorPublicProfile;
}) {
  const [showMessageDialog, setShowMessageDialog] = useState(false);
  const { userInfo } = useProfile();
  const [universityLogo, setUniversityLogo] = useState("");

  useEffect(() => {
    const fetchUniversityLogo = async () => {
      if (counselor.education && counselor.education.length > 0) {
        const logoUrl = await getUniversityLogoUrl(
          counselor.education[0].university_name
        );
        setUniversityLogo(logoUrl);
      }
    };

    fetchUniversityLogo();
  }, [counselor.education]);

  return (
    <div
      className="overflow-hidden rounded-lg flex flex-col bg-white border transition-all duration-300 hover:shadow-xl hover:-translate-y-1 group relative cursor-pointer"
      onClick={() => window.open(`/profile/${counselor.user_id}`, "_blank")}
    >
      <div className="relative">
        <div className="h-20 relative overflow-hidden bg-white">
          <div className="h-6 bg-blue-950 w-full transition-colors group-hover:bg-[#9C0E22]" />
          <div className="relative h-full px-4" />
        </div>

        {/* Profile Picture */}
        <div className="absolute flex items-center justify-between gap-8 -bottom-8 left-4 pr-8 w-full">
          <div className="relative w-24 h-24 rounded-full border-3 border-white overflow-hidden shadow-lg transition-transform group-hover:scale-105">
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity z-10" />
            <Image
              src={
                counselor.profile_picture_url ||
                generatePlaceholder(counselor.first_name, counselor.last_name)
              }
              alt={`${counselor.first_name} ${counselor.last_name}`}
              fill
              sizes="(max-width: 768px) 128px, 128px"
              quality={95}
              priority
              className="object-cover group-hover:opacity-95 transition-opacity"
            />
          </div>

          {/* University Logo */}
          {universityLogo && (
            <div className="relative">
              <img
                src={universityLogo}
                alt={counselor.education[0].university_name}
                className="w-[4rem] h-[4rem] border rounded-lg overflow-hidden transition-transform group-hover:scale-105"
              />
            </div>
          )}

          <div className="w-6 h-4 rounded-[2px] overflow-hidden shadow-sm">
            {getCountryFlag(counselor.country_of_residence)}
          </div>
        </div>
      </div>
      <div className="p-4 pt-12 flex-1">
        <div className="flex flex-col justify-between h-full">
          <div className="flex flex-col gap-0.5">
            <h3 className="text-lg font-medium group-hover:text-[#9C0E22] transition-all group-hover:translate-x-0.5">
              {counselor.first_name} {counselor.last_name}
            </h3>
            <p className="mt-1 text-sm text-gray-700 line-clamp-2">
              {counselor.tagline ||
                counselor.bio ||
                counselor.experience_description}
            </p>
          </div>
          <div>
            {/* Education */}
            {counselor.education && counselor.education.length > 0 && (
              <div className="mt-4 flex items-center gap-2 text-sm">
                <GraduationCap className="w-5 h-5 text-gray-700 flex-shrink-0" />
                <p className="font-medium text-base text-blue-950 transition-colors">
                  {counselor.education[0].university_name}
                </p>
              </div>
            )}

            {/* Pricing */}
            <div className="mt-4">
              <p className="text-sm text-gray-600">Starts at</p>
              <p className="font-[500] text-lg">${counselor.hourly_rate}/hr</p>
            </div>
          </div>
        </div>
      </div>
      <div className="px-4 pb-4">
        <div className="flex flex-col sm:grid sm:grid-cols-2 gap-2">
          <Button
            variant={"outline"}
            className="rounded-lg text-white bg-blue-950 hover:scale-[102%] hover:bg-[#9C0E22] transition-all z-10 text-sm h-10 w-full flex items-center justify-center shadow-sm hover:shadow"
            onClick={(e) => {
              e.stopPropagation();
              if (!userInfo) {
                window.open(
                  `/auth/login?redirect=/profile/${counselor.user_id}`,
                  "_blank"
                );
                return;
              }
              window.open(
                `/profile/${counselor.user_id}?booking=true`,
                "_blank"
              );
            }}
          >
            Book Session
          </Button>
          <Button
            variant={"outline"}
            className="rounded-lg text-white bg-blue-950 hover:scale-[102%] hover:bg-[#9C0E22] transition-all z-10 text-sm h-10 w-full flex items-center justify-center gap-2 shadow-sm hover:shadow"
            onClick={(e) => {
              e.stopPropagation();
              if (!userInfo) {
                window.open(
                  `/auth/login?redirect=/profile/${counselor.user_id}`,
                  "_blank"
                );
                return;
              }
              setShowMessageDialog(true);
            }}
          >
            Message
            <MessageCircle className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <SendMessageDialog
        open={showMessageDialog}
        onOpenChange={setShowMessageDialog}
        counselorInfo={counselor}
      />
    </div>
  );
}
