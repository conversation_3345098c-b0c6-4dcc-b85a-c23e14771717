import React, { useId } from "react";
import { Skeleton } from "@components/ui/skeleton";

export const CounselorCardSkeleton = () => {
  return (
    <div key={useId()} className="rounded-xl relative">
      <Skeleton className="w-full h-[28rem] rounded-xl" />
      <div className="absolute w-full h-full flex flex-col justify-end p-4 md:p-6 gap-y-3 rounded-xl inset-0">
        <div className="flex justify-between">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-6 w-24" />
        </div>
        <Skeleton className="flex justify-between p-4 sm:py-6 sm:px-5 bg-[#6b6b6b] rounded-lg mt-2.5 sm:mt-4" />
      </div>
    </div>
  );
};
