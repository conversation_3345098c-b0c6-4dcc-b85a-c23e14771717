import {
  Accordion,
  AccordionContent,
  Accordion<PERSON><PERSON>,
  AccordionTrigger,
} from "@components/ui/accordion";

const faqs = [
  {
    question: "What is AdmitPath, and how does it work?",
    answer:
      "AdmitPath is a platform that connects students with highly qualified university application counselors. We offer personalized 1:1 mentoring sessions, where students can receive expert guidance. Our platform also offers flexible packages to match the needs of each student.",
  },
  {
    question:
      "What's the difference between the mentoring packages and individual sessions?",
    answer:
      "Mentoring Packages provide comprehensive support over multiple hours, covering all aspects of your application process. Individual sessions are ideal for specific needs or targeted advice, such as essay review.",
  },
  {
    question: "How do I choose the right counselor for hourly sessions?",
    answer:
      "You can browse our list of expert counselors, view their profiles, and select the one whose background and expertise align with your needs",
  },
  // {
  //   question: "Can I change my package after purchasing?",
  //   answer:
  //     "Yes, you can upgrade or adjust your package if needed. Just contact our support team, and we'll help you make the change.",
  // },
  // {
  //   question: "How do I select counselors if I purchase a package?",
  //   answer:
  //     "When you purchase a mentoring package, our strategy team will schedule a meeting with you to understand your academic background, needs, and aspirations. Based on this discussion, we will carefully match you with the best counselors suited to your profile and goals.",
  // },
  {
    question: "How do I contact a counselor after my session?",
    answer:
      "You can reach out to your counselor for follow-up questions via messages after your hourly session. For mentoring packages, you will have more extensive communication, depending on the package you choose.",
  },
  {
    question: "What makes your counselors different from others?",
    answer:
      "Our counselors are current students or alumni of top universities, so they offer fresh, insider knowledge about the latest admissions trends and university-specific insights. Unlike traditional agencies, our counselors are genuinely connected to the universities you aspire to attend and can provide authentic, real-world advice.",
  },
  {
    question: "How do you ensure the quality of your counseling services?",
    answer:
      "We carefully select counselors based on their expertise and up-to-date knowledge of university admissions. We monitor feedback to maintain high standards. If you're not satisfied, our support team is ready to assist.",
  },
  {
    question: "What is your refund policy?",
    answer:
      "If you're not satisfied with a session, contact us within 48 hours, and we'll address your concerns. For mentoring packages, we offer partial refunds based on unused hours or can help you switch counselors.",
  },
  {
    question: "Are your services available internationally?",
    answer:
      "Yes! Our platform is available to students worldwide. We connect students with counselors from different regions, so no matter where you're based, you can receive expert advice and guidance tailored to your specific needs.",
  },
];

export function FAQSection() {
  return (
    <section className="my-10 md:my-20 bg-white container mx-auto px-4 md:px-6 font-clash-display">
      <div className="text-center space-y-4 mb-16">
        <h2 className="text-3xl md:text-4xl font-semibold tracking-tight">
          FAQs
        </h2>
        <p className="text-md md:text-lg font-normal text-gray-800/80 max-w-4xl mx-auto">
          Explore profiles of experienced mentors, current students, alumni, and
          certified professionals who can guide you through every step of your
          university application journey.
        </p>
      </div>
      <div className="max-w-5xl mx-auto">
        <Accordion type="single" collapsible className="w-full space-y-6">
          {faqs.map((faq, index) => (
            <AccordionItem
              key={index}
              value={`item-${index}`}
              className="border-b border-gray-200 last:border-0"
            >
              <AccordionTrigger className="text-xl md:text-2xl font-[400] text-left hover:no-underline pb-6">
                {faq.question}
              </AccordionTrigger>
              <AccordionContent className="text-lg font-normal text-gray-800/80">
                {faq.answer}
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </section>
  );
}
