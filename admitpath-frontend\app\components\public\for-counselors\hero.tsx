"use client";

import <PERSON><PERSON> from "lottie-react";
import { ArrowR<PERSON> } from "lucide-react";
import { But<PERSON> } from "@components/ui/button";
import animationData from "@/app/assets/lotties/CounselorPageHeader.json";
import Link from "next/link";

export default function HeroSection() {
  return (
    <section className="px-6 mx-auto container my-10 md:my-12 items-center gap-6 lg:gap-12 flex flex-col lg:flex-row lg:justify-between lg:px-[5%]">
      <div className="max-w-2xl flex-col flex gap-y-4 md:gap-y-6">
        <h1 className="font-[500] tracking-tight text-blue-950 text-3xl md:text-4xl 2xl:text-5xl">
          Join <PERSON>mit<PERSON>ath and Empower Students to Achieve Their Dreams
        </h1>
        <p className="text-lg text-gray-600">
          Become a mentor and help students navigate their academic and career
          journeys. Share your expertise, provide valuable guidance, and make a
          lasting impact on the next generation.
        </p>
        <Link href="/auth/signup/counselor">
          <Button className="bg-blue-950 rounded-xl p-6  text-white text-md sm:text-lg flex justify-center items-center">
            Become a Counselor <ArrowRight className="h-6 w-6 ml-2" />
          </Button>
        </Link>
      </div>
      <div className="relative">
        <Lottie
          animationData={animationData}
          loop={true}
          className="h-full w-full"
        />
      </div>
    </section>
  );
}
