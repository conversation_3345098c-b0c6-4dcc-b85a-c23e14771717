"use client";

import { useEffect } from "react";

const Mission = () => {
  const missionStatement: string =
    "The college admissions system is outdated. Traditional agencies lock students into rigid, overpriced packages, and limited personalized support. AdmitPath empowers counselors to offer flexible, on-demand guidance, ensuring every student gets the right advice from the right mentor. Together, we’re making college admissions more accessible.";

  useEffect(() => {
    const missionSection: HTMLElement | null =
      document.querySelector(".mission-section");
    const words: NodeListOf<HTMLElement> = document.querySelectorAll(
      ".mission-section .word"
    );

    const updateOpacity = (): void => {
      if (!missionSection) return;

      const sectionTop: number = missionSection.offsetTop;
      const sectionHeight: number = missionSection.offsetHeight;
      const scrollY: number = window.scrollY;
      const windowHeight: number = window.innerHeight;

      const sectionStart: number =
        sectionTop - windowHeight + sectionHeight * 0.4;
      const sectionEnd: number = sectionTop + sectionHeight;

      if (scrollY >= sectionStart && scrollY <= sectionEnd) {
        const scrollProgress: number =
          (scrollY - sectionStart) / (sectionEnd - sectionStart);

        words.forEach((word: HTMLElement, index: number) => {
          const wordProgress: number =
            scrollProgress - index * (0.5 / words.length);
          const opacity: number = Math.min(
            Math.max(wordProgress * words.length, 0.2),
            1
          );
          word.style.opacity = opacity.toString();
        });
      } else if (scrollY < sectionStart) {
        words.forEach((word: HTMLElement) => (word.style.opacity = "0.2"));
      } else if (scrollY > sectionEnd) {
        words.forEach((word: HTMLElement) => (word.style.opacity = "1"));
      }
    };

    window.addEventListener("scroll", updateOpacity);
    updateOpacity();

    return () => window.removeEventListener("scroll", updateOpacity);
  }, []);

  return (
    <section className="mission-section py-12 px-4 md:px-10 lg:px-[15%] bg-mainClr text-white text-center">
      <h2 className="text-3xl font-medium mb-6">Mission</h2>
      <h2 className="mission_text text-xl md:text-3xl">
        {missionStatement.split(" ").map((word: string, index: number) => (
          <span
            key={index}
            className="word inline-block opacity-20 leading-10 transition-opacity duration-300 ease-in-out"
          >
            {word}&nbsp;
          </span>
        ))}
      </h2>
    </section>
  );
};

export default Mission;
