import { UserPlus, CheckCircle, Briefcase, TrendingUp } from "lucide-react";

const onboardingSteps = [
  {
    id: 1,
    icon: <UserPlus size={40} color="#4F46E5" />,
    title: "Apply",
    desc: "Create your counselor profile and showcase your expertise to connect with students seeking guidance.",
  },
  {
    id: 2,
    icon: <CheckCircle size={40} color="#22C55E" />,
    title: "Get Verified",
    desc: "Our team carefully reviews applications to ensure top-quality counselors join our platform.",
  },
  {
    id: 3,
    icon: <Briefcase size={40} color="#F59E0B" />,
    title: "Start Coaching",
    desc: "Once approved, begin offering personalized guidance, helping students navigate their university admissions journey with confidence.",
  },
  {
    id: 4,
    icon: <TrendingUp size={40} color="#EF4444" />,
    title: "Grow Your Practice",
    desc: "Earn income, build credibility, and create an impact on lives of many.",
  },
];

export default function OnboardingProcess() {
  return (
    <section>
      <div className="my-8 md:my-12 border rounded-xl container mx-auto px-8 py-8 lg:px-16 xl:px-10 space-y-10 md:space-y-20 w-[84%]">
        <div className="content">
          <ul className="grid md:grid-cols-2 lg:grid-cols-4 gap-10 lg:gap-8">
            {onboardingSteps.map((item) => {
              const { id, icon, title, desc } = item;

              return (
                <li key={id}>
                  <span className="mb-2 md:mb-4 inline-block">{icon}</span>
                  <h3 className="title font-semibold mb-3">{title}</h3>
                  <p className="desc text-gray-600">{desc}</p>
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </section>
  );
}
