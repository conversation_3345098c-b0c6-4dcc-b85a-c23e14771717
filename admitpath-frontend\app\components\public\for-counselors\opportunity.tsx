import Image from "next/image";
import Link from "next/link";

import { ArrowR<PERSON> } from "lucide-react";
import { But<PERSON> } from "@components/ui/button";

export default function Opportunity() {
  return (
    <section>
      <div className="my-8 md:my-12 container mx-auto px-8 lg:px-16 xl:px-10 space-y-10 md:space-y-20">
        <div className="relative flex w-full flex-wrap items-center justify-end overflow-hidden rounded-3xl border bg-white bg-cover bg-center lg:flex-nowrap">
          {/* Left Image */}
          <div className="left-0 top-0 size-full lg:absolute lg:w-1/3">
            <Image
              src="/images/happy-college-students.jpg"
              alt="Unlock-Opportunity-Graphic"
              width={800}
              height={500}
              className="size-full object-right"
            />
          </div>

          {/* Right Text Content */}
          <div className="w-full px-4 py-10 lg:w-2/3 lg:py-12 lg:pl-12 lg:pr-24">
            <h2 className="mb-5 text-lg font-medium">Open New Doors</h2>
            <p className="mb-5 text-lg text-gray-600">
              Helping others achieve their dreams is truly rewarding. Join our
              platform and make a lasting impact by guiding individuals toward
              success.
            </p>

            {/* Call-to-Action Link */}
            <Link href="/auth/signup/counselor">
              <Button className="bg-blue-950 rounded-xl p-6  text-white text-md sm:text-lg flex justify-center items-center">
                Become a Counselor <ArrowRight className="h-6 w-6 ml-2" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
