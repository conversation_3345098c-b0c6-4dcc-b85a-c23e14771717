"use client";

import {
  Navigation,
  Pagination,
  Scrollbar,
  A11y,
  EffectCoverflow,
  Autoplay,
} from "swiper/modules";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import "swiper/css/effect-coverflow";

const testimonials = [
  {
    id: 1,
    photo: "",
    name: "<PERSON><PERSON>",
    logo: "./images/testimonials/bryn-mawr.png",
    text: "It’s fulfilling to help students who wouldn’t normally have access to personalized admissions advice. AdmitPath makes that possible, and I love being part of that journey.",
  },
  {
    id: 2,
    photo: "./images/testimonials/maaz.jpg",
    name: "<PERSON>",
    logo: "./images/testimonials/yale-nus-college.png",
    text: "It’s fulfilling to help students who wouldn’t normally have access to personalized admissions advice. AdmitPath makes that possible, and I love being part of that journey.",
  },
  {
    id: 3,
    photo: "",
    name: "<PERSON>",
    logo: "./images/testimonials/ucla.png",
    text: "This platform makes it so easy to connect with students who are specifically looking for someone with my background — it feels personalized for both sides.",
  },
  {
    id: 4,
    photo: "",
    name: "Anwaar <PERSON>",
    logo: "./images/testimonials/georgetown.png",
    text: "I love that AdmitPath lets students search by school and major — it means the students I work with genuinely value my experience and insights.",
  },
  {
    id: 5,
    photo: "",
    name: "Ahmed Zafar",
    logo: "./images/testimonials/stanford.png",
    text: "AdmitPath isn’t just a platform — it’s a community of students and counselors working together to make admissions more accessible and personalized.",
  },
  {
    id: 6,
    photo: "./images/testimonials/anvay.jpg",
    name: "Anvay Dixit",
    logo: "./images/testimonials/yale-nus-college.png",
    text: "AdmitPath treats counselors as true partners. We’re empowered to price fairly, work flexibly, and focus on what we do best — guiding students.",
  },
  {
    id: 7,
    photo: "./images/testimonials/arsum.png",
    name: "Arsum Nadeem",
    logo: "./images/testimonials/pomona.png",
    text: "I love that AdmitPath lets students search by school and major — it means the students I work with genuinely value my experience and insights.",
  },
  {
    id: 8,
    photo: "./images/testimonials/aizaz.png",
    name: "Aizaz Salahuddin",
    logo: "./images/testimonials/dartmouth.png",
    text: "AdmitPath is built for counselors like me who want flexibility. Whether I have 1 hour or 3 hours, I can easily find students who need exactly what I offer.",
  },
];

export default function Testimonials() {
  return (
    <section className="w-full bg-mainClr px-8 lg:px-16 xl:px-10 py-20 mt-10 md:mt-20 relative overflow-hidden">
      <div className="container w-[90%] mx-auto">
        <div className="">
          <h2 className="text-4xl md:text-5xl font-semibold text-white font-space">
            We&apos;re being talked about
          </h2>

          <p className="mt-4 text-lg text-white/90">
            Take a look at what they have to say about us - it&apos;s always
            interesting to hear different perspectives!
          </p>
        </div>

        <div className="py-20 relative">
          {/* Swiper Container */}
          <Swiper
            modules={[
              Navigation,
              Pagination,
              Scrollbar,
              A11y,
              EffectCoverflow,
              Autoplay,
            ]}
            effect="coverflow"
            grabCursor={true}
            centeredSlides={true}
            coverflowEffect={{
              rotate: 0,
              stretch: 0,
              depth: 100,
              modifier: 2,
              slideShadows: true,
            }}
            loop
            loopAdditionalSlides={2}
            autoplay={{
              delay: 3000,
              disableOnInteraction: false,
            }}
            navigation={{
              nextEl: ".swiper-button-next",
              prevEl: ".swiper-button-prev",
            }}
            breakpoints={{
              1024: { slidesPerView: 3 },
              768: { slidesPerView: 2 },
              480: { slidesPerView: 1 },
            }}
          >
            {testimonials.map(({ id, photo, name, logo, text }) => (
              <SwiperSlide key={id}>
                <div className="group relative bg-white shadow-2xl rounded-xl min-h-[340px] overflow-hidden">
                  {/* Testimonial Content */}
                  <div className="relative p-6 flex flex-col items-center text-center">
                    {/* Profile Photo */}
                    <div className="w-20 h-20 rounded-full bg-gray-200 flex items-center justify-center mb-4 overflow-hidden">
                      {photo ? (
                        <img
                          src={photo}
                          alt={name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <span className="text-gray-500 text-2xl font-bold">
                          {name.split(" ").map((word) => word[0])}
                        </span>
                      )}
                    </div>

                    {/* Testimonial Text */}
                    <p className="text-gray-700 text-sm leading-relaxed mb-4 flex-1">
                      {text}
                    </p>

                    {/* Name and Logo */}
                    <div className="mt-auto">
                      <h3 className="text-lg font-semibold text-gray-900">
                        {name}
                      </h3>
                      <div className="mt-2">
                        <img
                          src={logo}
                          alt={`${name}'s Logo`}
                          className="w-14 h-10 mx-auto"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>

          {/* Custom Navigation Arrows */}
          <div className="swiper-button-prev !bottom-0 !top-auto !left-[45%] !w-10 !h-10 !mt-0"></div>
          <div className="swiper-button-next !bottom-0 !top-auto !right-[45%] !w-10 !h-10 !mt-0"></div>

          {/* Custom CSS for Navigation Arrows */}
          <style>
            {`
          .swiper-button-prev,
          .swiper-button-next {
            position: absolute;
            bottom: 0;
            top: auto;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s ease;
          }

          .swiper-button-prev::after,
          .swiper-button-next::after {
            font-size: 20px;
            font-weight: bold;
          }

          .swiper-button-prev {
            left: 45%;
            transform: translateX(-50%);
          }

          .swiper-button-next {
            right: 45%;
            transform: translateX(50%);
          }
        `}
          </style>
        </div>
      </div>
    </section>
  );
}
