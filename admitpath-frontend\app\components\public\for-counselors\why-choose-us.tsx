"use client";

import <PERSON><PERSON> from "lottie-react";
import { <PERSON><PERSON> } from "@components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import webappAnimation from "@/app/assets/lotties/WebappScreen.json";
import Link from "next/link";

export default function WhyChooseUs() {
  return (
    <section className=" bg-white my-8 md:my-12 container mx-auto px-8 lg:px-16 xl:px-10 space-y-10 md:space-y-20">
      <div className="text-center">
        <h2 className="text-3xl font-semibold tracking-tight text-[#14162F] sm:text-4xl">
          Why Join AdmitPath?
        </h2>
        <p className="mx-auto mt-4 max-w-3xl text-lg text-gray-600">
          Guide. Grow. Earn.
        </p>
        <p className="mx-auto mt-4 max-w-3xl text-lg text-gray-600">
          Join <PERSON>mit<PERSON> and become part of a network of experienced mentors,
          university alumni, and academic professionals making a real impact.
        </p>
      </div>

      <div className=" grid grid-cols-1 gap-12 lg:grid-cols-2">
        <div className="flex flex-col gap-y-8 md:gap-y-12 justify-center">
          <Feature
            icon="🌟"
            title="Expand Your Impact"
            description="Help students navigate the complex university admissions process by sharing your expertise and firsthand experience."
          />
          <Feature
            icon="💰"
            title="Earn for Your Expertise"
            description="Monetize your counseling skills and academic knowledge. Set your availability, offer personalized guidance, and get compensated for your time while working flexibly on your own terms."
          />
          <Feature
            icon="🤝"
            title="Grow Your Professional Network"
            description="Connect with like-minded professionals, experienced mentors, and top university alumni. Stay updated on industry trends, and build valuable relationships."
          />
          <Link href="/auth/signup/counselor">
            <Button className="bg-blue-950 rounded-xl p-6  text-white text-md sm:text-lg flex justify-center items-center">
              Become a Counselor <ArrowRight className="h-6 w-6 ml-2" />
            </Button>
          </Link>
        </div>
        <div className="hidden lg:inline-block relative h-[600px] rounded-2xl bg-[#14162F] pt-8 pl-8">
          <Lottie
            animationData={webappAnimation}
            loop={true}
            className="h-full w-full"
          />
        </div>
      </div>
    </section>
  );
}

function Feature({
  icon,
  title,
  description,
}: {
  icon: string;
  title: string;
  description: string;
}) {
  return (
    <div className="flex gap-4">
      <div className="flex sm:h-16 sm:w-16 h-12 w-12 items-center justify-center rounded-lg bg-blue-50 text-2xl sm:text-3xl">
        {icon}
      </div>
      <div>
        <h1 className="font-[500] text-[#14162F] text-xl md:text-2xl">
          {title}
        </h1>
        <p className="mt-2 text-gray-600">{description}</p>
      </div>
    </div>
  );
}
