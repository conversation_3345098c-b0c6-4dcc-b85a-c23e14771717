"use client";
import React from "react";
import { <PERSON>R<PERSON> } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "../../ui/button";
import Lottie from "lottie-react";
import arrowAnimation from "@/app/assets/lotties/Arrow.json";
import webappAnimation from "@/app/assets/lotties/WebappScreen.json";
import starsAnimation from "@/app/assets/lotties/Stars.json";

const BecomeCounselor = () => {
  return (
    <div className="my-10 md:my-20 container mx-auto px-4 md:px-8 lg:px-16 xl:px-10 py-3 ">
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 p-4 sm:p-6 md:p-8 lg:p-12 bg-gradient-to-br from-blue-950 to-black/90 border rounded-xl overflow-hidden relative">
        {/* Text Content */}
        <div className="flex flex-col gap-y-4 sm:gap-y-6 md:gap-y-8 py-4 sm:py-8 lg:py-12 max-w-xl">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-medium text-zinc-100 leading-tight">
            Share Your Knowledge. Make a Difference. Earn
          </h1>
          <p className="text-base sm:text-lg md:text-xl text-zinc-100/90">
            Are you a current university student, alumnus, or certified expert?
            Join our platform and guide aspiring students to success while
            earning income.
          </p>

          <div className="flex gap-2 items-start relative pt-2 sm:pt-4">
            <Link href="/auth/signup">
              <Button className="bg-zinc-100 hover:bg-zinc-300 rounded-xl px-4 py-3 sm:p-6 text-blue-950 text-base sm:text-lg md:text-xl flex justify-center items-center">
                Join Now <ArrowRight className="h-5 w-5 sm:h-6 sm:w-6 ml-2" />
              </Button>
            </Link>
            {/* Arrow Lottie Animation */}
            <div className="w-24 lg:w-36 h-24 lg:h-36 pt-6">
              <Lottie
                animationData={arrowAnimation}
                loop={true}
                autoplay={true}
                style={{ width: "100%", height: "100%" }}
              />
            </div>
          </div>
        </div>
        {/* Stars Animation */}
        <div className="hidden sm:block absolute right-[1rem] lg:right-2/4 top-4 w-16 sm:w-20 md:w-24 h-16 sm:h-20 md:h-24">
          <Lottie
            animationData={starsAnimation}
            loop={true}
            autoplay={true}
            style={{ width: "100%", height: "100%" }}
          />
        </div>

        {/* Webapp Screen Animation - Only hidden on mobile */}
        <div className="hidden lg:block absolute bottom-0 right-0 w-[45%] h-[90%] transform translate-x-[8%] translate-y-[5%]">
          <Lottie
            animationData={webappAnimation}
            loop={true}
            autoplay={true}
            style={{ width: "100%", height: "100%", objectFit: "contain" }}
            className="rounded-t-xl"
          />
        </div>
      </div>
    </div>
  );
};

export default BecomeCounselor;
