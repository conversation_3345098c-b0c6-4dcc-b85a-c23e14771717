"use client";

import { Card } from "./TopCounselors";
import { cn } from "@/lib/utils";
import {
  CounselorCategory,
  useFeaturedCounselors,
} from "@/app/hooks/public/useFeaturedCounselors";

const categories = [
  {
    id: "top-ivy-league" as Counselor<PERSON>ate<PERSON><PERSON>,
    name: "Top Ivy League",
    description: "Expert counselors from Ivy League institutions",
  },
  {
    id: "liberal-arts" as CounselorCategory,
    name: "Liberal Arts",
    description: "Specialists in Liberal Arts college admissions",
  },
  {
    id: "top-ucs" as CounselorCategor<PERSON>,
    name: "Top UCs",
    description: "University of California system experts",
  },
  {
    id: "russel-group" as CounselorCategory,
    name: "Top UK",
    description: "Leading counselors from UK's prestigious Russell Group",
  },
];

const FeaturedCategories = () => {
  const { counselors, isLoading, error, category, changeCategory } =
    useFeaturedCounselors("top-ivy-league");

  return (
    <div className="container max-w-8xl mx-auto flex flex-col justify-center items-center my-10 md:my-20 px-4">
      <div className="w-full bg-gradient-to-br from-blue-950 to-black/90 rounded-2xl py-12 px-2 md:px-8 lg:px-16 xl:px-10 shadow-[0_4px_24px_-8px_rgba(0,0,0,0.1)]">
        <div className="w-full flex flex-col items-center justify-center gap-y-2.5 md:gap-y-4 text-center mb-8">
          <h2 className="text-2xl sm:text-4xl lg:text-5xl font-medium text-white">
            Featured Counselors
          </h2>
          <p className="text-lg md:text-xl text-blue-100 px-2">
            Find specialized counselors from top institutions worldwide
          </p>
        </div>

        {/* Category Tabs */}
        <div className="w-full flex justify-center  mb-6">
          <div className="flex flex-wrap justify-center items-center gap-2 md:gap-4 max-w-4xl">
            {categories.map((cat) => (
              <button
                key={cat.id}
                onClick={() => changeCategory(cat.id)}
                className={cn(
                  "px-4 py-2 rounded-full text-sm md:text-base font-medium transition-all duration-200",
                  category === cat.id
                    ? "bg-white text-blue-950"
                    : "bg-white/10 text-white hover:bg-white/20"
                )}
              >
                {cat.name}
              </button>
            ))}
          </div>
        </div>

        {/* Category Description */}
        <div className="w-full flex justify-center px-4">
          <p className="text-blue-100 text-center max-w-2xl mb-8">
            {categories.find((c) => c.id === category)?.description}
          </p>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="w-full flex justify-center py-12">
            <div className="animate-pulse text-blue-100">
              Loading counselors...
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="w-full flex justify-center py-8">
            <div className="text-red-400 text-center">{error}</div>
          </div>
        )}

        {/* Counselors Grid */}
        {!isLoading && !error && (
          <div className="flex flex-wrap justify-center items-center gap-6 w-full">
            {counselors.length > 0 ? (
              counselors.map((counselor) => (
                <Card key={counselor.user_id} profile={counselor} />
              ))
            ) : (
              <div className="w-full text-center text-blue-100 py-8">
                No counselors found in this category.
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default FeaturedCategories;
