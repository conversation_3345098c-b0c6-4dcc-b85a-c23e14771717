import React from "react";

const GetExpertHelpSection = () => {
    const features = [
        {
            icon: "👨‍🏫",
            title: "Expert Advisors Network",
            description:
                "Connect with verified advisors including current students and alumni from top universities, former admissions officers, and certified counselors.",
        },
        {
            icon: "🎓",
            title: "Proven Track Record",
            description:
                "Our students have achieved remarkable success, gaining admissions to Ivy League universities, UC schools, and other top-ranked institutions.",
        },
        {
            icon: "🛡️",
            title: "Secure & Guaranteed",
            description:
                "Experience peace of mind with secure transactions, comprehensive dispute resolution, and our satisfaction guarantee.",
        },
    ];

    return (
        <div className="w-full min-h-[400px] bg-gradient-to-br from-blue-950 to-black/90 text-white py-12 my-4 md:my-10">
            <div className="max-w-7xl mx-auto px-4">
                <div className="text-center mb-12">
                    <h4 className="text-sm uppercase tracking-wider mb-2">
                        ONE-ON-ONE ADVISING
                    </h4>
                    <h2 className="text-4xl font-medium mb-8">
                        Everything You Need for a Successful College Application
                    </h2>
                </div>

                <div className="grid md:grid-cols-3 gap-6">
                    {features.map((feature, index) => (
                        <div
                            key={index}
                            className="bg-white/10 backdrop-blur-lg rounded-lg p-6 hover:bg-white/15 transition-all"
                        >
                            <div className="flex items-center mb-4">
                                <span className="text-2xl mr-3">
                                    {feature.icon}
                                </span>
                                <h3 className="text-xl font-medium">
                                    {feature.title}
                                </h3>
                            </div>
                            <p className="text-gray-300 leading-relaxed">
                                {feature.description}
                            </p>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default GetExpertHelpSection;
