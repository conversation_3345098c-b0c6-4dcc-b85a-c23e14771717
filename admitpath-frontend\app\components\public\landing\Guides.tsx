"use client";

import React, { useEffect } from "react";
import Link from "next/link";
import { Button } from "../../ui/button";
import { ArrowRight } from "lucide-react";
import ResourceCard from "../../student/resources/card";
import { useResources } from "@/app/hooks/student/useResources";

const Guides = () => {
  const { resources, setAudience } = useResources();

  useEffect(() => {
    setAudience("public");
  }, []);

  return (
    <div className="container mx-auto flex flex-col justify-center items-center my-10 md:my-20 gap-y-6 md:gap-y-12 px-4 md:px-8 lg:px-16 xl:px-10">
      <div className="w-full flex flex-col items-center justify-center gap-y-2.5 md:gap-y-4 text-center drop-shadow-lg px-3">
        <div className="self-stretch text-2xl sm:text-4xl lg:text-5xl font-medium text-zinc-900">
          Guides to help you grow
        </div>
        <div className="text-lg md:text-xl text-blue-950 px-2">
          Access a library of videos, templates, and examples curated by
          Leland’s top coaches.
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6 w-full h-max">
        {resources.slice(0, 4).map((resource) => (
          <ResourceCard key={resource.id} resource={resource} />
        ))}
      </div>

      <Link href="/library">
        <Button className="bg-blue-950 hover:bg-blue-950/90 rounded-xl p-6 text-white text-lg sm:text-xl flex justify-center items-center">
          View more <ArrowRight className="h-6 w-6 ml-2" />
        </Button>
      </Link>
    </div>
  );
};

export default Guides;
