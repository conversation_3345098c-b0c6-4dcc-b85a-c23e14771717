"use client";

import type React from "react";
import { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import Lot<PERSON> from "lottie-react";
import { ArrowRight, Search } from "lucide-react";
import animationData from "@/app/assets/lotties/HomePageHeader.json";
import { findClosestMatch } from "@/app/utils/fuzzy-search";
import { useCounselors } from "@/app/hooks/public/useCounselors";
import useClickOutside from "@/app/hooks/student/useClickOutside";

const SERVICES = [
  "Personal Statement Guidance",
  "Essay Review",
  "University Shortlisting",
  "Extra Curricular Profile Building",
  "Supplementary Essay Guidance",
  "Financial Aid Advice",
  "Interview Preparation",
];

export default function Hero() {
  const router = useRouter();
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const { setFilters } = useCounselors();

  const searchContainerRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Scroll search bar into view on mobile when suggestions open
  useEffect(() => {
    if (isExpanded && window.innerWidth < 768) {
      const searchInput = searchInputRef.current;
      if (searchInput) {
        const rect = searchInput.getBoundingClientRect();
        const scrollTop = window.scrollY || document.documentElement.scrollTop;
        const viewportHeight = window.innerHeight;
        // Position the search input 40% from the top of the screen
        const targetPosition = rect.top + scrollTop - viewportHeight * 0.45;

        window.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });
      }
    }
  }, [isExpanded]);

  useClickOutside(searchContainerRef, () => {
    setIsExpanded(false);
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchValue || !findClosestMatch(searchValue, SERVICES)) {
      router.push("/explore");
      return;
    }
    const match = findClosestMatch(searchValue, SERVICES);
    if (match) {
      setSearchValue(match);
      setFilters({ service_type: [match] });
      router.push("/explore");
    }
  };

  const handleSuggestionClick = (service: string) => {
    setSearchValue(service);
    setFilters({ service_type: [service] });
    router.push("/explore");
  };

  return (
    <div className="relative w-full pb-10 overflow-hidden bg-white font-clash-display">
      {/* Lottie Background */}
      <div className="hidden md:inline-block w-full transform md:-mt-24 2xl:-mt-0 z-10">
        <Lottie
          animationData={animationData}
          loop={true}
          autoplay={true}
          style={{ width: "100%", height: "65rem" }}
        />
      </div>

      {/* Content Layer */}
      <div className="relative md:absolute inset-0 mx-auto container pt-10 md:pt-[15vh] px-3 sm:px-6 lg:px-8 text-center">
        <h1 className="mb-6 font-[600] tracking-tight leading-[1.2] md:leading-[1.34] text-blue-950 text-[2.2rem] md:text-[4.5vw] lg:text-[4vw] xl:text-[3vw]">
          Find the Right Guidance for
          <br className="my-4 md:inline-block hidden" /> Your{" "}
          <span className="text-[#800000] transition-all duration-500 ease-in-out hover:text-[#8B0000] hover:drop-shadow-sm cursor-default">
            College
          </span>{" "}
          Journey
        </h1>
        <p
          className="mx-auto mb-12 max-w-2xl leading-[1.2] md:leading-[1.34] text-gray-600"
          style={{
            fontSize: "clamp(1rem, 2.5vw, 1.25rem)",
          }}
        >
          Search and connect with experienced mentors
          <br className="my-4 md:inline-block hidden" /> to get personalized
          guidance for your university applications
        </p>

        {/* Search Form */}
        <div
          ref={searchContainerRef}
          className="mx-auto w-[95%] md:w-[50vw] lg:w-[40vw] max-w-2xl relative"
        >
          <form onSubmit={handleSubmit} className="relative">
            <div className="relative flex items-center">
              <Search className="absolute left-4 text-gray-500" />
              <input
                ref={searchInputRef}
                type="text"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                onFocus={() => setIsExpanded(true)}
                placeholder="What do you need help with today?"
                className="w-full rounded-lg bg-white py-4 md:py-8 pl-12 pr-12 text-[13px] md:text-base shadow-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-800 focus:border-transparent placeholder:text-gray-500 placeholder:text-[13px] md:placeholder:text-base"
              />
              <button
                type="submit"
                className="absolute right-2 rounded-lg bg-blue-950 p-2 text-white hover:bg-blue-950/90"
              >
                <ArrowRight className="h-5 w-5" />
              </button>
            </div>
          </form>

          {/* Suggestions Dropdown */}
          <div
            className={`
                fixed md:absolute left-0 right-0 md:top-full bottom-0 md:bottom-auto md:mt-2
                bg-white rounded-t-2xl md:rounded-lg shadow-2xl border border-gray-200 p-4
                z-[9999] h-[60vh] md:h-auto md:max-h-[300px] overflow-y-auto transition-all duration-300 ease-out
                ${
                  isExpanded
                    ? "translate-y-0 opacity-100"
                    : "translate-y-full md:translate-y-2 opacity-0 pointer-events-none"
                }
              `}
          >
            <h3 className="text-sm font-medium text-gray-500 mb-3">
              Popular searches
            </h3>
            <div className="grid grid-cols-1 sm:flex sm:flex-wrap gap-2">
              {SERVICES.map((service) => (
                <button
                  key={service}
                  type="button"
                  onClick={() => {
                    handleSuggestionClick(service);
                    setIsExpanded(false);
                  }}
                  className="px-4 py-3 sm:px-3 sm:py-1.5 rounded-full bg-gray-100 text-[15px] sm:text-sm text-gray-700 hover:bg-gray-200 transition-colors cursor-pointer text-left sm:text-center"
                >
                  {service}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
