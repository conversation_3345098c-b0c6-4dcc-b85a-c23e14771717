"use client";

import Image from "next/image";
import React, { useEffect, useState } from "react";
import Slider from "react-infinite-logo-slider";

const logos_path = [
  "/images/institution-logos/1.png",
  "/images/institution-logos/2.png",
  "/images/institution-logos/3.svg",
  "/images/institution-logos/4.png",
  "/images/institution-logos/5.png",
  "/images/institution-logos/6.png",
  "/images/institution-logos/7.png",
  "/images/institution-logos/8.png",
  "/images/institution-logos/9.png",
  "/images/institution-logos/10.png",
  "/images/institution-logos/11.png",
];

const LogoSlider = () => {
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    setLoaded(true);
  }, []);
  return (
    <div className="container mx-auto flex flex-col justify-center items-start md:-mt-48 lg:-mt-32 xl:-mt-12 2xl:mt-10 mb-8 sm:mb-10 md:mb-16 sm:gap-y-8 gap-y-4 px-4 md:px-8 lg:px-16 xl:px-10 z-10">
      <div className="flex gap-1 md:gap-4 justify-start md:justify-center items-center">
        <h1 className="font-medium text-black text-2xl">
          Counselors from Top Universities:
        </h1>
        <Image
          src={"/svgs/red-arrow-diagonal.svg"}
          width={50}
          height={50}
          alt=""
          className="animate-pendulum origin-center"
        />
      </div>
      <div className="w-full overflow-hidden py-6 sm:py-10  bg-white rounded-xl shadow-transparent drop-shadow-[0_10px_30px_rgba(0,0,0,0.05)]">
        <Slider
          width={loaded && window.innerWidth < 768 ? "100px" : "160px"}
          duration={40}
          pauseOnHover={false}
          blurBorders={true}
          blurBorderColor="#fff"
        >
          {logos_path.map((logo, index) => (
            <Slider.Slide key={index}>
              <Image
                width={800}
                height={800}
                src={logo}
                alt={`Logo ${index + 1}`}
                className="w-[3.3rem] md:w-20 object-cover h-auto"
              />
            </Slider.Slide>
          ))}
        </Slider>
      </div>
    </div>
  );
};

export default LogoSlider;
