"use client";

import { Check, Rocket, Zap } from "lucide-react";
import { Button } from "@components/ui/button";
import Link from "next/link";

interface PricingCardProps {
  title: string;
  description: string;
  price: number;
  features: string[];
  icon: React.ReactNode;
  isPopular?: boolean;
  className?: string;
}

function PricingCard({
  title,
  description,
  price,
  features,
  icon,
  isPopular,
  className,
}: PricingCardProps) {
  return (
    <div
      className={`relative rounded-2xl p-8 ${
        isPopular ? "bg-blue-950 text-white" : "bg-white border border-gray-200"
      } ${className}`}
    >
      {isPopular && (
        <div className="absolute top-0 left-0 right-0 w-full flex justify-center">
          <div className="bg-red-700 text-white px-4 py-1 rounded-full text-sm font-medium">
            MOST POPULAR
          </div>
        </div>
      )}

      <div className="flex flex-col gap-6">
        <div className="text-4xl mb-2">{icon}</div>

        <div>
          <h3 className="text-2xl font-bold tracking-tight">{title}</h3>
          <p
            className={`mt-2 ${isPopular ? "text-gray-200" : "text-gray-600"}`}
          >
            {description}
          </p>
        </div>

        <div className="flex items-baseline">
          <span className="text-4xl font-bold tracking-tight">
            ${price.toLocaleString()}
          </span>
        </div>

        <ul className="space-y-4">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center gap-3">
              <Check
                className={`h-5 w-5 flex-shrink-0 ${
                  isPopular ? "text-white" : "text-blue-950"
                }`}
              />
              <span>{feature}</span>
            </li>
          ))}
        </ul>

        <Button
          className={`w-full ${
            isPopular
              ? "bg-white text-blue-950 hover:bg-gray-100"
              : "bg-blue-950 text-white hover:bg-blue-900"
          }`}
        >
          Get started
        </Button>
      </div>
    </div>
  );
}

const pricingData = [
  {
    title: "ESSENTIAL PACKAGE",
    description: "Ideal for focused, essential support",
    price: 1000,
    icon: <Rocket className="text-blue-950" />,
    features: [
      "12 Hours of 1:1 Mentoring",
      "College Selection",
      "Essay Brainstorming & Review",
      "Supplemental Essays Guide",
      "Profile Building",
      "Financial Aid Advice",
    ],
  },
  {
    title: "PREMIUM PACKAGE",
    description: "For personalized attention and deeper support",
    price: 1500,
    icon: "😎",
    features: [
      "20 Hours of Mentoring",
      "Personalized College Selection Strategy",
      "Essay Development & Revisions",
      "Supplemental Essays Guide",
      "Profile Building",
      "Priority Financial Aid Support",
    ],
  },
  {
    title: "ELITE PACKAGE",
    description: "Complete, 24/7 support and exclusive guidance",
    price: 2500,
    icon: <Zap className="text-yellow-400" />,
    features: [
      "30 Hours of Mentoring",
      "Personalized College Selection Strategy",
      "Essay Development & Revisions",
      "Profile Building",
      "Priority Financial Aid Support",
      "Dedicated Manager + Founders' Guidance",
    ],
    isPopular: true,
  },
];

export default function Pricing() {
  return (
    <div className="container mx-auto flex flex-col justify-center items-center my-10 md:my-20 gap-y-6 md:gap-y-12">
      <div className="w-full flex flex-col items-center justify-center gap-y-2.5 md:gap-y-4 text-center drop-shadow-lg px-3">
        <div className="self-stretch text-2xl sm:text-4xl lg:text-5xl font-semibold text-zinc-900">
          Our Tailored Mentorship Packages: Choose the Right Fit for You
        </div>
        <div className="text-lg md:text-xl text-blue-950 px-2">
          At AdmitPath, we understand that every student has unique aspirations,
          needs, and dreams. Our counselors are current students and alumni from
          the universities you dream of attending, offering up-to-date insights
          and a personalized experience. For less than what other agencies
          charge, you will receive more hours, expert advice, and dedicated
          support.
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 w-full h-max px-4 md:px-8 md:mt-10">
        {pricingData.map((plan, index) => (
          <PricingCard key={index} {...plan} />
        ))}
      </div>

      <Link href="/auth/signup">
        <Button className="bg-blue-950 rounded-xl p-6 text-white text-lg sm:text-xl flex justify-center items-center">
          Get in Touch
        </Button>
      </Link>
    </div>
  );
}
