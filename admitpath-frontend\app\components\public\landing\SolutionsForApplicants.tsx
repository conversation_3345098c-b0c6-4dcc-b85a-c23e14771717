"use client";

import React from "react";
import Link from "next/link";
import { Button } from "../../ui/button";
import { ArrowRight } from "lucide-react";
import Image from "next/image";
import { useProfile } from "@/app/hooks/student/useProfile";
import { generatePlaceholder } from "@/app/utils/image";

const CardItem = ({
  profile_picture_url,
  title,
  description,
  color,
}: {
  profile_picture_url: string;
  title: string;
  description: string;
  color: string;
}) => {
  return (
    <div
      className={`flex flex-col xl:flex-row items-center justify-center gap-3 rounded-xl drop-shadow-sm hover:scale-[102%] hover:drop-shadow-2xl transition-all duration-300 cursor-pointer border border-solid border-x-[ghostwhite] border-y-[ghostwhite] p-5 xs:p-6 sm:p-8`}
      style={{ backgroundColor: color }}
    >
      <Image
        src={
          profile_picture_url ||
          generatePlaceholder(title.split(" ")[0], title.split(" ")[1])
        }
        alt={title}
        className="h-32 w-32"
        width={1000}
        height={1000}
      />
      <div className="flex flex-col gap-3">
        <h1 className="font-bold text-xl md:text-2xl text-gray-900">{title}</h1>
        <p className="text-lg text-gray-800/90">{description}</p>
      </div>
    </div>
  );
};

const SolutionsForApplicants = () => {
  const { userInfo } = useProfile();

  const cardData = [
    {
      profile_picture_url: "/images/objects/books.png",
      title: "Essay & Personal Statement Reviews",
      description: "Get feedback from experts who've been there",
      color: "#DFFFB5",
    },
    {
      profile_picture_url: "/images/objects/page.png",
      title: "University Selection & Shortlisting",
      description:
        "Craft a personalized university list based on your profile, goals, and preferences",
      color: "#BCFBFE",
    },
    {
      profile_picture_url: "/images/objects/bulb.png",
      title: "Supplemental Essay Brainstorming",
      description:
        "Showcase your strengths and create a standout application beyond academics",
      color: "#FFF2AB",
    },
    {
      profile_picture_url: "/images/objects/stats.png",
      title: "Extracurricular Profile Building",
      description:
        "Get personalized advice to craft essays for your dream universities",
      color: "#FFE1E1",
    },
    {
      profile_picture_url: "/images/objects/pouch.png",
      title: "Financial Aid Guidance",
      description:
        "Navigate scholarships, grants, and funding opportunities effortlessly",
      color: "#FFD9C2",
    },
  ];

  return (
    <div className="container mx-auto flex flex-col justify-center items-center my-10 md:my-20 gap-y-6 md:gap-y-12 px-4 md:px-8 lg:px-16 xl:px-10">
      <div className="w-full flex flex-col items-center justify-center gap-y-2.5 md:gap-y-4 text-center drop-shadow-lg px-3">
        <div className="self-stretch text-2xl sm:text-4xl lg:text-5xl font-semibold text-zinc-900">
          Solution for Every Applicant
        </div>
        <div className="text-lg md:text-xl text-blue-950 px-2">
          Find tailored guidance for every step of your application process
        </div>
      </div>
      <div className="w-full h-max hidden lg:flex flex-col gap-6 md:mt-10">
        <div className="grid grid-cols-3 gap-6 w-full h-max">
          {cardData.slice(0, 3).map((card, index) => (
            <CardItem
              key={index}
              profile_picture_url={card.profile_picture_url}
              title={card.title}
              description={card.description}
              color={card.color}
            />
          ))}
        </div>
        <div className="grid grid-cols-2 gap-6 w-full h-max">
          {cardData.slice(3, 5).map((card, index) => (
            <CardItem
              key={index}
              profile_picture_url={card.profile_picture_url}
              title={card.title}
              description={card.description}
              color={card.color}
            />
          ))}
        </div>
      </div>
      <div className="lg:hidden grid grid-cols-1 md:grid-cols-2 gap-6 w-full h-max px-4 md:px-8 md:mt-10">
        {cardData.map((card, index) => (
          <CardItem
            key={index}
            profile_picture_url={card.profile_picture_url}
            title={card.title}
            description={card.description}
            color={card.color}
          />
        ))}
      </div>
      {userInfo ? (
        <Link
          href={
            userInfo.userType === "counselor"
              ? "/counselor/dashboard"
              : "/student/dashboard"
          }
        >
          <Button className="bg-blue-950 hover:bg-blue-950/90 rounded-xl p-6 text-white text-lg sm:text-xl flex justify-center items-center">
            Go to Dashboard <ArrowRight className="h-6 w-6 ml-2" />
          </Button>
        </Link>
      ) : (
        <Link href="/auth/signup">
          <Button className="bg-blue-950 hover:bg-blue-950/90 rounded-xl p-6 text-white text-lg sm:text-xl flex justify-center items-center">
            Start Now <ArrowRight className="h-6 w-6 ml-2" />
          </Button>
        </Link>
      )}
    </div>
  );
};

export default SolutionsForApplicants;
