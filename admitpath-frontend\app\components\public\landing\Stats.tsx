import React from "react";

const Stats = () => {
  return (
    <div className="container mx-auto my-6 sm:my-12 py-6 md:py-16 flex items-center justify-center  self-stretch text-center text-xl font-medium leading-[1.8] text-gray-800/90 bg-white rounded-xl drop-shadow-[0_10px_30px_rgba(0,0,0,0.05)]">
      <div className="flex flex-grow flex-col justify-center items-center gap-y-0.5">
        <div className="text-center text-3xl md:text-4xl lg:text-6xl font-bold leading-[normal] tracking-[0.16px] text-red-800">
          50+
        </div>
        <div className="text-center text-lg md:text-xl">Counsellors</div>
      </div>
      <div className="flex flex-grow flex-col justify-center items-center gap-y-0.5">
        <div className="text-center text-3xl md:text-4xl lg:text-6xl font-bold leading-[normal] tracking-[0.16px] text-red-800">
          20+
        </div>
        <div className="text-center text-lg md:text-xl">Universities</div>
      </div>
      <div className="flex flex-grow flex-col justify-center items-center gap-y-0.5">
        <div className="text-center text-3xl md:text-4xl lg:text-6xl font-bold leading-[normal] tracking-[0.16px] text-red-800">
          10+
        </div>
        <div className="text-center text-lg md:text-xl">Countries</div>
      </div>
    </div>
  );
};

export default Stats;
