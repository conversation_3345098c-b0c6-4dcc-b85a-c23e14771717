"use client";

import React, { useState, useEffect, useRef } from "react";

const TestimonialsSection = () => {
  const testimonials = [
    {
      quote:
        "Before AdmitPath, I was overwhelmed with essays, deadlines, and choosing the right schools. My advisor helped me organize everything and gave me a strategy that worked. I got into my top-choice school!",
      author: "<PERSON>",
      role: "Incoming College Freshman",
      highlight: "I finally had a clear plan for my applications.",
    },
    {
      quote:
        "I thought my Common App essay was strong, but my AdmitPath advisor helped me refine it to truly reflect my story. The final version was much more compelling, and I got accepted into multiple Ivy League schools!",
      author: "<PERSON>",
      role: "Admitted to Columbia University",
      highlight: "The essay feedback completely changed my application!",
    },
    {
      quote:
        "I was so nervous about my interviews, but my AdmitPath advisor gave me personalized feedback and mock interview practice. I went in prepared and confident—and got into my dream school!",
      author: "<PERSON>",
      role: "Admitted to Stanford University",
      highlight:
        "I booked a session for interview prep, and it made all the difference.",
    },
    {
      quote:
        "I needed urgent help with my supplemental essays, and <PERSON>mit<PERSON><PERSON> connected me with an expert in just a few hours. Their feedback was incredibly detailed, and I know it helped strengthen my application!",
      author: "<PERSON>",
      role: "Admitted to NYU",
      highlight: "Fast, reliable, and actually helpful.",
    },
    {
      quote:
        "I didn't know how to showcase my extracurriculars to maximize merit-based scholarships. My advisor helped me position everything strategically, and I got into a top university with a full-ride scholarship!",
      author: "Daniel R.",
      role: "Admitted to USC with a Full-Ride Scholarship",
      highlight: "I secured over $80,000 in scholarships thanks to my advisor.",
    },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);
  const [cardWidth, setCardWidth] = useState(33.333);
  const [maxHeight, setMaxHeight] = useState(0);
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    const missionSection: HTMLElement | null =
      document.querySelector(".mission-section");
    const words: NodeListOf<HTMLElement> = document.querySelectorAll(
      ".mission-section .word"
    );

    const updateOpacity = (): void => {
      if (!missionSection) return;

      const sectionTop: number = missionSection.offsetTop;
      const sectionHeight: number = missionSection.offsetHeight;
      const scrollY: number = window.scrollY;
      const windowHeight: number = window.innerHeight;

      const sectionStart: number =
        sectionTop - windowHeight + sectionHeight * 0.4;
      const sectionEnd: number = sectionTop + sectionHeight;

      if (scrollY >= sectionStart && scrollY <= sectionEnd) {
        const scrollProgress: number =
          (scrollY - sectionStart) / (sectionEnd - sectionStart);

        words.forEach((word: HTMLElement, index: number) => {
          const wordProgress: number =
            scrollProgress - index * (0.5 / words.length);
          const opacity: number = Math.min(
            Math.max(wordProgress * words.length, 0.2),
            1
          );
          word.style.opacity = opacity.toString();
        });
      } else if (scrollY < sectionStart) {
        words.forEach((word: HTMLElement) => (word.style.opacity = "0.2"));
      } else if (scrollY > sectionEnd) {
        words.forEach((word: HTMLElement) => (word.style.opacity = "1"));
      }
    };

    window.addEventListener("scroll", updateOpacity);
    updateOpacity();

    return () => window.removeEventListener("scroll", updateOpacity);
  }, []);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 640) {
        setCardWidth(100);
      } else if (window.innerWidth < 1024) {
        setCardWidth(50);
      } else {
        setCardWidth(33.333);
      }

      // Reset height calculation
      setTimeout(() => {
        const heights = cardRefs.current
          .filter((ref): ref is HTMLDivElement => ref !== null)
          .map((ref) => ref.scrollHeight);
        const maxCardHeight = Math.max(...heights);
        setMaxHeight(maxCardHeight);
      }, 100);
    };

    window.addEventListener("resize", handleResize);
    handleResize();
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const nextSlide = () => {
    if (currentIndex < testimonials.length - 1) {
      setCurrentIndex((prev) => prev + 1);
    }
  };

  const prevSlide = () => {
    if (currentIndex > 0) {
      setCurrentIndex((prev) => prev - 1);
    }
  };

  return (
    <section className="w-full bg-gray-50 px-4 sm:px-6 md:px-8 lg:px-12 xl:px-16 2xl:px-24 py-12 sm:py-16">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <div className="mission-section mb-8">
            <h2 className="mission_text text-2xl sm:text-3xl text-blue-950 font-bold">
              {"Join thousands of students who found their path to success with AdmitPath".split(" ").map((word: string, index: number) => (
                <span
                  key={index}
                  className="word inline-block opacity-20 leading-10 transition-opacity duration-300 ease-in-out"
                >
                  {word}&nbsp;
                </span>
              ))}
            </h2>
          </div>
        </div>

        <div className="relative">
          <div className="overflow-hidden">
            <div
              className="flex transition-transform duration-500 ease-in-out py-2"
              style={{
                transform: `translateX(-${currentIndex * cardWidth}%)`,
              }}
            >
              {testimonials.map((testimonial, index) => (
                <div
                  key={index}
                  className="w-full md:w-1/2 lg:w-1/3 flex-shrink-0 px-2 sm:px-3 md:px-4"
                >
                  <div
                    ref={(el) => {
                      cardRefs.current[index] = el;
                    }}
                    className="bg-white rounded-lg p-6 transition-all duration-200 flex flex-col h-full border hover:shadow-md"
                    style={{ minHeight: maxHeight ? `${maxHeight}px` : "auto" }}
                  >
                    <div className="text-blue-950 font-medium text-base mb-3">
                      {testimonial.highlight}
                    </div>
                    <p className="text-gray-600 text-sm leading-relaxed flex-grow">
                      "{testimonial.quote}"
                    </p>
                    <div className="mt-4 pt-4 border-t">
                      <p className="font-semibold text-gray-900 text-sm">
                        {testimonial.author}
                      </p>
                      <p className="text-gray-500 text-xs">
                        {testimonial.role}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Navigation Controls */}
          <div className="mt-8 flex items-center justify-center gap-4 px-4">
            <button
              onClick={prevSlide}
              disabled={currentIndex === 0}
              className="w-8 h-8 rounded-full bg-white flex items-center justify-center text-blue-950 hover:bg-gray-50 disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 border"
              aria-label="Previous testimonial"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            {/* Progress Indicators */}
            <div className="flex gap-1.5">
              {testimonials.map((_, idx) => (
                <button
                  key={idx}
                  onClick={() => setCurrentIndex(idx)}
                  className={`w-2 h-2 rounded-full transition-all duration-200 ${idx === currentIndex ? 'bg-blue-950' : 'bg-gray-300 hover:bg-gray-400'}`}
                  aria-label={`Go to testimonial ${idx + 1}`}
                />
              ))}
            </div>

            <button
              onClick={nextSlide}
              disabled={currentIndex === testimonials.length - 1}
              className="w-8 h-8 rounded-full bg-white flex items-center justify-center text-blue-950 hover:bg-gray-50 disabled:opacity-40 disabled:cursor-not-allowed transition-all duration-200 border"
              aria-label="Next testimonial"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
