"use client";

import Link from "next/link";
import React, { useEffect, useId, useState } from "react";
import * as CountryFlags from "country-flag-icons/react/3x2";

const getCountryFlag = (countryCode: string = "US") => {
  const code = countryCode.toUpperCase();
  const FlagComponent = (CountryFlags as any)[code];
  return FlagComponent ? <FlagComponent /> : <CountryFlags.US />;
};
import { Button } from "../../ui/button";
import { ArrowRight, GraduationCap } from "lucide-react";

import { Spinner } from "@/app/components/ui/spinner";
import { CounselorPublicProfile } from "@/app/types/counselor";
import { useCounselors } from "@/app/hooks/public/useCounselors";
import Slider from "react-infinite-logo-slider";
import { generatePlaceholder } from "@/app/utils/image";
import Image from "next/image";

export const Card = ({
  profile,
}: {
  profile: Partial<CounselorPublicProfile>;
}) => {
  return (
    <Link
      href={`/profile/${profile.user_id}`}
      className="block w-[280px] sm:w-[300px] md:w-[320px] min-h-[390px] sm:min-h-[410px] max-h-[390px] sm:max-h-[410px] mx-3 sm:mx-4 md:mx-5 my-4 sm:my-6 md:my-8 z-10 rounded-xl bg-white shadow-md hover:shadow-xl transition-all duration-500 cursor-pointer overflow-hidden hover:scale-[1.02] hover:-translate-y-1 hover:rotate-3 hover:ring-1 hover:ring-blue-950/30"
    >
      <div className="relative h-[220px] w-full">
        <Image
          width={300}
          height={180}
          className="w-full h-full object-cover"
          src={
            profile.profile_picture_url ||
            generatePlaceholder(profile.first_name, profile.last_name)
          }
          alt={`${profile.first_name}'s profile`}
        />
        <div className="absolute top-2 left-2 w-6 h-4 rounded-sm overflow-hidden shadow-md">
          {getCountryFlag(profile.country_of_residence)}
        </div>
      </div>

      <div className="p-4 flex flex-col h-[190px]">
        <div className="space-y-2 mb-2">
          <div className="flex flex-col gap-1.5">
            <div className="flex items-start justify-between gap-3">
              <h3 className="text-xl font-semibold text-gray-900 truncate">
                {profile.first_name}
              </h3>
              <p className="text-lg font-[500] text-blue-950 whitespace-nowrap flex-shrink-0">
                ${profile.hourly_rate}/hr
              </p>
            </div>
          </div>

          <div className="h-[36px] text-sm text-gray-600 mt-2">
            <p className="line-clamp-2">
              {profile?.tagline || profile?.bio || ""}
            </p>
          </div>

          <div className="h-[24px] mt-0.5">
            {profile.education && profile.education.length > 0 && (
              <div className="flex items-center gap-2 text-sm">
                <GraduationCap className="w-4 h-4 text-blue-950 flex-shrink-0" />
                <p className="line-clamp-1 font-[500] text-[15px] text-blue-950">
                  {profile.education[0].university_name ||
                    profile.education[0].major}
                </p>
              </div>
            )}
          </div>
        </div>

        <div className="border-t pt-1.5 mt-auto pb-1">
          <div className="relative overflow-hidden">
            <div className="absolute left-0 top-0 bottom-0 w-6 bg-gradient-to-r from-white via-white to-transparent z-10"></div>
            <div className="absolute right-0 top-0 bottom-0 w-6 bg-gradient-to-l from-white via-white to-transparent z-10"></div>
            <div className="flex whitespace-nowrap animate-[scroll_15s_linear_infinite] hover:pause-animation">
              <div className="flex gap-1.5 shrink-0">
                {[
                  "Essay Review",
                  "Personal Statement",
                  "College List Building",
                  "Essay Brainstorming",
                ].map((service, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center whitespace-nowrap px-2 py-0.5 bg-blue-950/5 text-blue-950 text-[11px] font-medium border border-blue-950/10 rounded hover:bg-blue-950/10 transition-colors"
                  >
                    {service}
                  </span>
                ))}
              </div>
              <div className="flex gap-1.5 shrink-0">
                {[
                  "Essay Review",
                  "Personal Statement",
                  "College List Building",
                  "Essay Brainstorming",
                ].map((service, index) => (
                  <span
                    key={`duplicate-${index}`}
                    className="inline-flex items-center whitespace-nowrap px-2 py-0.5 bg-blue-950/5 text-blue-950 text-[11px] font-medium border border-blue-950/10 rounded hover:bg-blue-950/10 transition-colors"
                  >
                    {service}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

const TopCounselors = () => {
  const [topCounselors, setTopCounselors] = useState<CounselorPublicProfile[]>(
    []
  );
  const { fetchTopCounselors, loading } = useCounselors();
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    let isMounted = true;

    const loadTopCounselors = async () => {
      try {
        const counselors = await fetchTopCounselors();
        setLoaded(true);
        if (isMounted) {
          setTopCounselors(counselors || []);
        }
      } catch (error) {
        console.error("Error loading counselors:", error);
        if (isMounted) {
          setTopCounselors([]);
        }
      }
    };

    loadTopCounselors();

    return () => {
      isMounted = false;
    };
  }, [fetchTopCounselors]);

  return (
    <div className="container max-w-8xl mx-auto flex flex-col justify-center items-center my-10 md:my-20 gap-y-6 md:gap-y-12 px-4 md:px-8 lg:px-16 xl:px-10">
      <div className="w-full flex flex-col items-center justify-center gap-y-2.5 md:gap-y-4 text-center px-3">
        <div className="self-stretch text-2xl sm:text-4xl lg:text-5xl font-medium text-zinc-900">
          Meet Our Top Counselors
        </div>
        <div className="text-lg md:text-xl text-blue-950 px-2">
          Explore our most trusted and highly rated mentors, ready to guide you
          to success
        </div>
      </div>

      <div className="w-full overflow-hidden">
        {loading && topCounselors.length === 0 ? (
          <div className="flex justify-center items-center min-h-[300px]">
            <Spinner size="lg" />
          </div>
        ) : (
          <Slider
            width={loaded && window.innerWidth < 640 ? "330px" : "350px"}
            duration={60}
            pauseOnHover={true}
            blurBorders={false}
          >
            {[...topCounselors, ...topCounselors].map((counselor, index) => (
              <Slider.Slide key={index}>
                <Card profile={counselor} />
              </Slider.Slide>
            ))}
          </Slider>
        )}
      </div>

      <Link href="/explore">
        <Button className="bg-blue-950 hover:bg-blue-950/90 rounded-xl p-6 text-white text-lg sm:text-xl flex justify-center items-center">
          View More <ArrowRight className="h-6 w-6 ml-2" />
        </Button>
      </Link>
    </div>
  );
};

export default TopCounselors;
