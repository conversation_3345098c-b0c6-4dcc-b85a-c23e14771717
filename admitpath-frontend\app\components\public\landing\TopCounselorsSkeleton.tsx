import { Skeleton } from "@components/ui/skeleton";

export const TopCounselorsSkeleton = () => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 xl:gap-8 items-center justify-center w-full h-max md:mt-10">
      {[...Array(4)].map((_, index) => (
        <div key={`counselor-skeleton-${index}`} className="rounded-xl relative">
          <Skeleton className="w-full h-[24rem] rounded-xl" />
          <div className="absolute w-full h-full flex flex-col justify-end p-4 md:p-6 gap-y-3 rounded-xl inset-0">
            <div className="flex justify-between">
              <Skeleton className="h-8 w-32" />
              <Skeleton className="h-6 w-24" />
            </div>
            <Skeleton className="flex justify-between p-4 sm:py-6 sm:px-5 bg-[#6b6b6b] rounded-lg mt-2.5 sm:mt-4" />
          </div>
        </div>
      ))}
    </div>
  );
};

export default TopCounselorsSkeleton;
