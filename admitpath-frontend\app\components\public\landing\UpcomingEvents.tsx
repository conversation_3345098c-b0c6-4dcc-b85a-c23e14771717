import React from "react";
import { <PERSON>R<PERSON>, Clock } from "lucide-react"; // Adjust the import based on your icon library

const UpcomingEvents = () => {
  return (
    <div className="z-0 flex items-center self-stretch">
      <div className="relative flex h-full w-full flex-shrink-0 flex-col items-center justify-center gap-y-20 overflow-clip bg-white px-[120px] py-24">
        <div className="flex flex-col items-center gap-y-[15px] self-stretch text-center leading-[1.6] text-neutral-950 drop-shadow-lg">
          <div className="self-stretch text-5xl font-medium leading-[1.6]">
            Upcoming events
          </div>
          <div className="flex w-[1102px] items-center justify-center text-2xl leading-[1.6]">
            <p className="text-center">
              Lorem ipsum dolor sit amet consectetur. Phasellus non pellentesque
              sem in amet id. Vehicula consequat dolor commodo nunc quis et. A
              tellus enim
            </p>
          </div>
        </div>

        <div className="flex w-[896px] flex-wrap items-start justify-center drop-shadow-lg min-[1910px]:flex-nowrap">
          <img
            className="h-[482px] w-96 flex-shrink-0 rounded object-cover object-center"
            src="/images/people/1.jpeg"
            loading="lazy"
          />
          <div className="flex w-[503px] flex-shrink-0 flex-col items-start gap-y-4 rounded-r-[10px] border border-solid border-x-[lightgray] border-y-[lightgray] bg-white px-9 pb-16 pt-[18px]">
            <div className="flex w-96 items-start text-2xl font-medium leading-[38px] text-indigo-950">
              <p>Get Insights From Worlds Biggest Conference</p>
            </div>
            <div className="self-stretch pt-1 [max-width:426px]">
              <div className="flex items-center leading-[1.8] text-neutral-950">
                <p>
                  We make sure that the creators get their fees via community
                  marketplaces—no matter where...
                </p>
              </div>
            </div>
            <div className="flex flex-wrap items-center gap-x-2 gap-y-2 pt-[22px] min-[1910px]:flex-nowrap">
              <div className="bg-background-1 h-10 max-h-full w-10 max-w-full flex-shrink-0 rounded-full bg-no-repeat [background-position:0px_-1.26px] [background-size:100%_150%]" />
              <div className="pl-1 text-sm leading-[1.8] text-[lightslategray]">
                Instructor:
              </div>
              <div className="font-medium leading-[1.4] text-neutral-950">
                Martha Young
              </div>
            </div>
            <div className="flex flex-wrap items-center gap-x-[9px] gap-y-[9px] min-[1910px]:flex-nowrap">
              <Clock className="h-5 w-5 flex-shrink-0" />
              <div className="font-medium leading-[1.8] text-[dimgray]">
                Friday 20th Dec 2025, 5PM GMT
              </div>
            </div>
            <div className="flex flex-wrap items-center gap-x-16 gap-y-[13px] pt-14 text-sm font-medium leading-[1.8] min-[1910px]:flex-nowrap">
              <div className="flex items-center justify-center gap-x-3 rounded-xl bg-blue-950 px-11 py-3">
                <div className="text-white">Register now</div>
                <ArrowRight className="h-2.5 w-[19px] flex-shrink-0" />
              </div>
              <div className="flex items-center justify-center gap-x-3">
                <div className="text-zinc-900">View Details</div>
                <ArrowRight className="h-2.5 w-[19px] flex-shrink-0" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UpcomingEvents;
