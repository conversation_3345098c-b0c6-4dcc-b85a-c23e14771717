"use client";

import { zod<PERSON><PERSON>ol<PERSON> } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@components/ui/button";
import { Input } from "@components/ui/input";
import { useNewsletter } from "@/app/hooks/public/useNewsletter";

const newsletterSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
});

type NewsletterFormData = z.infer<typeof newsletterSchema>;

export function NewsletterForm() {
  const { subscribe, loading } = useNewsletter();
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<NewsletterFormData>({
    resolver: zodResolver(newsletterSchema),
  });

  const onSubmit = async (data: NewsletterFormData) => {
    try {
      await subscribe(data);
      reset();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="container mx-auto max-w-7xl my-10 md:my-20  px-4 sm:px-6">
      <div className="bg-gradient-to-br from-blue-950 to-black/90 rounded-xl px-6 md:px-10 py-10 md:py-16 relative overflow-hidden">
        {/* Decorative dots pattern */}
        {/* <div className="absolute top-0 left-0 grid grid-cols-7 gap-8 p-10">
        {[...Array(28)].map((_, i) => (
          <div key={i} className="w-2 h-2 bg-blue-700/30 rounded-full" />
        ))}
      </div> */}

        {/* Content */}
        <div className="relative z-10 max-w-4xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-medium text-white mb-4">
            Subscribe to receive future updates
          </h2>
          <p className="text-blue-100 mb-8">
            If you want to get updates from us, please subscribe to our
            newsletter.
          </p>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Input
                  {...register("name")}
                  placeholder="Enter your name"
                  className="w-full h-12 bg-white/95 border-0 text-gray-900 placeholder:text-gray-500"
                />
                {errors.name && (
                  <p className="text-gray-300 text-sm mt-1">
                    {errors.name.message}
                  </p>
                )}
              </div>
              <div className="flex-1">
                <Input
                  {...register("email")}
                  type="email"
                  placeholder="Enter your email"
                  className="w-full h-12 bg-white/95 border-0 text-gray-900 placeholder:text-gray-500"
                />
                {errors.email && (
                  <p className="text-gray-300 text-sm mt-1">
                    {errors.email.message}
                  </p>
                )}
              </div>
              <Button
                type="submit"
                className="h-12 px-8 bg-blue-950 hover:bg-blue-950/90 text-gray-100 font-semibold transition-colors"
                disabled={loading}
              >
                {loading ? "Subscribing..." : "Subscribe"}
              </Button>
            </div>
          </form>

          {/* Benefits section */}
          {/* <div className="mt-12 flex flex-wrap gap-6 text-white text-sm">
            <span>$50 Raffle Entry</span>
            <span>Essay Feedback from Ivy Experts</span>
            <span>Claim Common App Extracurricular Entry</span>
            <span>Q&A with Top College Admits</span>
          </div> */}
        </div>
      </div>
    </div>
  );
}

export default NewsletterForm;
