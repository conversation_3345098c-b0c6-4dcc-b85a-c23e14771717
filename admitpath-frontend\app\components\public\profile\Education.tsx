"use client";
import { CounselorPublicProfile } from "@/app/types/counselor";
import { getUniversityLogoUrl } from "@/app/utils/uni-logo";
import { GraduationCap } from "lucide-react";
import { useEffect, useState } from "react";

const capitalizeFirstLetter = (str: string) => {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

interface EducationProps {
  profileData: CounselorPublicProfile;
}

export default function Education({ profileData }: EducationProps) {
  // State to store logo URLs for each university
  const [universityLogos, setUniversityLogos] = useState<
    Record<string, string>
  >({});

  useEffect(() => {
    const fetchUniversityLogos = async () => {
      if (profileData.education && profileData.education.length > 0) {
        // Create an object to store all logo URLs
        const logoUrls: Record<string, string> = {};

        // Fetch logo for each university
        for (const edu of profileData.education) {
          try {
            const logoUrl = await getUniversityLogoUrl(edu.university_name);
            logoUrls[edu.university_name] = logoUrl;
          } catch (error) {
            console.error(
              `Failed to fetch logo for ${edu.university_name}:`,
              error
            );
            // Don't set anything in logoUrls for this university, will use fallback
          }
        }

        setUniversityLogos(logoUrls);
      }
    };

    fetchUniversityLogos();
  }, [profileData.education]);

  if (!profileData.education || profileData.education.length === 0) {
    return (
      <div
        id="education"
        className="w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6"
      >
        <h2 className="text-[15px] font-medium text-blue-950 mb-3">
          Education
        </h2>
        <p className="text-[13px] text-gray-500">
          No education history available.
        </p>
      </div>
    );
  }

  return (
    <div
      id="education"
      className="w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6"
    >
      <h2 className="text-[15px] font-medium text-blue-950 mb-6">Education</h2>
      <div className="space-y-6">
        {profileData.education.map((edu, index) => (
          <div key={index} className="flex gap-4">
            <div className="flex-shrink-0 w-16 h-16 flex items-center justify-center bg-gray-100 rounded-md">
              {universityLogos[edu.university_name] ? (
                <img
                  src={universityLogos[edu.university_name]}
                  alt={edu.university_name}
                  className="w-16 h-16 object-contain border border-gray-200 rounded-md overflow-hidden shadow-sm transition-transform group-hover:scale-105"
                  onError={(e) => {
                    // If image fails to load, replace with icon
                    e.currentTarget.style.display = "none";
                    const parent = e.currentTarget.parentElement;
                    if (parent) {
                      const icon = document.createElement("div");
                      icon.className =
                        "flex items-center justify-center w-full h-full";
                      icon.innerHTML =
                        '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-gray-400"><path d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c0 2 2 3 6 3s6-1 6-3v-5"/></svg>';
                      parent.appendChild(icon);
                    }
                  }}
                />
              ) : (
                <GraduationCap className="w-8 h-8 text-gray-400" />
              )}
            </div>
            <div className="flex-grow space-y-2">
              <h3 className="text-[15px] font-medium text-blue-950">
                {edu.university_name}
              </h3>
              <p className="text-[13px] text-gray-500">
                {capitalizeFirstLetter(edu.degree)} in {edu.major}
              </p>
              <p className="text-[13px] text-gray-500">
                {edu.start_date} - {edu.end_date}
              </p>
              {edu.grade && (
                <p className="text-[13px] text-gray-500">Grade: {edu.grade}</p>
              )}
              {edu.accomplishments && (
                <p className="text-[13px] text-gray-500 mt-2">
                  {edu.accomplishments}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
