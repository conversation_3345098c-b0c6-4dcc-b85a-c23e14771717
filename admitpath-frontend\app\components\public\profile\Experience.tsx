"use client";

import { CounselorPublicProfile } from "@/app/types/counselor";
import { Briefcase } from "lucide-react";

interface ExperienceProps {
  profileData: CounselorPublicProfile;
}

export default function Experience({ profileData }: ExperienceProps) {
  if (
    !profileData.professional_experience ||
    profileData.professional_experience.length === 0
  ) {
    return (
      <div
        id="experience"
        className="w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6"
      >
        <h2 className="text-[15px] font-medium text-blue-950 mb-3">
          Experience
        </h2>
        <p className="text-[13px] text-gray-500">
          No professional experience available.
        </p>
      </div>
    );
  }

  return (
    <div
      id="experience"
      className="w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6"
    >
      <h2 className="text-[15px] font-medium text-blue-950 mb-6">Experience</h2>
      <div className="space-y-6">
        {profileData.professional_experience.map((exp, index) => (
          <div key={index} className="flex gap-4">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 rounded-lg bg-blue-50 flex items-center justify-center">
                <Briefcase className="w-5 h-5 text-blue-600" />
              </div>
            </div>
            <div className="flex-grow space-y-2">
              <h2 className="text-[15px] font-medium text-blue-950">
                {exp.company_name}
              </h2>
              <p className="text-[15px] font-medium text-blue-950">
                {exp.role}
              </p>
              <p className="text-[13px] text-gray-500">
                {exp.start_date} - {exp.end_date}
              </p>
              {exp.experience_description && (
                <p className="text-[13px] text-gray-500 mt-2">
                  {exp.experience_description}
                </p>
              )}
            </div>
          </div>
        ))}
      </div>

      {profileData.has_mentored_before &&
        profileData.experience_description && (
          <div className="mt-8 pt-6 border-t border-gray-200">
            <h3 className="text-[15px] font-medium text-blue-950 mb-3">
              Mentoring Experience
            </h3>
            <p className="text-[13px] text-gray-500">
              {profileData.experience_description}
            </p>
          </div>
        )}
    </div>
  );
}
