"use client";

import { CounselorPublicProfile } from "@/app/types/counselor";

interface OverviewProps {
  profileData: CounselorPublicProfile;
}

export function Overview({ profileData }: OverviewProps) {
  if (!profileData || !profileData.bio) {
    return (
      <div
        id="overview"
        className="w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6 mb-6"
      >
        <h2 className="text-[15px] font-medium text-blue-950 mb-3">Overview</h2>
        <p className="text-[13px] text-gray-500">
          No overview information available.
        </p>
      </div>
    );
  }

  return (
    <div
      id="overview"
      className="w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6 mb-6"
    >
      <h2 className="text-[15px] font-medium text-blue-950 mb-3">Overview</h2>
      <p className="text-[13px] text-gray-500">{profileData.bio}</p>
    </div>
  );
}
