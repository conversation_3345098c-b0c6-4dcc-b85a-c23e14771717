import { useEffect, useState } from 'react';
import { CounselorPublicProfile } from '@/app/types/counselor';
import { RecommendedCounselorCard } from './RecommendedCounselorCard';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface PopularCounselorsSliderProps {
  counselors: CounselorPublicProfile[];
}

export function PopularCounselorsSlider({ counselors }: PopularCounselorsSliderProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const handlePrev = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : counselors.length - 1));
    setTimeout(() => setIsAnimating(false), 300);
  };

  const handleNext = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentIndex((prev) => (prev < counselors.length - 1 ? prev + 1 : 0));
    setTimeout(() => setIsAnimating(false), 300);
  };

  if (counselors.length === 0) return null;

  return (
    <div className="relative">
      <div className="overflow-hidden">
        <div 
          className="flex transition-transform duration-300 ease-in-out"
          style={{ transform: `translateX(-${currentIndex * 100}%)` }}
        >
          {counselors.map((counselor) => (
            <div key={counselor.user_id} className="w-full flex-shrink-0">
              <RecommendedCounselorCard counselor={counselor} />
            </div>
          ))}
        </div>
      </div>
      
      {counselors.length > 1 && (
        <>
          <button
            onClick={handlePrev}
            className="absolute -left-4 top-1/2 -translate-y-1/2 w-8 h-8 rounded-full bg-white shadow-md flex items-center justify-center hover:bg-gray-50 transition-colors"
            aria-label="Previous counselor"
          >
            <ChevronLeft className="w-5 h-5" />
          </button>
          <button
            onClick={handleNext}
            className="absolute -right-4 top-1/2 -translate-y-1/2 w-8 h-8 rounded-full bg-white shadow-md flex items-center justify-center hover:bg-gray-50 transition-colors"
            aria-label="Next counselor"
          >
            <ChevronRight className="w-5 h-5" />
          </button>
        </>
      )}
    </div>
  );
}
