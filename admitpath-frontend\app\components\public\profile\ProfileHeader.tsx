import Image from "next/image";
import { But<PERSON> } from "@components/ui/button";
import { NavigationTabs } from "./NavigationTabs";
import { ArrowLeft } from "lucide-react";
import { CounselorPublicProfile } from "@/app/types/counselor";
import { SendMessageDialog } from "../../student/sessions/send-message-dialog";
import { useState } from "react";
import { generatePlaceholder } from "@/app/utils/image";

interface ProfileHeaderProps {
  counselorUserId: number;
  profileData: CounselorPublicProfile;
  setIsBookingOpen: (open: boolean) => void;
}

export function ProfileHeader({
  counselorUserId,
  profileData,
  setIsBookingOpen,
}: ProfileHeaderProps) {
  const [showSendMessage, setShowSendMessage] = useState(false);

  const handleCloseTab = () => {
    window.close();

    // If somehow still here
    history.back();
  };

  return (
    <div className="sticky top-0 bg-white/95 backdrop-blur-sm z-20 pt-3 sticky-header shadow-sm">
      <button
        onClick={handleCloseTab}
        className="flex items-center gap-2 text-[13px] mb-6 font-medium font-clash text-blue-950 hover:text-blue-800 transition-colors duration-200 group"
      >
        <ArrowLeft className="w-6 h-6 mr-1.5 font-medium transition-transform duration-200 group-hover:-translate-x-1" />
        Go back
      </button>

      <div className="flex flex-col md:flex-row items-start justify-between gap-5 mb-6">
        <div className="flex md:items-center gap-5">
          <div className="relative w-20 h-20 md:w-[150px] md:h-[150px] flex-shrink-0 group">
            <Image
              src={
                profileData?.profile_picture_url ||
                generatePlaceholder(
                  profileData?.first_name,
                  profileData?.last_name
                )
              }
              alt="Profile picture"
              width={150}
              height={150}
              className="rounded-full object-cover w-full h-full ring-4 ring-gray-50 transition-transform duration-300 group-hover:scale-[1.02] shadow-md"
            />
          </div>

          <div className="flex-shrink">
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-[16px] md:text-[24px] font-[500] text-blue-950 truncate">
                {profileData?.first_name || "Counselor"}{" "}
                {profileData?.last_name || ""}
              </h1>
            </div>

            <p className="text-[16px] md:text-[20px] break-words text-gray-600">
              {profileData?.tagline || ""}
            </p>

            <p className="text-[13px] md:text-[16px] text-gray-600">
              {profileData?.professional_experience?.[0]?.role || "Counselor"}
            </p>
          </div>
        </div>

        <div className="w-full md:w-auto md:min-w-[380px] p-4 md:p-5 space-y-3 md:space-y-4 border border-gray-200 rounded-xl bg-white shadow-sm hover:shadow-md hover:border-gray-300 transition-all duration-300">
          <div className="flex gap-3">
            <Button
              onClick={() => {
                localStorage.getItem("access_token")
                  ? setShowSendMessage(true)
                  : (window.location.href = "/auth/login");
              }}
              variant="outline"
              className="flex-1 h-9 md:h-10 text-[13px] md:text-[14px] bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300 transition-colors duration-200"
            >
              Message
            </Button>
            <Button
              className="flex-1 h-9 md:h-10 text-[13px] md:text-[14px] bg-blue-950 hover:bg-blue-900 transition-colors duration-200 shadow-sm hover:shadow"
              onClick={() => setIsBookingOpen(true)}
            >
              Select Services
            </Button>
          </div>
        </div>
      </div>
      <NavigationTabs counselor={profileData} />
      <SendMessageDialog
        open={showSendMessage}
        onOpenChange={setShowSendMessage}
        counselorInfo={profileData}
      />
    </div>
  );
}
