import { GraduationCap } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { CounselorPublicProfile } from "@/app/types/counselor";
import * as CountryFlags from "country-flag-icons/react/3x2";
import { generatePlaceholder } from "@/app/utils/image";

const getCountryFlag = (countryCode: string | null = "US") => {
  const code = (countryCode || "US").toUpperCase();
  const FlagComponent = (CountryFlags as any)[code];
  return FlagComponent ? <FlagComponent /> : <CountryFlags.US />;
};

export function RecommendedCounselorCard({
  counselor,
}: {
  counselor: CounselorPublicProfile;
}) {
  return (
    <Link href={`/profile/${counselor.user_id}`} className="block">
      <div className="overflow-hidden rounded-lg flex bg-white border transition-all duration-200 hover:shadow-md group relative h-32">
        <div className="relative w-32 flex-shrink-0">
          <Image
            src={
              counselor.profile_picture_url ||
              generatePlaceholder(counselor.first_name, counselor.last_name)
            }
            alt={`${counselor.first_name} ${counselor.last_name}`}
            width={128}
            height={128}
            className="h-full w-full object-cover group-hover:opacity-95 transition-opacity"
          />
          <div className="absolute top-2 left-2 w-5 h-4 rounded-sm overflow-hidden shadow-md">
            {getCountryFlag(counselor.country_of_residence)}
          </div>
        </div>

        <div className="p-3 flex-1 min-w-0">
          <div className="flex flex-col justify-between h-full">
            <div>
              <h3 className="text-base font-medium group-hover:text-[#9C0E22] transition-colors truncate">
                {counselor.first_name} {counselor.last_name}
              </h3>
              {counselor.education && counselor.education.length > 0 && (
                <div className="flex items-center gap-1.5 text-sm text-gray-700 mt-1">
                  <GraduationCap className="w-4 h-4 text-blue-950 flex-shrink-0" />
                  <p className="truncate">
                    {counselor.education[0].university_name ||
                      counselor.education[0].major}
                  </p>
                </div>
              )}
            </div>
            <div className="text-sm text-gray-500">
              Starting at ${counselor.hourly_rate}/hr
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
}
