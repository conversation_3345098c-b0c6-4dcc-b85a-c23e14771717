import { useEffect, useState } from "react";
import { CounselorPublicProfile } from "@/app/types/counselor";
import { RecommendedCounselorCard } from "./RecommendedCounselorCard";

interface RecommendedCounselorsProps {
  currentCounselorId: number;
  allCounselors: CounselorPublicProfile[] | null;
}

export function RecommendedCounselors({
  currentCounselorId,
  allCounselors,
}: RecommendedCounselorsProps) {
  const [recommendedCounselors, setRecommendedCounselors] = useState<
    CounselorPublicProfile[]
  >([]);

  useEffect(() => {
    if (!allCounselors) return;
    // Filter out the current counselor
    const otherCounselors = allCounselors.filter(
      (counselor) => counselor.user_id !== currentCounselorId
    );

    // Randomly select 4 counselors
    const shuffled = [...otherCounselors].sort(() => Math.random() - 0.5);
    setRecommendedCounselors(shuffled.slice(0, 4));
  }, [currentCounselorId, allCounselors]);

  if (recommendedCounselors.length === 0) return null;

  return (
    <div className="mt-8 border-t pt-8">
      <h2 className="text-xl font-medium mb-4">Other Popular Counselors</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {recommendedCounselors.map((counselor) => (
          <RecommendedCounselorCard
            key={counselor.user_id}
            counselor={counselor}
          />
        ))}
      </div>
    </div>
  );
}
