import React, { useEffect } from "react";
import { format } from "date-fns";
import { useCounselorReviews } from "@/app/hooks/public/useCounselorReviews";
import { generatePlaceholder } from "@/app/utils/image";

interface CounselorReviewsProps {
  counselorId: number;
  maxReviews?: number;
}

export const CounselorReviews: React.FC<CounselorReviewsProps> = ({
  counselorId,
  maxReviews = 8,
}) => {
  const { fetchReviews, reviewsData, isLoading, error, setPage, reset } =
    useCounselorReviews();

  useEffect(() => {
    // Calculate optimal page size based on maxReviews
    fetchReviews(counselorId, 1, maxReviews);

    // Cleanup function
    return () => {
      reset();
    };
  }, [counselorId, maxReviews, fetchReviews, reset]);

  if (isLoading && !reviewsData) {
    return <div className="flex justify-center p-8">Loading reviews...</div>;
  }

  if (error) {
    return (
      <div className="text-red-500 p-4">Error loading reviews: {error}</div>
    );
  }

  if (!reviewsData || reviewsData.total_reviews === 0) {
    return (
      <div
        id="reviews"
        className="w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6"
      >
        <h2 className="text-[15px] font-medium text-blue-950 mb-3">Reviews</h2>
        <p className="text-[13px] text-gray-500">
          No reviews available for this counselor.
        </p>
      </div>
    );
  }

  return (
    <div
      id="reviews"
      className="w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6"
    >
      {/* Reviews Header */}
      <div className="flex items-center justify-between mb-2">
        <h2 className="text-[15px] font-medium text-blue-950 mb-6">Reviews</h2>
        <div className="flex items-center">
          <div className="flex items-center mr-2">
            {[1, 2, 3, 4, 5].map((star) => (
              <svg
                key={star}
                className={`w-5 h-5 ${
                  star <= Math.round(reviewsData.average_rating)
                    ? "text-yellow-400"
                    : "text-gray-300"
                }`}
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            ))}
          </div>
          <span className="text-lg font-[500]">
            {reviewsData.average_rating.toFixed(1)}
          </span>
          <span className="text-gray-500 ml-2">
            {reviewsData.total_reviews} review(s)
          </span>
        </div>
      </div>

      {/* Reviews Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {reviewsData.reviews.slice(0, maxReviews).map((review) => (
          <div
            key={review.id}
            className="bg-white border rounded-lg hover:scale-[100.7%] transition-all  cursor-pointer p-4 flex"
          >
            {/* Student Avatar */}
            <div className="mr-4 flex-shrink-0">
              <img
                src={
                  review.student_profile_picture ||
                  generatePlaceholder(
                    review.student_name.split(/s+/)[0],
                    review.student_name.split(/s+/)[1]
                  )
                }
                alt={review.student_name}
                className="w-12 h-12 rounded-full object-cover"
              />
            </div>

            {/* Review Content */}
            <div className="flex-1">
              <div className="font-medium mb-1">{review.student_name}</div>
              {/* Star Rating */}
              <div className="flex mb-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <svg
                    key={star}
                    className={`w-4 h-4 ${
                      star <= review.rating
                        ? "text-yellow-400"
                        : "text-gray-300"
                    }`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>

              {/* Review Comment */}
              <p className="text-gray-700 mb-2 line-clamp-3">
                {review.comment}
              </p>

              {/* Review Date */}
              <p className="text-gray-500 text-sm">
                {format(new Date(review.created_at), "PPP")}
              </p>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination (if showing all reviews) */}
      {reviewsData.total_pages > 1 && (
        <div className="flex justify-center mt-6">
          <nav className="flex items-center">
            <button
              onClick={() => setPage(reviewsData.page - 1)}
              disabled={!reviewsData.has_prev}
              className={`px-3 py-1 rounded-l border ${
                !reviewsData.has_prev
                  ? "bg-gray-100 text-gray-400"
                  : "bg-white text-blue-500 hover:bg-blue-50"
              }`}
            >
              Previous
            </button>

            <div className="px-4 py-1 border-t border-b">
              Page {reviewsData.page} of {reviewsData.total_pages}
            </div>

            <button
              onClick={() => setPage(reviewsData.page + 1)}
              disabled={!reviewsData.has_next}
              className={`px-3 py-1 rounded-r border ${
                !reviewsData.has_next
                  ? "bg-gray-100 text-gray-400"
                  : "bg-white text-blue-500 hover:bg-blue-50"
              }`}
            >
              Next
            </button>
          </nav>
        </div>
      )}
    </div>
  );
};
