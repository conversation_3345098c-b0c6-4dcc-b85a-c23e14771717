"use client";

import { useState } from "react";
import { Card } from "@components/ui/card";
import { Button } from "@components/ui/button";
import type { CounselorPublicProfile } from "@/app/types/counselor";
import { User } from "lucide-react";
// import { PackageSelectionDialog } from "./PackageSelection";

interface ServicesProps {
  setIsBookingOpen: (open: boolean) => void;
  profileData: CounselorPublicProfile;
  setSelectedService: (serviceType: string) => void;
}

export function Services({
  setIsBookingOpen,
  profileData,
  setSelectedService,
}: ServicesProps) {
  const hasServices = profileData.services && profileData.services.length > 0;

  if (!hasServices) {
    return (
      <div
        id="services"
        className="w-full bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6"
      >
        <h2 className="text-[15px] font-medium text-blue-950 mb-3">Services</h2>
        <p className="text-[13px] text-gray-500">
          No services available at the moment.
        </p>
      </div>
    );
  }

  const freeIntroCall = profileData.services.find(
    (service) =>
      service.service_type.toLowerCase().includes("free") &&
      service.service_type.toLowerCase().includes("intro")
  );

  const otherServices = profileData.services.filter(
    (service) =>
      !service.service_type.toLowerCase().includes("free") ||
      !service.service_type.toLowerCase().includes("intro")
  );

  return (
    <div id="services" className="w-full space-y-6">
      {/* Services Section */}
      <div className="bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6">
        <h2 className="text-xl font-semibold text-blue-950 mb-6">Services</h2>

        {/* Free Intro Call Section */}
        {freeIntroCall && (
          <div className="mb-6 flex flex-col sm:flex-row items-start sm:items-center justify-between bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border border-blue-200 p-4 gap-4">
            <div className="flex items-start sm:items-center gap-4 w-full sm:w-auto">
              <div className="bg-blue-100 w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0">
                <User className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-blue-950">
                  {freeIntroCall.service_type}
                </h3>
                <p className="text-sm text-gray-600">
                  {freeIntroCall.description}
                </p>
              </div>
            </div>
            <Button
              className="w-full sm:w-32 bg-blue-950 hover:bg-blue-950/90 text-white whitespace-nowrap sm:ml-4"
              onClick={() => {
                setSelectedService(freeIntroCall.service_type);
                setIsBookingOpen(true);
              }}
            >
              Book Free Call
            </Button>
          </div>
        )}

        {/* Services Grid */}
        <div className="mt-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {otherServices.map((service) => (
              <Card
                key={service.id}
                className="p-6 flex flex-col h-[400px] border border-gray-100 transition-all duration-300 hover:border-blue-200 hover:shadow-[0_8px_30px_rgb(0,0,0,0.06)] group rounded-xl overflow-hidden"
              >
                <div className="flex flex-col h-full">
                  {" "}
                  {/* Service Icon - Fixed Height */}
                  <div
                    className="w-12 h-12 rounded-full flex items-center justify-center mb-4 transition-all duration-300 group-hover:scale-105"
                    style={{
                      backgroundColor: service.service_type.includes("Essay")
                        ? "#FFF7E6"
                        : service.service_type.includes("Hour")
                        ? "#E6F6EC"
                        : "#E6F0FF",
                    }}
                  >
                    <User
                      className="w-6 h-6"
                      style={{
                        color: service.service_type.includes("Essay")
                          ? "#FFB020"
                          : service.service_type.includes("Hour")
                          ? "#10B981"
                          : "#2563EB",
                      }}
                    />
                  </div>
                  {/* Service Name - Fixed Height */}
                  <h3 className="text-xl font-medium text-blue-950 mb-4 line-clamp-2 transition-colors duration-300 group-hover:text-blue-900">
                    {service.service_type}
                  </h3>
                  {/* Description - Scrollable */}
                  <div className="flex-1 line-clamp-4 mb-4 pr-2">
                    <p className="text-gray-500">{service.description}</p>
                  </div>
                  {/* Price and Button - Fixed Height */}
                  <div className="mt-auto">
                    <div className="mb-4">
                      <div className="text-sm text-gray-500">Price</div>
                      <div className="text-2xl font-medium text-blue-950/90 group-hover:text-blue-950 transition-colors duration-300">
                        ${service.price}
                      </div>
                    </div>
                    <Button
                      className="w-full bg-blue-950 hover:bg-blue-900 rounded-lg transition-all duration-300"
                      onClick={() => {
                        setSelectedService(service.service_type);
                        setIsBookingOpen(true);
                      }}
                    >
                      Select Service
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Package selection is disabled in this phase */}
      {/* <PackageSelectionDialog
        open={packageDialogOpen}
        onOpenChange={setPackageDialogOpen}
        profileData={profileData}
      /> */}
    </div>
  );
}
