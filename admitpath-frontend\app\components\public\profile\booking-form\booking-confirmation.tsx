"use client";
import { Calendar, Clock, CreditCard } from "lucide-react";
import type { CounselorPublicProfile } from "@/app/types/counselor";
import Image from "next/image";
import { generatePlaceholder } from "@/app/utils/image";
import { useState, useEffect } from "react";
import { usePromoCode } from "@/app/hooks/student/usePromoCode";

interface BookingConfirmationProps {
  counselor: CounselorPublicProfile;
  selectedService: string;
  selectedDate: Date;
  sessionTime: { start_time: Date; end_time: Date };
  amount: number;
  timezone: string;
  onPromoCodeValidated?: (code: string | null, finalAmount: number) => void;
}

export function BookingConfirmation({
  counselor,
  selectedService,
  selectedDate,
  sessionTime,
  amount,
  timezone,
  onPromoCodeValidated,
}: BookingConfirmationProps) {
  const [promoCode, setPromoCode] = useState("");
  const { verifyPromoCode, promoDetails, error, loading, clearPromoCode } =
    usePromoCode();
  const [finalAmount, setFinalAmount] = useState(amount);

  useEffect(() => {
    const validatePromo = async () => {
      if (promoCode.trim()) {
        await verifyPromoCode(promoCode, counselor.counselor_id, amount);
      } else {
        // Reset states when input is empty
        setFinalAmount(amount);
        onPromoCodeValidated?.(null, amount);
        clearPromoCode();
      }
    };

    const timeoutId = setTimeout(validatePromo, 500);
    return () => clearTimeout(timeoutId);
  }, [promoCode, counselor.counselor_id, amount]);

  useEffect(() => {
    if (promoDetails) {
      let newAmount = amount;
      if (promoDetails.type === "percentage" && promoDetails.percentage) {
        newAmount = amount * (1 - promoDetails.percentage / 100);
      } else if (promoDetails.type === "amount" && promoDetails.amount) {
        newAmount = Math.max(0, amount - promoDetails.amount);
      } else if (promoDetails.type === "fixed_price" && promoDetails.amount) {
        newAmount = promoDetails.amount;
      }
      setFinalAmount(newAmount);
      onPromoCodeValidated?.(promoCode, newAmount);
    }
  }, [promoDetails, amount]);

  const handlePromoCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPromoCode(value);

    if (!value.trim()) {
      // Immediately clear promo details when input is empty
      setFinalAmount(amount);
      onPromoCodeValidated?.(null, amount);
      clearPromoCode();
    }
  };

  const getDiscountText = () => {
    if (!promoDetails) return null;
    if (promoDetails.type === "percentage") {
      return `${promoDetails.percentage}% off`;
    }
    return `$${promoDetails.amount} off`;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const service = counselor.services.find(
    (s) => s.service_type === selectedService
  )!;

  return (
    <div className="bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100">
      <div className="p-6 space-y-6">
        <div className="flex items-start gap-4">
          <div className="relative w-16 h-16 rounded-full overflow-hidden flex-shrink-0">
            <Image
              src={
                counselor.profile_picture_url ||
                generatePlaceholder(counselor.first_name, counselor.last_name)
              }
              alt={counselor.first_name}
              fill
              className="object-cover"
            />
          </div>

          <div>
            <h4 className="text-lg font-medium text-gray-900">
              {counselor.first_name}
            </h4>
            <p className="text-sm text-gray-500">{"Counselor"}</p>
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="w-5 h-5 text-[#0F1C2D]" />
              <span className="font-medium text-gray-700">Date</span>
            </div>
            <p className="text-gray-800">{formatDate(selectedDate)}</p>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <Clock className="w-5 h-5 text-[#0F1C2D]" />
              <span className="font-medium text-gray-700">Time</span>
            </div>
            <p className="text-gray-800">
              {formatTime(sessionTime.start_time)} -{" "}
              {formatTime(sessionTime.end_time)}
            </p>
            <p className="text-xs text-gray-500 mt-1">Timezone: {timezone}</p>
          </div>
        </div>

        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex items-center gap-2 mb-2">
            <CreditCard className="w-5 h-5 text-[#0F1C2D]" />
            <span className="font-medium text-gray-700">Service Details</span>
          </div>
          <div className="flex justify-between items-start">
            <div>
              <p className="text-gray-800 font-medium">
                {service.service_type}
              </p>
              <p className="text-sm text-gray-600 mt-1">
                {service.description}
              </p>
            </div>
            <div className="text-right">
              <p className="text-xl font-semibold text-[#0F1C2D]">${amount}</p>
              <p className="text-xs text-gray-500">
                {service.service_type.toLowerCase().includes("15 minutes")
                  ? "15 Minute Session"
                  : "1 hour session"}
              </p>
            </div>
          </div>
        </div>
        <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
          <p className="text-sm text-blue-800">
            By clicking "Book Session", a session will get booked with{" "}
            {counselor.first_name} {counselor.last_name} based on the details
            you provided.{" "}
            {amount !== 0 && "And you will be redirected to payment."}
          </p>
        </div>
      </div>
      <div className="p-4 border-t border-gray-100">
        <div className="space-y-4">
          {/* Price Display */}
          <div>
            <h3 className="font-medium mb-4">Payment Details</h3>
            {promoDetails ? (
              <div className="space-y-2">
                <div className="flex justify-between items-center text-gray-500">
                  <span>Original price</span>
                  <span className="line-through">${amount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-900">Final price</span>
                  <span className="text-xl font-semibold text-green-600">
                    ${finalAmount.toFixed(2)}
                  </span>
                </div>
              </div>
            ) : (
              <div className="flex justify-between items-center">
                <span className="font-medium text-gray-900">Price</span>
                <span className="text-xl font-semibold text-gray-900">
                  ${amount.toFixed(2)}
                </span>
              </div>
            )}
          </div>

          {/* Promo Code Section */}
          {amount > 0 && (
            <div className="mt-4 pt-4 border-t border-gray-100">
              <details className="group">
                <summary className="flex items-center gap-1 text-sm text-blue-600 cursor-pointer hover:text-blue-800">
                  <span>Have a promo code?</span>
                  <svg
                    className="w-4 h-4 transition-transform group-open:rotate-180"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </summary>

                <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                  <div className="relative">
                    <input
                      type="text"
                      value={promoCode}
                      onChange={handlePromoCodeChange}
                      className="w-full px-4 py-2.5 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 placeholder:text-gray-400"
                      placeholder="Enter promo code"
                    />
                    {loading && (
                      <div className="absolute right-3 top-1/2 -translate-y-1/2">
                        <div className="animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full" />
                      </div>
                    )}
                  </div>

                  {promoCode.trim() && !loading && error && (
                    <div className="flex items-center gap-2 text-red-500 text-sm mt-2">
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      {error}
                    </div>
                  )}

                  {promoCode.trim() && !loading && promoDetails && (
                    <div className="flex items-center gap-2 text-green-600 text-sm mt-2">
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      Promo code applied! {getDiscountText()}
                    </div>
                  )}
                </div>
              </details>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
