"use client";
import { cn } from "@/lib/utils";
import { Check<PERSON>ir<PERSON>, Loader2, CreditCard } from "lucide-react";

interface BookingProgressProps {
  status: "idle" | "booking" | "redirecting";
}

export function BookingProgress({ status }: BookingProgressProps) {
  return (
    <div className="flex flex-col items-center justify-center py-10">
      <div className="relative w-24 h-24 mb-6">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="absolute inset-0 rounded-full border-4 border-t-[#0F1C2D] border-r-transparent border-b-transparent border-l-transparent animate-spin"></div>
          <Loader2 className="w-12 h-12 text-[#0F1C2D] animate-spin" />
        </div>
      </div>

      <h3 className="text-xl font-semibold text-gray-900 mb-2 text-center">
        {window.location.pathname.includes("/packages")
          ? "Purchasing Your Package..."
          : "Booking Your Session..."}
      </h3>

      <p className="text-gray-600 text-center max-w-md mb-6">
        {status === "booking"
          ? window.location.pathname.includes("/packages")
            ? "Please wait while we process your package purchase."
            : "Please wait while we process your booking request."
          : "You're being redirected to the payment page."}
      </p>

      <div className="w-full max-w-md">
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300"></div>
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="px-2 bg-white text-gray-500">Progress</span>
          </div>
        </div>

        <div className="mt-6 space-y-6">
          <div className="flex items-center">
            <div
              className={cn(
                "flex-shrink-0 h-8 w-8 rounded-full  flex items-center justify-center",
                status === "booking" ? "bg-white" : "bg-[#0F1C2D]"
              )}
            >
              {status === "booking" ? (
                <Loader2 className="h-5 w-5 text-gray-500 animate-spin" />
              ) : (
                <CheckCircle className="h-5 w-5 text-white" />
              )}
            </div>
            <div className={cn("ml-4", status === "booking" && "opacity-40")}>
              <p className="text-sm font-medium text-gray-900">
                {window.location.pathname.includes("/packages")
                  ? "Package Details Verified"
                  : "Session Details Verified"}
              </p>
              <p className="text-xs text-gray-500">
                {window.location.pathname.includes("/packages")
                  ? "Your selected package and service are confirmed"
                  : "Your selected time and service are confirmed"}
              </p>
            </div>
          </div>
        </div>
      </div>

      {status === "redirecting" && (
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 animate-pulse">
            Redirecting to payment gateway...
          </p>
        </div>
      )}
    </div>
  );
}
