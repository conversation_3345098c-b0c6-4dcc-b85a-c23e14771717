"use client";

import { ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  addMonths,
  subMonths,
  isSameDay,
  format,
  getDay,
  addDays,
  isWithinInterval,
} from "date-fns";
import React from "react";
import { useCounselors } from "@/app/hooks/public/useCounselors";

interface DateChooserProps {
  counselorId: number;
  selectedDate: Date | null;
  onDateSelect: (date: Date) => void;
  datesLoading: boolean;
  timezone: string;
}

export function DateChooser({
  counselorId,
  selectedDate,
  onDateSelect,
  datesLoading,
  timezone,
}: DateChooserProps) {
  const [currentDate, setCurrentDate] = React.useState(new Date());
  const { fetchAvailableDates, availableDates } = useCounselors();

  React.useEffect(() => {
    const loadDates = async () => {
      const start = startOfMonth(currentDate);
      const end = endOfMonth(currentDate);
      try {
        await fetchAvailableDates(counselorId, start, end, timezone);
      } catch (error) {
        console.error("Failed to fetch dates:", error);
      }
    };
    loadDates();
  }, [currentDate, counselorId, fetchAvailableDates, timezone]);

  const getCalendarDays = () => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);

    // Get the first day of the calendar (Monday of the week that includes the first day of the month)
    // Note: getDay() returns 0 for Sunday, 1 for Monday, etc.
    // We adjust to make Monday=0, Sunday=6 for our grid
    const startDay = getDay(monthStart);
    const firstCalendarDay = addDays(
      monthStart,
      startDay === 0 ? -6 : -(startDay - 1)
    );

    // Get the last day of the calendar (Sunday of the week that includes the last day of the month)
    const endDay = getDay(monthEnd);
    const lastCalendarDay = addDays(monthEnd, endDay === 0 ? 0 : 7 - endDay);

    // Get all days in the calendar range
    return eachDayOfInterval({ start: firstCalendarDay, end: lastCalendarDay });
  };

  const previousMonth = () => setCurrentDate(subMonths(currentDate, 1));
  const nextMonth = () => setCurrentDate(addMonths(currentDate, 1));

  const isAvailable = (date: Date) => {
    return availableDates.includes(format(date, "yyyy-MM-dd"));
  };

  const isToday = (date: Date) => isSameDay(date, new Date());

  const isCurrentMonth = (date: Date) => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    return isWithinInterval(date, { start: monthStart, end: monthEnd });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900">
          {format(currentDate, "MMMM yyyy")}
        </h3>
        <div className="flex gap-2">
          <button
            onClick={previousMonth}
            className="p-2 hover:bg-gray-100 rounded-full"
            disabled={datesLoading}
          >
            <ChevronLeft className="h-5 w-5 text-gray-600" />
          </button>
          <button
            onClick={nextMonth}
            className="p-2 hover:bg-gray-100 rounded-full"
            disabled={datesLoading}
          >
            <ChevronRight className="h-5 w-5 text-gray-600" />
          </button>
        </div>
      </div>

      {datesLoading ? (
        <div className="grid grid-cols-7 gap-1 animate-pulse">
          {[...Array(35)].map((_, i) => (
            <div key={i} className="h-10 bg-gray-200 rounded-md" />
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-7 gap-1 text-center text-sm">
          {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map((day) => (
            <div key={day} className="py-2 text-gray-400 font-medium">
              {day.charAt(0)}
            </div>
          ))}
          {getCalendarDays().map((day) => {
            const dayStr = format(day, "yyyy-MM-dd");
            const available = isCurrentMonth(day) && isAvailable(day);
            const inCurrentMonth = isCurrentMonth(day);

            return (
              <button
                key={dayStr}
                onClick={() => available && onDateSelect(day)}
                className={cn(
                  "py-2 rounded-md text-sm font-medium transition-colors",
                  !inCurrentMonth &&
                    "opacity-30 bg-gray-50 text-gray-400 cursor-not-allowed",
                  inCurrentMonth &&
                    !available &&
                    "opacity-50 cursor-not-allowed",
                  isToday(day) && inCurrentMonth && "ring-2 ring-blue-500",
                  selectedDate && isSameDay(day, selectedDate) && inCurrentMonth
                    ? "bg-[#8B141A] text-white"
                    : inCurrentMonth && available
                    ? "bg-green-100 hover:bg-green-200 text-green-800"
                    : inCurrentMonth
                    ? "bg-gray-100 text-gray-400"
                    : ""
                )}
                disabled={!available || !inCurrentMonth}
              >
                {inCurrentMonth ? format(day, "d") : ""}
              </button>
            );
          })}
        </div>
      )}
    </div>
  );
}
