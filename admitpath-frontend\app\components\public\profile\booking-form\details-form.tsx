"use client";

import { CounselorPublicProfile } from "@/app/types/counselor";
import { cn } from "@/lib/utils";

interface DetailsFormProps {
  counselor: CounselorPublicProfile | null;
  selectedService: string;
  serviceDetails: string;
  onServiceChange: (service: string) => void;
}

export function DetailsForm({
  counselor,
  selectedService,
  serviceDetails,
  onServiceChange,
}: DetailsFormProps) {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Provide Details</h3>
        <p className="text-sm text-gray-500">
          Please provide your specific details below.
        </p>
      </div>

      <div className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">
            Select Service <span className="text-red-500">*</span>
          </label>
          {counselor?.services.length === 0 ? (
            <p>No services listed</p>
          ) : (
            <select
              value={selectedService}
              onChange={(e) => onServiceChange(e.target.value)}
              className="w-full p-2 border rounded-md bg-white"
            >
              <option value="">Select a service</option>
              {counselor?.services.map((service) => (
                <option
                  key={service.id}
                  value={service.service_type}
                  className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                >
                  {service.service_type}
                </option>
              ))}
            </select>
          )}
        </div>

        <div
          className={cn(
            "space-y-2 transition-all",
            serviceDetails ? "h-min opacity-100" : "h-0 opacity-0"
          )}
        >
          <label className="text-sm font-medium">Service Details:</label>
          <textarea
            value={serviceDetails}
            readOnly
            className="w-full p-2 border rounded-md h-32 bg-gray-50"
          />
        </div>
      </div>
    </div>
  );
}
