"use client";

import { X } from "lucide-react";
import { DialogClose } from "@components/ui/dialog";
import { useCounselors } from "@/app/hooks/public/useCounselors";
import { useSessions } from "@/app/hooks/student/useSessions";
import { usePayments } from "@/app/hooks/student/usePayments";
import { useRouter } from "next/navigation";
import type { CounselorPublicProfile } from "@/app/types/counselor";
import { DetailsForm } from "./details-form";
import { DateChooser } from "./date-chooser";
import { TimeChooser } from "./time-chooser";
import { BookingServiceSkeleton } from "../skeletons";
import { BookingConfirmation } from "./booking-confirmation";
import { BookingProgress } from "./booking-progress";
import { useEffect, useMemo, useState } from "react";
import { useProfile } from "@/app/hooks/student/useProfile";

interface BookingFormProps {
    counselor: CounselorPublicProfile;
    counselor_user_id: number;
    selectedService: string;
    setSelectedService: (serviceType: string) => void;
}

export default function BookingForm({
    counselor,
    counselor_user_id,
    selectedService,
    setSelectedService,
}: BookingFormProps) {
    const router = useRouter();
    const { fetchTimeSlots, availabilityLoading, timeSlots, datesLoading } =
        useCounselors();
    const { createSession } = useSessions();
    const { createCheckoutSession } = usePayments();
    const { userInfo } = useProfile();

    const [step, setStep] = useState(1);
    const [selectedDate, setSelectedDate] = useState<Date | null>(null);
    const [selectedTime, setSelectedTime] = useState<string | null>(null);
    const [serviceDetails, setServiceDetails] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isLoadingServices, setIsLoadingServices] = useState(true);
    const [bookingStatus, setBookingStatus] = useState<
        "idle" | "booking" | "redirecting"
    >("idle");
    const [promoCode, setPromoCode] = useState<string | null>(null);
    const [finalAmount, setFinalAmount] = useState<number>(0);

    useEffect(() => {
        if (counselor?.services) {
            setIsLoadingServices(false);
        }
    }, [counselor?.services]);

    const isStepValid = useMemo(() => {
        if (userInfo?.userType !== "student") return false;
        switch (step) {
            case 1:
                return selectedService !== "";
            case 2:
                return selectedDate !== null;
            case 3:
                return selectedTime !== null;
            case 4:
                return true; // Confirmation step is always valid
            default:
                return false;
        }
    }, [step, selectedService, selectedDate, selectedTime]);

    const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    const handleServiceChange = (serviceType: string) => {
        setSelectedService(serviceType);
    };

    useEffect(() => {
        // Find service by service_type, handling both regular and custom types
        const service = counselor.services.find(
            (s) => s.service_type === selectedService
        );
        setServiceDetails(service?.description || "");
    }, [selectedService, counselor.services]);

    const getSessionAmount = () => {
        return counselor.services.find(
            (s) => s.service_type === selectedService
        )!.price;
    };

    const getSessionDuration = () => {
        const selectedServiceObject = counselor.services.find(
            (s) => s.service_type === selectedService
        );
        const is15Minute = selectedServiceObject!.service_type
            .toLowerCase()
            .includes("15 minutes");
        if (!selectedTime) return null;
        const start_time = new Date(selectedTime);
        const end_time = new Date(
            start_time.getTime() + (is15Minute ? 15 : 60) * 60 * 1000
        );

        return { start_time, end_time };
    };

    useEffect(() => {
        const fetchSlots = async () => {
            if (selectedDate) {
                try {
                    await fetchTimeSlots(
                        counselor_user_id,
                        selectedDate,
                        userTimezone
                    );
                    setSelectedTime(null);
                } catch (error) {
                    console.error("Failed to fetch slots:", error);
                }
            }
        };

        fetchSlots();
    }, [selectedDate, counselor_user_id, fetchTimeSlots, userTimezone]);

    const handlePromoCodeValidated = (code: string | null, amount: number) => {
        setPromoCode(code);
        setFinalAmount(amount);
    };

    const handleSubmit = async () => {
        if (!counselor || !selectedDate || !selectedTime || !selectedService)
            return;

        try {
            setIsSubmitting(true);
            setBookingStatus("booking");
            setStep(5);

            const { start_time, end_time } = getSessionDuration()!;

            // Always send original amount, not the discounted amount
            const originalAmount = getSessionAmount();

            const session = await createSession(counselor_user_id, {
                service_type: selectedService,
                description: serviceDetails,
                date: selectedDate.toISOString(),
                start_time: start_time.toISOString(),
                end_time: end_time.toISOString(),
                amount: originalAmount, // Send original amount
                promo_code: promoCode, // Backend will handle the discount
            });

            const checkoutSession = await createCheckoutSession(session.id);

            if (checkoutSession.checkout_url) {
                setBookingStatus("redirecting");
                setTimeout(
                    () => router.push(checkoutSession.checkout_url),
                    1500
                );
            }
        } catch (error) {
            console.error("Booking failed:", error);
            setBookingStatus("idle");
            setStep(4);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="w-full max-w-2xl mx-auto bg-white p-6">
            <div className="space-y-6">
                <div className="flex justify-between items-center">
                    <div className="space-y-1">
                        <h2 className="text-xl font-medium text-[#111827]">
                            {step === 1
                                ? "Select Service"
                                : step === 2 || step === 3
                                ? "Select available time"
                                : step === 4
                                ? "Confirm Booking"
                                : "Processing Booking"}
                        </h2>
                        <p className="text-sm text-gray-500">
                            {step === 2 || step === 3
                                ? "Please select your preferred date and time"
                                : step === 4
                                ? "Please review your booking details before confirming"
                                : step === 5
                                ? "Please wait while we process your booking"
                                : ""}
                        </p>
                    </div>
                    <span className="text-md text-gray-500">Step {step}/5</span>
                </div>

                {step === 1 &&
                    (isLoadingServices ? (
                        <BookingServiceSkeleton />
                    ) : (
                        <DetailsForm
                            counselor={counselor}
                            selectedService={selectedService}
                            serviceDetails={serviceDetails}
                            onServiceChange={handleServiceChange}
                        />
                    ))}

                {step === 2 && (
                    <>
                        <DateChooser
                            counselorId={counselor_user_id}
                            selectedDate={selectedDate!}
                            onDateSelect={setSelectedDate}
                            datesLoading={datesLoading}
                            timezone={userTimezone}
                        />
                    </>
                )}

                {step === 3 && (
                    <>
                        <TimeChooser
                            slotsLoading={availabilityLoading}
                            availableSlots={timeSlots}
                            selectedTime={selectedTime}
                            onTimeSelect={setSelectedTime}
                            timezone={userTimezone}
                        />
                    </>
                )}

                {step === 4 && (
                    <BookingConfirmation
                        counselor={counselor}
                        selectedService={selectedService}
                        selectedDate={selectedDate!}
                        sessionTime={getSessionDuration()!}
                        amount={getSessionAmount()}
                        timezone={userTimezone}
                        onPromoCodeValidated={handlePromoCodeValidated}
                    />
                )}

                {step === 5 && <BookingProgress status={bookingStatus} />}

                <div className="flex justify-end pt-6 space-x-3">
                    {/* Back Button - shows for steps 2-4 */}
                    {step === 2 && (
                        <DialogClose asChild className="mr-auto">
                            <button className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100">
                                Cancel{" "}
                                <X className="w-4 h-4 ml-1.5 inline-block" />
                            </button>
                        </DialogClose>
                    )}
                    {step > 1 && step < 5 && (
                        <button
                            onClick={() => setStep(step - 1)}
                            className="ml-auto px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 rounded-lg hover:bg-gray-100"
                        >
                            ← Back
                        </button>
                    )}

                    {/* Continue/Submit Button - shows on all steps except final */}
                    {step < 4 && (
                        <button
                            onClick={() => setStep(step + 1)}
                            disabled={!isStepValid}
                            className="px-6 py-2 text-sm font-medium text-white bg-[#0F1C2D] rounded-lg hover:bg-[#0F1C2D]/90 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Continue →
                        </button>
                    )}

                    {/* Book Session Button - only on confirmation step */}
                    {step === 4 && (
                        <button
                            onClick={handleSubmit}
                            disabled={isSubmitting}
                            className="px-6 py-2 text-sm font-medium text-white bg-[#0F1C2D] rounded-lg hover:bg-[#0F1C2D]/90 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isSubmitting ? "Processing..." : "Book Session"}
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
}
