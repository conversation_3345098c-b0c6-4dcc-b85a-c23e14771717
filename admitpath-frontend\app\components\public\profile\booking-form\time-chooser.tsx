"use client";

import { cn } from "@/lib/utils";

interface TimeChooserProps {
  slotsLoading?: boolean;
  availableSlots: string[];
  selectedTime: string | null;
  onTimeSelect: (time: string) => void;
  timezone: string;
}

export function TimeChooser({
  slotsLoading = false,
  availableSlots,
  selectedTime,
  onTimeSelect,
  timezone,
}: TimeChooserProps) {
  const formatSlot = (isoString: string) => {
    const start = new Date(isoString);
    return start
      .toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
        timeZone: timezone,
      })
      .toLowerCase();
  };

  if (slotsLoading) {
    return (
      <div className="space-y-4">
        <div className="h-6 bg-gray-200 rounded animate-pulse w-48" />
        <div className="grid grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="h-12 bg-gray-200 rounded animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-sm font-medium text-gray-700">
          Available time slots
        </h3>
        <p className="text-xs text-gray-500">Times shown in {timezone}</p>
      </div>

      <div className="grid md:grid-cols-4 grid-cols-3 gap-4">
        {availableSlots.length > 0 ? (
          availableSlots.map((slot) => (
            <button
              key={slot}
              onClick={() => onTimeSelect(slot)}
              className={cn(
                "py-3 px-4 rounded-lg text-sm font-medium transition-colors",
                slot === selectedTime
                  ? "bg-[#8B141A] text-white"
                  : "bg-green-100 hover:bg-green-200 text-green-800"
              )}
            >
              {formatSlot(slot)}
            </button>
          ))
        ) : (
          <div className="col-span-4 text-center text-gray-500">
            No available time slots for this date
          </div>
        )}
      </div>
    </div>
  );
}
