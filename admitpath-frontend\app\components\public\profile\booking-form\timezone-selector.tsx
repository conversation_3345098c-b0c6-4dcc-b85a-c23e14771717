"use client";

import { Info } from "lucide-react";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@components/ui/select";
import { TIMEZONE_OPTIONS } from "@/app/components/counselor/availability/constants";
import { useEffect } from "react";

interface TimezoneSelectorProps {
    selectedTimezone: string;
    setSelectedTimezone: (timezone: string) => void;
}

export function TimezoneSelector({
    selectedTimezone,
    setSelectedTimezone,
}: TimezoneSelectorProps) {
    // Load saved timezone from localStorage on initial render
    useEffect(() => {
        const savedTimezone = localStorage.getItem('userTimezone');
        if (savedTimezone) {
            setSelectedTimezone(savedTimezone);
        }
    }, [setSelectedTimezone]);

    // Save selected timezone to localStorage whenever it changes
    const handleTimezoneChange = (timezone: string) => {
        localStorage.setItem('userTimezone', timezone);
        setSelectedTimezone(timezone);
    };

    return (
        <div className="space-y-2 pt-2 pb-4">
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                    <h3 className="text-sm font-medium text-gray-700">Timezone</h3>
                    <Info className="h-4 w-4 text-gray-400" />
                </div>
                <p className="text-xs text-gray-500">
                    Times shown in your selected timezone
                </p>
            </div>
            
            <Select value={selectedTimezone} onValueChange={handleTimezoneChange}>
                <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select your timezone" />
                </SelectTrigger>
                <SelectContent className="bg-white max-h-[300px]">
                    {/* User's local timezone */}
                    <SelectItem 
                        key="user-timezone"
                        value={Intl.DateTimeFormat().resolvedOptions().timeZone}
                        className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                    >
                        Your Timezone ({Intl.DateTimeFormat().resolvedOptions().timeZone})
                    </SelectItem>
                    
                    {/* Common timezones */}
                    {TIMEZONE_OPTIONS.map((tz, index) => (
                        <SelectItem
                            key={`timezone-${index}`}
                            value={tz.value}
                            className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                        >
                            {tz.label}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>
    );
}