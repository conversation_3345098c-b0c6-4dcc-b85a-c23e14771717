"use client";

import { useEffect, useState } from "react";
import { usePublicPackages } from "@/app/hooks/public/usePublicPackages";
import { PackageData } from "@/app/types/counselor/package";
import { Button } from "@/app/components/ui/button";
import { PackageDetailsDialog } from "./package-details-dialog";
import { CounselorPublicProfile } from "@/app/types/counselor";
import { useProfile } from "@/app/hooks/student/useProfile";
import { useRouter } from "next/navigation";

interface PublicPackagesProps {
  counselor: CounselorPublicProfile;
}

export default function PublicPackages({ counselor }: PublicPackagesProps) {
  const { packages, loading, fetchCounselorPackages } = usePublicPackages();
  const [selectedPackage, setSelectedPackage] = useState<PackageData | null>(
    null
  );
  const { userInfo } = useProfile();
  const router = useRouter();

  useEffect(() => {
    fetchCounselorPackages(counselor.user_id);
  }, [counselor, fetchCounselorPackages]);

  if (loading) {
    return (
      <div
        id="packages"
        className="bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6"
      >
        <h2 className="text-xl font-semibold text-blue-950 mb-6">Packages</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="flex flex-col border rounded-xl p-6 hover:shadow-md transition-shadow duration-200 h-[500px] relative animate-pulse"
            >
              <div className="h-7 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-full mb-4"></div>
              <div className="space-y-2 mb-6">
                {[1, 2].map((j) => (
                  <div
                    key={j}
                    className="h-10 bg-blue-50 rounded-lg w-full"
                  ></div>
                ))}
              </div>
              <div className="mb-6">
                <div className="h-5 bg-gray-200 rounded w-1/3 mb-2"></div>
                <div className="space-y-2">
                  {[1, 2, 3].map((k) => (
                    <div key={k} className="flex items-start gap-2">
                      <div className="w-4 h-4 bg-green-200 rounded-full mt-1"></div>
                      <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="absolute bottom-6 left-6 right-6">
                <div className="flex items-center mb-4">
                  <div className="w-4 h-4 bg-gray-200 rounded-full mr-1"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                </div>
                <div className="flex justify-between items-end pt-4 border-t">
                  <div>
                    <div className="h-4 bg-gray-200 rounded w-16 mb-1"></div>
                    <div className="h-8 bg-gray-200 rounded w-24"></div>
                  </div>
                  <div className="h-10 bg-gray-200 rounded w-32"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div
      id="packages"
      className="bg-white rounded-lg border border-[#E2E8F0] p-4 md:p-6"
    >
      <h2 className="text-xl font-semibold text-blue-950 mb-6">Packages</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {packages.items.length === 0 ? (
          <p className="text-[13px] text-gray-500">
            No packages available, This counselor hasn't created any service
            packages yet
          </p>
        ) : (
          packages.items.map((pkg) => (
            <div
              key={pkg.id}
              className="flex flex-col border rounded-xl p-6 hover:shadow-md transition-shadow duration-200 h-[330px] relative"
            >
              <h3 className="text-xl font-semibold mb-2">{pkg.title}</h3>
              <p className="text-gray-600 mb-4 line-clamp-2">
                {pkg.description}
              </p>

              <div className="mb-6 space-y-2">
                {pkg.package_items.slice(0, 2).map((service, idx) => (
                  <div
                    key={idx}
                    className="bg-blue-50 px-3 py-2 rounded-lg text-blue-800 font-medium flex justify-between"
                  >
                    <span>{service.service_name}</span>
                    <span>{service.hours} hours</span>
                  </div>
                ))}
                {pkg.package_items.length > 2 && (
                  <div className="text-sm text-blue-600 font-medium px-3 py-2">
                    + {pkg.package_items.length - 2} more service
                    {pkg.package_items.length - 2 > 1 ? "s" : ""}
                  </div>
                )}
              </div>

              <div className="absolute bottom-6 left-6 right-6">
                <div className="flex justify-between items-end pt-4 border-t">
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Price</p>
                    <p className="text-2xl font-medium">${pkg.total_price}</p>
                  </div>
                  <Button
                    onClick={() => {
                      if (!userInfo) {
                        router.push(
                          `/auth/login?redirect=/profile/${counselor.user_id}`
                        );
                        return;
                      }
                      setSelectedPackage(pkg);
                    }}
                  >
                    View Details
                  </Button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {selectedPackage && (
        <PackageDetailsDialog
          pkg={selectedPackage}
          counselor={counselor}
          onClose={() => setSelectedPackage(null)}
        />
      )}
    </div>
  );
}
