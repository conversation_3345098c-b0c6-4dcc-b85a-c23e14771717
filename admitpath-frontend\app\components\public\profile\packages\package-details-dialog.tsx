"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/app/components/ui/dialog";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { PackageData } from "@/app/types/counselor/package";
import { Clock } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/app/components/ui/tabs";
import { PurchaseConfirmation } from "./purchase-confirmation";
import { CounselorPublicProfile } from "@/app/types/counselor";
import { useProfile } from "@/app/hooks/student/useProfile";

interface PackageDetailsDialogProps {
  pkg: PackageData;
  counselor: CounselorPublicProfile;
  onClose: () => void;
}

export function PackageDetailsDialog({
  pkg,
  counselor,
  onClose,
}: PackageDetailsDialogProps) {
  const [currentStep, setCurrentStep] = useState<
    "details" | "service-selection" | "confirmation" | "booking"
  >("details");
  const [selectedService, setSelectedService] = useState<string>("");
  const { userInfo } = useProfile();

  const isCounselor = userInfo?.userType === "counselor";

  const totalHours = pkg.package_items.reduce(
    (acc, item) => acc + item.hours,
    0
  );

  const handlePurchaseClick = () => {
    if (isCounselor) {
      return;
    }
    setCurrentStep("service-selection");
  };

  return (
    <Dialog open={!!pkg} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto overflow-x-hidden w-[95vw] p-4 md:p-6">
        <DialogHeader>
          <DialogTitle className="text-xl">Purchase Package</DialogTitle>
        </DialogHeader>
        {currentStep === "details" ? (
          <>
            <Tabs defaultValue="overview" className="w-full">
              <div className="flex justify-center mb-6">
                <TabsList className="w-full max-w-md">
                  <TabsTrigger value="overview" className="flex-1">
                    Overview
                  </TabsTrigger>
                  <TabsTrigger value="services" className="flex-1">
                    Services & Hours
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="overview" className="space-y-4">
                <div className="space-y-4">
                  <h3 className="font-medium">{pkg.title}</h3>
                  <p className="text-gray-600">{pkg.description}</p>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="text-sm text-gray-500 mb-1">Total Hours</h4>
                    <p className="font-medium flex items-center gap-2">
                      <Clock className="w-4 h-4 text-blue-500" />
                      {totalHours} hours
                    </p>
                  </div>

                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="text-sm text-gray-500 mb-1">Price</h4>
                    <p className="font-medium text-xl">${pkg.total_price}</p>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="services" className="space-y-4">
                <div className="space-y-4">
                  <h3 className="font-medium">Included Services</h3>
                  <div className="grid gap-3">
                    {pkg.package_items.map((service, idx) => (
                      <div key={idx} className="border rounded-lg p-4">
                        <div className="flex justify-between items-center">
                          <h4 className="font-medium">
                            {service.service_name}
                          </h4>
                          <span className="text-sm font-medium bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
                            {service.hours} hours
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
            </Tabs>

            <DialogFooter className="mt-8 pt-6 border-t">
              <div className="w-full flex flex-col-reverse sm:flex-row items-center justify-between gap-6">
                <Button variant="outline" onClick={onClose}>
                  Close
                </Button>

                <div className="w-full sm:w-auto flex flex-col sm:flex-row items-center gap-6">
                  <div className="text-center sm:text-right">
                    <p className="text-sm text-gray-500">Total Price</p>
                    <p className="text-2xl font-bold">${pkg.total_price}</p>
                  </div>

                  <Button
                    disabled={isCounselor}
                    onClick={handlePurchaseClick}
                    className="w-full sm:w-auto px-8"
                    size="lg"
                  >
                    Continue to Purchase
                  </Button>
                </div>
              </div>
            </DialogFooter>
          </>
        ) : currentStep === "service-selection" ? (
          <>
            <DialogHeader>
              <DialogTitle className="text-xl">
                Select Your First Service
              </DialogTitle>
              <DialogDescription className="text-base mt-2">
                Choose which service you'd like to start with for your package
              </DialogDescription>
            </DialogHeader>

            <div className="py-6 space-y-6">
              <div className="grid gap-4">
                {pkg.package_items.map((service) => (
                  <div
                    key={service.service_name}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedService === service.service_name
                        ? "border-blue-500 bg-blue-50"
                        : "hover:border-gray-400"
                    }`}
                    onClick={() => setSelectedService(service.service_name)}
                  >
                    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                      <div className="flex items-center gap-3">
                        <div
                          className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                            selectedService === service.service_name
                              ? "border-blue-500"
                              : "border-gray-300"
                          }`}
                        >
                          {selectedService === service.service_name && (
                            <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                          )}
                        </div>
                        <span className="font-medium">
                          {service.service_name}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <DialogFooter className="mt-6 pt-6 border-t">
              <div className="w-full flex flex-col-reverse sm:flex-row items-center justify-between gap-6">
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep("details")}
                >
                  Back
                </Button>

                <Button
                  onClick={() => setCurrentStep("confirmation")}
                  className="w-full sm:w-auto px-8"
                  size="lg"
                  disabled={!selectedService}
                >
                  Continue
                </Button>
              </div>
            </DialogFooter>
          </>
        ) : currentStep === "confirmation" ? (
          <PurchaseConfirmation
            pkg={pkg}
            counselor={counselor}
            selectedService={selectedService}
            onBack={() => setCurrentStep("service-selection")}
            onComplete={() => {
              setCurrentStep("booking");
              setTimeout(() => onClose(), 2000);
            }}
          />
        ) : currentStep === "booking" ? (
          <div className="py-8 text-center space-y-4">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto"></div>
            <h3 className="text-xl font-medium">Purchasing Package</h3>
            <p className="text-gray-500">
              Please wait while we process your purchase...
            </p>
          </div>
        ) : null}
      </DialogContent>
    </Dialog>
  );
}
