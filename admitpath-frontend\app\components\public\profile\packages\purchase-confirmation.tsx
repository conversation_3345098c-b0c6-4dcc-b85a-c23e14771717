"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import { PackageData } from "@/app/types/counselor/package";
import {
  CheckCircle,
  Clock,
  Calendar,
  AlertCircle,
  AlertTriangle,
} from "lucide-react";
import { usePackagePayments } from "@/app/hooks/student/usePackagePayments";
import usePackages from "@/app/hooks/student/usePackages";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
import { Input } from "@/app/components/ui/input";
import { useProfile } from "@/app/hooks/student/useProfile";
import { useCounselors } from "@/app/hooks/public/useCounselors";
import { usePromoCode } from "@/app/hooks/student/usePromoCode";
import { DateChooser } from "../booking-form/date-chooser";
import { TimeChooser } from "../booking-form/time-chooser";
import { CounselorPublicProfile } from "@/app/types/counselor";

interface PurchaseConfirmationProps {
  pkg: PackageData;
  counselor: CounselorPublicProfile;
  selectedService: string;
  onBack: () => void;
  onComplete: () => void;
}

export function PurchaseConfirmation({
  pkg,
  counselor,
  selectedService,
  onBack,
  onComplete,
}: PurchaseConfirmationProps) {
  const router = useRouter();
  const { createCheckoutSession, loading: paymentLoading } =
    usePackagePayments();
  const { subscribeToPackage, loading: subscriptionLoading } = usePackages();
  const { userInfo } = useProfile();
  const { fetchTimeSlots, availabilityLoading, timeSlots, datesLoading } =
    useCounselors();
  const {
    verifyPromoCode,
    promoDetails,
    error: promoError,
    loading: promoLoading,
    clearPromoCode,
  } = usePromoCode();

  const loading =
    paymentLoading ||
    subscriptionLoading ||
    availabilityLoading ||
    datesLoading ||
    promoLoading;

  // Session scheduling states
  const [step, setStep] = useState<"date" | "time" | "details">("date");
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const duration = 60; // Default 1 hour session duration

  const [promoCode, setPromoCode] = useState("");
  const [finalPrice, setFinalPrice] = useState(pkg.total_price);
  const [purchaseStatus, setPurchaseStatus] = useState<
    "idle" | "processing" | "redirecting"
  >("idle");

  const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;

  // Handle promo code validation
  useEffect(() => {
    const validatePromo = async () => {
      if (promoCode.trim()) {
        await verifyPromoCode(
          promoCode,
          counselor.counselor_id,
          pkg.total_price
        );
      } else {
        // Reset states when input is empty
        setFinalPrice(pkg.total_price);
        clearPromoCode();
      }
    };

    const timeoutId = setTimeout(validatePromo, 500);
    return () => clearTimeout(timeoutId);
  }, [
    promoCode,
    counselor.counselor_id,
    pkg.total_price,
    verifyPromoCode,
    clearPromoCode,
  ]);

  // Update final price when promo details change
  useEffect(() => {
    if (promoDetails) {
      let newAmount = pkg.total_price;
      if (promoDetails.type === "percentage" && promoDetails.percentage) {
        newAmount = pkg.total_price * (1 - promoDetails.percentage / 100);
      } else if (promoDetails.type === "amount" && promoDetails.amount) {
        newAmount = Math.max(0, pkg.total_price - promoDetails.amount);
      } else if (promoDetails.type === "fixed_price" && promoDetails.amount) {
        newAmount = promoDetails.amount;
      }
      setFinalPrice(newAmount);
    }
  }, [promoDetails, pkg.total_price]);

  // Handle promo code input change
  const handlePromoCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPromoCode(value);

    if (!value.trim()) {
      // Immediately clear promo details when input is empty
      setFinalPrice(pkg.total_price);
      clearPromoCode();
    }
  };

  // Get discount text for display
  const getDiscountText = () => {
    if (!promoDetails) return null;
    if (promoDetails.type === "percentage") {
      return `${promoDetails.percentage}% off`;
    }
    return `$${promoDetails.amount} off`;
  };

  // Fetch time slots when date is selected
  useEffect(() => {
    const fetchSlots = async () => {
      if (selectedDate && step === "time") {
        try {
          await fetchTimeSlots(counselor.user_id, selectedDate, userTimezone);
          setSelectedTime(null);
        } catch (error) {
          console.error("Error fetching time slots:", error);
          toast.error("Failed to load available time slots");
        }
      }
    };

    fetchSlots();
  }, [selectedDate, counselor.user_id, fetchTimeSlots, userTimezone, step]);

  // Calculate total hours
  const totalHours = pkg.package_items.reduce(
    (acc, item) => acc + item.hours,
    0
  );

  const handlePurchase = async () => {
    try {
      setPurchaseStatus("processing");

      // Create subscription first with the first session data
      let subscriptionData;

      if (selectedDate && selectedTime) {
        // If date and time are selected, create a session
        const startTime = new Date(selectedTime);
        const endTime = new Date(startTime.getTime() + duration * 60 * 1000);

        subscriptionData = {
          package_id: pkg.id!,
          selected_service: selectedService,
          first_session: {
            event_name: selectedService,
            date: selectedDate.toISOString(),
            start_time: startTime.toISOString(),
            end_time: endTime.toISOString(),
            student_email: userInfo?.email || "",
          },
        };
      } else {
        // If no date/time selected, provide empty values
        subscriptionData = {
          package_id: pkg.id!,
          selected_service: selectedService,
          first_session: {
            event_name: selectedService,
            date: "", // Empty string signals to the backend that no session should be created
            start_time: "",
            end_time: "",
            student_email: userInfo?.email || "",
          },
        };
      }

      // Add promo code to subscription data if it exists and is valid
      if (promoCode.trim() && promoDetails) {
        (subscriptionData as any).promo_code = promoCode;
      }

      const subscriptionResponse = await subscribeToPackage(subscriptionData);
      const subscriptionId = subscriptionResponse.id;

      // Then create checkout session with the subscription ID
      const checkoutData = {
        subscription_id: subscriptionId,
        package_id: pkg.id!,
        selected_service: selectedService,
      };

      const checkoutResponse = await createCheckoutSession(checkoutData);

      if (checkoutResponse.checkout_url) {
        setPurchaseStatus("redirecting");
        // No success toast here - we'll show success on the success page after payment
        setTimeout(() => {
          router.push(checkoutResponse.checkout_url);
          onComplete();
        }, 1000);
      }
    } catch (error) {
      console.error("Error in package purchase process:", error);
      setPurchaseStatus("idle");
    }
  };

  // Render different content based on the current step
  const renderStepContent = () => {
    if (step === "date") {
      return (
        <>
          <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-blue-700">
                Schedule Your First Session
              </h3>
              <p className="text-sm text-blue-600 mt-1">
                Please select a date for your first session with the counselor.
              </p>
            </div>
          </div>

          <div className="border rounded-lg p-5 space-y-2">
            <h3 className="font-medium text-lg">{pkg.title}</h3>
            <p className="text-gray-600">{pkg.description}</p>
          </div>

          <div className="border rounded-lg p-5 space-y-4">
            <h3 className="font-medium text-lg">
              Select Date for First Session
            </h3>
            <p className="text-gray-600">
              Choose a date for your first session with the counselor.
            </p>
            <DateChooser
              counselorId={counselor.user_id}
              selectedDate={selectedDate}
              onDateSelect={setSelectedDate}
              datesLoading={datesLoading}
              timezone={userTimezone}
            />
          </div>
        </>
      );
    } else if (step === "time") {
      return (
        <>
          <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 flex items-start gap-3">
            <AlertCircle className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-blue-700">
                Schedule Your First Session
              </h3>
              <p className="text-sm text-blue-600 mt-1">
                Please select a time slot for your first session.
              </p>
            </div>
          </div>

          <div className="border rounded-lg p-5 space-y-2">
            <h3 className="font-medium text-lg">{pkg.title}</h3>
            <p className="text-gray-600">{pkg.description}</p>
          </div>

          <div className="border rounded-lg p-5 space-y-4">
            <h3 className="font-medium text-lg">
              Select Time for First Session
            </h3>
            <p className="text-gray-600">
              Choose a time slot for your first session.
            </p>
            <TimeChooser
              slotsLoading={availabilityLoading}
              availableSlots={timeSlots}
              selectedTime={selectedTime}
              onTimeSelect={setSelectedTime}
              timezone={userTimezone}
            />
          </div>
        </>
      );
    }

    // Last step is "details" (purchase confirmation)
    return (
      <>
        <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 flex items-start gap-3">
          <AlertCircle className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="font-medium text-blue-700">Confirm Your Purchase</h3>
            <p className="text-sm text-blue-600 mt-1">
              Please review the package details before proceeding with your
              purchase.
            </p>
          </div>
        </div>

        <div className="border rounded-lg p-5 space-y-4">
          <h3 className="font-medium text-lg">{pkg.title}</h3>
          <p className="text-gray-600">{pkg.description}</p>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
            <div className="bg-gray-50 p-3 rounded-lg">
              <h4 className="text-sm text-gray-500 mb-1">Total Hours</h4>
              <p className="font-medium flex items-center gap-2">
                <Clock className="w-4 h-4 text-blue-500" />
                {totalHours} hours
              </p>
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg mt-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h4 className="text-sm font-medium text-green-800 mb-1">
                  First Selected Service
                </h4>
                <p className="text-sm text-green-700 flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  {selectedService}
                </p>
              </div>

              {selectedDate && selectedTime && (
                <div>
                  <h4 className="text-sm font-medium text-green-800 mb-1">
                    First Session Scheduled
                  </h4>
                  <p className="text-sm text-green-700 flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-green-600" />
                    {selectedDate.toLocaleDateString()} at{" "}
                    {new Date(selectedTime).toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </>
    );
  };

  // Determine which buttons to show based on the current step
  const renderButtons = () => {
    if (step === "date") {
      return (
        <div className="flex justify-between items-center pt-4 border-t">
          <Button variant="outline" onClick={onBack} disabled={loading}>
            Back
          </Button>

          <Button
            onClick={() => setStep("time")}
            disabled={!selectedDate || loading}
            className="min-w-[150px]"
          >
            Continue
          </Button>
        </div>
      );
    } else if (step === "time") {
      return (
        <div className="flex justify-between items-center pt-4 border-t">
          <Button
            variant="outline"
            onClick={() => setStep("date")}
            disabled={loading}
          >
            Back
          </Button>

          <Button
            onClick={() => setStep("details")}
            disabled={!selectedTime || loading}
            className="min-w-[150px]"
          >
            Continue to Checkout
          </Button>
        </div>
      );
    } else if (step === "details") {
      return (
        <div className="flex justify-between items-center pt-4 border-t">
          <Button
            variant="outline"
            onClick={() => setStep("time")}
            disabled={loading}
          >
            Back
          </Button>

          <Button
            onClick={handlePurchase}
            disabled={loading || purchaseStatus !== "idle"}
            className="min-w-[150px]"
          >
            {purchaseStatus === "processing"
              ? "Processing..."
              : purchaseStatus === "redirecting"
              ? "Redirecting..."
              : "Confirm Purchase"}
          </Button>
        </div>
      );
    }

    // Default buttons
    return (
      <div className="flex justify-between items-center pt-4 border-t">
        <Button variant="outline" onClick={onBack} disabled={loading}>
          Back
        </Button>

        <Button
          onClick={handlePurchase}
          disabled={loading || purchaseStatus !== "idle"}
          className="min-w-[150px]"
        >
          {purchaseStatus === "processing"
            ? "Processing..."
            : purchaseStatus === "redirecting"
            ? "Redirecting..."
            : "Confirm Purchase"}
        </Button>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {renderStepContent()}
      {step === "details" && (
        <>
          <div className="border rounded-lg p-5">
            <h3 className="font-medium mb-4">Payment Details</h3>

            {/* Price Display */}
            {promoDetails ? (
              <div className="space-y-2">
                <div className="flex justify-between items-center text-gray-500">
                  <span>Original price</span>
                  <span className="line-through">
                    ${pkg.total_price.toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="font-medium text-gray-900">Final price</span>
                  <span className="text-xl font-semibold text-green-600">
                    ${finalPrice.toFixed(2)}
                  </span>
                </div>
              </div>
            ) : (
              <div className="flex justify-between items-center">
                <span className="font-medium text-gray-900">Price</span>
                <span className="text-xl font-semibold text-gray-900">
                  ${pkg.total_price.toFixed(2)}
                </span>
              </div>
            )}

            {/* Promo Code Section */}
            {pkg.total_price > 0 && (
              <div className="mt-4 pt-4 border-t border-gray-100">
                <details className="group">
                  <summary className="flex items-center gap-1 text-sm text-blue-600 cursor-pointer hover:text-blue-800">
                    <span>Have a promo code?</span>
                    <svg
                      className="w-4 h-4 transition-transform group-open:rotate-180"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </summary>

                  <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                    <div className="relative">
                      <Input
                        type="text"
                        value={promoCode}
                        onChange={handlePromoCodeChange}
                        placeholder="Enter promo code"
                        className="w-full"
                      />
                      {promoLoading && (
                        <div className="absolute right-3 top-1/2 -translate-y-1/2">
                          <div className="animate-spin h-5 w-5 border-2 border-blue-500 border-t-transparent rounded-full" />
                        </div>
                      )}
                    </div>

                    {promoCode.trim() && !promoLoading && promoError && (
                      <div className="flex items-center gap-2 text-red-500 text-sm mt-2">
                        <AlertTriangle className="w-4 h-4" />
                        {promoError}
                      </div>
                    )}

                    {promoCode.trim() && !promoLoading && promoDetails && (
                      <div className="flex items-center gap-2 text-green-600 text-sm mt-2">
                        <CheckCircle className="w-4 h-4" />
                        Promo code applied! {getDiscountText()}
                      </div>
                    )}
                  </div>
                </details>
              </div>
            )}
          </div>
        </>
      )}

      {renderButtons()}
    </div>
  );
}
