import Head from "next/head";

export default function HeadWrapper({
  title,
  description,
}: {
  title: string;
  description?: string;
}) {
  return (
    <Head>
      <title>{`${title || ""} | Student's Portal | AdmitPath`}</title>
      <meta
        name="description"
        content={`Student Dashboard Portal for accessing and managing all your sessions and resources. ${
          description || ""
        } | AdmitPath`}
      />
    </Head>
  );
}
