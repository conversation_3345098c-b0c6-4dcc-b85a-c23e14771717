const Loader = () => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-white z-50">
      <div className="relative inline-flex items-center justify-center p-8">
        <img
          src="/icons/logo.png"
          alt="Logo"
          className="w-32 h-32 sm:w-24 sm:h-24 lg:w-32 lg:h-32 object-contain z-10"
        />
        <div
          className="absolute rounded-full border-8 border-t-transparent animate-spin"
          style={{
            width: "120%",
            height: "120%",
            borderColor: "rgba(239, 68, 68, 0.2)",
            borderTopColor: "rgb(239, 68, 68)",
          }}
        />
      </div>
    </div>
  );
};

export default Loader;
