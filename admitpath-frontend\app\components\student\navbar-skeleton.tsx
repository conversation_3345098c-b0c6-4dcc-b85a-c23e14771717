import { Skeleton } from "@/app/components/ui/skeleton";
import { Bell, ChevronDown } from "lucide-react";

export default function NavbarSkeleton() {
    return (
        <nav className="flex justify-between items-center p-4">
            <div className="flex-1" /> {/* Empty space on the left */}
            <div className="flex items-center gap-4">
                <button className="p-2 text-gray-900 focus:outline-none rounded-lg bg-white">
                    <Bell className="w-6 h-6" />
                </button>
                <div className="flex items-center space-x-2">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="flex flex-col gap-1">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-3 w-32" />
                    </div>
                </div>
            </div>
            <button className="outline-none hover:opacity-80">
          <ChevronDown
            className="w-6 h-6 transform transition-transform duration-200 rotate-0"
          />
        </button>
        </nav>
    )
}
