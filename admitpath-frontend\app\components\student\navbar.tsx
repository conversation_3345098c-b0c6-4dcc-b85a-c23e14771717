"use client";

import { Bell, <PERSON>u } from "lucide-react";
import { UserDropdown } from "./user-dropdown";
import { useProfile } from "@/app/hooks/student/useProfile";
import NavbarSkeleton from "./navbar-skeleton";
import { useAuth } from "@/app/hooks/useAuth";
import { useRouter } from "next/navigation";

interface NavbarProps {
  onMenuClick: () => void;
}

const Navbar = ({ onMenuClick }: NavbarProps) => {
  const { userInfo } = useProfile();

  const { logout } = useAuth();
  const router = useRouter();
  const handleLogout = async () => {
    try {
      logout();
      router.push("/auth/login");
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  return !userInfo ? (
    <NavbarSkeleton />
  ) : (
    <nav className="flex justify-between items-center p-4">
      <button
        className="md:hidden p-2 text-gray-900 focus:outline-none rounded-lg bg-white"
        onClick={onMenuClick}
      >
        <Menu className="w-6 h-6" />
      </button>
      <div className="flex items-center space-x-4 ml-auto">
        <button className="p-3 text-gray-900 focus:outline-none rounded-xl bg-white hover:bg-gray-100 transition-all">
          <Bell className="w-6 h-6" />
        </button>

        <UserDropdown
          userInfo={userInfo}
          onLogoutClick={() => handleLogout()}
        />
      </div>
    </nav>
  );
};

export default Navbar;
