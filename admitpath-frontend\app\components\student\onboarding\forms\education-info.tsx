"use client";

import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { useProfile } from "@hooks/student/useProfile";
import { Button } from "@/app/components/ui/button";
import { EducationInfo } from "@/app/types/student/profile";
import { Plus, Trash2 } from "lucide-react";

const EducationInfoForm = () => {
  const {
    educationInfo,
    submitEducationInfo,
    loading,
    currentStep,
    setCurrentStep,
    deleteEducationInfo,
  } = useProfile();

  const [formData, setFormData] = useState<EducationInfo[]>([
    {
      current_education_level: "high_school",
      institution_name: "",
      institution_type: "school",
      start_date: "",
      end_date: "",
      is_current: false,
    },
  ]);

  const [isValid, setIsValid] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    if (educationInfo) {
      if (educationInfo.length > 0) {
        setFormData(
          educationInfo.map((edu) => ({
            ...edu,
            institution_type: edu.institution_type || "school",
            start_date: edu.start_date ? edu.start_date.split("T")[0] : "",
            end_date: edu.end_date ? edu.end_date.split("T")[0] : "",
          }))
        );
      }
    } else {
      setFormData([
        {
          current_education_level: "high_school",
          institution_name: "",
          institution_type: "school",
          start_date: "",
          end_date: "",
          is_current: false,
        },
      ]);
    }
  }, [educationInfo]);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    let isFormValid = true;

    formData.forEach((item, index) => {
      if (!item.current_education_level) {
        newErrors[`education_level_${index}`] = "Education level is required";
        isFormValid = false;
      }

      if (!item.institution_name.trim()) {
        newErrors[`institution_name_${index}`] = "Institution name is required";
        isFormValid = false;
      }

      if (!item.start_date) {
        newErrors[`start_date_${index}`] = "Start date is required";
        isFormValid = false;
      }

      if (!item.is_current) {
        if (!item.end_date) {
          newErrors[`end_date_${index}`] = "End date is required";
          isFormValid = false;
        } else {
          const startDate = new Date(item.start_date);
          const endDate = new Date(item.end_date);

          if (endDate < startDate) {
            newErrors[`end_date_${index}`] =
              "End date must be after start date";
            isFormValid = false;
          }

          if (endDate > new Date()) {
            newErrors[`end_date_${index}`] = "End date cannot be in the future";
            isFormValid = false;
          }
        }
      }
    });

    if (hasAttemptedSubmit) {
      setErrors(newErrors);
    }
    setIsValid(isFormValid);
    return isFormValid;
  };

  useEffect(() => {
    if (hasAttemptedSubmit) {
      validateForm();
    }
  }, [formData, hasAttemptedSubmit]);

  const handleSubmit = async () => {
    setHasAttemptedSubmit(true);
    const isFormValid = validateForm();

    if (!isFormValid) {
      toast.error("Please fill in all required fields correctly");
      return;
    }

    if (isSubmitted) {
      if (currentStep) setCurrentStep(currentStep + 1);
      return;
    }

    const loadingToast = toast.loading("Submitting educational information");

    try {
      const formattedData = formData.map((item) => ({
        ...item,
        start_date: new Date(item.start_date).toISOString(),
        end_date:
          item.is_current || !item.end_date
            ? ""
            : new Date(item.end_date).toISOString(),
      }));

      await submitEducationInfo(formattedData);

      toast.dismiss(loadingToast);
      toast.success("Education information saved successfully!");
      setIsSubmitted(true);
      if (currentStep) setCurrentStep(currentStep + 1);
    } catch (error) {
      console.error(error);
      toast.dismiss(loadingToast);
      toast.error("Failed to save education information");
    }
  };

  const handleAddMore = () => {
    setFormData((prev) => [
      ...prev,
      {
        current_education_level: "high_school",
        institution_name: "",
        institution_type: "school",
        start_date: "",
        end_date: "",
        is_current: false,
      },
    ]);
  };

  const handleDelete = async (index: number) => {
    if (formData.length === 1) {
      toast.error("You must have at least one education entry");
      return;
    }

    const education = formData[index];
    if (education.id) {
      await deleteEducationInfo(education.id);
    }

    setFormData((prev) => prev.filter((_, i) => i !== index));
    setErrors((prev) => {
      const newErrors = { ...prev };
      Object.keys(newErrors).forEach((key) => {
        if (key.includes(`_${index}`)) {
          delete newErrors[key];
        }
      });
      return newErrors;
    });
  };

  return (
    <div className="space-y-8">
      <div>
        <h2 className="sm:text-3xl text-2xl font-semibold">
          Educational <span className="text-blue-500">Background</span>
        </h2>
        <p className="text-gray-600 mt-2">
          Please provide information about your education history.
        </p>
      </div>

      <div className="space-y-6">
        {formData.map((item, index) => (
          <div key={index} className="relative space-y-6 p-6 border rounded-xl">
            {formData.length > 1 && (
              <button
                type="button"
                onClick={() => handleDelete(index)}
                className="absolute top-4 right-4 p-2 text-gray-500 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
              >
                <Trash2 className="w-5 h-5" />
              </button>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-medium">
                  Current Education Level{" "}
                  <span className="text-red-500">*</span>
                </label>
                <select
                  className={`w-full p-3 bg-gray-50 border rounded-xl ${
                    errors[`education_level_${index}`] ? "border-red-500" : ""
                  }`}
                  value={item.current_education_level}
                  onChange={(e) =>
                    setFormData((prev) =>
                      prev.map((item, i) =>
                        i === index
                          ? {
                              ...item,
                              current_education_level: e.target
                                .value as EducationInfo["current_education_level"],
                            }
                          : item
                      )
                    )
                  }
                  required
                >
                  <option value="">Select level</option>
                  <option value="high_school">High School</option>
                  <option value="bachelors">Bachelor's Degree</option>
                  <option value="masters">Master's Degree</option>
                  <option value="phd">PhD</option>
                  <option value="other">Other</option>
                </select>
                {hasAttemptedSubmit && errors[`education_level_${index}`] && (
                  <p className="text-sm text-red-500">
                    {errors[`education_level_${index}`]}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium">
                  Institution Type <span className="text-red-500">*</span>
                </label>
                <select
                  className="w-full p-3 bg-gray-50 border rounded-xl"
                  value={item.institution_type}
                  onChange={(e) =>
                    setFormData((prev) =>
                      prev.map((item, i) =>
                        i === index
                          ? {
                              ...item,
                              institution_type: e.target
                                .value as EducationInfo["institution_type"],
                            }
                          : item
                      )
                    )
                  }
                  required
                >
                  <option value="">Select type</option>
                  <option value="school">School</option>
                  <option value="college">College</option>
                  <option value="university">University</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div className="space-y-2 md:col-span-2">
                <label className="block text-sm font-medium">
                  Institution Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={item.institution_name}
                  onChange={(e) =>
                    setFormData((prev) =>
                      prev.map((item, i) =>
                        i === index
                          ? { ...item, institution_name: e.target.value }
                          : item
                      )
                    )
                  }
                  className={`w-full p-3 bg-gray-50 border rounded-xl ${
                    errors[`institution_name_${index}`] ? "border-red-500" : ""
                  }`}
                  placeholder="Enter institution name"
                  required
                />
                {hasAttemptedSubmit && errors[`institution_name_${index}`] && (
                  <p className="text-sm text-red-500">
                    {errors[`institution_name_${index}`]}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium">
                  Start Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  value={item.start_date}
                  onChange={(e) =>
                    setFormData((prev) =>
                      prev.map((item, i) =>
                        i === index
                          ? { ...item, start_date: e.target.value }
                          : item
                      )
                    )
                  }
                  className={`w-full p-3 bg-gray-50 border rounded-xl ${
                    errors[`start_date_${index}`] ? "border-red-500" : ""
                  }`}
                  required
                  max={new Date().toISOString().split("T")[0]}
                />
                {hasAttemptedSubmit && errors[`start_date_${index}`] && (
                  <p className="text-sm text-red-500">
                    {errors[`start_date_${index}`]}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium">
                  End Date{" "}
                  {!item.is_current && <span className="text-red-500">*</span>}
                </label>
                <input
                  type="date"
                  value={item.is_current ? "" : item.end_date || ""}
                  onChange={(e) =>
                    setFormData((prev) =>
                      prev.map((item, i) =>
                        i === index
                          ? { ...item, end_date: e.target.value }
                          : item
                      )
                    )
                  }
                  className={`w-full p-3 bg-gray-50 border rounded-xl ${
                    errors[`end_date_${index}`] ? "border-red-500" : ""
                  } ${item.is_current ? "opacity-50 cursor-not-allowed" : ""}`}
                  disabled={item.is_current}
                  required={!item.is_current}
                  min={item.start_date}
                  max={new Date().toISOString().split("T")[0]}
                />
                {hasAttemptedSubmit && errors[`end_date_${index}`] && (
                  <p className="text-sm text-red-500">
                    {errors[`end_date_${index}`]}
                  </p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={item.is_current}
                    onChange={(e) =>
                      setFormData((prev) =>
                        prev.map((item, i) =>
                          i === index
                            ? {
                                ...item,
                                is_current: e.target.checked,
                                end_date: e.target.checked
                                  ? ""
                                  : item.end_date || "",
                              }
                            : item
                        )
                      )
                    }
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm font-medium">
                    Currently studying here
                  </span>
                </label>
              </div>
            </div>
          </div>
        ))}

        <Button
          type="button"
          variant="outline"
          className="w-full"
          onClick={handleAddMore}
        >
          <Plus className="w-4 h-4 mr-2" />
          Add another education
        </Button>
      </div>

      <div className="flex justify-end gap-4">
        <Button
          onClick={() => currentStep && setCurrentStep(currentStep - 1)}
          variant="outline"
          disabled={loading}
        >
          Back
        </Button>
        <Button onClick={handleSubmit} disabled={loading}>
          Next
        </Button>
      </div>
    </div>
  );
};

export default EducationInfoForm;
