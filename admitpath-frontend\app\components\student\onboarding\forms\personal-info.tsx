"use client";

import { useProfile } from "@hooks/student/useProfile";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { countries } from "@/lib/countries";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import BirthdayPicker from "@components/common/date-of-birth";
import { Dialog } from "@headlessui/react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";

const personalInfoSchema = z.object({
  nationality: z.string().min(1, "Nationality is required"),
  country_of_residence: z.string().min(1, "Country of residence is required"),
  gender: z.string().min(1, "Gender is required"),
  date_of_birth: z
    .string()
    .min(1, "Date of birth is required")
    .refine((date) => new Date(date) < new Date(), {
      message: "Date of birth must be in the past",
    }),
});

type PersonalInfoFormData = z.infer<typeof personalInfoSchema>;

const PersonalInfoForm = () => {
  const {
    submitPersonalInfo,
    clearError,
    currentStep,
    setCurrentStep,
    loading,
    personalInfo,
    userInfo,
  } = useProfile();

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedField, setSelectedField] = useState<
    "nationality" | "country_of_residence" | null
  >(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<PersonalInfoFormData>({
    resolver: zodResolver(personalInfoSchema),
    defaultValues: {
      nationality: personalInfo?.nationality || "",
      country_of_residence: personalInfo?.country_of_residence || "",
      gender: personalInfo?.gender || "",
      date_of_birth: personalInfo?.date_of_birth?.split("T")[0] || "",
    },
  });

  const formValues = watch();

  useEffect(() => {
    clearError();
  }, [clearError]);

  const onSubmit = async (data: PersonalInfoFormData) => {
    if (!currentStep) return;
    const loadingToast = toast.loading("Submitting personal information");

    const formattedData = {
      ...data,
      first_name: userInfo!.firstName,
      last_name: userInfo!.lastName,
      date_of_birth: new Date(data.date_of_birth).toISOString(),
    };

    try {
      await submitPersonalInfo(formattedData);
      toast.update(loadingToast, {
        render: "Personal information updated successfully",
        type: "success",
        isLoading: false,
        autoClose: 2000,
      });
      setCurrentStep(currentStep + 1);
    } catch (error) {
      toast.update(loadingToast, {
        render: "Failed to update personal information",
        type: "error",
        isLoading: false,
        autoClose: 2000,
      });
    }
  };

  const handleOpenModal = (field: "nationality" | "country_of_residence") => {
    setSelectedField(field);
    setSearchTerm("");
    setIsModalOpen(true);
  };

  const handleCountrySelect = (code: string) => {
    if (selectedField) {
      setValue(selectedField, code);
      setIsModalOpen(false);
    }
  };

  const getFieldLabel = (field: string) => {
    const country = countries.find((c) => c.code === field);
    return country ? country.name : "Please select";
  };

  const filteredCountries = countries.filter((country) =>
    country.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      <div>
        <h2 className="sm:text-3xl text-2xl font-semibold">
          Tell us about <span className="text-blue-500">yourself</span>
        </h2>
        <p className="text-gray-600 mt-2">
          Please complete the following details to let us know more about you.
          It will take less than 5 minutes!
        </p>
      </div>
      <div className="gap-y-6 gap-x-8 w-full grid lg:grid-cols-2 grid-cols-1">
        <div>
          <label className="block mb-2 text-sm font-medium text-gray-900">
            Nationality<span className="text-red-500">*</span>
          </label>
          <button
            type="button"
            onClick={() => handleOpenModal("nationality")}
            className="w-full p-3 text-left border rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            {getFieldLabel(formValues.nationality)}
          </button>
          {errors.nationality && (
            <p className="mt-1 text-sm text-red-600">
              {errors.nationality.message}
            </p>
          )}
        </div>

        <div>
          <label className="block mb-2 text-sm font-medium text-gray-900">
            Country of Residence<span className="text-red-500">*</span>
          </label>
          <button
            type="button"
            onClick={() => handleOpenModal("country_of_residence")}
            className="w-full p-3 text-left border rounded-lg focus:ring-blue-500 focus:border-blue-500 bg-white"
          >
            {getFieldLabel(formValues.country_of_residence)}
          </button>
          {errors.country_of_residence && (
            <p className="mt-1 text-sm text-red-600">
              {errors.country_of_residence.message}
            </p>
          )}
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Gender<span className="text-red-500">*</span>
          </label>
          <Select
            onValueChange={(value) => setValue("gender", value)}
            value={watch("gender")}
          >
            <SelectTrigger
              className={`shadow-none bg-white ${
                errors.gender ? "border-red-500" : ""
              }`}
            >
              <SelectValue placeholder="Select gender" />
            </SelectTrigger>
            <SelectContent className="bg-white">
              <SelectItem
                className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                value="male"
              >
                Male
              </SelectItem>
              <SelectItem
                className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                value="female"
              >
                Female
              </SelectItem>
              <SelectItem
                className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                value="other"
              >
                Other
              </SelectItem>
            </SelectContent>
          </Select>
          {errors.gender && (
            <p className="text-red-500 text-sm">{errors.gender.message}</p>
          )}
        </div>

        <BirthdayPicker
          value={formValues.date_of_birth}
          onChange={(date) => setValue("date_of_birth", date)}
          required={true}
        />
        {errors.date_of_birth && (
          <p className="mt-1 text-sm text-red-600">
            {errors.date_of_birth.message}
          </p>
        )}
      </div>

      <div className="flex justify-end space-x-4">
        <button
          type="submit"
          disabled={loading}
          className="inline-flex justify-center rounded-md border border-transparent bg-indigo-950 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-indigo-950/90 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
        >
          {loading ? "Saving..." : "Next"}
        </button>
      </div>

      {/* Country Selection Modal */}
      <Dialog
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        className="relative z-50"
      >
        <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
        <div className="fixed inset-0 flex items-center justify-center p-4">
          <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
            <Dialog.Title className="text-lg font-medium leading-6 text-gray-900 mb-4">
              Select{" "}
              {selectedField === "nationality"
                ? "Nationality"
                : "Country of Residence"}
            </Dialog.Title>

            {/* Search Input */}
            <div className="mb-4">
              <input
                type="text"
                placeholder="Search countries..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                autoFocus={true}
                className="w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            {/* Countries List */}
            <div className="mt-2 max-h-[60vh] overflow-y-auto">
              {filteredCountries.map((country) => (
                <button
                  key={country.code}
                  type="button"
                  className="w-full text-left px-4 py-2 hover:bg-gray-100 focus:bg-gray-100 rounded-lg"
                  onClick={() => handleCountrySelect(country.code)}
                >
                  {country.name}
                </button>
              ))}
            </div>
          </Dialog.Panel>
        </div>
      </Dialog>
    </form>
  );
};

export default PersonalInfoForm;
