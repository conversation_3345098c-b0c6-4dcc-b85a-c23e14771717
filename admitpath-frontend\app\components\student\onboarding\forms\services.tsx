"use client";

import { useRouter } from "next/navigation";
import { useProfile } from "@hooks/student/useProfile";
import { useState } from "react";
import { toast } from "react-toastify";

const ServicesForm = () => {
  const router = useRouter();
  const {
    submitExpectedServices,
    currentStep,
    setCurrentStep,
    loading,
    servicesInfo,
  } = useProfile();
  const [formData, setFormData] = useState<{
    selected_services: string[];
    additional_details: string;
  }>({
    selected_services: servicesInfo?.selected_services || [],
    additional_details: servicesInfo?.additional_details || "",
  });

  const handleServiceSelect = (service: string) => {
    setFormData((prev) => ({
      ...prev,
      selected_services: prev.selected_services.includes(service)
        ? prev.selected_services.filter((s) => s !== service)
        : [...prev.selected_services, service],
    }));
  };

  const handleSubmit = async () => {
    if (formData.selected_services.length === 0) {
      toast.error("Please select at least one service");
      return;
    }

    const loadingToast = toast.loading("Submitting service preferences");
    try {
      await submitExpectedServices(formData);
      toast.dismiss(loadingToast);
      toast.success("Service preferences saved successfully!");
    } catch (error: any) {
      toast.dismiss(loadingToast);
      toast.error(error.message || "Failed to save service preferences");
    }
  };

  const handleBack = () => {
    if (currentStep) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div className="space-y-8">
      <h2 className="sm:text-3xl text-2xl font-semibold">Expected Services</h2>
      <div className="space-y-6">
        <div className="space-y-4">
          <label className="block text-sm font-medium">
            Select Services <span className="text-red-500">*</span>
          </label>
          {["Essay Review", "Interview Prep", "Application Review"].map(
            (service) => (
              <label
                key={service}
                className="flex items-center space-x-3 p-4 border rounded-xl hover:bg-gray-50 cursor-pointer"
              >
                <input
                  type="checkbox"
                  className="h-5 w-5 rounded border-gray-300"
                  checked={formData.selected_services.includes(service)}
                  onChange={() => handleServiceSelect(service)}
                />
                <span>{service}</span>
              </label>
            )
          )}
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium">Other Preferences</label>
          <textarea
            className="w-full p-3 bg-gray-50 border rounded-xl h-32"
            placeholder="Tell us more about what you're looking for..."
            value={formData.additional_details}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                additional_details: e.target.value,
              }))
            }
          />
        </div>

        <div className="flex justify-end gap-4 mt-8">
          <button
            onClick={handleBack}
            disabled={loading}
            className="px-6 py-3 bg-gray-100 rounded-full text-black sm:text-xl text-lg font-medium hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Back
          </button>
          <button
            onClick={handleSubmit}
            disabled={loading}
            className="px-6 py-3 bg-gray-900 rounded-full text-white sm:text-xl text-lg font-medium hover:bg-[#15154B]/90 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Submit
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9 18L15 12L9 6"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ServicesForm;
