"use client";

import OnboardingSteps from "@/app/components/student/onboarding/steps";
import { useProfile } from "@hooks/student/useProfile";
import { OnboardingSkeleton } from "./loading-skeleton";
import { useEffect } from "react";

const steps = ["Personal information", "Educational background"] as const;

export default function CreateProfile() {
  const { currentStep, figureCurrentStep } = useProfile();

  useEffect(() => {
    figureCurrentStep();
  }, [figureCurrentStep]);

  return !currentStep ? (
    <OnboardingSkeleton />
  ) : (
    <div className="w-full flex flex-col justify-start md:flex-row md:items-start md:justify-center gap-6">
      {/* Left sidebar with steps */}
      <div className="w-full md:w-[280px] xl:w-[320px] bg-white rounded-2xl shadow-lg p-6 md:sticky md:top-8">
        <div className="space-y-6">
          {steps.map((label, index) => (
            <div key={index} className="flex items-center gap-3">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  index + 1 <= currentStep
                    ? "bg-blue-600 text-white"
                    : "border-2"
                }`}
              >
                {index + 1}
              </div>
              <span
                className={
                  index + 1 <= currentStep ? "font-medium" : "opacity-60"
                }
              >
                {label}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 bg-white rounded-2xl shadow-lg p-6 sm:p-8">
        <OnboardingSteps />
      </div>
    </div>
  );
}
