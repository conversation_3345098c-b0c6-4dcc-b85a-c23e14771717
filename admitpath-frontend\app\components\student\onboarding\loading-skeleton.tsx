"use client";

import { Skeleton } from "@components/ui/skeleton";

export function OnboardingSkeleton() {
  return (
    <div className="w-full flex flex-col justify-start md:flex-row md:items-start md:justify-center gap-6">
      {/* Left sidebar skeleton */}
      <div className="w-full md:w-[280px] xl:w-[320px] bg-white rounded-2xl shadow-lg p-6 md:sticky md:top-8">
        <div className="space-y-6">
          {[1, 2, 3].map((_, index) => (
            <div key={index} className="flex items-center gap-3">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  index === 0
                    ? "bg-blue-600 text-white"
                    : "border-2 border-gray-200"
                }`}
              >
                {index + 1}
              </div>
              <Skeleton className="h-4 w-32" />
            </div>
          ))}
        </div>
      </div>

      {/* Main content skeleton */}
      <div className="flex-1 bg-white rounded-2xl shadow-lg p-6 sm:p-8">
        <div className="space-y-8">
          {/* Title and subtitle */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-4 w-full max-w-[600px]" />
            </div>
          </div>

          {/* Form fields */}
          <div className="space-y-6">
            {/* Section title */}
            <Skeleton className="h-6 w-40" />

            {/* Name fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>

            {/* Nationality and Country fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>

            {/* Gender and Date fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
          </div>

          {/* Buttons */}
          <div className="flex justify-between pt-6">
            <Skeleton className="h-10 w-24" />
            <Skeleton className="h-10 w-24" />
          </div>
        </div>
      </div>
    </div>
  );
}
