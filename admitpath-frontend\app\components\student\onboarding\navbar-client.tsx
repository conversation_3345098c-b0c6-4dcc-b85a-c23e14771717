"use client";

import Image from "next/image";
import { ChevronDown } from "lucide-react";
import { useProfile } from "@/app/hooks/student/useProfile";
import { useEffect } from "react";
import { generatePlaceholder } from "@/app/utils/image";

const NavbarClient = () => {
  const { fetchUserInfo, userInfo } = useProfile();

  useEffect(() => {
    fetchUserInfo();
  }, [fetchUserInfo]);

  return (
    <div className="flex items-center gap-2">
      <Image
        src={
          userInfo?.profile_picture_url ||
          generatePlaceholder(userInfo?.firstName, userInfo?.lastName)
        }
        alt="Profile"
        width={40}
        height={40}
        className="rounded-full "
      />
      <div className="text-white">
        <p className="font-medium">{userInfo?.firstName || "User"}</p>
        <p className="text-sm opacity-90">{userInfo?.email}</p>
      </div>
    </div>
  );
};

export default NavbarClient;
