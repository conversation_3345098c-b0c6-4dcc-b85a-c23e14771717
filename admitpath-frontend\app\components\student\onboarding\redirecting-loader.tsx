import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function RedirectingLoader() {
  const router = useRouter();

  useEffect(() => {
    router.push("/explore");
  }, [router]);

  return (
    <div className="flex flex-col justify-center items-center h-max md:gap-y-6 gap-y-4">
      <div className="relative inline-flex items-center justify-center md:p-4 p-2">
        <Image
          src={"/icons/logo.png"}
          alt="logo"
          width={40}
          height={40}
          className="rounded-full "
        />
        <div
          className="absolute rounded-full border-4  border-t-transparent animate-spin"
          style={{
            width: "120%",
            height: "120%",
            borderColor: "rgba(239, 68, 68, 0.2)",
            borderTopColor: "rgb(239, 68, 68)",
          }}
        />
      </div>
      <h1 className="sm:text-2xl text-xl text-center font-semibold italic text-gray-800/60">
        Profile completed!
      </h1>
    </div>
  );
}
