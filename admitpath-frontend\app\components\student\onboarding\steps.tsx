"use client";

import { PersonalInfoForm, EducationForm, ServicesForm } from "./forms";
import { useProfile } from "@hooks/student/useProfile";
import RedirectingLoader from "./redirecting-loader";

const OnboardingSteps = () => {
  const { currentStep } = useProfile();

  const renderForm = () => {
    switch (currentStep) {
      case 1:
        return <PersonalInfoForm />;
      case 2:
        return <EducationForm />;
      // case 3:
      //   return <ServicesForm />;
      case 3:
        return <RedirectingLoader />;
      default:
        return <PersonalInfoForm />;
    }
  };

  return <div className="space-y-8">{renderForm()}</div>;
};

export default OnboardingSteps;
