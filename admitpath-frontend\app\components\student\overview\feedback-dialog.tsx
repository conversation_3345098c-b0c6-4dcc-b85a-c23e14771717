"use client";

import type React from "react";

import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@/app/components/ui/dialog";
import { Video, Clock, ArrowLeft } from "lucide-react";
import { useState } from "react";
import Image from "next/image";
import { generatePlaceholder } from "@/app/utils/image";

interface FeedbackDialogProps {
  feedback: {
    session_id: number;
    event_name: string;
    start_time: string;
    end_time: string;
    instructor_name: string;
    maxCharLimit?: number;
    time?: string;
  } | null;
  onClose: () => void;
  onSubmit: (data: {
    rating: number;
    received_expected_service: boolean;
    comment: string;
    additional_feedback: string;
  }) => void;
  isSubmitting: boolean;
}

export const FeedbackDialog = ({
  feedback,
  onClose,
  onSubmit,
  isSubmitting,
}: FeedbackDialogProps) => {
  const [formData, setFormData] = useState({
    serviceAsExpected: "yes",
    rating: 0,
    counsellorComments: "",
    additionalFeedback: "",
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleClose = () => {
    setFormData({
      serviceAsExpected: "yes",
      rating: 0,
      counsellorComments: "",
      additionalFeedback: "",
    });
    setErrors({});
    onClose();
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newErrors: Record<string, string> = {};

    if (formData.rating === 0) {
      newErrors.rating = "Please provide a rating";
    }
    if (!formData.counsellorComments.trim()) {
      newErrors.counsellorComments =
        "Please provide comments for the counsellor";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    onSubmit({
      rating: formData.rating,
      received_expected_service: formData.serviceAsExpected === "yes",
      comment: formData.counsellorComments,
      additional_feedback: formData.additionalFeedback,
    });
  };

  return (
    <Dialog open={!!feedback} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto rounded-2xl">
        <div className="bg-gray-50 p-4">
          <DialogTitle className="text-xl font-semibold">Feedback</DialogTitle>
        </div>
        <div className="px-4 py-3 border-b bg-white">
          <p className="text-gray-900 text-base font-medium">
            Provide feedback for your session
          </p>
        </div>
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-xl space-y-3">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3">
                <div className="flex items-start gap-3">
                  <div className="p-2.5 bg-purple-100 rounded-xl shrink-0">
                    <Video className="w-5 h-5 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="font-medium">{feedback?.event_name}</h3>
                    <p className="text-gray-500 text-sm"></p>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-gray-900 font-medium shrink-0">
                  <Clock className="w-4 h-4" />
                  {feedback && (
                    <span className="text-sm">
                      {new Date(feedback!.start_time).toLocaleDateString()}{" "}
                      {new Date(feedback!.start_time).toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                      {" - "}
                      {new Date(feedback!.end_time).toLocaleTimeString([], {
                        hour: "2-digit",
                        minute: "2-digit",
                      })}
                    </span>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Image
                  src={generatePlaceholder(
                    feedback?.instructor_name.trim().split(/s+/)[0],
                    feedback?.instructor_name.trim().split(/\s+/)[1]
                  )}
                  alt={feedback?.instructor_name || "Instructor"}
                  width={32}
                  height={32}
                  className="rounded-full"
                />
                <span className="text-sm">{feedback?.instructor_name}</span>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium block">
                Did you receive service as expected?
              </label>
              <div className="flex gap-6">
                <div className="flex items-center gap-3">
                  <input
                    type="radio"
                    name="service"
                    value="yes"
                    id="yes"
                    checked={formData.serviceAsExpected === "yes"}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        serviceAsExpected: e.target.value,
                      })
                    }
                    className="w-5 h-5 text-[#15194B] cursor-pointer"
                  />
                  <label htmlFor="yes" className="text-sm cursor-pointer">
                    Yes
                  </label>
                </div>
                <div className="flex items-center gap-3">
                  <input
                    type="radio"
                    name="service"
                    value="no"
                    id="no"
                    checked={formData.serviceAsExpected === "no"}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        serviceAsExpected: e.target.value,
                      })
                    }
                    className="w-5 h-5 text-[#15194B] cursor-pointer"
                  />
                  <label htmlFor="no" className="text-sm cursor-pointer">
                    No
                  </label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium block">
                Please rate your session
              </label>
              <div className="flex gap-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    type="button"
                    onClick={() => setFormData({ ...formData, rating: star })}
                    className={`p-1 ${
                      formData.rating >= star
                        ? "text-yellow-400"
                        : "text-gray-300"
                    }`}
                  >
                    <svg
                      className="w-8 h-8"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </button>
                ))}
              </div>
              {errors.rating && (
                <p className="text-red-500 text-sm">{errors.rating}</p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium block">
                Would you like to provide any comments for the counsellor?
              </label>
              <div className="relative">
                <textarea
                  value={formData.counsellorComments}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      counsellorComments: e.target.value,
                    })
                  }
                  maxLength={feedback?.maxCharLimit}
                  className="w-full min-h-[100px] md:min-h-[160px] p-2 md:p-3 text-sm rounded-lg border focus:outline-none focus:ring-2 focus:ring-gray-200"
                  placeholder="Enter your comments here..."
                />
                <span className="absolute bottom-1.5 md:bottom-2 right-1.5 md:right-2 text-xs md:text-sm text-gray-400">
                  {formData.counsellorComments.length}/{feedback?.maxCharLimit}
                </span>
              </div>
              {errors.counsellorComments && (
                <p className="text-red-500 text-xs md:text-sm">
                  {errors.counsellorComments}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium block">
                Would you like to share any other feedback? (Optional)
              </label>
              <div className="relative">
                <textarea
                  value={formData.additionalFeedback}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      additionalFeedback: e.target.value,
                    })
                  }
                  maxLength={feedback?.maxCharLimit}
                  className="w-full min-h-[100px] md:min-h-[160px] p-2 md:p-3 text-sm rounded-lg border focus:outline-none focus:ring-2 focus:ring-gray-200"
                  placeholder="Enter additional feedback..."
                />
                <span className="absolute bottom-1.5 md:bottom-2 right-1.5 md:right-2 text-xs md:text-sm text-gray-400">
                  {formData.additionalFeedback.length}/{feedback?.maxCharLimit}
                </span>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 border rounded-xl hover:bg-gray-50 transition-colors flex items-center gap-2 text-sm"
            >
              <ArrowLeft className="w-4 h-4" />
              Back
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-[#15194B] text-white rounded-xl font-medium hover:bg-gray-900 transition-colors text-sm disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Submitting...
                </>
              ) : (
                "Continue"
              )}
            </button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
