"use client";

import { useEffect } from "react";
import { useOverview } from "@hooks/student/useOverview";
import { StatsCards } from "@components/student/overview/stats-card";
import { UpcomingSessions } from "@components/student/overview/upcoming-sessions";
import { PendingFeedbacks } from "@components/student/overview/pending-feedback";
import { toast } from "react-toastify";
import { useSessions } from "@/app/hooks/student/useSessions";
import HeadWrapper from "../head-wrapper";

export default function Overview() {
  const { fetchStats, fetchPendingFeedbacks } = useOverview();
  const { fetchSessions } = useSessions();

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        await Promise.all([
          fetchStats(),
          fetchSessions(Intl.DateTimeFormat().resolvedOptions().timeZone),
          fetchPendingFeedbacks(),
        ]);
      } catch (error: any) {
        toast.error(error.message || "Failed to load dashboard data");
      }
    };

    loadDashboardData();
  }, [fetchStats, fetchSessions, fetchPendingFeedbacks]);

  return (
    <>
      <HeadWrapper title="Dashboard" />
      <div className="bg-white flex flex-col gap-6 rounded-2xl border sm:p-6 p-4">
        <StatsCards />
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
          <div className="lg:col-span-7">
            <UpcomingSessions />
          </div>
          <div className="lg:col-span-5 flex flex-col gap-6">
            <PendingFeedbacks />
          </div>
        </div>
      </div>
    </>
  );
}
