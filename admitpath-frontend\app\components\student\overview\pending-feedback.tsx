import { MessageSquare, Video } from "lucide-react";
import { useState, useEffect } from "react";
import { FeedbackDialog } from "./feedback-dialog";
import { SuccessDialog } from "./success-dialog";
import { useOverview } from "@hooks/student/useOverview";
import { toast } from "react-toastify";
import { PendingFeedbacksSkeleton } from "./skeleton";

interface PendingFeedbackSession {
  session_id: number;
  event_name: string;
  start_time: string;
  end_time: string;
  instructor_name: string;
}

export const PendingFeedbacks = () => {
  const { pendingFeedbacks, submitFeedback } = useOverview();
  const [selectedFeedback, setSelectedFeedback] =
    useState<PendingFeedbackSession | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (pendingFeedbacks !== undefined) {
      setLoading(false);
    }
  }, [pendingFeedbacks]);

  const handleSubmitFeedback = async (data: {
    rating: number;
    received_expected_service: boolean;
    comment: string;
    additional_feedback: string;
  }) => {
    try {
      if (!selectedFeedback) return;
      setIsSubmitting(true);
      toast.loading("Submitting feedback...");
      await submitFeedback(selectedFeedback.session_id, data);

      setIsSubmitting(false);
      setSelectedFeedback(null);
      toast.dismiss();
      toast.success("Feedback submitted successfully!");
    } catch (error: any) {
      toast.error(error.message || "Failed to submit feedback");
    }
  };

  return (
    <>
      {!pendingFeedbacks || loading ? (
        <PendingFeedbacksSkeleton />
      ) : (
        <div className="rounded-2xl border flex flex-col gap-6 md:p-6 p-4">
          <h3 className="text-xl font-medium">Pending Feedback</h3>
          {pendingFeedbacks.length > 0 ? (
            <div className="space-y-4">
              {pendingFeedbacks.map((feedback) => (
                <div
                  key={feedback.session_id}
                  onClick={() => setSelectedFeedback(feedback)}
                  className="p-4 bg-gray-50 border rounded-2xl cursor-pointer hover:border-gray-300 hover:shadow-sm transition-all duration-200"
                >
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-200 rounded-xl">
                      <Video className="w-5 h-5 text-green-800" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium">
                        {feedback.event_name}
                      </h3>
                      <p className="text-gray-500">
                        {new Date(feedback.start_time).toLocaleTimeString([], {
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                        • Instructor: {feedback.instructor_name}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-6 text-center">
              <MessageSquare className="w-10 h-10 text-gray-400 mb-2" />
              <p className="text-gray-600 font-medium">No pending feedback</p>
              <p className="text-gray-500 text-sm">
                All feedback has been submitted
              </p>
            </div>
          )}
        </div>
      )}

      <FeedbackDialog
        feedback={selectedFeedback}
        onClose={() => setSelectedFeedback(null)}
        onSubmit={handleSubmitFeedback}
        isSubmitting={isSubmitting}
      />
    </>
  );
};
