import Image from "next/image";
import Link from "next/link";

interface RecentPackage {
  id: string;
  counsellor: {
    name: string;
    avatar: string;
    isOnline: boolean;
  };
  title: string;
  description: string;
  amount: number;
}

interface RecentPackagesProps {
  packages: RecentPackage[];
}

export const RecentPackages = ({ packages }: RecentPackagesProps) => {
  // Show only first 2 packages
  const displayedPackages = packages.slice(0, 2);

  return (
    <div className="rounded-2xl border flex flex-col gap-6 md:p-6 p-4">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-medium text-gray-700">Packages</h2>
      </div>
      <div className="flex flex-col gap-4">
        {displayedPackages.map((pkg) => (
          <div
            key={pkg.id}
            className="bg-gray-50 border rounded-2xl p-4 space-y-4 cursor-pointer"
          >
            <div className="flex items-center gap-3">
              <div className="relative">
                <Image
                  src={pkg.counsellor.avatar}
                  alt={pkg.counsellor.name}
                  width={50}
                  height={50}
                  className="rounded-full"
                />
                {pkg.counsellor.isOnline && (
                  <div className="absolute bottom-0 right-0 w-2.5 h-2.5 bg-green-500 rounded-full border-2 border-white" />
                )}
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium">{pkg.counsellor.name}</h3>
              </div>
              <div className="text-right">
                <span className="text-2xl sm:text-3xl font-medium text-gray-700">
                  ${pkg.amount}
                </span>
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-700 text-lg">{pkg.title}</h4>
              <p className="text-md text-gray-600">{pkg.description}</p>
            </div>
          </div>
        ))}
      </div>
      <Link
        href="/student/dashboard/packages"
        className="text-lg text-center text-black transition-all cursor-pointer font-medium"
      >
        View all
      </Link>
    </div>
  );
};
