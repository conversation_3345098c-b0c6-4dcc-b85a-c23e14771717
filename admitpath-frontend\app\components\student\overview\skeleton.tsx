import { Skeleton } from "@/app/components/ui/skeleton";

export const StatsSkeleton = () => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="flex items-center gap-4 p-4 border rounded-xl">
          <Skeleton className="h-12 w-12 rounded-xl" /> {/* Icon */}
          <div className="flex flex-row sm:flex-col gap-2">
            <Skeleton className="h-6 w-16" /> {/* Number */}
            <Skeleton className="h-4 w-32" /> {/* Label */}
          </div>
        </div>
      ))}
    </div>
  );
};

export const UpcomingSessionsSkeleton = () => {
  return (
    <div className="space-y-4">
      <Skeleton className="h-8 w-48" /> {/* Title */}
      {[...Array(3)].map((_, i) => (
        <div key={i} className="flex items-center gap-4 p-4 border rounded-xl">
          <Skeleton className="h-12 w-12 rounded-full" /> {/* Avatar */}
          <div className="space-y-2 flex-1">
            <Skeleton className="h-5 w-3/4" /> {/* Session name */}
            <Skeleton className="h-4 w-1/2" /> {/* Counselor name */}
            <Skeleton className="h-4 w-1/3" /> {/* Time */}
          </div>
        </div>
      ))}
    </div>
  );
};

export const PendingFeedbacksSkeleton = () => {
  return (
    <div className="space-y-4">
      <Skeleton className="h-8 w-48" /> {/* Title */}
      <div className="p-4 border rounded-xl space-y-2">
        <Skeleton className="h-5 w-full" />
        <Skeleton className="h-4 w-3/4" />
      </div>
    </div>
  );
};
