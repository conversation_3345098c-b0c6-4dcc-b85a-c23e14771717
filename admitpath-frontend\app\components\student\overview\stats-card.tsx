import { Video, Calendar, Users } from "lucide-react";
import { useOverview } from "@hooks/student/useOverview";
import { StatsSkeleton } from "./skeleton";

export const StatsCards = () => {
  const { stats } = useOverview();

  const cards = [
    {
      icon: <Video className="w-6 h-6 text-purple-600" />,
      value: stats?.total_sessions || 0,
      label: "Total Sessions Booked",
      bgColor: "bg-purple-100",
    },
    {
      icon: <Calendar className="w-6 h-6 text-orange-600" />,
      value: stats?.upcoming_sessions || 0,
      label: "Upcoming Sessions",
      bgColor: "bg-orange-100",
    },
    {
      icon: <Users className="w-6 h-6 text-blue-600" />,
      value: stats?.total_counsellors || 0,
      label: "Total Counsellors",
      bgColor: "bg-blue-100",
    },
  ];

  return !stats ? (
    <StatsSkeleton />
  ) : (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {cards.map((card, index) => (
        <div
          key={index}
          className="flex items-center gap-4 md:p-6 p-4 rounded-2xl border"
        >
          <div className={`p-3 ${card.bgColor} rounded-xl`}>{card.icon}</div>
          <div className="flex flex-row md:flex-col items-center md:items-start gap-2">
            <h3 className="md:text-4xl text-2xl font-medium">{card.value}</h3>
            <p className="md:text-lg text-md text-gray-600">{card.label}</p>
          </div>
        </div>
      ))}
    </div>
  );
};
