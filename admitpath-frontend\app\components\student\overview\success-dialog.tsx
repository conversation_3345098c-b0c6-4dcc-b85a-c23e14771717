import { Dialog, DialogContent, DialogTitle } from "@/app/components/ui/dialog";
import Image from "next/image";

interface SuccessDialogProps {
  open: boolean;
  onClose: () => void;
}

export const SuccessDialog = ({ open, onClose }: SuccessDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-sm text-center">
        <DialogTitle className="sr-only">Success</DialogTitle>
        <div className="flex flex-col items-center gap-4 py-4">
          <Image
            src="/svgs/green-tick.svg"
            alt="Success"
            width={100}
            height={100}
          />
          <h2 className="text-xl font-semibold">Successfully submitted!</h2>
          <p className="text-gray-500">
            Thank you for providing your feedback.
          </p>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-[#15194B] text-white rounded-xl font-medium hover:bg-gray-900 transition-colors"
          >
            Done
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
