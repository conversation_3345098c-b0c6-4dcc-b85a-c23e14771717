import { Calendar, Clock, Video } from "lucide-react";
import { useSessions } from "@hooks/student/useSessions";
import { UpcomingSessionsSkeleton } from "./skeleton";
import Link from "next/link";
import Image from "next/image";
import { useState, useEffect } from "react";
import { SendMessageDialog } from "@/app/components/student/sessions/send-message-dialog";
import { generatePlaceholder } from "@/app/utils/image";
import { Session } from "@/app/types/student/sessions";

// Helper function to check if a session needs feedback - same as in sessions/index.tsx
const isPendingFeedback = (session: Session): boolean => {
  if (session.status === "upcoming") {
    const endTime = new Date(session.end_time);
    const currentTime = new Date();
    return endTime < currentTime;
  }
  return false;
};

export const UpcomingSessions = () => {
  const { sessions } = useSessions();
  const [showSendMessage, setShowSendMessage] = useState(false);
  const [selectedCounselorInfo, setSelectedCounselorInfo] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (sessions) {
      setLoading(false);
    }
  }, [sessions]);

  const formatTime = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    return `${start.toLocaleDateString([], {
      month: "short",
      day: "numeric",
    })} :- ${start.toLocaleTimeString([], {
      hour: "numeric",
      minute: "2-digit",
    })} - ${end.toLocaleTimeString([], {
      hour: "numeric",
      minute: "2-digit",
    })}`;
  };

  return !sessions ? (
    <UpcomingSessionsSkeleton />
  ) : (
    <div className="rounded-2xl border flex flex-col gap-6 md:p-6 p-4">
      <h3 className="text-xl font-medium">Upcoming Sessions</h3>

      {/* Message dialog */}
      {selectedCounselorInfo && (
        <SendMessageDialog
          open={showSendMessage}
          onOpenChange={setShowSendMessage}
          counselorInfo={selectedCounselorInfo}
        />
      )}

      {sessions.items.filter(
        (session) =>
          session.status === "upcoming" && !isPendingFeedback(session)
      ).length > 0 ? (
        <div className="space-y-4">
          {sessions.items
            .filter(
              (session) =>
                session.status === "upcoming" && !isPendingFeedback(session)
            )
            .map((session) => (
              <div
                key={session.id}
                className="p-4 bg-gray-50 border rounded-2xl hover:border-gray-300 hover:shadow-sm transition-all duration-200"
              >
                <div className="flex flex-col gap-4">
                  {/* Top row: Session info and join button */}
                  <div className="flex sm:flex-row flex-col sm:items-start items-center justify-between gap-x-4 gap-y-5">
                    <div className="flex items-start gap-3 flex-grow">
                      <div className="p-2 bg-purple-100 rounded-xl flex-shrink-0">
                        <Video className="w-6 h-6 text-purple-600" />
                      </div>
                      <div>
                        <h3 className="text-lg font-medium mb-1">
                          {session.event_name}
                        </h3>
                        <div className="flex items-center gap-1 text-gray-500">
                          <Clock className="w-4 h-4" />
                          <span>
                            {formatTime(session.start_time, session.end_time)}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex 2xl:flex-row sm:flex-col flex-row gap-2">
                      {session.meeting_link ? (
                        <Link
                          href={session.meeting_link}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="px-4 py-2 bg-navy-700 bg-gray-800 text-white rounded-lg hover:bg-navy-800 transition-colors whitespace-nowrap flex-shrink-0"
                        >
                          Join Session
                        </Link>
                      ) : (
                        <button
                          disabled
                          className="px-4 py-2 bg-gray-400 text-white rounded-lg whitespace-nowrap flex-shrink-0 cursor-not-allowed"
                        >
                          No Link Yet
                        </button>
                      )}
                      <button
                        onClick={() => {
                          setSelectedCounselorInfo({
                            user_id: session.counselor_user_id,
                            first_name:
                              session.counselor_name?.split(" ")[0] || "",
                            last_name:
                              session.counselor_name?.split(" ")[1] || "",
                            profile_picture_url:
                              session.counselor_profile_picture || null,
                          });
                          setShowSendMessage(true);
                        }}
                        className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors whitespace-nowrap flex-shrink-0"
                      >
                        Message
                      </button>
                    </div>
                  </div>

                  {/* Bottom row: Counselor info */}
                  <div className="flex items-center gap-3 border-t pt-3">
                    <Image
                      src={
                        session.counselor_profile_picture ||
                        generatePlaceholder(
                          session.counselor_name?.split(" ")[0] || "",
                          session.counselor_name?.split(" ")[1] || ""
                        )
                      }
                      alt="Counselor"
                      width={40}
                      height={40}
                      className="rounded-full object-cover w-10 h-10 flex-shrink-0"
                    />
                    <div>
                      <div className="font-medium">
                        {session.counselor_name || ""}
                      </div>
                      <div className="text-sm text-gray-500">Counselor</div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
        </div>
      ) : loading ? (
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <Calendar className="w-12 h-12 text-gray-400 mb-3" />
          <p className="text-gray-600 font-medium">Loading...</p>
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <Calendar className="w-12 h-12 text-gray-400 mb-3" />
          <p className="text-gray-600 font-medium">No upcoming sessions</p>
          <p className="text-gray-500 text-sm">
            Book a session with one of our counselors
          </p>
        </div>
      )}
    </div>
  );
};
