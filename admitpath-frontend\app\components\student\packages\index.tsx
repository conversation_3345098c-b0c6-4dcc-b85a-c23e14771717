"use client";

import { useEffect, useState } from "react";
import usePackages, { StudentPackage } from "@/app/hooks/student/usePackages";
import { PackageCard } from "./package-card";
import { PackageDialog } from "./package-dialog";
import PackagesSkeleton from "./skeleton";
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from "@/app/components/ui/tabs";
import { Clock, CheckCircle } from "lucide-react";
import { Button } from "@/app/components/ui/button";
import { useRouter } from "next/navigation";
import HeadWrapper from "../head-wrapper";

export default function Packages() {
  const { activePackages, completedPackages, loading, fetchPackages } =
    usePackages();
  const [selectedPackage, setSelectedPackage] = useState<StudentPackage | null>(
    null
  );
  const [activeTab, setActiveTab] = useState("active");
  const router = useRouter();

  // Fetch packages based on the active tab
  useEffect(() => {
    fetchPackages(activeTab);
  }, [fetchPackages, activeTab]);

  // Initial fetch of active packages
  useEffect(() => {
    if (activePackages.length === 0) {
      fetchPackages("active");
    }
  }, [fetchPackages, activePackages.length]);

  return (
    <>
      <HeadWrapper title="My Packages" />
      <div className="sm:p-6 p-3 bg-white rounded-2xl flex-1">
        <h1 className="text-2xl font-semibold mb-6">My Packages</h1>

        <Tabs
          defaultValue="active"
          className="w-full"
          onValueChange={(value) => setActiveTab(value)}
        >
          <TabsList className="mb-6">
            <TabsTrigger value="active" className="px-6">
              <Clock className="mr-2 h-4 w-4" />
              Active Packages
            </TabsTrigger>
            <TabsTrigger value="completed" className="px-6">
              <CheckCircle className="mr-2 h-4 w-4" />
              Completed Packages
            </TabsTrigger>
          </TabsList>

          <TabsContent value="active" className="mt-0">
            {loading ? (
              <PackagesSkeleton />
            ) : (
              <>
                {activePackages.length === 0 ? (
                  <div className="text-center py-12 bg-gray-50 rounded-lg">
                    <h3 className="text-lg font-medium text-gray-700 mb-2">
                      No active packages
                    </h3>
                    <p className="text-gray-500 mb-6">
                      Browse counselors to find and purchase packages
                    </p>
                    <Button onClick={() => router.push("/explore")}>
                      Browse Counselors
                    </Button>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {activePackages.map((pkg) => (
                      <PackageCard
                        key={pkg.id}
                        pkg={pkg}
                        onSelect={() => setSelectedPackage(pkg)}
                      />
                    ))}
                  </div>
                )}
              </>
            )}
          </TabsContent>

          <TabsContent value="completed" className="mt-0">
            {loading ? (
              <PackagesSkeleton />
            ) : (
              <>
                {completedPackages.length === 0 ? (
                  <div className="text-center py-12 bg-gray-50 rounded-lg">
                    <h3 className="text-lg font-medium text-gray-700 mb-2">
                      No completed packages
                    </h3>
                    <p className="text-gray-500">
                      Your completed packages will appear here
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {completedPackages.map((pkg) => (
                      <PackageCard
                        key={pkg.id}
                        pkg={pkg}
                        onSelect={() => setSelectedPackage(pkg)}
                      />
                    ))}
                  </div>
                )}
              </>
            )}
          </TabsContent>
        </Tabs>

        {selectedPackage && (
          <PackageDialog
            pkg={selectedPackage}
            onClose={() => setSelectedPackage(null)}
          />
        )}
      </div>
    </>
  );
}
