import Image from "next/image";
import { ArrowRight, Calendar, Clock } from "lucide-react";
import { StudentPackage } from "@/app/hooks/student/usePackages";
import { generatePlaceholder } from "@/app/utils/image";

// Define the props interface
interface PackageCardProps {
  pkg: StudentPackage;
  onSelect: (pkg: StudentPackage) => void;
}

export const PackageCard: React.FC<PackageCardProps> = ({ pkg, onSelect }) => {
  // Calculate overall progress
  const calculateProgress = () => {
    let totalHours = 0;
    let usedHours = 0;

    Object.values(pkg.service_hours).forEach((service) => {
      totalHours += service.total;
      usedHours += service.used;
    });

    return {
      totalHours,
      usedHours,
      percentage:
        totalHours > 0 ? Math.round((usedHours / totalHours) * 100) : 0,
    };
  };

  const progress = calculateProgress();
  const daysLeft =
    pkg.end_date && pkg.status === "active" && pkg.end_date !== null
      ? Math.max(
          0,
          Math.ceil(
            (new Date(pkg.end_date).getTime() - new Date().getTime()) /
              (1000 * 60 * 60 * 24)
          )
        )
      : null;

  return (
    <div
      className="bg-white rounded-xl p-5 space-y-4 border hover:shadow-md cursor-pointer transition-all duration-200"
      onClick={() => onSelect(pkg)}
    >
      <div className="flex justify-between items-start">
        <div>
          <h3 className="text-lg font-semibold text-gray-800 line-clamp-1">
            {pkg.package.title}
          </h3>
          <p className="text-sm text-gray-500 flex items-center gap-1 mt-1">
            <Calendar className="h-3 w-3" />
            Started: {new Date(pkg.start_date).toLocaleDateString()}
          </p>
        </div>
        <div className="text-right">
          <span
            className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
              pkg.status === "active"
                ? "bg-green-100 text-green-800"
                : pkg.status === "pending"
                ? "bg-yellow-100 text-yellow-800"
                : pkg.status === "completed"
                ? "bg-blue-100 text-blue-800"
                : pkg.status === "cancelled"
                ? "bg-red-100 text-red-800"
                : "bg-gray-100 text-gray-800"
            }`}
          >
            {pkg.status.charAt(0).toUpperCase() + pkg.status.slice(1)}
          </span>
        </div>
      </div>

      <div className="flex items-center gap-2 pt-2">
        <Image
          src={
            pkg.package.counselor_profile_picture ||
            generatePlaceholder(
              pkg.package.counselor_name.split(" ")[0],
              pkg.package.counselor_name.split(" ")[1]
            )
          }
          alt={pkg.package.counselor_name}
          width={40}
          height={40}
          className="rounded-full"
        />
        <div>
          <span className="font-medium text-gray-800">
            {pkg.package.counselor_name}
          </span>
          {daysLeft !== null && (
            <p className="text-sm text-gray-500 flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {daysLeft} days left
            </p>
          )}
        </div>
      </div>

      <div className="space-y-1">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Progress</span>
          <span className="font-medium">
            {progress.usedHours}/{progress.totalHours} hours used
          </span>
        </div>
        <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
          <div
            className="h-full bg-blue-500 rounded-full"
            style={{ width: `${progress.percentage}%` }}
          ></div>
        </div>
      </div>

      <div className="pt-2 flex justify-between items-center">
        <div className="text-sm text-gray-500">
          <span className="font-medium text-gray-700">
            {pkg.sessions.length}
          </span>{" "}
          sessions
        </div>
        <button className="flex items-center text-blue-600 font-medium text-sm">
          View Details
          <ArrowRight className="w-4 h-4 ml-1" />
        </button>
      </div>
    </div>
  );
};
