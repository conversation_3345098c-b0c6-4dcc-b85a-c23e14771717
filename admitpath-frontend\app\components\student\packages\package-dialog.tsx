import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  DialogDescription,
  DialogHeader,
  DialogFooter,
} from "@/app/components/ui/dialog";
import { Button } from "@/app/components/ui/button";
import { StudentPackage } from "@/app/hooks/student/usePackages";
import { PackageSubscription } from "@/app/types/student/package";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/app/components/ui/tabs";
import { Calendar, Clock, CheckCircle, List } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import { generatePlaceholder } from "@/app/utils/image";
import SessionsListDialog from "./SessionsListDialog";

interface PackageDialogProps {
  pkg: StudentPackage;
  onClose: () => void;
}

export function PackageDialog({ pkg, onClose }: PackageDialogProps) {
  const [sessionsDialogOpen, setSessionsDialogOpen] = useState(false);

  // Calculate days remaining
  const daysLeft =
    pkg.end_date && pkg.status === "active" && pkg.end_date !== null
      ? Math.max(
          0,
          Math.ceil(
            (new Date(pkg.end_date).getTime() - new Date().getTime()) /
              (1000 * 60 * 60 * 24)
          )
        )
      : null;

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Format time
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <Dialog open={!!pkg} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl">{pkg.package.title}</DialogTitle>
            <span
              className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${
                pkg.status === "active"
                  ? "bg-green-100 text-green-800"
                  : pkg.status === "pending"
                  ? "bg-yellow-100 text-yellow-800"
                  : pkg.status === "completed"
                  ? "bg-blue-100 text-blue-800"
                  : pkg.status === "cancelled"
                  ? "bg-red-100 text-red-800"
                  : "bg-gray-100 text-gray-800"
              }`}
            >
              {pkg.status.charAt(0).toUpperCase() + pkg.status.slice(1)}
            </span>
          </div>
          <DialogDescription className="text-base mt-2">
            {pkg.package.description}
          </DialogDescription>
        </DialogHeader>

        <div className="flex items-center gap-4 py-4 border-y my-4">
          <Image
            src={
              pkg.package.counselor_profile_picture ||
              generatePlaceholder(
                pkg.package.counselor_name.split(" ")[0],
                pkg.package.counselor_name.split(" ")[1]
              )
            }
            alt={pkg.package.counselor_name}
            width={48}
            height={48}
            className="rounded-full"
          />
          <div>
            <h3 className="font-medium">{pkg.package.counselor_name}</h3>
            <p className="text-sm text-gray-500">Your counselor</p>
          </div>
        </div>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="services">Services & Hours</TabsTrigger>
            <TabsTrigger value="sessions">Sessions</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="text-sm text-gray-500 mb-1">Start Date</h4>
                <p className="font-medium flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-blue-500" />
                  {formatDate(pkg.start_date)}
                </p>
              </div>
              {pkg.end_date !== null && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="text-sm text-gray-500 mb-1">End Date</h4>
                  <p className="font-medium flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-blue-500" />
                    {formatDate(pkg.end_date)}
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="services" className="space-y-4">
            <div className="space-y-4">
              <h3 className="font-medium">Service Hours</h3>
              <div className="grid gap-3">
                {Object.entries(pkg.service_hours).map(([service, hours]) => (
                  <div key={service} className="border rounded-lg p-4">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-medium">{service}</h4>
                      <span className="text-sm font-medium">
                        {hours.used}/{hours.total} hours used
                      </span>
                    </div>
                    <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                      <div
                        className="h-full bg-blue-500 rounded-full"
                        style={{
                          width: `${(hours.used / hours.total) * 100}%`,
                        }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="sessions" className="space-y-4">
            <div className="flex justify-between items-center mb-4">
              <h3 className="font-medium">Your Sessions</h3>
            </div>

            {pkg.sessions.length === 0 ? (
              <div className="text-center py-8 bg-gray-50 rounded-lg">
                <h4 className="text-gray-700 mb-2">
                  No sessions scheduled yet
                </h4>
                <p className="text-gray-500 mb-4">
                  Your counselor will schedule sessions for you. Please check
                  back later or contact your counselor.
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {pkg.sessions.map((session) => (
                  <div key={session.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">{session.service_name}</h4>
                        <p className="text-sm text-gray-500">
                          {formatDate(session.date)} •{" "}
                          {formatTime(session.start_time)} -{" "}
                          {formatTime(session.end_time)}
                        </p>
                      </div>
                      <span
                        className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                          session.status === "completed"
                            ? "bg-green-100 text-green-800"
                            : session.status === "upcoming"
                            ? "bg-blue-100 text-blue-800"
                            : session.status === "cancelled"
                            ? "bg-red-100 text-red-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {session.status}
                      </span>
                    </div>
                  </div>
                ))}

                {pkg.sessions.length > 3 && (
                  <div className="flex justify-center mt-4">
                    <Button
                      variant="outline"
                      className="flex items-center gap-2"
                      onClick={() => setSessionsDialogOpen(true)}
                    >
                      <List className="w-4 h-4" />
                      View All Sessions
                    </Button>
                  </div>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>

      <SessionsListDialog
        isOpen={sessionsDialogOpen}
        onClose={() => setSessionsDialogOpen(false)}
        subscription={pkg as unknown as PackageSubscription}
      />
    </Dialog>
  );
}
