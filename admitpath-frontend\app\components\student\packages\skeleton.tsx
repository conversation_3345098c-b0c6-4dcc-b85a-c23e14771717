import { Skeleton } from "@components/ui/skeleton";

export default function PackagesSkeleton() {
  return (
    <div className="w-full space-y-4">
      <Skeleton className="h-8 w-32" /> {/* My Packages title */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3].map((i) => (
          <div key={i} className="rounded-lg border bg-card p-6 shadow-sm">
            <div className="flex items-center justify-between mb-4">
              <Skeleton className="h-6 w-24" /> {/* Package 1 */}
              <Skeleton className="h-6 w-16" /> {/* $15 */}
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-32" /> {/* Start: Jan 20 2024 */}
                <Skeleton className="h-4 w-24" /> {/* Amount paid */}
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-20" /> {/* Instructor */}
                <div className="flex items-center gap-2">
                  <Skeleton className="h-8 w-8 rounded-full" /> {/* Avatar */}
                  <Skeleton className="h-4 w-32" /> {/* Counsellor name */}
                </div>
              </div>
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" /> {/* Sessions progress */}
                <Skeleton className="h-2 w-full rounded-full" />{" "}
                {/* Progress bar */}
                <div className="flex justify-end">
                  <Skeleton className="h-4 w-12" /> {/* 4/10 */}
                </div>
              </div>
              <Skeleton className="h-10 w-full rounded-md" />{" "}
              {/* View Details button */}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
