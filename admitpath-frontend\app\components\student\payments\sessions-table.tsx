"use client";

import { useEffect } from "react";
import Link from "next/link";
import { usePayments } from "@/app/hooks/student/usePayments";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import { Button } from "@/app/components/ui/button";
import { DollarSign, ChevronLeft, ChevronRight } from "lucide-react";
import { PaymentsTableSkeleton } from "./skeleton";
import { format } from "date-fns";

export function SessionsTable() {
  const {
    sessions,
    loading,
    filters,
    pagination,
    updateFilters,
    fetchSessions,
  } = usePayments();

  useEffect(() => {
    fetchSessions();
  }, [fetchSessions]);

  if (loading) {
    return <PaymentsTableSkeleton />;
  }

  return (
    <div className="bg-white rounded-xl p-6">
      <div className="flex lg:flex-row flex-col lg:items-center lg:justify-between gap-y-4 mb-6">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-gray-100 rounded-xl">
            <DollarSign className="w-5 h-5" />
          </div>
          <h2 className="text-xl font-medium">Payment History</h2>
        </div>

        <div className="flex  items-center gap-3">
          <Select
            value={filters.service_type || "all"}
            onValueChange={(value: any) =>
              updateFilters({
                service_type: value === "all" ? undefined : value,
                page: 1,
              })
            }
          >
            <SelectTrigger className="">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="session">Session</SelectItem>
              <SelectItem value="package">Package</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Counselor</TableHead>
            <TableHead>Service</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Amount</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {sessions.length === 0 ? (
            <TableRow>
              <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                No payment records found
              </TableCell>
            </TableRow>
          ) : (
            sessions.map((session) => (
              <TableRow key={session.id}>
                <TableCell>
                  <Link
                    href={`/profile/${session.counselor_id}`}
                    className="text-blue-600 hover:text-blue-700 font-medium transition-colors hover:underline"
                  >
                    {session.counselor_name}
                  </Link>
                </TableCell>
                <TableCell>{session.service_name}</TableCell>
                <TableCell>
                  {format(new Date(session.payment_date), "MMM d, yyyy")}
                </TableCell>
                <TableCell>
                  <span
                    className={`px-2 py-1 rounded-full text-sm capitalize ${
                      session.status === "completed"
                        ? "bg-green-100 text-green-800"
                        : session.status === "pending"
                        ? "bg-yellow-100 text-yellow-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {session.status}
                  </span>
                </TableCell>
                <TableCell>${session.amount}</TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>

      {sessions.length > 0 && (
        <div className="mt-6 flex items-center justify-between">
          <p className="text-sm text-gray-500">
            Showing {(pagination.page - 1) * pagination.size + 1} to{" "}
            {Math.min(pagination.page * pagination.size, pagination.total)} of{" "}
            {pagination.total} results
          </p>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => updateFilters({ page: pagination.page - 1 })}
              disabled={pagination.page === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => updateFilters({ page: pagination.page + 1 })}
              disabled={!pagination.hasMore}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
