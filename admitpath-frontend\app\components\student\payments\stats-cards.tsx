"use client";

import { useEffect } from "react";
import { usePayments } from "@/app/hooks/student/usePayments";
import { Target, Calendar, Clock } from "lucide-react";
import { StatsCardsSkeleton } from "./skeleton";

export function StatsCards() {
    const { stats, loading, fetchStats } = usePayments();

    useEffect(() => {
        fetchStats();
    }, [fetchStats]);

    if (loading || !stats) {
        return <StatsCardsSkeleton />;
    }

    return (
        <div className="grid md:grid-cols-3 gap-4 lg:gap-6">
            {/* <div className="bg-white rounded-2xl p-6 flex items-center justify-between">
        <div>
          <p className="text-gray-500 mb-1">Total Amount Spent</p>
          <p className="text-2xl font-semibold">${stats.total_amount_spent}</p>
        </div>
        <div className="p-4 bg-gray-100 rounded-xl">
          <Target className="w-6 h-6 text-gray-700" />
        </div>
      </div> */}

            <div className="bg-white rounded-2xl p-6 flex items-center justify-between">
                <div>
                    <p className="text-gray-500 mb-1">Total Sessions</p>
                    <p className="text-2xl font-semibold">
                        {stats.total_sessions}
                    </p>
                </div>
                <div className="p-4 bg-gray-100 rounded-xl">
                    <Calendar className="w-6 h-6 text-gray-700" />
                </div>
            </div>

            <div className="bg-white rounded-2xl p-6 flex items-center justify-between">
                <div>
                    <p className="text-gray-500 mb-1">Total Hours Spent</p>
                    <p className="text-2xl font-semibold">
                        {stats.total_hours_spent}
                    </p>
                </div>
                <div className="p-4 bg-gray-100 rounded-xl">
                    <Clock className="w-6 h-6 text-gray-700" />
                </div>
            </div>
        </div>
    );
}
