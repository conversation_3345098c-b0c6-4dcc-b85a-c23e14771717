"use client";

import { useRef, useEffect, useState } from "react";
import { useProfile } from "@/app/hooks/student/useProfile";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { toast } from "react-toastify";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import { Plus } from "lucide-react";
import { EducationInfo } from "@/app/types/student/profile";
import { Label } from "@/app/components/ui/label";
import { Card, CardContent } from "@/app/components/ui/card";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/app/components/ui/avatar";

interface EducationalInfoFormProps {
  onSuccess: () => void;
}

export function EducationalInfoForm({ onSuccess }: EducationalInfoFormProps) {
  const {
    userInfo,
    educationInfo,
    loading,
    fetchEducationalBackground,
    submitEducationInfo,
    deleteEducationInfo,
  } = useProfile();

  const [formData, setFormData] = useState<EducationInfo[]>([]);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    fetchEducationalBackground();
  }, [fetchEducationalBackground]);

  useEffect(() => {
    if (educationInfo) {
      setFormData(
        educationInfo.map((edu) => ({
          ...edu,
          institution_type: edu.institution_type || "school",
        }))
      );
    } else {
      setFormData([
        {
          current_education_level: "",
          institution_name: "",
          institution_type: "school",
          start_date: "",
          end_date: "",
          is_current: false,
        },
      ]);
    }
  }, [educationInfo]);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};
    let isValid = true;

    formData.forEach((education, index) => {
      // Validate education level
      if (!education.current_education_level) {
        newErrors[`education_level_${index}`] = "Education level is required";
        isValid = false;
      }

      // Validate institution name
      if (!education.institution_name.trim()) {
        newErrors[`institution_name_${index}`] = "Institution name is required";
        isValid = false;
      }

      // Validate start date
      if (!education.start_date) {
        newErrors[`start_date_${index}`] = "Start date is required";
        isValid = false;
      }

      // Validate end date
      if (!education.is_current && !education.end_date) {
        newErrors[`end_date_${index}`] = "End date is required";
        isValid = false;
      }

      // Validate date logic
      if (education.start_date && education.end_date && !education.is_current) {
        const startDate = new Date(education.start_date);
        const endDate = new Date(education.end_date);
        if (endDate < startDate) {
          newErrors[`end_date_${index}`] =
            "End date cannot be before start date";
          isValid = false;
        }
      }
    });

    setErrors(newErrors);
    if (!isValid) {
      toast.error("Please fill in all required fields correctly");
    }
    return isValid;
  };

  const handleInputChange = (
    index: number,
    field: keyof EducationInfo,
    value: any
  ) => {
    setFormData((prev) =>
      prev.map((item, i) =>
        i === index
          ? {
              ...item,
              [field]:
                field === "start_date" || field === "end_date"
                  ? new Date(value).toISOString()
                  : value,
            }
          : item
      )
    );

    if (errors[`${field}_${index}`]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[`${field}_${index}`];
        return newErrors;
      });
    }
  };

  const handleAddMore = () => {
    setFormData((prev) => [
      ...prev,
      {
        current_education_level: "",
        institution_name: "",
        institution_type: "school",
        start_date: "",
        end_date: "",
        is_current: false,
      },
    ]);
  };

  const handleDelete = async (index: number) => {
    const education = formData[index];
    if (education.id) {
      await deleteEducationInfo(education.id);
    }
    setFormData((prev) => prev.filter((_, i) => i !== index));

    setErrors((prev) => {
      const newErrors = { ...prev };
      Object.keys(newErrors).forEach((key) => {
        if (key.includes(`_${index}`)) {
          delete newErrors[key];
        }
      });
      return newErrors;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const validEducationData = formData.map((edu) => ({
        ...edu,
        end_date: edu.is_current ? undefined : edu.end_date,
      }));

      await submitEducationInfo(validEducationData);
      toast.success("Educational information updated successfully");
      onSuccess();
    } catch (error) {
      console.error("Failed to submit education info:", error);
      toast.error("Failed to update educational information");
    }
  };

  if (!userInfo) return null;

  return (
    <Card>
      <CardContent className="p-2.5 sm:p-6">
        <div className="flex items-center gap-4 mb-8">
          <Avatar className="w-12 h-12">
            <AvatarImage
              src={userInfo?.profile_picture_url || "/placeholder.svg"}
            />
            <AvatarFallback>{`${userInfo?.firstName?.[0]}${userInfo?.lastName?.[0]}`}</AvatarFallback>
          </Avatar>
          <div>
            <h2 className="text-xl font-semibold">
              {userInfo?.firstName} {userInfo?.lastName}
            </h2>
            <p className="text-sm text-muted-foreground">
              Edit Profile / Educational Information
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="grid gap-6">
          {formData.map((education, index) => (
            <div key={index} className="space-y-6 pb-6 border-b last:border-0">
              <div className="flex justify-between items-start">
                <div className="space-y-2 flex-1">
                  <Label htmlFor={`education-${index}`}>
                    Current Education Level{" "}
                    <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={education.current_education_level}
                    onValueChange={(value) =>
                      handleInputChange(index, "current_education_level", value)
                    }
                  >
                    <SelectTrigger
                      className={`bg-white cursor-pointer hover:bg-gray-50 text-gray-900 ${
                        errors[`education_level_${index}`]
                          ? "border-red-500"
                          : ""
                      }`}
                    >
                      <SelectValue placeholder="Select education level" />
                    </SelectTrigger>
                    <SelectContent className="bg-white">
                      <SelectItem
                        className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                        value="high_school"
                      >
                        High School
                      </SelectItem>
                      <SelectItem
                        className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                        value="bachelors"
                      >
                        Bachelor's Degree
                      </SelectItem>
                      <SelectItem
                        className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                        value="masters"
                      >
                        Master's Degree
                      </SelectItem>
                      <SelectItem
                        className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                        value="phd"
                      >
                        PhD
                      </SelectItem>
                      <SelectItem
                        className="cursor-pointer hover:bg-gray-100 transition-all rounded-lg"
                        value="other"
                      >
                        Other
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  {errors[`education_level_${index}`] && (
                    <p className="text-sm text-red-500">
                      {errors[`education_level_${index}`]}
                    </p>
                  )}
                </div>
                {formData.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    className="text-red-500 hover:text-red-600 hover:bg-red-50"
                    onClick={() => handleDelete(index)}
                  >
                    Delete
                  </Button>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor={`institution-${index}`}>
                  Institution Name <span className="text-red-500">*</span>
                </Label>
                <Input
                  id={`institution-${index}`}
                  value={education.institution_name}
                  onChange={(e) =>
                    handleInputChange(index, "institution_name", e.target.value)
                  }
                  placeholder="Enter institution name"
                  className={
                    errors[`institution_name_${index}`] ? "border-red-500" : ""
                  }
                />
                {errors[`institution_name_${index}`] && (
                  <p className="text-sm text-red-500">
                    {errors[`institution_name_${index}`]}
                  </p>
                )}
              </div>

              <div className="grid sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor={`start-date-${index}`}>
                    Start Date <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    type="date"
                    value={education.start_date.split("T")[0]}
                    onChange={(e) =>
                      handleInputChange(index, "start_date", e.target.value)
                    }
                    className={
                      errors[`start_date_${index}`] ? "border-red-500" : ""
                    }
                    max={new Date().toISOString().split("T")[0]}
                  />
                  {errors[`start_date_${index}`] && (
                    <p className="text-sm text-red-500">
                      {errors[`start_date_${index}`]}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor={`end-date-${index}`}>
                    End Date{" "}
                    {!education.is_current && (
                      <span className="text-red-500">*</span>
                    )}
                  </Label>
                  <Input
                    type="date"
                    value={education.end_date?.split("T")[0] || ""}
                    onChange={(e) =>
                      handleInputChange(index, "end_date", e.target.value)
                    }
                    disabled={education.is_current}
                    className={
                      errors[`end_date_${index}`] ? "border-red-500" : ""
                    }
                    min={education.start_date.split("T")[0]}
                    max={new Date().toISOString().split("T")[0]}
                  />
                  {errors[`end_date_${index}`] && (
                    <p className="text-sm text-red-500">
                      {errors[`end_date_${index}`]}
                    </p>
                  )}
                </div>
              </div>

              <label className="flex items-center gap-2 mt-2">
                <input
                  type="checkbox"
                  checked={education.is_current}
                  onChange={(e) =>
                    handleInputChange(index, "is_current", e.target.checked)
                  }
                  className="rounded border-gray-300"
                />
                <span className="text-sm">Currently studying here</span>
              </label>
            </div>
          ))}

          <Button
            type="button"
            variant="outline"
            className="w-fit"
            onClick={handleAddMore}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add more education
          </Button>

          <div className="flex justify-end gap-4 mt-6">
            <Button type="button" variant="ghost" onClick={onSuccess}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "Saving..." : "Save changes"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
