"use client";

import { useState } from "react";
import { PersonalInfoForm } from "./edit-personal-info";
import { EducationalInfoForm } from "./edit-educational-info";
import { cn } from "@components/lib/utils";
import { X } from "lucide-react";

type Tab = "personal" | "educational";

interface ProfileEditorProps {
  initialTab?: Tab;
  onClose: () => void;
}

export function ProfileEditor({
  initialTab = "personal",
  onClose,
}: ProfileEditorProps) {
  const [activeTab, setActiveTab] = useState<Tab>(initialTab);

  return (
    <div className="container flex flex-col gap-y-4 sm:gap-y-8 mx-auto">
      <div className="flex justify-between items-start ">
        <h1 className="text-2xl sm:text-3xl font-semibold">Edit Profile</h1>
        {onClose && (
          <button
            onClick={onClose}
            className=" text-gray-500 hover:text-gray-700"
          >
            <X className="h-5 w-5" />
          </button>
        )}
      </div>
      <div className="flex lg:flex-row flex-col gap-6 ">
        <div className="space-y-2 ">
          <div
            onClick={() => setActiveTab("personal")}
            className={cn(
              "sm:text-[1rem] text-sm cursor-pointer transition-colors",
              activeTab === "personal"
                ? "font-medium text-primary"
                : "text-gray-600/70 hover:text-primary"
            )}
          >
            Personal information
          </div>
          <div
            onClick={() => setActiveTab("educational")}
            className={cn(
              "sm:text-[1rem] text-sm cursor-pointer transition-colors",
              activeTab === "educational"
                ? "font-medium text-primary"
                : "text-gray-600/70 hover:text-primary"
            )}
          >
            Educational background
          </div>
        </div>

        <div className="bg-white rounded-xl flex-1">
          {activeTab === "personal" ? (
            <PersonalInfoForm onSuccess={onClose} />
          ) : (
            <EducationalInfoForm onSuccess={onClose} />
          )}
        </div>
      </div>
    </div>
  );
}
