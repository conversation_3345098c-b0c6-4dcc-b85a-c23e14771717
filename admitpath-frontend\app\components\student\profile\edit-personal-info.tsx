"use client";

import { useProfile } from "@/app/hooks/student/useProfile";
import { useProfilePicture } from "@/app/hooks/student/useProfilePicture";
import { Avatar, AvatarFallback, AvatarImage } from "@components/ui/avatar";
import { Button } from "@components/ui/button";
import { Card, CardContent } from "@components/ui/card";
import { Input } from "@components/ui/input";
import { Label } from "@components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@components/ui/select";
import { Textarea } from "@components/ui/textarea";
import { useState, useEffect } from "react";
import { countries } from "@/lib/countries";
import { generatePlaceholder } from "@/app/utils/image";

interface PersonalInfoFormProps {
  onSuccess: () => void;
}

export function PersonalInfoForm({ onSuccess }: PersonalInfoFormProps) {
  const { userInfo, personalInfo, submitPersonalInfo } = useProfile();
  const {
    uploadProfilePicture,
    deleteProfilePicture,
    loading: pictureLoading,
  } = useProfilePicture();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState(
    personalInfo || {
      first_name: userInfo?.firstName || "",
      last_name: userInfo?.lastName || "",
      nationality: "",
      country_of_residence: "",
      gender: "",
      date_of_birth: null,
      goals: "",
    }
  );

  const [dateInputs, setDateInputs] = useState({
    day: "",
    month: "",
    year: "",
  });

  useEffect(() => {
    if (personalInfo) {
      setFormData(personalInfo);
      if (personalInfo.date_of_birth) {
        const date = new Date(personalInfo.date_of_birth);
        setDateInputs({
          day: date.getDate().toString().padStart(2, "0"),
          month: (date.getMonth() + 1).toString().padStart(2, "0"),
          year: date.getFullYear().toString(),
        });
      }
    }
  }, [personalInfo]);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const validateAndUpdateDate = (updatedDateInputs: typeof dateInputs) => {
    const { day, month, year } = updatedDateInputs;
    
    // Only proceed if all fields have values
    if (!day || !month || !year) {
      setFormData((prev) => ({ ...prev, date_of_birth: null }));
      return;
    }

    const dayNum = parseInt(day);
    const monthNum = parseInt(month);
    const yearNum = parseInt(year);

    // Basic validation
    if (dayNum < 1 || dayNum > 31 || monthNum < 1 || monthNum > 12 || yearNum < 1900 || yearNum > new Date().getFullYear()) {
      setFormData((prev) => ({ ...prev, date_of_birth: null }));
      return;
    }

    // Create date and validate it's a real date
    const date = new Date(yearNum, monthNum - 1, dayNum);
    
    // Check if the date is valid (handles cases like Feb 30th)
    if (
      date.getFullYear() === yearNum &&
      date.getMonth() === monthNum - 1 &&
      date.getDate() === dayNum
    ) {
      const dateStr = `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
      setFormData((prev) => ({ ...prev, date_of_birth: dateStr }));
    } else {
      setFormData((prev) => ({ ...prev, date_of_birth: null }));
    }
  };

  const handleDateChange = (
    e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>
  ) => {
    const { name, value } = e.target;
    let newValue = value;

    // Validation for input constraints
    if (name === "day") {
      const day = parseInt(value);
      if (day < 1) newValue = "1";
      if (day > 31) newValue = "31";
    }
    if (name === "year") {
      const year = parseInt(value);
      const currentYear = new Date().getFullYear();
      if (year > currentYear) newValue = currentYear.toString();
      if (year < 1900) newValue = "1900";
    }

    const updatedDateInputs = { ...dateInputs, [name]: newValue };
    setDateInputs(updatedDateInputs);
    
    // Validate and update the form data immediately
    validateAndUpdateDate(updatedDateInputs);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      // Format the date properly for the API
      const formattedData = {
        ...formData,
        date_of_birth: formData.date_of_birth
          ? new Date(formData.date_of_birth).toISOString()
          : null,
      };
      await submitPersonalInfo(formattedData);
      onSuccess();
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardContent className="pt-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="flex items-center gap-6">
            <Avatar className="h-24 w-24">
              <AvatarImage
                src={
                  userInfo?.profile_picture_url ||
                  generatePlaceholder(userInfo?.firstName, userInfo?.lastName)
                }
              />
              <AvatarFallback>
                {userInfo?.firstName?.[0]}
                {userInfo?.lastName?.[0]}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-2">
              <input
                type="file"
                id="profile-picture"
                accept="image/*"
                className="hidden"
                onChange={async (e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    try {
                      await uploadProfilePicture(file);
                    } catch (error) {
                      // Error is handled by the hook
                    }
                  }
                }}
              />
              <div className="flex lg:flex-row flex-col gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() =>
                    document.getElementById("profile-picture")?.click()
                  }
                  disabled={pictureLoading}
                >
                  {pictureLoading ? "Uploading..." : "Upload Picture"}
                </Button>
                {userInfo?.profile_picture_url && (
                  <Button
                    type="button"
                    variant="destructive"
                    onClick={async () => {
                      try {
                        await deleteProfilePicture();
                      } catch (error) {
                        // Error is handled by the hook
                      }
                    }}
                    disabled={pictureLoading}
                  >
                    Remove Picture
                  </Button>
                )}
              </div>
              {!userInfo?.profile_picture_url && (
                <p className="text-sm text-gray-500">
                  Recommended: Square image, at least 400x400 pixels
                </p>
              )}
            </div>
          </div>

          <div className="grid sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                name="first_name"
                value={formData.first_name}
                onChange={handleInputChange}
                className="bg-white"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                name="last_name"
                value={formData.last_name}
                onChange={handleInputChange}
                className="bg-white"
              />
            </div>
          </div>

          <div className="grid sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="nationality">Nationality</Label>
              <Select
                value={formData.nationality}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, nationality: value }))
                }
              >
                <SelectTrigger className="bg-white cursor-pointer hover:bg-gray-50 text-gray-900">
                  <SelectValue
                    placeholder="Select nationality"
                    className="text-gray-900"
                  >
                    {countries.find((c) => c.code === formData.nationality)
                      ?.name || "Select nationality"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent className="bg-white">
                  {countries.map((country) => (
                    <SelectItem
                      key={country.code}
                      value={country.code}
                      className="cursor-pointer hover:bg-gray-100 text-gray-900 transition-all rounded-lg"
                    >
                      {country.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="residence">Country of residence</Label>
              <Select
                value={formData.country_of_residence}
                onValueChange={(value) =>
                  setFormData((prev) => ({
                    ...prev,
                    country_of_residence: value,
                  }))
                }
              >
                <SelectTrigger className="bg-white cursor-pointer hover:bg-gray-50 text-gray-900">
                  <SelectValue
                    placeholder="Select country"
                    className="text-gray-900"
                  >
                    {countries.find(
                      (c) => c.code === formData.country_of_residence
                    )?.name || "Select country"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent className="bg-white">
                  {countries.map((country) => (
                    <SelectItem
                      key={country.code}
                      value={country.code}
                      className="cursor-pointer hover:bg-gray-100 text-gray-900 transition-all rounded-lg"
                    >
                      {country.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="gender">Gender</Label>
              <Select
                value={formData.gender}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, gender: value }))
                }
              >
                <SelectTrigger className="bg-white cursor-pointer hover:bg-gray-50 text-gray-900">
                  <SelectValue
                    placeholder="Select gender"
                    className="text-gray-900"
                  >
                    {formData.gender === "male"
                      ? "Male"
                      : formData.gender === "female"
                      ? "Female"
                      : formData.gender === "other"
                      ? "Other"
                      : "Select gender"}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent className="bg-white">
                  <SelectItem
                    value="male"
                    className="cursor-pointer hover:bg-gray-100 text-gray-900 transition-all rounded-lg"
                  >
                    Male
                  </SelectItem>
                  <SelectItem
                    value="female"
                    className="cursor-pointer hover:bg-gray-100 text-gray-900 transition-all rounded-lg"
                  >
                    Female
                  </SelectItem>
                  <SelectItem
                    value="other"
                    className="cursor-pointer hover:bg-gray-100 text-gray-900 transition-all rounded-lg"
                  >
                    Other
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="date_of_birth">
                Date of Birth <span className="text-red-500">*</span>
              </Label>
              <div className="flex space-x-2">
                <Input
                  type="number"
                  name="day"
                  placeholder="DD"
                  min="1"
                  max="31"
                  value={dateInputs.day}
                  onChange={handleDateChange}
                  className="w-20 bg-white"
                />
                <select
                  name="month"
                  value={dateInputs.month}
                  onChange={handleDateChange}
                  className="flex-1 bg-white border border-input rounded-md px-3 py-2 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                >
                  <option value="">Select Month</option>
                  {Array.from({ length: 12 }, (_, i) => {
                    const month = (i + 1).toString().padStart(2, "0");
                    return (
                      <option key={month} value={month}>
                        {new Date(2000, i).toLocaleString("default", {
                          month: "long",
                        })}
                      </option>
                    );
                  })}
                </select>
                <Input
                  type="number"
                  name="year"
                  placeholder="YYYY"
                  min="1900"
                  max={new Date().getFullYear()}
                  value={dateInputs.year}
                  onChange={handleDateChange}
                  className="w-24 bg-white"
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="goal">Goal</Label>
            <Textarea
              value={formData.goals || ""}
              name="goals"
              onChange={handleInputChange}
              className="min-h-[100px] bg-white"
              placeholder="Share your academic and career goals..."
              maxLength={400}
            />
            <div className="text-xs text-right text-muted-foreground">
              {formData.goals?.length || 0}/400
            </div>
          </div>

          <div className="flex justify-end gap-4 mt-6">
            <Button type="button" variant="ghost">
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? "Saving..." : "Save changes"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}