import { EducationInfo, humanizeValue } from "@/app/types/student/profile";
import { format } from "date-fns";
import { Pencil } from "lucide-react";
import { EducationSkeleton } from "./skeleton";

export const EducationBlock = ({
  educationInfo,
  onEditClick,
}: {
  educationInfo: EducationInfo[] | null;
  onEditClick: () => void;
}) => {
  return !educationInfo ? (
    <EducationSkeleton />
  ) : (
    <div className="space-y-3 sm:space-y-4 rounded-xl border p-2.5 sm:p-4 lg:p-6 ">
      <div className="flex justify-between items-center">
        <h2 className="text-base sm:text-xl font-semibold">Education</h2>
        <button 
          onClick={onEditClick}
          className="p-3 text-gray-700 text-base sm:text-lg font-semibold hover:bg-gray-200 rounded-xl bg-gray-100 transition-all whitespace-nowrap"
        >
          <Pencil className="w-5 h-5 sm:w-6 sm:h-6" />
        </button>
      </div>
      {educationInfo.map((education, index) => (
        <div
          key={index}
          className="space-y-1 sm:space-y-2 flex-1 min-w-0 pb-4 sm:pb-6 border-b-2 last:border-b-0"
        >
          <h3 className="font-medium text-base sm:text-lg break-words">
            {education.institution_name}
          </h3>
          <p className="text-gray-600 text-md break-words">
            {humanizeValue(
              education.current_education_level,
              "current_education_level"
            )}
          </p>
          <p className="text-gray-500 text-md ">
            {format(new Date(education.start_date), "PP")} -{" "}
            {education.is_current
              ? "Present"
              : format(new Date(education.end_date!), "PP")}
          </p>
        </div>
      ))}
    </div>
  );
};
