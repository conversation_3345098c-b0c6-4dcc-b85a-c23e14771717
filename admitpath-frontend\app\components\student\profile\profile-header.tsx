import {
  EducationInfo,
  humanizeValue,
  UserInfo,
} from "@/app/types/student/profile";
import Image from "next/image";
import { ProfileHeaderSkeleton } from "./skeleton";
import { generatePlaceholder } from "@/app/utils/image";

export const ProfileHeader = ({
  userInfo,
  educationInfo,
  onEditClick,
}: {
  userInfo: UserInfo | null;
  educationInfo: EducationInfo[] | null;
  onEditClick: () => void;
}) => {
  return !userInfo || !educationInfo ? (
    <ProfileHeaderSkeleton />
  ) : (
    <div className="flex flex-col sm:flex-row items-start justify-between gap-4 sm:gap-6">
      <div className="flex flex-row items-center gap-4 sm:gap-6">
        <div className="relative">
          <Image
            src={
              userInfo.profile_picture_url ||
              generatePlaceholder(userInfo.firstName, userInfo.lastName)
            }
            alt={`${userInfo.firstName} ${userInfo.lastName}`}
            width={140}
            height={140}
            className="rounded-full border-4 w-32 h-32 sm:w-[140px] sm:h-[140px]"
          />
          <div className="absolute bottom-2 right-2 w-6 h-6 sm:w-8 sm:h-8 bg-green-500 rounded-full border-4 border-white" />
        </div>
        <div className="space-y-2 sm:space-y-3 text-left">
          <h1 className="text-xl sm:text-2xl font-semibold">
            {userInfo.firstName} {userInfo.lastName}
          </h1>
          <div className="text-gray-600 text-sm sm:text-base">
            {educationInfo[0]?.institution_name || "No institution added"}
          </div>
        </div>
      </div>
      <button
        onClick={onEditClick}
        className="p-3 text-gray-700 text-base sm:mx-0 mx-auto sm:text-lg font-semibold hover:bg-gray-200 rounded-xl bg-gray-100 transition-all whitespace-nowrap"
      >
        Edit Profile
      </button>
    </div>
  );
};
