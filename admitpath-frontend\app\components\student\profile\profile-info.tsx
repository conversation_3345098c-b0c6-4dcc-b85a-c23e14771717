import {
  humanizeValue,
  PersonalInfo,
  UserInfo,
} from "@/app/types/student/profile";
import { format } from "date-fns";
import { Calendar, Mail, MapPin, UserIcon } from "lucide-react";
import { ProfileInfoGridSkeleton } from "./skeleton";

export const ProfileInfoGrid = ({
  loading,
  userInfo,
  personalInfo,
}: {
  loading: boolean;
  userInfo: UserInfo | null;
  personalInfo: PersonalInfo | null;
}) => {
  const infoItems = [
    {
      icon: <Mail className="w-5 h-5" />,
      label: "Email Address",
      value: userInfo?.email,
    },
    ...(personalInfo
      ? [
          {
            icon: <MapPin className="w-5 h-5" />,
            label: "Located in",
            value: personalInfo.country_of_residence,
          },
          {
            icon: <Calendar className="w-5 h-5" />,
            label: "Date of Birth",
            value: personalInfo.date_of_birth ? format(new Date(personalInfo.date_of_birth), "PP") : "",
          },
          {
            icon: <UserIcon className="w-5 h-5" />,
            label: "Gender",
            value: humanizeValue(personalInfo.gender, "gender"),
          },
        ]
      : []),
  ];

  return loading || !userInfo ? (
    <ProfileInfoGridSkeleton />
  ) : (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 border rounded-xl p-3">
      {infoItems.map((item, index) => (
        <div
          key={index}
          className="flex items-center gap-3 text-gray-600 p-4 rounded-lg"
        >
          <button className="rounded-lg border border-gray-200 bg-gray-50 p-2 my-1 shrink-0">
            {item.icon}
          </button>
          <div className="flex flex-col justify-between min-w-0">
            <p className="text-md font-semibold text-black truncate">
              {item.value}
            </p>
            <p className="text-xs text-gray-500">{item.label}</p>
          </div>
        </div>
      ))}
    </div>
  );
};
