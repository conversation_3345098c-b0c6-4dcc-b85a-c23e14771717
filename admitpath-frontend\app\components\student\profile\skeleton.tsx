"use client";

export const ProfileHeaderSkeleton = () => {
  return (
    <div className="flex flex-col sm:flex-row items-start justify-between gap-4 sm:gap-6">
      <div className="flex flex-row items-center gap-4 sm:gap-6">
        <div className="w-32 h-32 sm:w-[140px] sm:h-[140px] bg-gray-200 rounded-full" />
        <div className="space-y-2 sm:space-y-3 text-left">
          <div className="h-8 w-48 bg-gray-200 rounded-lg" />
          <div className="h-4 w-32 bg-gray-200 rounded-lg" />
        </div>
      </div>
      <div className="h-10 w-32 bg-gray-200 rounded-xl" />
    </div>
  );
};

export const ProfileInfoGridSkeleton = () => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 border rounded-xl p-3">
      {[...Array(4)].map((_, index) => (
        <div
          key={index}
          className="flex items-center gap-3 text-gray-600 p-4 rounded-lg"
        >
          <div className="rounded-lg border border-gray-200 bg-gray-200 p-2 my-1 w-10 h-10" />
          <div className="flex flex-col justify-between min-w-0 space-y-2">
            <div className="h-5 w-24 bg-gray-200 rounded-lg" />
            <div className="h-4 w-20 bg-gray-200 rounded-lg" />
          </div>
        </div>
      ))}
    </div>
  );
};

export const GoalsSkeleton = () => {
  return (
    <div className="space-y-3 rounded-xl border p-2.5 sm:p-4 lg:p-6">
      <div className="h-6 w-20 bg-gray-200 rounded-lg mb-4" />
      <div className="space-y-2">
        <div className="h-4 w-full bg-gray-200 rounded-lg" />
        <div className="h-4 w-3/4 bg-gray-200 rounded-lg" />
      </div>
    </div>
  );
};

export const EducationSkeleton = () => {
  return (
    <div className="space-y-3 rounded-xl border p-2.5 sm:p-4 lg:p-6">
      <div className="flex justify-between items-center mb-4">
        <div className="h-6 w-24 bg-gray-200 rounded-lg" />
        <div className="h-10 w-10 bg-gray-200 rounded-lg" />
      </div>
      {[...Array(2)].map((_, index) => (
        <div key={index} className="flex gap-4 pb-6 border-b last:border-0">
          <div className="w-16 h-16 bg-gray-200 rounded-lg" />
          <div className="flex-1 space-y-2">
            <div className="h-5 w-48 bg-gray-200 rounded-lg" />
            <div className="h-4 w-32 bg-gray-200 rounded-lg" />
            <div className="h-4 w-40 bg-gray-200 rounded-lg" />
          </div>
        </div>
      ))}
    </div>
  );
};
