import Image from "next/image";

const ResourceCard = ({ resource }: { resource: any }) => {
  return (
    <div
      className="bg-white rounded-xl overflow-hidden cursor-pointer transition-all duration-200 border-2 hover:shadow-md p-2"
      onClick={() => window.open(resource.file_url)}
    >
      <div className="relative aspect-video">
        <Image
          src={resource.image_url || 'https://placehold.co/600x400/e2e8f0/64748b?text=Resource'}
          alt={resource.title}
          fill
          className="object-cover rounded-xl"
          unoptimized={!resource.image_url}
        />
      </div>
      <div className="p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-500">
            {resource.category}
          </span>
          <span className="text-sm text-gray-400">
            {new Date(resource.created_at).toLocaleDateString()}
          </span>
        </div>
        <h3 className="font-semibold mb-2 line-clamp-2">{resource.title}</h3>
        <p className="text-sm text-gray-600 line-clamp-2">
          {resource.description}
        </p>
      </div>
    </div>
  );
};

export default ResourceCard;
