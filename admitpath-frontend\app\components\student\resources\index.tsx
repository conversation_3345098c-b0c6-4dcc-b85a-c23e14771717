"use client";

import { useEffect, useRef } from "react";
import { Search } from "lucide-react";
import { useResources } from "@/app/hooks/student/useResources";
import ResourceSkeleton from "@/app/components/student/resources/skeleton";
import ResourceCard from "./card";

export default function Resources() {
  const {
    resources,
    loading,
    hasMore,
    searchTerm,
    setSearchTerm,
    loadMore,
    setAudience,
  } = useResources();

  const observerTarget = useRef(null);

  useEffect(() => {
    setAudience("student");
  }, []);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loading) {
          loadMore();
        }
      },
      { threshold: 1.0 }
    );

    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => observer.disconnect();
  }, [hasMore, loading, loadMore]);

  const renderContent = () => {
    if (loading && resources.length === 0) {
      return (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, index) => (
            <ResourceSkeleton key={`skeleton-${index}`} />
          ))}
        </div>
      );
    }

    if (!loading && resources.length === 0) {
      return (
        <div className="text-center py-12">
          <p className="text-gray-500">No resources found</p>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {resources.map((resource) => (
          <ResourceCard resource={resource} />
        ))}

        {loading && (
          <>
            {Array.from({ length: 4 }).map((_, index) => (
              <ResourceSkeleton key={`load-more-skeleton-${index}`} />
            ))}
          </>
        )}

        {/* Observer target */}
        {hasMore && <div ref={observerTarget} className="h-10" />}
      </div>
    );
  };

  return (
    <div className="max-w-7xl mx-auto sm:px-4 px-1 py-6 bg-white rounded-xl">
      {/* Header */}
      <div className="flex md:flex-row flex-col gap-y-4 md:justify-between md:items-center mb-8 px-2">
        <h1 className="text-2xl font-semibold">Resources</h1>
        <div className="relative md:w-[400px] lg:w-[500px]">
          <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 w-6 h-6" />
          <input
            type="text"
            placeholder="Search"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full sm:text-lg text-md pl-12 pr-4 py-3 rounded-xl border bg-gray-100 border-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-200"
          />
        </div>
      </div>

      {renderContent()}
    </div>
  );
}
