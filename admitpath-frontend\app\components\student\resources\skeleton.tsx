const ResourceSkeleton = () => {
  return (
    <div className="bg-white rounded-xl overflow-hidden cursor-pointer transition-all duration-200 border-2 border-white hover:border-gray-100 hover:shadow-md p-2 animate-pulse">
      {/* Thumbnail skeleton */}
      <div className="relative aspect-video rounded-xl bg-gray-200" />

      {/* Content skeleton */}
      <div className="p-4">
        {/* Category and date */}
        <div className="flex items-center justify-between mb-2">
          <div className="h-4 w-16 bg-gray-200 rounded" />
          <div className="h-4 w-24 bg-gray-200 rounded" />
        </div>

        {/* Title */}
        <div className="space-y-2 mb-2">
          <div className="h-4 bg-gray-200 rounded w-full" />
          <div className="h-4 bg-gray-200 rounded w-4/5" />
        </div>

        {/* Description */}
        <div className="space-y-2">
          <div className="h-3 bg-gray-200 rounded w-full" />
          <div className="h-3 bg-gray-200 rounded w-2/3" />
        </div>
      </div>
    </div>
  );
};

export default ResourceSkeleton;
