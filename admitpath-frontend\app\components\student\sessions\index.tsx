"use client";

import { useSessions } from "@hooks/student/useSessions";
import { useEffect, useState } from "react";
import { SessionsList } from "./sessions-list";
import { SessionsSkeleton, PendingSessionsSkeleton } from "./skeleton";
import { Session } from "@/app/types/student/sessions";
import { SessionCard } from "./session-card";
import { Calendar } from "lucide-react";

type TabType = "upcoming" | "completed" | "cancelled" | "pending";

interface Tab {
  id: string;
  label: string;
  count?: number | null;
}

// Helper function to check if a session needs feedback
const isPendingFeedback = (session: Session): boolean => {
  if (session.status === "upcoming") {
    const endTime = new Date(session.end_time);
    const currentTime = new Date();
    return endTime < currentTime;
  }
  return false;
};

export default function Sessions() {
  const { sessions, fetchSessions } = useSessions();
  const [activeTab, setActiveTab] = useState<TabType>("upcoming");
  const [tabs, setTabs] = useState<Tab[]>([]);

  useEffect(() => {
    if (!sessions || !sessions.total) {
      fetchSessions(Intl.DateTimeFormat().resolvedOptions().timeZone);
    }
  }, [fetchSessions]);

  useEffect(() => {
    if (sessions) {
      const upcomingCount = sessions.items.filter(
        (s) => s.status === "upcoming" && !isPendingFeedback(s)
      ).length;
      const completedCount = sessions.items.filter(
        (s) => s.status === "completed"
      ).length;
      const cancelledCount = sessions.items.filter(
        (s) => s.status === "cancelled"
      ).length;
      const pendingCount = sessions.items.filter((s) =>
        isPendingFeedback(s)
      ).length;

      const tabsList = [
        {
          id: "upcoming",
          label: "Upcoming",
          count: upcomingCount > 0 ? upcomingCount : null,
        },
        {
          id: "pending",
          label: "Feedback Needed",
          count: pendingCount > 0 ? pendingCount : null,
        },
        {
          id: "completed",
          label: "Completed",
          count: completedCount > 0 ? completedCount : null,
        },
        {
          id: "cancelled",
          label: "Cancelled",
          count: cancelledCount > 0 ? cancelledCount : null,
        },
      ];
      setTabs(tabsList);
    }
  }, [sessions]);

  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (sessions) {
      setLoading(false);
    }
  }, [sessions]);

  return (
    <div className="container bg-white mx-auto md:p-8 sm:p-6 p-4 rounded-xl space-y-6">
      <div className="space-y-2">
        <h1 className="text-2xl font-semibold">Your Sessions</h1>
        <p className="text-gray-600">You can view all of your sessions here</p>
      </div>

      {!sessions ? (
        <SessionsSkeleton />
      ) : (
        <>
          <div className="border-b">
            <div className="flex gap-8 overflow-x-auto">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as TabType)}
                  className={`pb-4 px-2 relative text-base font-medium transition-colors ${
                    activeTab === tab.id
                      ? "text-gray-900 border-b-2 border-gray-900"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  <div className="flex items-center gap-2">
                    {tab.label}
                    {tab.count && (
                      <span className="bg-yellow-100 text-yellow-800 text-xs px-2.5 py-0.5 rounded-full">
                        {tab.count}
                      </span>
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>

          <div className="mt-6">
            {activeTab === "upcoming" &&
              (loading ? (
                <SessionsSkeleton />
              ) : (
                <SessionsList
                  sessions={sessions.items.filter((s) => !isPendingFeedback(s))}
                  status="upcoming"
                />
              ))}
            {activeTab === "pending" &&
              (loading ? (
                <PendingSessionsSkeleton />
              ) : (
                <div className="space-y-4">
                  {sessions.items
                    .filter((s) => isPendingFeedback(s))
                    .map((session) => (
                      <SessionCard key={session.id} session={session} />
                    ))}
                  {sessions.items.filter((s) => isPendingFeedback(s)).length ===
                    0 && (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <Calendar className="w-12 h-12 text-gray-400 mb-3" />
                      <p className="text-gray-600 font-medium">
                        No sessions needing feedback
                      </p>
                      <p className="text-gray-500 text-sm mt-1">
                        Sessions that need feedback will appear here
                      </p>
                    </div>
                  )}
                </div>
              ))}
            {activeTab === "completed" &&
              (loading ? (
                <SessionsSkeleton />
              ) : (
                <SessionsList sessions={sessions.items} status="completed" />
              ))}
            {activeTab === "cancelled" &&
              (loading ? (
                <SessionsSkeleton />
              ) : (
                <SessionsList sessions={sessions.items} status="cancelled" />
              ))}
          </div>
        </>
      )}
    </div>
  );
}
