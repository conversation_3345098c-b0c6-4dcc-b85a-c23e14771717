import { Calendar, ChevronDown, Clock, Video } from "lucide-react";
import { Session } from "@/app/types/student/sessions";
import { Button } from "@/app/components/ui/button";
import { useState, useEffect } from "react";
import { SessionDetailsDialog } from "./session-details-dialog";
import { SendMessageDialog } from "./send-message-dialog";
import Image from "next/image";
import { generatePlaceholder } from "@/app/utils/image";
import { useRouter } from "next/navigation";

interface SessionCardProps {
  session: Session;
}

export const SessionCard = ({ session }: SessionCardProps) => {
  // State for UI interactions
  const [showSendMessage, setShowSendMessage] = useState(false);
  const [showDetails, setShowDetails] = useState(false);
  const [defaultTab, setDefaultTab] = useState<"details" | "notes">("details");
  const [needsFeedback, setNeedsFeedback] = useState(false);
  const router = useRouter();

  // Check if session needs feedback (status is upcoming but end time has passed)
  useEffect(() => {
    if (session.status === "upcoming") {
      const endTime = new Date(session.end_time);
      const currentTime = new Date();
      if (endTime < currentTime) {
        setNeedsFeedback(true);
      }
    }
  }, [session]);

  const formatDate = (date: string) => {
    return new Date(date).toLocaleDateString("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric",
    });
  };

  const formatTime = (start: string, end: string) => {
    const startTime = new Date(start).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
    const endTime = new Date(end).toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
    return `${startTime} - ${endTime}`;
  };

  // No longer needed as we're using direct link

  const getStatusStyles = () => {
    if (needsFeedback) {
      return "bg-purple-100 text-purple-600 border border-purple-300";
    }

    switch (session.status) {
      case "cancelled":
        return "bg-yellow-100 text-yellow-600";
      case "completed":
        return "bg-green-600 text-white";
      default:
        return "bg-green-100 text-green-600";
    }
  };

  const getStatusLabel = () => {
    if (needsFeedback) {
      return "Feedback Needed";
    }

    switch (session.status) {
      case "cancelled":
        return "Cancelled";
      case "completed":
        return "Completed";
      default:
        return "Upcoming";
    }
  };

  const handleActionClick = () => {
    if (session.status === "completed") {
      setShowDetails(true);
      setDefaultTab("notes");
      return;
    }
    if (needsFeedback) {
      router.push("/student/dashboard");
      return;
    }
    setShowSendMessage(true);
  };

  return (
    <div className="border border-gray-200 rounded-lg">
      <div className="flex md:flex-row flex-col md:items-center items-start justify-between sm:px-6 p-4 sm:py-5 gap-y-4 text-gray-700 bg-gray-100/60">
        <div className="flex md:flex-row flex-col-reverse md:items-center items-start gap-2">
          <h3 className="text-xl font-medium">{session.event_name}</h3>
          <span
            className={`px-3 py-1 text-lg font-semibold rounded-2xl ${getStatusStyles()}`}
          >
            {getStatusLabel()}
          </span>
        </div>

        <Button onClick={() => setShowDetails(true)}>
          View Details
          <ChevronDown className="w-6 h-6 transform transition-transform duration-200 hover:rotate-180 rotate-0" />
        </Button>
      </div>

      <div className="flex md:flex-row flex-col md:items-center items-start gap-6 sm:px-6 p-4 sm:py-5 gap-y-2 border-b-2 text-gray-600 text-md font-medium">
        <div className="flex items-center gap-2">
          <Calendar className="w-6 h-6" />
          <span>{formatDate(session.date)}</span>
        </div>
        <div className="flex items-center gap-2">
          <Clock className="w-6 h-6" />
          <span>{formatTime(session.start_time, session.end_time)}</span>
        </div>
      </div>

      <div className="flex md:flex-row flex-col justify-between md:items-center sm:px-6 p-4 sm:py-5 gap-y-5">
        <div className="space-y-4 text-md font-medium">
          <div className="space-y-2">
            <p className="text-gray-600">Counselor</p>
            <div className="flex items-center gap-3">
              <div className="relative w-10 h-10 flex-shrink-0">
                <Image
                  src={
                    session.counselor_profile_picture ||
                    generatePlaceholder(
                      session.counselor_name?.split(" ")[0] || "",
                      session.counselor_name?.split(" ")[1] || ""
                    )
                  }
                  alt="Counselor"
                  fill
                  className="rounded-full object-cover"
                />
              </div>
              <div>
                <div className="font-medium text-gray-900">
                  {session.counselor_name || ""}
                </div>
                <div className="text-sm text-gray-500">Counselor</div>
              </div>
            </div>
          </div>

          <div className="space-y-2 ">
            <p className="text-gray-600">Meeting link</p>
            <div className="flex items-center gap-2">
              <div className="p-2 bg-blue-100 rounded-full">
                <Video className="w-5 h-5 text-blue-600" />
              </div>
              <a
                href={session.meeting_link}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-500 underline hover:text-blue-700 text-sm mt-2 inline-block"
              >
                Join Meeting
              </a>
            </div>
          </div>
        </div>
        <button
          onClick={handleActionClick}
          className="w-[70%] mx-auto sm:mx-0 sm:w-auto text-md bg-gray-900 hover:bg-gray-950 rounded-2xl sm:p-3 p-2 text-white font-medium"
        >
          {needsFeedback
            ? "Provide Feedback"
            : session.status === "upcoming"
            ? "Send Message"
            : "View Notes"}
        </button>
      </div>

      <SessionDetailsDialog
        session={session}
        open={showDetails}
        onOpenChange={setShowDetails}
        defaultTab={defaultTab}
      />
      <SendMessageDialog
        open={showSendMessage}
        onOpenChange={setShowSendMessage}
        counselorInfo={{
          user_id: session.counselor_user_id,
          first_name: session.counselor_name?.split(" ")[0] || "",
          last_name: session.counselor_name?.split(" ")[1] || "",
          profile_picture_url: session.counselor_profile_picture || null,
        }}
      />
    </div>
  );
};
