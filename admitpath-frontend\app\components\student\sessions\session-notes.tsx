import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@components/ui/tabs";
import { useState, useEffect } from "react";
import { Clock, ChevronRight } from "lucide-react";
import { Button } from "@components/ui/button";
import { Textarea } from "./textarea";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@components/ui/dialog";
import { useSessions } from "@hooks/student/useSessions";
import { toast } from "react-toastify";

interface SessionNotesProps {
  sessionId: number;
  counselorNotes: string | null;
  studentNotes: string | null;
}

interface NoteDetailProps {
  content: string;
  author: string;
  timestamp: string;
  onRemove: () => Promise<void>;
}

const NoteDetail = ({
  content,
  author,
  timestamp,
  onRemove,
}: NoteDetailProps) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-medium">{author}</h3>
        <div className="flex items-center gap-2 text-gray-500">
          <Clock className="w-4 h-4" />
          <span>{new Date(timestamp).toLocaleTimeString()}</span>
        </div>
      </div>
      <p className="text-gray-600 whitespace-pre-wrap">{content}</p>
      <div className="flex justify-between items-center mt-4">
        <button
          onClick={onRemove}
          className="text-red-500 hover:text-red-600 transition-colors"
        >
          Remove
        </button>
        <Button variant="outline" onClick={() => window.history.back()}>
          <ChevronRight className="w-4 h-4 mr-2" />
          Back
        </Button>
      </div>
    </div>
  );
};

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });
};

export const SessionNotes = ({
  sessionId,
  counselorNotes,
  studentNotes,
}: SessionNotesProps) => {
  const [newNote, setNewNote] = useState("");
  const [showSuccess, setShowSuccess] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isViewingNote, setIsViewingNote] = useState(false);
  const [selectedNoteType, setSelectedNoteType] = useState<
    "counselor" | "student" | null
  >(null);

  const {
    updateSessionNotes,
    createSessionNotes,
    loading,
    currentNotes,
    fetchSessionNotes,
  } = useSessions();

  useEffect(() => {
    fetchSessionNotes(sessionId);
  }, [sessionId, fetchSessionNotes]);

  const handleSaveNote = async () => {
    if (!newNote.trim()) return;

    try {
      const payload = {
        student_notes: newNote,
        counselor_notes: counselorNotes || " ",
      };

      // If either type of note exists, use PUT, otherwise use POST
      if (currentNotes || counselorNotes || studentNotes) {
        await updateSessionNotes(sessionId, payload);
      } else {
        await createSessionNotes(sessionId, payload);
      }

      setShowSuccess(true);
      setNewNote("");
      setIsEditing(false);
      setIsViewingNote(false);
    } catch (error) {
      toast.error("Failed to save note");
    }
  };

  const renderNoteInput = () => {
    return (
      <div className="space-y-2">
        <label className="text-gray-600">
          Add notes<span className="text-red-500">*</span>
        </label>
        <div className="relative">
          <Textarea
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
            placeholder="Enter here..."
            className="min-h-[200px] resize-none"
            maxLength={400}
          />
          <span className="absolute bottom-2 right-2 text-sm text-gray-500">
            {newNote.length}/400
          </span>
        </div>
        <div className="flex justify-end gap-3 mt-4">
          <Button
            variant="outline"
            onClick={() => {
              setNewNote("");
              setIsEditing(false);
            }}
          >
            Discard
          </Button>
          <Button
            onClick={handleSaveNote}
            disabled={!newNote.trim() || loading}
          >
            Save
          </Button>
        </div>
      </div>
    );
  };

  const renderNotePreview = (
    content: string,
    timestamp: string,
    author: string,
    type: "counselor" | "student"
  ) => (
    <div
      onClick={() => {
        if (type === "student") {
          setNewNote(content);
          setSelectedNoteType(type);
          setIsViewingNote(true);
        }
      }}
      className={`bg-gray-50 rounded-lg p-4 ${
        type === "student" ? "cursor-pointer hover:bg-gray-100" : ""
      } transition-colors`}
    >
      <div className="flex items-center justify-between mb-2">
        <h4 className="font-medium">{author}</h4>
        <div className="flex items-center gap-2 text-gray-500 text-sm">
          <Clock className="w-4 h-4" />
          <span>{formatTime(timestamp)}</span>
        </div>
      </div>
      <p className="text-gray-600 line-clamp-2">{content}</p>
      {type === "student" && (
        <ChevronRight className="w-4 h-4 text-gray-400 mt-2" />
      )}
    </div>
  );

  if (isViewingNote) {
    return (
      <div className="space-y-4">
        {isEditing ? (
          <>
            <div className="relative">
              <Textarea
                value={newNote}
                onChange={(e) => setNewNote(e.target.value)}
                className="min-h-[300px] resize-none"
                maxLength={400}
              />
              <span className="absolute bottom-2 right-2 text-sm text-gray-500">
                {newNote.length}/400
              </span>
            </div>
            <div className="flex justify-between items-center mt-4">
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleSaveNote}
                disabled={!newNote.trim() || loading}
              >
                Save Changes
              </Button>
            </div>
          </>
        ) : (
          <>
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-medium">Your Note</h3>
              <div className="flex items-center gap-2 text-gray-500">
                <Clock className="w-4 h-4" />
                <span>{formatTime(currentNotes?.created_at || "")}</span>
              </div>
            </div>
            <p className="text-gray-600 whitespace-pre-wrap">{newNote}</p>
            <div className="flex justify-between items-center mt-4">
              <Button variant="outline" onClick={() => setIsEditing(true)}>
                Edit
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setIsViewingNote(false);
                  setNewNote("");
                }}
              >
                Back
              </Button>
            </div>
          </>
        )}
      </div>
    );
  }

  return (
    <>
      <Tabs defaultValue="counsellor" className="w-full">
        <div className="overflow-x-auto scrollbar-hide -mx-2 px-2">
          <TabsList className="bg-gray-100/80 p-1 rounded-lg inline-flex w-auto">
            <TabsTrigger
              value="counsellor"
              className="data-[state=active]:bg-white rounded-md px-4 py-2 whitespace-nowrap"
            >
              For counsellor
            </TabsTrigger>
            <TabsTrigger
              value="yourself"
              className="data-[state=active]:bg-white rounded-md px-4 py-2 whitespace-nowrap"
            >
              For yourself
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="counsellor" className="mt-6">
          {counselorNotes && counselorNotes.trim() ? (
            renderNotePreview(
              counselorNotes,
              currentNotes?.created_at || "",
              "Counselor",
              "counselor"
            )
          ) : (
            <p className="text-gray-500">No notes from counselor yet.</p>
          )}
        </TabsContent>

        <TabsContent value="yourself" className="mt-6">
          {studentNotes && studentNotes.trim()
            ? renderNotePreview(
                studentNotes,
                currentNotes?.created_at || "",
                "You",
                "student"
              )
            : renderNoteInput()}
        </TabsContent>
      </Tabs>

      {/* Success Dialog */}
      <Dialog open={showSuccess} onOpenChange={setShowSuccess}>
        <DialogContent className="text-center">
          <DialogHeader>
            <DialogTitle>Successfully saved!</DialogTitle>
          </DialogHeader>
          <div className="py-6">
            <Image
              src="/images/success.png"
              alt="Success"
              width={100}
              height={100}
              className="mx-auto"
            />
          </div>
          <DialogDescription>
            Your note has been saved successfully.
          </DialogDescription>
          <Button onClick={() => setShowSuccess(false)} className="mt-4">
            Done
          </Button>
        </DialogContent>
      </Dialog>
    </>
  );
};
