import { Session } from "@/app/types/student/sessions";
import { SessionCard } from "./session-card";
import { Calendar } from "lucide-react";

interface SessionsListProps {
  sessions: Session[];
  status: "upcoming" | "completed" | "cancelled" | "pending";
}

export const SessionsList = ({ sessions, status }: SessionsListProps) => {
  const filteredSessions = sessions.filter(
    (session) => session.status === status
  );

  if (filteredSessions.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <Calendar className="w-12 h-12 text-gray-400 mb-3" />
        <p className="text-gray-600 font-medium">
          {status === "upcoming" && "No upcoming sessions"}
          {status === "completed" && "No completed sessions"}
          {status === "cancelled" && "No cancelled sessions"}
          {status === "pending" && "No sessions needing feedback"}
        </p>
        <p className="text-gray-500 text-sm mt-1">
          {status === "upcoming" && "Book a session with one of our counselors"}
          {status === "completed" && "All sessions are up to date"}
          {status === "cancelled" && "Your cancelled sessions will appear here"}
          {status === "pending" &&
            "Sessions that need feedback will appear here"}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4 ">
      {filteredSessions.map((session) => (
        <SessionCard key={session.id} session={session} />
      ))}
    </div>
  );
};
