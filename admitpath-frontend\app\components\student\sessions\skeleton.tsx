import { Skeleton } from "@components/ui/skeleton";

export const PendingSessionsSkeleton = () => {
  return (
    <div className="space-y-4">
      {[1, 2].map((i) => (
        <div
          key={i}
          className="border border-gray-200 rounded-lg p-4 bg-gray-50/50"
        >
          <div className="flex flex-col gap-4">
            {/* Top row */}
            <div className="flex sm:flex-row flex-col sm:items-start items-center justify-between gap-x-4 gap-y-5">
              <div className="flex items-start gap-3 flex-grow">
                <Skeleton className="h-10 w-10 rounded-xl" />
                <div>
                  <Skeleton className="h-6 w-48 mb-2" />
                  <Skeleton className="h-4 w-32" />
                </div>
              </div>
              <Skeleton className="h-10 w-32 rounded-lg" />
            </div>

            {/* Bottom row */}
            <div className="flex items-center gap-3 border-t pt-3">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div>
                <Skeleton className="h-5 w-32 mb-1" />
                <Skeleton className="h-4 w-20" />
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export const SessionsSkeleton = () => {
  return (
    <div className="space-y-6 sm:space-y-8 md:space-y-12">
      {/* Tabs Skeleton */}
      <div className="flex flex-wrap gap-8 border-b sm:gap-12 md:gap-16">
        {[1, 2, 3].map((i) => (
          <div key={i} className="pb-4 px-2 sm:px-4 md:px-6 w-full sm:w-auto">
            <Skeleton className="h-6 w-full sm:w-24 md:w-32" />
          </div>
        ))}
      </div>

      {/* Session Cards Skeleton */}
      <div className="space-y-4 sm:space-y-8 md:space-y-12">
        {[1].map((i) => (
          <div
            key={i}
            className="border border-gray-200 rounded-lg sm:max-w-md md:max-w-xl lg:max-w-3xl xl:max-w-4xl mx-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between px-6 py-5 bg-gray-100/60">
              <div className="flex items-center gap-2 sm:gap-4 md:gap-6">
                <Skeleton className="h-7 w-48 sm:w-64 md:w-80" />
                <Skeleton className="h-7 w-24 sm:w-32 md:w-40 rounded-2xl" />
              </div>
              <Skeleton className="h-10 w-32 sm:w-40 md:w-48" />
            </div>

            {/* Date/Time */}
            <div className="flex items-center gap-6 px-6 py-5 border-b-2 sm:gap-8 md:gap-12">
              <div className="flex items-center gap-2 sm:gap-4 md:gap-6">
                <Skeleton className="h-6 w-6 sm:w-8 md:w-10" />
                <Skeleton className="h-6 w-32 sm:w-40 md:w-48" />
              </div>
              <div className="flex items-center gap-2 sm:gap-4 md:gap-6">
                <Skeleton className="h-6 w-6 sm:w-8 md:w-10" />
                <Skeleton className="h-6 w-32 sm:w-40 md:w-48" />
              </div>
            </div>

            {/* Details */}
            <div className="flex md:flex-row flex-col justify-between md:items-center px-6 py-5 sm:py-8 md:py-12">
              <div className="space-y-4 sm:space-y-8 md:space-y-12">
                <div className="space-y-2 sm:space-y-4 md:space-y-6">
                  <Skeleton className="h-5 w-24 sm:w-32 md:w-40" />
                  <div className="flex items-center gap-3 sm:gap-4 md:gap-6">
                    <Skeleton className="h-12 w-12 sm:h-16 sm:w-16 md:h-20 md:w-20 rounded-full" />
                    <Skeleton className="h-6 w-32 sm:w-40 md:w-48" />
                  </div>
                </div>

                <div className="space-y-2 sm:space-y-4 md:space-y-6">
                  <Skeleton className="h-5 w-24 sm:w-32 md:w-40" />
                  <div className="flex items-center gap-2 sm:gap-4 md:gap-6">
                    <Skeleton className="h-9 w-9 sm:h-12 sm:w-12 md:h-16 md:w-16 rounded-full" />
                    <Skeleton className="h-6 w-32 sm:w-40 md:w-48" />
                  </div>
                </div>
              </div>
              <Skeleton className="w-full sm:w-32 md:w-48 h-12 rounded-2xl mt-4 sm:mt-6 md:mt-8" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
