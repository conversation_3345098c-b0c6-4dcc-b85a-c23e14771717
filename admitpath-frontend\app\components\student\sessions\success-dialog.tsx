"use client";

import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@components/ui/dialog";
import { Button } from "@components/ui/button";

interface SuccessDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onContinue: () => void;
}

export function SuccessDialog({
  open,
  onOpenChange,
  onContinue,
}: SuccessDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="text-center">
        <DialogHeader>
          <DialogTitle>Session Created Successfully!</DialogTitle>
        </DialogHeader>
        <div className="py-6">
          <Image
            src="/images/success.png"
            alt="Success"
            width={100}
            height={100}
            className="mx-auto"
          />
        </div>
        <DialogDescription>
          Your session has been created successfully. You will now be redirected
          to complete the payment.
        </DialogDescription>
        <Button onClick={onContinue} className="mt-4">
          Continue to Payment
        </Button>
      </DialogContent>
    </Dialog>
  );
}
