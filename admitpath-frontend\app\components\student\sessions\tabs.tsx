import { cn } from "@components/lib/utils";

interface Tab {
  id: string;
  label: string;
  count?: number;
}

interface SessionTabsProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string;
}

export const SessionTabs = ({
  tabs,
  activeTab,
  onTabChange,
  className,
}: SessionTabsProps) => {
  return (
    <div className={cn("flex gap-8 border-b", className)}>
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => onTabChange(tab.id)}
          className={`pb-4 px-2 relative text-gray-900 font-medium transition-colors ${
            activeTab === tab.id
              ? "border-b-2 border-gray-900"
              : "hover:text-gray-600"
          }`}
        >
          <div className="flex items-center gap-2">
            {tab.label}
            {tab.count && tab.count > 0 && (
              <span className="bg-yellow-100 text-yellow-800 text-xs px-2.5 py-0.5 rounded-full">
                {tab.count}
              </span>
            )}
          </div>
        </button>
      ))}
    </div>
  );
};
