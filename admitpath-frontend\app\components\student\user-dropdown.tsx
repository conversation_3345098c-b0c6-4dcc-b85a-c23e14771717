"use client";

import { LogOut, ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/app/components/ui/dropdown-menu";
import { useState } from "react";
import Image from "next/image";
import { UserInfo } from "@/app/types/student/profile";
import { generatePlaceholder } from "@/app/utils/image";

interface UserDropdownProps {
  userInfo: UserInfo | null;
  onLogoutClick: () => void;
}

export const UserDropdown = ({
  userInfo,
  onLogoutClick,
}: UserDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleLogoutClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onLogoutClick();
  };

  return (
    <div className="flex items-center space-x-2">
      <div className="flex items-center gap-4">
        <Image
          className="rounded-full object-cover w-[3rem] h-[3rem]"
          src={
            userInfo?.profile_picture_url ||
            generatePlaceholder(userInfo?.firstName, userInfo?.lastName)
          }
          alt={`${userInfo?.firstName}`}
          width={100}
          height={100}
        />
        <div className="sm:flex flex-col hidden">
          <span className="font-medium text-gray-900">
            {userInfo?.firstName} {userInfo?.lastName}
          </span>
          <span className="text-sm text-gray-500">{userInfo?.email}</span>
        </div>
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <DropdownMenuTrigger asChild>
            <button className="outline-none hover:opacity-80">
              <ChevronDown
                className={`w-6 h-6 transform transition-transform duration-200 ${
                  isOpen ? "rotate-180" : "rotate-0"
                }`}
              />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="p-2">
            <DropdownMenuItem
              onClick={handleLogoutClick}
              className="flex items-center gap-3 px-4 py-3 text-white bg-red-500 hover:bg-red-600/90 rounded-lg cursor-pointer"
            >
              <span className="font-semibold text-base">Logout</span>
              <LogOut className="w-6 h-6" />
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};
