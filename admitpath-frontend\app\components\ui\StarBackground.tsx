"use client";

import { useEffect, useRef } from "react";

const StarBackground = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    let stars: Array<{ x: number; y: number; size: number; brightness: number }> = [];
    let mouseX = window.innerWidth / 2;
    let mouseY = window.innerHeight / 2;
    let animationFrameId: number;

    const createStars = () => {
      stars = [];
      const numberOfStars = 100;
      
      for (let i = 0; i < numberOfStars; i++) {
        // Make about 30% of stars bright by default
        const brightness = Math.random() < 0.3 ? 1 : 0.3;
        stars.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: 1.5,
          brightness
        });
      }
    };

    const drawStars = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      stars.forEach((star) => {
        const distance = Math.sqrt(
          Math.pow(mouseX - star.x, 2) + Math.pow(mouseY - star.y, 2)
        );
        
        // If mouse is nearby, brighten the star, otherwise use its default brightness
        const opacity = distance < 100 ? 1 : star.brightness;
        ctx.fillStyle = `rgba(255, 255, 255, ${opacity})`;
        
        ctx.beginPath();
        ctx.arc(star.x, star.y, star.size, 0, Math.PI * 2);
        ctx.fill();
      });

      animationFrameId = requestAnimationFrame(drawStars);
    };

    const handleResize = () => {
      canvas.width = canvas.offsetWidth;
      canvas.height = canvas.offsetHeight;
      createStars();
    };

    const handleMouseMove = (e: MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      mouseX = e.clientX - rect.left;
      mouseY = e.clientY - rect.top;
    };

    canvas.width = canvas.offsetWidth;
    canvas.height = canvas.offsetHeight;
    
    window.addEventListener("resize", handleResize);
    canvas.addEventListener("mousemove", handleMouseMove);
    
    createStars();
    drawStars();

    return () => {
      window.removeEventListener("resize", handleResize);
      canvas.removeEventListener("mousemove", handleMouseMove);
      cancelAnimationFrame(animationFrameId);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="absolute top-0 left-0 w-full h-full"
    />
  );
};

export default StarBackground;
