import { cn } from "@/lib/utils";

interface CounselorCardSkeletonProps {
  className?: string;
  variant?: "explore" | "home";
}

export function CounselorCardSkeleton({ 
  className, 
  variant = "explore" 
}: CounselorCardSkeletonProps) {
  if (variant === "home") {
    return (
      <div className={cn(
        "w-[280px] sm:w-[300px] md:w-[320px] min-h-[390px] sm:min-h-[410px] max-h-[390px] sm:max-h-[410px] mx-3 sm:mx-4 md:mx-5 my-4 sm:my-6 md:my-8 rounded-xl bg-white shadow-md overflow-hidden animate-pulse",
        className
      )}>
        {/* Image skeleton */}
        <div className="relative h-[220px] w-full bg-gray-200" />
        
        {/* Content skeleton */}
        <div className="p-4 space-y-3">
          {/* Name */}
          <div className="h-6 bg-gray-200 rounded w-3/4" />
          
          {/* Bio/tagline */}
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-full" />
            <div className="h-4 bg-gray-200 rounded w-2/3" />
          </div>
          
          {/* University */}
          <div className="flex items-center gap-2 mt-4">
            <div className="w-5 h-5 bg-gray-200 rounded" />
            <div className="h-4 bg-gray-200 rounded w-1/2" />
          </div>
          
          {/* Price */}
          <div className="mt-4 space-y-1">
            <div className="h-3 bg-gray-200 rounded w-16" />
            <div className="h-5 bg-gray-200 rounded w-20" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn(
      "overflow-hidden rounded-lg flex flex-col bg-white border animate-pulse",
      className
    )}>
      <div className="relative">
        {/* Header background */}
        <div className="h-20 relative overflow-hidden bg-gray-200">
          <div className="h-6 bg-gray-300 w-full" />
        </div>

        {/* Profile Picture and University Logo skeleton */}
        <div className="absolute flex items-center justify-between gap-8 -bottom-8 left-4 pr-8 w-full">
          <div className="relative w-24 h-24 rounded-full bg-gray-200 border-3 border-white shadow-lg" />
          
          {/* University Logo skeleton */}
          <div className="w-16 h-16 bg-gray-200 rounded-lg" />
          
          {/* Country flag skeleton */}
          <div className="w-6 h-4 bg-gray-200 rounded-sm" />
        </div>
      </div>
      
      <div className="p-4 pt-12 flex-1">
        <div className="flex flex-col justify-between h-full">
          <div className="flex flex-col gap-2">
            {/* Name skeleton */}
            <div className="h-6 bg-gray-200 rounded w-3/4" />
            
            {/* Bio skeleton */}
            <div className="space-y-2 mt-1">
              <div className="h-4 bg-gray-200 rounded w-full" />
              <div className="h-4 bg-gray-200 rounded w-2/3" />
            </div>
          </div>
          
          <div>
            {/* Education skeleton */}
            <div className="mt-4 flex items-center gap-2">
              <div className="w-5 h-5 bg-gray-200 rounded" />
              <div className="h-4 bg-gray-200 rounded w-1/2" />
            </div>

            {/* Pricing skeleton */}
            <div className="mt-4 space-y-1">
              <div className="h-3 bg-gray-200 rounded w-16" />
              <div className="h-5 bg-gray-200 rounded w-20" />
            </div>
          </div>
        </div>
      </div>
      
      {/* Buttons skeleton */}
      <div className="px-4 pb-4">
        <div className="flex flex-col sm:grid sm:grid-cols-2 gap-2">
          <div className="h-10 bg-gray-200 rounded-lg" />
          <div className="h-10 bg-gray-200 rounded-lg" />
        </div>
      </div>
    </div>
  );
}

// Multiple skeletons for loading states
export function CounselorCardSkeletonGrid({ 
  count = 8, 
  variant = "explore" 
}: { 
  count?: number; 
  variant?: "explore" | "home";
}) {
  if (variant === "home") {
    return (
      <div className="flex flex-wrap justify-center items-center gap-6 w-full">
        {Array.from({ length: count }).map((_, index) => (
          <CounselorCardSkeleton key={index} variant="home" />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4">
      {Array.from({ length: count }).map((_, index) => (
        <CounselorCardSkeleton key={index} variant="explore" />
      ))}
    </div>
  );
}
