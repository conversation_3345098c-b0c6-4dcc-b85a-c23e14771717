import Image from "next/image";

interface LogoLoaderProps {
  size?: number;
  text?: string;
}

export function LogoLoader({ size = 40, text }: LogoLoaderProps) {
  return (
    <div className="flex flex-col justify-center items-center h-max gap-y-4">
      <div className="relative inline-flex items-center justify-center p-2">
        <Image
          src="/icons/logo.png"
          alt="logo"
          width={size}
          height={size}
          className="rounded-full"
        />
        <div
          className="absolute rounded-full border-4 border-t-transparent animate-spin"
          style={{
            width: "120%",
            height: "120%",
            borderColor: "rgba(239, 68, 68, 0.2)",
            borderTopColor: "rgb(239, 68, 68)",
          }}
        />
      </div>
      {text && (
        <h2 className="text-lg text-center font-medium text-gray-600">
          {text}
        </h2>
      )}
    </div>
  );
}
