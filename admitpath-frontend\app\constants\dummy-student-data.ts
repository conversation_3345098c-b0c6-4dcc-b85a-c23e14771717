export interface StudentProfile {
  name: string;
  email: string;
  avatar: string;
  country: string;
  grade: string;
  dateOfBirth: string;
  gender: string;
  bio: string;
  notifications: {
    count: number;
    hasUnread: boolean;
  };
  education: {
    school: string;
    degree: string;
    logo: string;
    startDate: string;
    endDate: string;
  }[];
  packages: {
    title: string;
    description: string;
    price: number;
  }[];
}

export const studentData: StudentProfile = {
  name: "<PERSON><PERSON>",
  email: "<EMAIL>",
  avatar: "/images/person.png",
  country: "Karachi, Pakistan",
  grade: "Grade 9 student",
  dateOfBirth: "24 December 2000",
  gender: "Male",
  notifications: {
    count: 3,
    hasUnread: true,
  },
  bio: "Lorem ipsum dolor sit amet consectetur. Imperdiet eget ac mattis sed et faucibus mauris pulvinar. Sit gravida commodo tortor id. Dignissim dolor lacus elit purus elit. Suscipit aliquet amet scelerisque amet scelerisque amet scelerisque amet egestas amet scelerisque amet justo. Augue lectus augue libero vel lutpat malesuada massa. Sit mauris proin condimentum tristique ut risus. Augue amet venenatis convallis fermentum. Elit scelerisque ullamcorper nunc pellentesque ut lectus. Pelle viverra interdum diam lacus. Vitae vitae pharetra pharetra.",
  education: [
    {
      school: "University of London",
      degree: "Master Degree in computer science and mathematics",
      logo: "/images/university-logo.png",
      startDate: "January 20 2020",
      endDate: "March 11 2024",
    },
  ],
  packages: [
    {
      title: "5 Essay Review Sessions for $200",
      description:
        "Includes two 30-minute brainstorming sessions and one detailed feedback session",
      price: 15,
    },
    {
      title: "5 Essay Review Sessions for $200",
      description:
        "Includes two 30-minute brainstorming sessions and one detailed feedback session",
      price: 15,
    },
  ],
};

export interface Resource {
  id: number;
  title: string;
  description: string;
  thumbnail: string;
  category: string;
  date: string;
}

export const resourcesData: Resource[] = Array.from({ length: 50 }, (_, i) => ({
  id: i + 1,
  title:
    i % 2 === 0
      ? "Nextjs 15 Course - Learn full stack development"
      : "Complete Physics Course for JEE Advanced Preparation",
  description:
    "Lorem ipsum dolor sit amet consectetur. Imperdiet eget ac mattis sed et faucibus mauris pulvinar. Sit gravida commodo tortor id.",
  thumbnail: i % 2 === 0 ? "/images/dummy-tn-2.jpg" : "/images/dummy-tn-1.png",
  category: i % 2 === 0 ? "Mathematics" : "Physics",
  date: new Date(2024, 0, i + 1).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  }),
}));

export interface Package {
  id: string;
  title: string;
  startDate: string;
  amountPaid: number;
  instructor: {
    name: string;
    avatar: string;
    isOnline: boolean;
  };
  serviceName: string;
  description: string;
  whatToExpect: string[];
  sessionsProgress: {
    completed: number;
    total: number;
  };
}

export const studentPackages: Package[] = [
  {
    id: "pkg1",
    title: "Package 1",
    startDate: "Jan 20 2024",
    amountPaid: 15,
    instructor: {
      name: "Counsellor name",
      avatar: "/images/person.png",
      isOnline: true,
    },
    serviceName: "Service name",
    description: "5 Essay Review Sessions for $200",
    whatToExpect: [
      "Lorem ipsum dolor sit amet consectetur",
      "Lorem ipsum dolor sit amet consectetur.",
      "Lorem ipsum dolor sit amet consectetur",
      "Lorem ipsum dolor sit amet consectetur",
    ],
    sessionsProgress: {
      completed: 4,
      total: 10,
    },
  },
  {
    id: "pkg2",
    title: "Package 2",
    startDate: "Feb 15 2024",
    amountPaid: 20,
    instructor: {
      name: "Professor Smith",
      avatar: "/images/person.png",
      isOnline: false,
    },
    serviceName: "Advanced Math Prep",
    description: "10 Math Sessions for $300",
    whatToExpect: [
      "In-depth review of algebra and geometry",
      "Practice problems and homework help",
      "Personalized feedback and progress tracking",
      "Access to online resources and study materials",
    ],
    sessionsProgress: {
      completed: 6,
      total: 10,
    },
  },
  {
    id: "pkg3",
    title: "Package 3",
    startDate: "Mar 1 2024",
    amountPaid: 25,
    instructor: {
      name: "Dr. Johnson",
      avatar: "/images/person.png",
      isOnline: true,
    },
    serviceName: "Science and Technology",
    description: "12 Science and Tech Sessions for $400",
    whatToExpect: [
      "Exploration of biology, chemistry, and physics",
      "Hands-on experiments and projects",
      "Discussion of emerging technologies and innovations",
      "Preparation for science fairs and competitions",
    ],
    sessionsProgress: {
      completed: 8,
      total: 12,
    },
  },
];

export interface DashboardStats {
  totalSessions: number;
  upcomingSessions: number;
  totalCounsellors: number;
}

export interface UpcomingSession {
  id: string;
  name: string;
  time: string;
  counsellor: {
    name: string;
    avatar: string;
    isOnline: boolean;
  };
}

export interface PendingFeedback {
  id: string;
  name: string;
  time: string;
  instructor: string;
  description: string;
  maxCharLimit: number;
}

export const dashboardData = {
  stats: {
    totalSessions: 120,
    upcomingSessions: 8,
    totalCounsellors: 4,
  },
  upcomingSessions: [
    {
      id: "1",
      name: "Session name",
      time: "7 AM - 8:30 AM",
      counsellor: {
        name: "Counsellor name",
        avatar: "/images/person.png",
        isOnline: true,
      },
    },
    {
      id: "2",
      name: "Session name",
      time: "7 AM - 8:30 AM",
      counsellor: {
        name: "Counsellor name",
        avatar: "/images/person.png",
        isOnline: false,
      },
    },
    {
      id: "3",
      name: "Math Session",
      time: "9 AM - 10:30 AM",
      counsellor: {
        name: "Ms. Johnson",
        avatar: "/images/person.png",
        isOnline: true,
      },
    },
    {
      id: "4",
      name: "Science Session",
      time: "11 AM - 12:30 PM",
      counsellor: {
        name: "Dr. Smith",
        avatar: "/images/person.png",
        isOnline: true,
      },
    },
    {
      id: "5",
      name: "English Session",
      time: "2 PM - 3:30 PM",
      counsellor: {
        name: "Ms. Thompson",
        avatar: "/images/person.png",
        isOnline: false,
      },
    },
    {
      id: "6",
      name: "History Session",
      time: "4 PM - 5:30 PM",
      counsellor: {
        name: "Mr. Davis",
        avatar: "/images/person.png",
        isOnline: true,
      },
    },
    {
      id: "7",
      name: "Computer Science Session",
      time: "6 PM - 7:30 PM",
      counsellor: {
        name: "Dr. Lee",
        avatar: "/images/person.png",
        isOnline: true,
      },
    },
    {
      id: "8",
      name: "Physics Session",
      time: "8 PM - 9:30 PM",
      counsellor: {
        name: "Dr. Patel",
        avatar: "/images/person.png",
        isOnline: false,
      },
    },
  ],
  pendingFeedback: [
    {
      id: "1",
      name: "Math Session 1",
      time: "7 AM - 8:30 AM",
      instructor: "Haseeb",
      description: "Advanced calculus and trigonometry concepts discussion",
      maxCharLimit: 400,
    },
    {
      id: "2",
      name: "Nextjs Session 2",
      time: "7 AM - 8:30 AM",
      instructor: "Abdullah",
      description: "Full-stack application development with Next.js",
      maxCharLimit: 400,
    },
  ],
  recentPackages: [
    {
      id: "pkg1",
      counsellor: {
        name: "Jennifer Markus",
        avatar: "/images/person.png",
        isOnline: true,
      },
      title: "5 Essay Review Sessions for $200",
      description: "Includes two 30-minute brainstorming session...",
      amount: 15,
    },
    {
      id: "pkg2",
      counsellor: {
        name: "Jennifer Markus",
        avatar: "/images/person.png",
        isOnline: true,
      },
      title: "5 Essay Review Sessions for $200",
      description: "Includes two 30-minute brainstorming session...",
      amount: 15,
    },
  ],
};

export interface PaymentStats {
  totalAmount: number;
  totalSessions: number;
  totalHours: number;
}

export interface PaymentHistory {
  id: string;
  date: string;
  amount: number;
  status: "completed" | "pending" | "failed";
  paymentMethod: string;
  counsellor: {
    name: string;
    avatar: string;
  };
  sessionDetails: {
    title: string;
    date: string;
    time: string;
  };
}

export interface UpcomingPayment {
  id: string;
  dueDate: string;
  amount: number;
  status: "due" | "overdue";
  counsellor: {
    name: string;
    avatar: string;
  };
  sessionDetails: {
    title: string;
    date: string;
    time: string;
  };
}

export interface BankInfo {
  primary: {
    bankName: string;
    accountNumber: string;
  };
  secondary?: {
    bankName: string;
    accountNumber: string;
  };
}

export interface SessionPayment {
  id: string;
  counsellor: {
    name: string;
    email: string;
    avatar: string;
  };
  date: string;
  status: "pending" | "completed" | "rejected";
  serviceName: string;
  amount: number;
}

export const paymentsData = {
  stats: {
    totalAmount: 2400,
    totalSessions: 24,
    totalHours: 10,
  },
  bankInfo: {
    primary: {
      bankName: "Bank of London",
      accountNumber: "3490",
    },
    secondary: undefined,
  },
  history: [
    {
      id: "1",
      date: "Mar 12, 2024",
      amount: 150,
      status: "completed" as const,
      paymentMethod: "Credit Card",
      counsellor: {
        name: "Dr. Smith",
        avatar: "/images/person.png",
      },
      sessionDetails: {
        title: "Math Session",
        date: "Mar 10, 2024",
        time: "10:00 AM",
      },
    },
    // Add more payment history items...
  ],
  upcomingPayments: [
    {
      id: "1",
      dueDate: "Mar 20, 2024",
      amount: 200,
      status: "due" as const,
      counsellor: {
        name: "Jennifer Marcus",
        avatar: "/images/person.png",
      },
      sessionDetails: {
        title: "Physics Session",
        date: "Mar 18, 2024",
        time: "2:00 PM",
      },
    },
    // Add more upcoming payments...
  ],
  sessions: [
    {
      id: "1",
      counsellor: {
        name: "Alis Elsvin",
        email: "<EMAIL>",
        avatar: "/images/person.png",
      },
      date: "72/4/27",
      status: "pending" as const,
      serviceName: "Essay review",
      amount: 120,
    },
    {
      id: "2",
      counsellor: {
        name: "Jennifer Marcus",
        email: "<EMAIL>",
        avatar: "/images/person.png",
      },
      date: "40/8/20",
      status: "completed" as const,
      serviceName: "Essay review",
      amount: 120,
    },
    {
      id: "3",
      counsellor: {
        name: "David Smith",
        email: "<EMAIL>",
        avatar: "/images/person.png",
      },
      date: "12/08/69",
      status: "rejected" as const,
      serviceName: "Essay review",
      amount: 120,
    },
    {
      id: "4",
      counsellor: {
        name: "Sarah Johnson",
        email: "<EMAIL>",
        avatar: "/images/person.png",
      },
      date: "6/12/70",
      status: "completed" as const,
      serviceName: "Essay review",
      amount: 120,
    },
    {
      id: "5",
      counsellor: {
        name: "Michael Brown",
        email: "<EMAIL>",
        avatar: "/images/person.png",
      },
      date: "27/09/07",
      status: "completed" as const,
      serviceName: "Essay review",
      amount: 120,
    },
    {
      id: "6",
      counsellor: {
        name: "Emma Wilson",
        email: "<EMAIL>",
        avatar: "/images/person.png",
      },
      date: "18-06-23",
      status: "completed" as const,
      serviceName: "Essay review",
      amount: 120,
    },
    {
      id: "7",
      counsellor: {
        name: "James Taylor",
        email: "<EMAIL>",
        avatar: "/images/person.png",
      },
      date: "8/9/76",
      status: "completed" as const,
      serviceName: "Essay review",
      amount: 120,
    },
    {
      id: "8",
      counsellor: {
        name: "Olivia Davis",
        email: "<EMAIL>",
        avatar: "/images/person.png",
      },
      date: "12-02-54",
      status: "completed" as const,
      serviceName: "Essay review",
      amount: 120,
    },
    {
      id: "9",
      counsellor: {
        name: "William Moore",
        email: "<EMAIL>",
        avatar: "/images/person.png",
      },
      date: "22/08/72",
      status: "completed" as const,
      serviceName: "Essay review",
      amount: 120,
    },
    {
      id: "10",
      counsellor: {
        name: "Sophie Clark",
        email: "<EMAIL>",
        avatar: "/images/person.png",
      },
      date: "72/5/7",
      status: "completed" as const,
      serviceName: "Essay review",
      amount: 120,
    },
    {
      id: "11",
      counsellor: {
        name: "Daniel Lee",
        email: "<EMAIL>",
        avatar: "/images/person.png",
      },
      date: "15/07/24",
      status: "pending" as const,
      serviceName: "Essay review",
      amount: 120,
    },
    {
      id: "12",
      counsellor: {
        name: "Isabella White",
        email: "<EMAIL>",
        avatar: "/images/person.png",
      },
      date: "30/11/24",
      status: "pending" as const,
      serviceName: "Essay review",
      amount: 120,
    },
  ],
};
