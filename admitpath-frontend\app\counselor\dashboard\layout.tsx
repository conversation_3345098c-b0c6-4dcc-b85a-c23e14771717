"use client";

import { useState, useEffect, useRef } from "react";
import { useProfile } from "@/app/hooks/counselor/useProfile";
import { usePathname } from "next/navigation";
import useClickOutside from "@/app/hooks/student/useClickOutside";
import Sidebar from "@/app/components/counselor/Sidebar";
import HamburgerMenu from "@/app/components/counselor/HamburgerMenu";
import Overlay from "@/app/components/counselor/Overlay";
import Header from "@/app/components/counselor/Header";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const { getUserInfo, userInfo } = useProfile();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useClickOutside(dropdownRef, () => setIsDropdownOpen(false));

  useEffect(() => {
    getUserInfo();
  }, []);

  const isActive = (url: string) =>
    pathname === url ||
    (url !== "/counselor/dashboard" && pathname.startsWith(url));

  return (
    <div className="bg-[#F8F8F8] flex min-h-screen pb-3">
      <Sidebar
        isSidebarOpen={isSidebarOpen}
        setIsSidebarOpen={setIsSidebarOpen}
        isActive={isActive}
      />
      <HamburgerMenu toggleSidebar={() => setIsSidebarOpen(!isSidebarOpen)} />
      <Overlay
        isVisible={isSidebarOpen}
        onClick={() => setIsSidebarOpen(false)}
      />
      <main className="w-full md:pl-[95px] lg:pl-[18%] pb-7 px-4 md:px-7">
        <Header
          userInfo={userInfo}
          isDropdownOpen={isDropdownOpen}
          toggleDropdown={() => setIsDropdownOpen(!isDropdownOpen)}
          dropdownRef={dropdownRef}
        />
        {children}
      </main>
    </div>
  );
}
