"use client";

import { useEffect } from "react";
import { useServices } from "@/app/hooks/counselor/useService";
import { useProfile } from "@/app/hooks/counselor/useProfile";
import { MainButton } from "@/app/components/common/mainBtn";
import StyledIcon from "@/app/components/common/styledIcon";
import ThreeDotIconDropdown from "@/app/components/common/threeDotIconMenu";
import { faBoxOpen } from "@fortawesome/free-solid-svg-icons";
import AddServicePopup from "@/app/components/counselor/profile/view/popup/AddServicePopup";
import EditServicePopup from "@/app/components/counselor/profile/view/popup/EditServicePopup";
import DeleteServicePopup from "@/app/components/counselor/profile/view/popup/DeleteServicePopup";
import { useState } from "react";

export default function ServicesPage() {
  const {
    services,
    loading,
    error,
    fetchServices,
    fetchSingleService,
    deleteService,
    selectedService,
  } = useServices();
  const { userInfo } = useProfile();

  const [addServicePopup, setAddServicePopup] = useState(false);
  const [editServicePopup, setEditServicePopup] = useState(false);
  const [deleteServicePopup, setDeleteServicePopup] = useState(false);

  useEffect(() => {
    if (userInfo?.counselor_id) {
      fetchServices(userInfo.counselor_id);
    }
  }, [userInfo?.counselor_id]);

  ////////////// function handles update service items
  const handleEditService = async (serviceId: number) => {
    await fetchSingleService(serviceId);
    setEditServicePopup(true);
  };

  ////////////// function handles delete service items
  const handleDeleteService = async (serviceId: number) => {
    await fetchSingleService(serviceId);
    setDeleteServicePopup(true);
  };

  return (
    <div>
      <AddServicePopup
        isPopupOpen={addServicePopup}
        setIsPopupOpen={setAddServicePopup}
      />
      <EditServicePopup
        isPopupOpen={editServicePopup}
        setIsPopupOpen={setEditServicePopup}
        serviceData={selectedService}
      />
      <DeleteServicePopup
        isPopupOpen={deleteServicePopup}
        setIsPopupOpen={setDeleteServicePopup}
        serviceData={selectedService}
        onSave={async () => {
          if (selectedService) {
            await deleteService(selectedService?.id);
            setDeleteServicePopup(false);
            fetchServices(userInfo?.counselor_id);
          }
        }}
      />

      <div className="bg-white rounded-xl p-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold">Services</h1>
          <MainButton
            variant="neutral"
            onClick={() => setAddServicePopup(true)}
          >
            Add service
          </MainButton>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-red-500 text-center py-4">
            {error}
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && services.length === 0 && (
          <div className="text-center py-8 text-neutral5">
            <p>No services found. Click "Add service" to create your first service.</p>
          </div>
        )}

        {/* Services Cards */}
        {!loading && !error && services.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {services.map((service) => (
              <div
                key={service.id}
                className="border p-4 rounded-xl flex flex-col justify-between bg-white shadow-sm hover:shadow-md transition-shadow"
              >
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <StyledIcon
                      icon={faBoxOpen}
                      bgColor="#F2C94C33"
                      color="#F2C94C"
                    />

                    <ThreeDotIconDropdown
                      handleEdit={() => handleEditService(service.id)}
                      handleRemove={() => handleDeleteService(service.id)}
                    />
                  </div>
                  <h3 className="text-neutral8 font-semibold mb-2">
                    {service.service_type}
                  </h3>
                  <p className="text-neutral5 text-sm mb-2">
                    {service.description}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-neutral5 mb-1">Price</p>
                  <p className="text-lg font-bold mb-4 text-neutral8">
                    ${service.price}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
