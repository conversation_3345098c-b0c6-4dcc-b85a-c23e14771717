"use client";

import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useProfile } from "@/app/hooks/counselor/useProfile";

export default function CounselorPage({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { userInfo, getUserInfo } = useProfile();

  useEffect(() => {
    if (!localStorage.getItem("access_token")) {
      router.replace("/auth/login");
      return;
    }
    getUserInfo();
  }, [getUserInfo]);

  useEffect(() => {
    if (userInfo) {
      const { isProfileComplete, is_verified } = userInfo;

      // If not a counselor, redirect to student dashboard
      if (userInfo.userType !== "counselor") {
        router.replace("/student/dashboard");
        return;
      }

      const isOnboardingPath = pathname?.startsWith("/counselor/onboarding");
      const isDashboardPath = pathname?.startsWith("/counselor/dashboard");

      // If profile is not complete, only allow access to onboarding
      if (!isProfileComplete && !isOnboardingPath) {
        router.replace("/counselor/onboarding");
        return;
      }

      // If profile is complete but not verified, show completion page
      if (
        isProfileComplete &&
        !is_verified &&
        (isDashboardPath || isOnboardingPath)
      ) {
        router.replace("/counselor/profile-complete");
        return;
      }

      // If profile is complete and verified, don't allow access to onboarding
      if (isProfileComplete && is_verified && isOnboardingPath) {
        router.replace("/counselor/dashboard");
        return;
      }
    }
  }, [userInfo, router, pathname]);

  return children;
}
