@tailwind base;
@tailwind components;
@tailwind utilities;

@keyframes scroll {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

@layer utilities {
    .pause-animation {
        animation-play-state: paused;
    }

    .scrollbar-hide {
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }
    .scrollbar-hide::-webkit-scrollbar {
        display: none; /* Chrome, Safari and Opera */
    }
}

html,
body {
    /* height: 100%; */
}

/* width */
.popupz::-webkit-scrollbar {
    width: 10px;
}

/* Track */
.popupz::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 8px;
}

/* Handle */
.popupz::-webkit-scrollbar-thumb {
    background: #998989;
    border-radius: 8px;
}

/* Handle on hover */
.popupz::-webkit-scrollbar-thumb:hover {
    background: #5e5858;
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 0 0% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 3.9%;
        --primary: 0 0% 9%;
        --primary-foreground: 0 0% 98%;
        --secondary: 0 0% 96.1%;
        --secondary-foreground: 0 0% 9%;
        --muted: 0 0% 96.1%;
        --muted-foreground: 0 0% 45.1%;
        --accent: 0 0% 96.1%;
        --accent-foreground: 0 0% 9%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 89.8%;
        --input: 0 0% 89.8%;
        --ring: 0 0% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
    }
    .dark {
        --background: 0 0% 3.9%;
        --foreground: 0 0% 98%;
        --card: 0 0% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 0 0% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 0 0% 9%;
        --secondary: 0 0% 14.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 0 0% 14.9%;
        --muted-foreground: 0 0% 63.9%;
        --accent: 0 0% 14.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 14.9%;
        --input: 0 0% 14.9%;
        --ring: 0 0% 83.1%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
    }
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }
    body {
        @apply bg-background text-foreground;
    }
}

/* Montserrat Font */

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-ExtraLight.woff2") format("woff2");
    font-weight: 200;
    font-style: normal;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-ExtraLightItalic.woff2") format("woff2");
    font-weight: 200;
    font-style: italic;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-Light.woff2") format("woff2");
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-LightItalic.woff2") format("woff2");
    font-weight: 300;
    font-style: italic;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-Regular.woff2") format("woff2");
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-Italic.woff2") format("woff2");
    font-weight: 400;
    font-style: italic;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-Medium.woff2") format("woff2");
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-MediumItalic.woff2") format("woff2");
    font-weight: 500;
    font-style: italic;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-SemiBold.woff2") format("woff2");
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-SemiBoldItalic.woff2") format("woff2");
    font-weight: 600;
    font-style: italic;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-Bold.woff2") format("woff2");
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-BoldItalic.woff2") format("woff2");
    font-weight: 700;
    font-style: italic;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-ExtraBold.woff2") format("woff2");
    font-weight: 800;
    font-style: normal;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-ExtraBoldItalic.woff2") format("woff2");
    font-weight: 800;
    font-style: italic;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-Black.woff2") format("woff2");
    font-weight: 900;
    font-style: normal;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-BlackItalic.woff2") format("woff2");
    font-weight: 900;
    font-style: italic;
}

/* Variable weight fonts */
@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat[wght].woff2") format("woff2");
    font-weight: 100 900;
    font-style: normal;
}

@font-face {
    font-family: "Montserrat";
    src: url("/fonts/Montserrat/Montserrat-Italic[wght].woff2") format("woff2");
    font-weight: 100 900;
    font-style: italic;
}

/* ClashDisplay Font */

@font-face {
    font-family: "ClashDisplay";
    src: url("/fonts/ClashDisplay/ClashDisplay-ExtraLight.woff2") format("woff2");
    font-weight: 200;
    font-style: normal;
}

@font-face {
    font-family: "ClashDisplay";
    src: url("/fonts/ClashDisplay/ClashDisplay-Light.woff2") format("woff2");
    font-weight: 300;
    font-style: normal;
}

@font-face {
    font-family: "ClashDisplay";
    src: url("/fonts/ClashDisplay/ClashDisplay-Regular.woff2") format("woff2");
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: "ClashDisplay";
    src: url("/fonts/ClashDisplay/ClashDisplay-Medium.woff2") format("woff2");
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: "ClashDisplay";
    src: url("/fonts/ClashDisplay/ClashDisplay-SemiBold.woff2") format("woff2");
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: "ClashDisplay";
    src: url("/fonts/ClashDisplay/ClashDisplay-Bold.woff2") format("woff2");
    font-weight: 700;
    font-style: normal;
}

@font-face {
    font-family: "ClashDisplay";
    src: url("/fonts/ClashDisplay/ClashDisplay-ExtraBold.woff2") format("woff2");
    font-weight: 800;
    font-style: normal;
}


/* Variable weight fonts */
@font-face {
    font-family: "ClashDisplay";
    src: url("/fonts/ClashDisplay/ClashDisplay-Variable.woff2") format("woff2");
    font-weight: 100 900;
    font-style: normal;
}