import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import {
    AvailabilityCreate,
    AvailabilityResponse,
} from "@/app/types/counselor/availability";
import { toast } from "react-toastify";

interface AvailabilityState {
    loading: boolean;
    status: "Loading" | "Confirm Availability" | "Saving" | "Deleting";
    error: string | null;
    availabilities: AvailabilityResponse[];
    timeSlots: string[]; // Changed to string array
    availableDates: string[];
    fetchAvailabilities: (timezone: string) => Promise<void>;
    createAvailability: (
        data: AvailabilityCreate,
        timezone: string
    ) => Promise<void>;
    updateAvailability: (id: number, data: AvailabilityCreate) => Promise<void>;
    deleteAvailability: (id: number) => Promise<void>;
    clearError: () => void;
}

export const useAvailability = create<AvailabilityState>((set) => ({
    loading: false,
    status: "Loading",
    error: null,
    availabilities: [],
    timeSlots: [],
    availableDates: [],

    fetchAvailabilities: async (timezone) => {
        set({ loading: true, status: "Loading" });
        try {
            const response = await apiClient.get<AvailabilityResponse[]>(
                "/counselor/availability",
                { params: { timezone } }
            );
            console.log(response.data);
            set({
                availabilities: response.data,
                loading: false,
                status: "Confirm Availability",
            });
        } catch (error) {
            set({
                error: "Failed to fetch availability",
                loading: false,
                status: "Confirm Availability",
            });
            throw error;
        }
    },

    createAvailability: async (data, timezone) => {
        set({ loading: true, status: "Saving" });
        try {
            console.log("Saving availability data:", JSON.stringify(data));
            console.log("Using timezone:", timezone);
            
            const response = await apiClient.post("/counselor/availability", data, {
                params: { timezone },
            });
            
            console.log("Save response:", response.data);
            set({ loading: false, status: "Confirm Availability" });
        } catch (error: any) {
            console.error("Save error:", error.response?.data);
            const message =
                error.response?.data?.detail?.[0]?.msg || "Creation failed";
            toast.error(message);
            set({
                error: message,
                loading: false,
                status: "Confirm Availability",
            });
            throw error;
        }
    },

    updateAvailability: async (id, data) => {
        set({ loading: true, status: "Saving" });
        try {
            console.log(`Updating availability ID ${id} with data:`, JSON.stringify(data));
            console.log("Using timezone:", data.timezone);
            
            const response = await apiClient.put(`/counselor/availability/${id}`, data, {
                params: { timezone: data.timezone },
            });
            
            console.log("Update response:", response.data);
            set({ loading: false, status: "Confirm Availability" });
        } catch (error: any) {
            console.error("Update error:", error.response?.data);
            const message = error.response?.data?.detail || "Update failed";
            toast.error(message);
            set({
                error: "Update failed",
                loading: false,
                status: "Confirm Availability",
            });
            throw error;
        }
    },

    deleteAvailability: async (id) => {
        set({ loading: true, status: "Deleting" });
        try {
            await apiClient.delete(`/counselor/availability/${id}`);
            set({ loading: false, status: "Confirm Availability" });
        } catch (error) {
            set({
                error: "Deletion failed",
                loading: false,
                status: "Confirm Availability",
            });
            throw error;
        }
    },

    clearError: () => set({ error: null }),
}));
