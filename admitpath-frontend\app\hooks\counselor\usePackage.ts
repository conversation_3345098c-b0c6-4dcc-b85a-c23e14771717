import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import { toast } from "react-toastify";
import { PackageData, PackagePayload, PackageSession, PackageSubscription, PackagesState } from "@/app/types/counselor/package";

export const usePackages = create<PackagesState>((set, get) => ({
  packages: { total: 0, items: [] },
  selectedPackage: null,
  subscriptions: [],
  activeSubscriptions: [],
  completedSubscriptions: [],
  loading: false,
  error: null,

  fetchPackages: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get("/counselor/packages");
      set({ packages: response.data });
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || "Error fetching packages";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  fetchSinglePackage: async (packageId) => {
    try {
      // Don't set global loading state, just fetch the package
      const response = await apiClient.get<PackageData>(`/counselor/packages/package/${packageId}`);
      set({ selectedPackage: response.data });
      return response.data;
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || "Error fetching package";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    }
  },

  fetchSubscriptions: async (status = "active") => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get<PackageSubscription[]>(
        `/counselor/packages/subscriptions?status=${status}`
      );

      // Store subscriptions in the appropriate state based on status
      if (status === "active") {
        set({
          subscriptions: response.data,
          activeSubscriptions: response.data,
          loading: false
        });
      } else if (status === "completed") {
        set({
          completedSubscriptions: response.data,
          loading: false
        });
      } else {
        set({ subscriptions: response.data, loading: false });
      }
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || "Error fetching package subscriptions";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  addPackage: async (packageData: PackagePayload) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.post<PackageData>("/counselor/packages", packageData);
      toast.success("Package added successfully!");

      set((state) => ({
        packages: {
          total: state.packages.total + 1,
          items: [...state.packages.items, response.data],
        },
      }));
      return response.data;
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to add the package. Please try again.";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  updatePackage: async (packageId, packageData) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.put<PackageData>(`/counselor/packages/package/${packageId}`, packageData);
      toast.success("Package updated successfully!");

      // Update the package in the state
      set((state) => ({
        packages: {
          total: state.packages.total,
          items: state.packages.items.map((pack) =>
            pack.id === packageId ? response.data : pack
          ),
        },
        // If the selected package is the one being updated, update it too
        selectedPackage: state.selectedPackage?.id === packageId ? response.data : state.selectedPackage,
      }));
      return response.data;
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || "Error updating package";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  deletePackage: async (packageId) => {
    try {
      set({ loading: true, error: null });
      await apiClient.delete(`/counselor/packages/package/${packageId}?permanent=true`);
      toast.success("Package deleted successfully!");

      set((state) => ({
        packages: {
          total: state.packages.total - 1,
          items: state.packages.items.filter((pack) => pack.id !== packageId),
        },
        // If the selected package is the one being deleted, clear it
        selectedPackage: state.selectedPackage?.id === packageId ? null : state.selectedPackage,
      }));
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || "Error deleting package";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  createPackageSession: async (subscriptionId, sessionData) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.post<PackageSession>(
        `/counselor/packages/subscription/${subscriptionId}/sessions`,
        sessionData
      );
      toast.success("Package session created successfully!");

      // Update the subscription in the state
      set((state) => {
        const updatedSubscriptions = state.subscriptions.map((subscription) => {
          if (subscription.id === subscriptionId) {
            return {
              ...subscription,
              sessions: [...subscription.sessions, response.data],
            };
          }
          return subscription;
        });

        return { subscriptions: updatedSubscriptions };
      });

      return response.data;
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || "Error creating package session";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  clearError: () => set({ error: null }),
}));
