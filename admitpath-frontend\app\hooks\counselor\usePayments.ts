"use client";

import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import { toast } from "react-toastify";
import { BankDetail, Paymentstate } from "@/app/types/counselor/payments";

// Hook for managing bank details
export const usePayments = create<Paymentstate>((set, get) => ({
  loading: false,
  error: null,
  bankAccounts: null,

  fetchBankAccounts: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get("/counselor/bank-details/accounts");
      set({ bankAccounts: response.data.items, loading: false });
    } catch (error: any) {
      set({
        error: error.response?.data?.detail || "Failed to fetch bank accounts",
        loading: false,
      });
    }
  },

  createBankAccount: async (data: BankDetail) => {
    const loadingToast = toast.loading("Adding account...");
    try {
      set({ loading: true, error: null });
      const response = await apiClient.post("/counselor/bank-details", data);
      set((state) => ({
        bankAccounts: state.bankAccounts
          ? [...state.bankAccounts, response.data]
          : [response.data],
      }));
      toast.dismiss(loadingToast);
      toast.success("Bank account added successfully");
    } catch (error: any) {
      toast.dismiss(loadingToast);
      set({
        error: error.response?.data?.detail || "Failed to add bank account",
      });
      toast.error("Failed to add bank account");
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  updateBankAccount: async (id: number, data: Partial<BankDetail>) => {
    const loadingToast = toast.loading("Updating account...");
    try {
      set({ loading: true, error: null });
      const response = await apiClient.put(
        `/counselor/bank-details/${id}`,
        data
      );
      set((state) => ({
        bankAccounts:
          state.bankAccounts?.map((account) =>
            account.id === id ? { ...account, ...response.data } : account
          ) || null,
      }));
      toast.dismiss(loadingToast);
      toast.success("Bank account updated successfully");
    } catch (error: any) {
      toast.dismiss(loadingToast);
      set({
        error: error.response?.data?.detail || "Failed to update bank account",
      });
      toast.error("Failed to update bank account");
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  deleteBankAccount: async (id: number) => {
    const loadingToast = toast.loading("Deleting account...");
    try {
      set({ loading: true, error: null });
      await apiClient.delete(`/counselor/bank-details/${id}`);
      set((state) => ({
        bankAccounts:
          state.bankAccounts?.filter((account) => account.id !== id) || null,
      }));
      toast.dismiss(loadingToast);
      toast.success("Bank account deleted successfully");
    } catch (error: any) {
      toast.dismiss(loadingToast);
      set({
        error: error.response?.data?.detail || "Failed to delete bank account",
      });
      toast.error("Failed to delete bank account");
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  clearError: () => set({ error: null }),
}));
