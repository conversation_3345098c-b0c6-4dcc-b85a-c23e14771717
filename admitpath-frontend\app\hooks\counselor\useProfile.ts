"use client";

import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import { ProfileState } from "@/app/types/counselor/profile";

export const useProfile = create<ProfileState>((set, get) => ({
  loading: false,
  error: null,
  userInfo: null,
  personalInfo: null,
  educationInfo: null,
  experienceInfo: null,
  documentInfo: null,
  profilePicture: null,

  getUserInfo: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get(
        `/user/me?timezone=${Intl.DateTimeFormat().resolvedOptions().timeZone}`
      );
      set({ userInfo: response.data });
    } catch (error: any) {
      set({ error: error.response?.data?.detail || "Something went wrong" });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  getPersonalInfo: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get("/counselor/profile/personal-info");
      set({ personalInfo: response.data });
    } catch (error: any) {
      set({ error: error.response?.data?.detail || "Something went wrong" });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  getEducationInfo: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get("/counselor/profile/education");
      set({ educationInfo: response.data });
    } catch (error: any) {
      set({ error: error.response?.data?.detail || "Something went wrong" });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  getExperienceInfo: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get(
        "/counselor/profile/professional-experience"
      );
      set({ experienceInfo: response.data });
    } catch (error: any) {
      set({ error: error.response?.data?.detail || "Something went wrong" });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  getDocumentInfo: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get("/counselor/profile/documents");
      set({ documentInfo: response.data });
    } catch (error: any) {
      set({ error: error.response?.data?.detail || "Something went wrong" });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  // Upload Profile Picture
  uploadProfilePicture: async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files ? e.target.files[0] : null;
    const validFiles = ["image/png", "image/jpeg", "image/jpg", "image/webp"];

    if (!file) {
      alert("No image selected.");
      return;
    }

    if (!validFiles.includes(file.type)) {
      alert(
        "Invalid file type. Please upload an image in png, jpeg, jpg, or webp format."
      );
      return;
    }

    const formData = new FormData();
    formData.append("file", file);

    try {
      const response = await apiClient.post("/user/profile-picture", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      set({ profilePicture: response.data.profile_picture_url });
    } catch (error: any) {
      set({ error: error.response?.data?.detail || "Something went wrong" });
      throw error;
    }
  },

  deleteProfilePicture: async () => {
    try {
      // The correct endpoint should be the same as the backend's configured route
      await apiClient.delete("/user/profile-picture");
      // After deletion, refetch user info to update the UI
      const { getUserInfo } = get();
      await getUserInfo();
      set({ profilePicture: null });
    } catch (error: any) {
      console.error("Error deleting profile picture:", error);
      set({ error: error.response?.data?.detail || "Something went wrong" });
      throw error;
    }
  },

  clearError: () => set({ error: null }),
}));
