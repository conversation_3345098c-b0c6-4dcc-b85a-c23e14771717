import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import { toast } from "react-toastify";
import { ServicesState } from "@/app/types/counselor/service";

export const useServices = create<ServicesState>((set) => ({
  services: [],
  selectedService: null,
  loading: false,
  error: null,

  fetchServices: async (id) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get(`/counselor/services-offered/${id}`);
      const fetchedServices = response.data.map((service: any) => ({
        ...service,
        service_type: service.custom_type || service.service_type,
      }));
      set({ services: fetchedServices });
    } catch (error) {
      set({ error: "Error fetching services" });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  fetchSingleService: async (serviceId) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get(
        `/counselor/services-offered/service/${serviceId}`
      );
      set({ selectedService: response.data });
    } catch (error) {
      set({ error: "Error fetching single service" });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  addService: async (serviceData) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.post(
        "/counselor/services-offered/",
        serviceData
      );
      toast.success("Service added successfully!");
      set((state) => ({ services: [...state.services, response.data] }));
    } catch (error: any) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to add the service. Please try again.";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  updateService: async (serviceId, serviceData) => {
    try {
      set({ loading: true, error: null });
      await apiClient.put(
        `/counselor/services-offered/${serviceId}`,
        serviceData
      );
      toast.success("Service updated successfully!");
      set((state) => ({
        services: state.services.map((service) =>
          service.id === serviceId ? { ...service, ...serviceData } : service
        ),
      }));
    } catch (error) {
      set({ error: "Error updating service" });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  deleteService: async (serviceId) => {
    try {
      set({ loading: true, error: null });
      await apiClient.delete(`/counselor/services-offered/${serviceId}`);
      toast.success("Service deleted successfully!");
      set((state) => ({
        services: state.services.filter((service) => service.id !== serviceId),
      }));
    } catch (error) {
      set({ error: "Error deleting service" });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  clearError: () => set({ error: null }),
}));
