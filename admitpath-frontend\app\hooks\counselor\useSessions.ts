"use client";

import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import {
  NotesPayload,
  SessionsState,
  Session,
} from "@/app/types/counselor/sessions";
import { toast } from "react-toastify";

export const useSessions = create<SessionsState>((set, get) => ({
  loading: false,
  error: null,
  sessions: null,
  currentSession: null,
  currentNotes: null,
  studentsFeedback: null,

  fetchSessions: async (timezone, params) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get(`/counselor/counseling-sessions?timezone=${timezone}`, {
        params,
      });
      set({
        sessions: {
          items: response.data.items,
          total: response.data.total,
        },
      });
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.detail || "Failed to fetch sessions";
      set({ error: errorMessage });
      toast.error(errorMessage);
    } finally {
      set({ loading: false });
    }
  },

  createSession: async (sessionData) => {
    set({ loading: true, error: null });
    try {
      const response = await apiClient.post(
        "/counselor/counseling-sessions",
        sessionData
      );
      set((state) => ({
        sessions: state.sessions
          ? {
              total: state.sessions.total + 1,
              items: [...state.sessions?.items, response.data],
            }
          : null,
      }));
      toast.success("Session added successfully.");
      return { success: true, data: response.data };
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.detail || "Failed to create session";
      set({ error: errorMessage });
      toast.error(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      set({ loading: false });
    }
  },

  getSingleSession: async (sessionId: number, timezone: string) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get(
        `/counselor/counseling-sessions/${sessionId}?timezone=${timezone}`
      );
      set({ currentSession: response.data });
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.detail || "Failed to fetch single session";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  updateSession: async (sessionId: number, updatedData: Partial<Session>, timezone:string) => {
    try {
      set({ loading: true, error: null });

      // Get the current session to preserve the event_name
      const currentSession = get().sessions?.items.find(session => session.id === sessionId);

      // Ensure we're not changing the event_name
      const dataToSend = {
        ...updatedData,
        event_name: currentSession?.event_name || updatedData.event_name
      };

      const response = await apiClient.put(
        `/counselor/counseling-sessions/${sessionId}?timezone=${timezone}`,
        dataToSend
      );
      const updatedSession = response.data;
      set((state) => ({
        sessions: state.sessions
          ? {
              ...state.sessions,
              items: state.sessions.items.map((session) =>
                session.id === sessionId ? updatedSession : session
              ),
            }
          : null,
      }));
      toast.success("Session updated successfully.");
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.detail || "Failed to update session";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  deleteSession: async (sessionId: number) => {
    try {
      set({ loading: true, error: null });
      await apiClient.delete(`/counselor/counseling-sessions/${sessionId}`);
      set((state) => ({
        sessions: state.sessions
          ? {
              total: state.sessions.total - 1,
              items: state.sessions.items.filter(
                (session) => session.id !== sessionId
              ),
            }
          : null,
      }));
      toast.success("Session cancelled successfully.");
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.detail || "Failed to delete session";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  fetchSessionNotes: async (sessionId: number) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get(
        `/counselor/counseling-sessions/${sessionId}/notes`
      );
      set({ currentNotes: response.data });
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.detail || "Failed to fetch session notes";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  createSessionNotes: async (sessionId: number, data: NotesPayload) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.post(
        `/counselor/counseling-sessions/${sessionId}/notes`,
        data
      );
      set({ currentNotes: response.data });
      toast.success("Notes added successfully.");
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.detail || "Failed to create session notes";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  updateSessionNotes: async (sessionId: number, data: NotesPayload) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.put(
        `/counselor/counseling-sessions/${sessionId}/notes`,
        data
      );
      set({ currentNotes: response.data });
      toast.success("Notes updated successfully.");
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.detail || "Failed to update session notes";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  deleteSessionNotes: async (sessionId: number) => {
    try {
      set({ loading: true, error: null });
      await apiClient.delete(`/sessions/${sessionId}/notes`);
      set({ currentNotes: null });
      toast.success("Notes deleted successfully.");
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.detail || "Failed to delete session notes";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  getStudentsFeedback: async (counselorId: number) => {
    try {
      const response = await apiClient.get(
        `/counselors/${counselorId}/reviews`
      );
      set({ studentsFeedback: response.data });
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.detail || "Failed to fetch students feedback";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  clearError: () => set({ error: null }),
}));
