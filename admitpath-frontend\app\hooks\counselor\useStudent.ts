"use client";

import { create } from "zustand";
import apiClient from "@/lib/apiClient";

interface StudentProfile {
  student_id: number;
  user_id: number;
  first_name: string;
  last_name: string;
}

interface StudentState {
  loading: boolean;
  error: string | null;
  students: { [key: number]: StudentProfile };
  getStudentInfo: (student_id: number) => Promise<StudentProfile | null>;
  clearError: () => void;
}

export const useStudent = create<StudentState>((set, get) => ({
  loading: false,
  error: null,
  students: {},

  getStudentInfo: async (student_id: number) => {
    // Check if already cached
    const cachedStudent = get().students[student_id];
    if (cachedStudent) {
      return cachedStudent;
    }

    try {
      set({ loading: true, error: null });

      const response = await apiClient.get(
        `/student/profile/by-student-id/${student_id}`
      );
      const studentProfile = response.data;

      set((state) => ({
        students: {
          ...state.students,
          [student_id]: studentProfile,
        },
        loading: false,
      }));

      return studentProfile;
    } catch (error: any) {
      set({
        error: error.response?.data?.detail || "Failed to fetch student info",
        loading: false,
      });
      return null;
    }
  },

  clearError: () => set({ error: null }),
}));
