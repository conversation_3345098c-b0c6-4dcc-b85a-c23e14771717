import { create } from "zustand";
import { toast } from "react-toastify";
import apiClient from "@/lib/apiClient";

interface ContactMessage {
  email: string;
  service: string;
  message: string;
}

interface ContactResponse {
  id: number;
  email: string;
  service: string;
  message: string;
  created_at: string;
  is_addressed: boolean;
  addressed_at: string | null;
}

interface ContactState {
  loading: boolean;
  error: string | null;
  lastSubmission: ContactResponse | null;
  submitContact: (data: ContactMessage) => Promise<void>;
  clearError: () => void;
}

export const useContact = create<ContactState>((set) => ({
  loading: false,
  error: null,
  lastSubmission: null,

  submitContact: async (data: ContactMessage) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.post<ContactResponse>("/contact", data);

      set({
        lastSubmission: response.data,
        error: null,
      });

      toast.success("Message sent successfully!");
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.detail || "Failed to send message";
      set({
        error: errorMessage,
        lastSubmission: null,
      });

      toast.error("Failed to send message");

      throw error;
    } finally {
      set({ loading: false });
    }
  },

  clearError: () => set({ error: null }),
}));
