import { create } from "zustand";
import apiClient from "@/lib/apiClient";

export interface ReviewResponse {
  id: number;
  student_name: string;
  student_profile_picture?: string;
  rating: number;
  received_expected_service: boolean;
  comment: string;
  additional_feedback?: string;
  created_at: string; // ISO date string
}

export interface CounselorReviewsResponse {
  average_rating: number;
  total_reviews: number;
  reviews: ReviewResponse[];
  page: number;
  page_size: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

interface CounselorReviewsState {
  reviewsData: CounselorReviewsResponse | null;

  isLoading: boolean;
  error: string | null;

  fetchReviews: (
    counselorId: number,
    page?: number,
    pageSize?: number
  ) => Promise<void>;
  setPage: (page: number) => Promise<void>;
  reset: () => void;

  currentCounselorId: number | null;
}

export const useCounselorReviews = create<CounselorReviewsState>(
  (set, get) => ({
    // Initial state
    reviewsData: null,
    isLoading: false,
    error: null,
    currentCounselorId: null,

    // Actions
    fetchReviews: async (counselorId, page = 1, pageSize = 10) => {
      set({ isLoading: true, error: null, currentCounselorId: counselorId });

      try {
        const response = await apiClient.get<CounselorReviewsResponse>(
          `/counselors/${counselorId}/reviews`,
          { params: { page, page_size: pageSize } }
        );

        set({
          reviewsData: response.data,
          isLoading: false,
        });
      } catch (error) {
        set({
          error:
            error instanceof Error ? error.message : "Failed to fetch reviews",
          isLoading: false,
        });
      }
    },

    setPage: async (page) => {
      const { currentCounselorId, reviewsData } = get();
      if (!currentCounselorId || !reviewsData) return;

      await get().fetchReviews(currentCounselorId, page, reviewsData.page_size);
    },

    reset: () => {
      set({
        reviewsData: null,
        isLoading: false,
        error: null,
        currentCounselorId: null,
      });
    },
  })
);
