"use client";

import { create } from "zustand";
import { persist } from "zustand/middleware";
import apiClient from "@/lib/apiClient";
import {
  CounselorPublicProfile,
  CounselorListResponse,
} from "@/app/types/counselor";
import { format } from "date-fns";
import { preloadUniversityLogos } from "@/app/utils/uni-logo";

interface Filters {
  service_type?: string[];
  country?: string[];
  university?: string[];
  name?: string;
}

interface ApiParams extends Filters {
  page: number;
  limit: number;
  service_types?: string;
  countries?: string;
  universities?: string;
  name?: string;
}

interface CounselorsState {
  loading: boolean;
  profileLoading: boolean;
  availabilityLoading: boolean;
  datesLoading: boolean;
  error: string | null;
  counselors: CounselorPublicProfile[] | null;
  counselorIds: number[];
  filters: Filters;
  currentPage: number;
  totalPages: number;
  hasMore: boolean;
  availableDates: string[];
  timeSlots: string[];

  fetchAllCounselors: () => Promise<void>;
  fetchCounselorProfile: (userId: number) => Promise<CounselorPublicProfile>;
  setFilters: (filters: Filters) => void;
  fetchTopCounselors: () => Promise<CounselorPublicProfile[]>;
  fetchTimeSlots: (
    counselorUserId: number,
    targetDate: Date,
    timezone: string
  ) => Promise<string[]>;
  fetchAvailableDates: (
    counselorUserId: number,
    startDate: Date,
    endDate: Date,
    timezone: string
  ) => Promise<string[]>;
  loadMore: () => Promise<void>;
  setLoading: (loading: boolean) => void;
  clearFilters: () => void;
}

export const useCounselors = create<CounselorsState>()(
  persist(
    (set, get) => ({
      loading: false,
      profileLoading: false,
      availabilityLoading: false,
      datesLoading: false,
      error: null,
      counselors: null,
      counselorIds: [],
      filters: {},
      currentPage: 1,
      totalPages: 1,
      hasMore: false,
      availableDates: [],
      timeSlots: [],

      setLoading: (loading) => {
        set({ loading });
      },
      fetchAllCounselors: async () => {
        try {
          const { filters, currentPage } = get();

          // Only set loading true if this is the first page
          if (currentPage === 1) {
            set({ loading: true, error: null });
          }

          // Format the parameters for the API call
          const params: ApiParams = {
            page: currentPage,
            limit: 16,
          };

          // Only add filters that have a value
          if (filters.service_type && filters.service_type.length > 0) {
            params.service_types = filters.service_type.join(",");
          }

          if (filters.country && filters.country.length > 0) {
            params.countries = filters.country.join(",");
          }

          if (filters.university && filters.university.length > 0) {
            params.universities = filters.university.join(",");
          }

          // Only add name if it exists and is not empty
          if (filters.name && filters.name.trim() !== "") {
            params.name = filters.name.trim();
          }

          const { data } = await apiClient.get<CounselorListResponse>(
            "/counselor/profiles/public-profile/list",
            { params }
          );

          if (data.items && data.items.length > 0) {
            // Preload university logos for better performance
            const universityNames = data.items
              .filter(
                (counselor) =>
                  counselor.education && counselor.education.length > 0
              )
              .map((counselor) => counselor.education[0].university_name)
              .filter(Boolean);

            if (universityNames.length > 0) {
              preloadUniversityLogos(universityNames);
            }

            set((state) => {
              // For first page or filter changes, replace the list
              const isFirstPage = currentPage === 1;

              return {
                counselors: isFirstPage
                  ? data.items
                  : [...(state.counselors || []), ...data.items],
                counselorIds: isFirstPage
                  ? data.items.map((c) => c.user_id)
                  : [
                      ...new Set([
                        ...state.counselorIds,
                        ...data.items.map((c) => c.user_id),
                      ]),
                    ],
                totalPages: data.pages,
                hasMore: data.has_next,
                loading: false,
                error: null,
              };
            });
          } else {
            set((state) => ({
              ...state,
              counselors: currentPage === 1 ? [] : state.counselors,
              counselorIds: currentPage === 1 ? [] : state.counselorIds,
              totalPages: currentPage === 1 ? 0 : state.totalPages,
              hasMore: false,
              loading: false,
              error: currentPage === 1 ? "No counselors found" : null,
            }));
          }
        } catch (error) {
          set({
            error: "Failed to fetch counselors",
            loading: false,
          });
          console.error("Error fetching counselors:", error);
        }
      },

      fetchCounselorProfile: async (userId: number) => {
        try {
          set({ profileLoading: true, error: null });
          const { data } = await apiClient.get<CounselorPublicProfile>(
            `/counselor/profile/public-profile/user/${userId}`
          );

          // Update counselors state with the fetched profile
          set((state) => {
            // Create a new array with existing counselors
            const updatedCounselors = [...(state.counselors || [])];

            // Find if this counselor already exists in our array
            const existingIndex = updatedCounselors.findIndex(
              (c) => c.user_id === userId
            );

            // If counselor exists, update it, otherwise add it
            if (existingIndex >= 0) {
              updatedCounselors[existingIndex] = data;
            } else {
              updatedCounselors.push(data);
            }

            // Update counselorIds if needed
            let updatedIds = [...state.counselorIds];
            if (!updatedIds.includes(userId)) {
              updatedIds.push(userId);
            }

            return {
              counselors: updatedCounselors,
              counselorIds: updatedIds,
              profileLoading: false,
            };
          });

          return data;
        } catch (error) {
          console.error("Error fetching counselor profile:", error);
          set({
            error: "Failed to fetch counselor profile",
            profileLoading: false,
          });
          throw error;
        }
      },

      fetchTopCounselors: async () => {
        try {
          set({ loading: true, error: null });
          const { data } = await apiClient.get<CounselorListResponse>(
            "/counselor/profiles/public-profile/list",
            { params: { page: 1, limit: 8 } }
          );

          // Check if we have any items before updating state
          if (data.items && data.items.length > 0) {
            // Preload university logos for top counselors
            const universityNames = data.items
              .filter(
                (counselor) =>
                  counselor.education && counselor.education.length > 0
              )
              .map((counselor) => counselor.education[0].university_name)
              .filter(Boolean);

            if (universityNames.length > 0) {
              preloadUniversityLogos(universityNames);
            }

            set({
              counselors: data.items,
              counselorIds: data.items.map((c) => c.user_id),
              loading: false,
            });
            return data.items;
          } else {
            set({
              counselors: [],
              counselorIds: [],
              loading: false,
              error: "No counselors found",
            });
            return [];
          }
        } catch (error) {
          set({
            error: "Failed to fetch top counselors",
            loading: false,
            counselors: [],
            counselorIds: [],
          });
          console.error("Error fetching top counselors:", error);
          return [];
        }
      },

      fetchAvailableDates: async (
        counselorUserId,
        startDate,
        endDate,
        timezone
      ) => {
        try {
          set({ datesLoading: true });
          const response = await apiClient.get<string[]>(
            `/counselor/availability/public/${counselorUserId}/dates`,
            {
              params: {
                start_date: format(startDate, "yyyy-MM-dd"),
                end_date: format(endDate, "yyyy-MM-dd"),
                timezone,
              },
            }
          );
          set({ availableDates: response.data, datesLoading: false });
          return response.data;
        } catch (error) {
          set({
            error: "Failed to fetch dates",
            datesLoading: false,
          });
          throw error;
        }
      },

      fetchTimeSlots: async (counselorUserId, targetDate, timezone) => {
        try {
          set({ availabilityLoading: true });
          const response = await apiClient.get<string[]>(
            `/counselor/availability/public/${counselorUserId}/slots`,
            {
              params: {
                target_date: format(targetDate, "yyyy-MM-dd"),
                timezone,
              },
            }
          );
          set({
            timeSlots: response.data,
            availabilityLoading: false,
          });
          return response.data;
        } catch (error) {
          set({
            error: "Failed to fetch time slots",
            availabilityLoading: false,
          });
          throw error;
        }
      },

      setFilters: (filters: Filters) => {
        set({
          filters,
          currentPage: 1,
        });
      },
      clearFilters: () => {
        set({
          filters: {},
          counselors: [],
          currentPage: 1,
          hasMore: false,
        });
      },

      loadMore: async () => {
        const { currentPage, hasMore } = get();
        if (!hasMore) return;
        set({ currentPage: currentPage + 1 });
        await get().fetchAllCounselors();
      },
    }),
    {
      name: "counselors-storage",
      partialize: (state) =>
        Object.fromEntries(
          Object.entries(state).filter(([key]) => ["filters"].includes(key))
        ),
    }
  )
);
