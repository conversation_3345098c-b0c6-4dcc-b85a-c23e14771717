import { useState, useEffect } from "react";
import { CounselorPublicProfile } from "@/app/types/counselor";
import apiClient from "@/lib/apiClient";

export type CounselorCategory =
  | "top-ivy-league"
  | "russel-group"
  | "liberal-arts"
  | "top-ucs";

interface FeaturedCounselorsResponse {
  category: string;
  items: Partial<CounselorPublicProfile>[];
}

export const useFeaturedCounselors = (
  initialCategory: CounselorCategory = "top-ivy-league"
) => {
  const [category, setCategory] = useState<CounselorCategory>(initialCategory);
  const [counselors, setCounselors] = useState<
    Partial<CounselorPublicProfile>[]
  >([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCounselors = async (selectedCategory: CounselorCategory) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await apiClient.get<FeaturedCounselorsResponse>(
        `/counselor/profiles/public-profile/list/featured?category=${selectedCategory}`
      );
      setCounselors(response.data.items);
    } catch (err) {
      console.error("Error fetching featured counselors:", err);
      setError("Failed to load counselors. Please try again later.");
      setCounselors([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCounselors(category);
  }, [category]);

  const changeCategory = (newCategory: CounselorCategory) => {
    setCategory(newCategory);
  };

  return {
    counselors,
    isLoading,
    error,
    category,
    changeCategory,
  };
};
