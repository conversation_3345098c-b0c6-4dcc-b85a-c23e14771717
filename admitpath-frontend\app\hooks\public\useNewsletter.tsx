import { create } from "zustand";
import { toast } from "react-toastify";
import apiClient from "@/lib/apiClient";

interface NewsletterSubscription {
  name: string;
  email: string;
}

interface NewsletterResponse {
  id: number;
  name: string;
  email: string;
  subscribed_at: string;
}

interface NewsletterState {
  loading: boolean;
  error: string | null;
  lastSubscription: NewsletterResponse | null;
  subscribe: (data: NewsletterSubscription) => Promise<void>;
  clearError: () => void;
}

export const useNewsletter = create<NewsletterState>((set) => ({
  loading: false,
  error: null,
  lastSubscription: null,

  subscribe: async (data: NewsletterSubscription) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.post<NewsletterResponse>(
        "/newsletter/subscribe",
        data
      );

      set({
        lastSubscription: response.data,
        error: null,
      });

      toast.success("Successfully subscribed to newsletter!");
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.detail || "Failed to subscribe to newsletter";
      set({
        error: errorMessage,
        lastSubscription: null,
      });

      toast.error("Failed to subscribe to newsletter");

      throw error;
    } finally {
      set({ loading: false });
    }
  },

  clearError: () => set({ error: null }),
}));
