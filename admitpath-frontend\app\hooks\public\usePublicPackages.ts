import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import { toast } from "react-toastify";
import { PackageData } from "@/app/types/counselor/package";

export interface PublicPackagesState {
  packages: { total: number; items: PackageData[] };
  selectedPackage: PackageData | null;
  loading: boolean;
  error: string | null;
  fetchCounselorPackages: (counselorId: number) => Promise<void>;
  fetchPackageById: (packageId: number) => Promise<void>;
  clearError: () => void;
}

export const usePublicPackages = create<PublicPackagesState>((set) => ({
  packages: { total: 0, items: [] },
  selectedPackage: null,
  loading: false,
  error: null,

  fetchCounselorPackages: async (counselorId) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get(`/counselor/packages/${counselorId}/packages`);
      set({ packages: response.data });
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || "Error fetching counselor packages";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  fetchPackageById: async (packageId) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get<PackageData>(`/counselor/packages/${packageId}`);
      set({ selectedPackage: response.data });
    } catch (error: any) {
      const errorMessage = error?.response?.data?.message || "Error fetching package details";
      set({ error: errorMessage });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  clearError: () => set({ error: null }),
}));
