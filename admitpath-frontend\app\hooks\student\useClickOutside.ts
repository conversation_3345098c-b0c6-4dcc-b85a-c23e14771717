import { useEffect } from "react";

function useClickOutside(
  ref: React.RefObject<HTMLElement | null>,
  callback: () => void
) {
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      const target = (event as TouchEvent).touches?.[0]?.target || event.target;

      if (ref.current && target && !ref.current.contains(target as Node)) {
        callback();
      }
    };

    const options = { passive: true };

    document.addEventListener("mousedown", handleClickOutside);
    document.addEventListener("touchstart", handleClickOutside, options);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("touchstart", handleClickOutside);
    };
  }, [ref, callback]);
}

export default useClickOutside;
