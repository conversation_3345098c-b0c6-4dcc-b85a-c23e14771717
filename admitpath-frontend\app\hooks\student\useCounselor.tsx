"use client";

import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import { CounselorPublicProfile } from "@/app/types/counselor";

interface CounselorState {
  loading: boolean;
  error: string | null;
  counselors: { [key: number]: CounselorPublicProfile };
  fetchCounselorInfo: (
    counselor_user_id: number
  ) => Promise<CounselorPublicProfile | null>;
  clearError: () => void;
}

export const useCounselor = create<CounselorState>((set, get) => ({
  loading: false,
  error: null,
  counselors: {},

  fetchCounselorInfo: async (counselor_id: number) => {
    // Check if already cached
    const cachedCounselor = get().counselors[counselor_id];
    if (cachedCounselor) {
      return cachedCounselor;
    }

    try {
      set({ loading: true, error: null });

      // Get counselor profile by counselor_id instead of user_id
      const profileResponse = await apiClient.get(
        `/counselor/profile/by-counselor-id/${counselor_id}`
      );
      const counselorProfile = profileResponse.data;

      set((state) => ({
        counselors: {
          ...state.counselors,
          [counselor_id]: counselorProfile,
        },
        loading: false,
      }));

      return counselorProfile;
    } catch (error: any) {
      set({
        error: error.response?.data?.detail || "Failed to fetch counselor info",
        loading: false,
      });
      return null;
    }
  },

  clearError: () => set({ error: null }),
}));
