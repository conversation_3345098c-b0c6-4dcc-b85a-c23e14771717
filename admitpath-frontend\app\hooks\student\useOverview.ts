"use client";

import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import { OverviewState, ReviewData } from "@/app/types/student/overview";

export const useOverview = create<OverviewState>((set) => ({
  loading: false,
  error: null,
  stats: null,
  pendingFeedbacks: [],
  totalPendingFeedbacks: 0,

  fetchStats: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get("/student/dashboard/stats");
      set({ stats: response.data });
    } catch (error: any) {
      set({ error: error.response?.data?.detail || "Failed to fetch stats" });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  fetchPendingFeedbacks: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get(
        `/pending-feedback?timezone=${
          Intl.DateTimeFormat().resolvedOptions().timeZone
        }`
      );
      set({
        pendingFeedbacks: response.data.sessions,
        totalPendingFeedbacks: response.data.total,
      });
    } catch (error: any) {
      set({
        error:
          error.response?.data?.detail || "Failed to fetch pending feedbacks",
      });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  submitFeedback: async (sessionId: number, data: ReviewData) => {
    try {
      set({ loading: true, error: null });
      await apiClient.post(`/sessions/${sessionId}/reviews`, data);
      // Refresh pending feedbacks after submission
      const response = await apiClient.get(
        `/pending-feedback?timezone=${
          Intl.DateTimeFormat().resolvedOptions().timeZone
        }`
      );
      set({
        pendingFeedbacks: response.data.sessions,
        totalPendingFeedbacks: response.data.total,
      });
    } catch (error: any) {
      set({
        loading: false,
        error: error.response?.data?.detail || "Failed to submit feedback",
      });
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  clearError: () => set({ error: null }),
}));
