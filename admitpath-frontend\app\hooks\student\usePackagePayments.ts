import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import { toast } from "react-toastify";
import { FirstSessionData } from "./usePackages";

export interface PackageCheckoutPayload {
  package_id: number; // Still required by the backend
  subscription_id: number; // Required - the ID of the created subscription
  first_session?: FirstSessionData; // Optional
  selected_service: string; // Still required by the backend
}

export interface PackageCheckoutResponse {
  checkout_url: string;
  session_id: string;
  payment_id: number;
}

export interface PackagePaymentsState {
  loading: boolean;
  error: string | null;
  createCheckoutSession: (data: PackageCheckoutPayload) => Promise<PackageCheckoutResponse>;
  clearError: () => void;
}

export const usePackagePayments = create<PackagePaymentsState>((set) => ({
  loading: false,
  error: null,

  createCheckoutSession: async (data) => {
    set({ loading: true, error: null });
    try {
      const response = await apiClient.post<PackageCheckoutResponse>(
        "/payments/package/create-checkout-session",
        data
      );
      set({ loading: false });
      return response.data;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || "Failed to create checkout session";
      set({ error: errorMessage, loading: false });
      toast.error(errorMessage);
      throw error;
    }
  },

  clearError: () => set({ error: null }),
}));
