import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import { toast } from "react-toastify";

export interface StudentPackageItem {
  service_name: string;
  hours: {
    total: number;
    used: number;
    remaining: number;
  };
}

export interface StudentPackage {
  id: number;
  package: {
    id: number;
    title: string;
    description: string;
    counselor_name: string;
    counselor_profile_picture: string;
  };
  start_date: string;
  end_date: string | null;
  status: 'pending' | 'active' | 'completed' | 'cancelled' | 'expired';
  service_hours: {
    [service_name: string]: {
      total: number;
      used: number;
    };
  };
  sessions: {
    id: number;
    date: string;
    start_time: string;
    end_time: string;
    status: string;
    service_name: string;
  }[];
  progress?: {
    [service_name: string]: {
      total: number;
      used: number;
      remaining: number;
    };
  };
}

export interface FirstSessionData {
  event_name: string;
  date: string;
  start_time: string;
  end_time: string;
  student_email: string;
}

export interface PackageSubscriptionPayload {
  package_id: number;
  selected_service: string;
  first_session: FirstSessionData;
  promo_code?: string;
}

export interface SessionFromPackagePayload {
  selected_service: string;
  date: string;
  start_time: string;
  end_time: string;
}

export interface PackagesState {
  packages: StudentPackage[];
  selectedPackage: StudentPackage | null;
  loading: boolean;
  error: string | null;
  activePackages: StudentPackage[];
  completedPackages: StudentPackage[];
  fetchPackages: (status?: string) => Promise<void>;
  fetchPackageById: (subscriptionId: number) => Promise<void>;
  subscribeToPackage: (data: PackageSubscriptionPayload) => Promise<any>;
  clearError: () => void;
}

const usePackages = create<PackagesState>((set, get) => ({
  packages: [],
  selectedPackage: null,
  loading: false,
  error: null,
  activePackages: [],
  completedPackages: [],

  fetchPackages: async (status = "active") => {
    set({ loading: true, error: null });
    try {
      const response = await apiClient.get<StudentPackage[]>(
        `/student/packages/my-packages?status=${status}`
      );

      // Store packages in the appropriate state based on status
      if (status === "active") {
        set({
          packages: response.data,
          activePackages: response.data,
          loading: false
        });
      } else if (status === "completed") {
        set({
          completedPackages: response.data,
          loading: false
        });
      } else {
        set({ packages: response.data, loading: false });
      }
    } catch (error: any) {
      set({
        error: error.response?.data?.message || "Failed to fetch packages",
        loading: false,
      });
    }
  },

  fetchPackageById: async (subscriptionId) => {
    set({ loading: true, error: null });
    try {
      const response = await apiClient.get<StudentPackage>(
        `/student/packages/${subscriptionId}`
      );
      set({ selectedPackage: response.data, loading: false });
    } catch (error: any) {
      set({
        error: error.response?.data?.message || "Failed to fetch package details",
        loading: false,
      });
    }
  },

  subscribeToPackage: async (data) => {
    set({ loading: true, error: null });
    try {
      const response = await apiClient.post(
        "/student/packages/subscribe",
        data
      );
      return response.data;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || "Failed to subscribe to package";
      set({ error: errorMessage, loading: false });
      toast.error(errorMessage);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  clearError: () => set({ error: null }),
}));

export default usePackages;
