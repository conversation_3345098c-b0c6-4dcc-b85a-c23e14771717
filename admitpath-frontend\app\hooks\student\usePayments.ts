"use client";

import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import { toast } from "react-toastify";
import { PaymentState, PaymentFilters } from "@/app/types/student/payments";

export const usePayments = create<PaymentState>((set, get) => ({
  loading: false,
  error: null,
  stats: null,
  sessions: [],
  pagination: {
    total: 0,
    page: 1,
    size: 10,
    hasMore: false,
  },
  filters: {
    page: 1,
    page_size: 10,
  },

  fetchStats: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get("/payments/student/payment-stats");
      set({ stats: response.data, loading: false });
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.detail || "Failed to fetch payment stats";
      set({ error: errorMessage, loading: false });
      toast.error(errorMessage);
    }
  },

  fetchSessions: async (filters?: PaymentFilters) => {
    try {
      set({ loading: true, error: null });
      const currentFilters = { ...get().filters, ...filters };
      set({ filters: currentFilters });

      const queryParams = new URLSearchParams();
      if (currentFilters.service_type)
        queryParams.append("service_type", currentFilters.service_type);
      if (currentFilters.page)
        queryParams.append("page", currentFilters.page.toString());
      if (currentFilters.page_size)
        queryParams.append("page_size", currentFilters.page_size.toString());

      const response = await apiClient.get(
        `/payments/student/payment-history?${queryParams}`
      );

      set({
        sessions: response.data.items,
        pagination: {
          total: response.data.total,
          page: response.data.page,
          size: response.data.size,
          hasMore: response.data.has_more,
        },
        loading: false,
      });
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.detail || "Failed to fetch payment sessions";
      set({ error: errorMessage, loading: false });
      toast.error(errorMessage);
    }
  },

  updateFilters: (newFilters: Partial<PaymentFilters>) => {
    const currentFilters = get().filters;
    const updatedFilters = { ...currentFilters, ...newFilters };
    set({ filters: updatedFilters });
    get().fetchSessions(updatedFilters);
  },

  clearError: () => set({ error: null }),

  createCheckoutSession: async (sessionId: number) => {
    try {
      set({ loading: true, error: null });
      const { data } = await apiClient.post(
        "/payments/session/create-checkout-session",
        {
          session_id: sessionId,
        }
      );
      set({ loading: false });
      return data;
    } catch (error: any) {
      set({
        error:
          error.response?.data?.detail || "Failed to create checkout session",
        loading: false,
      });
      throw error;
    }
  },
}));
