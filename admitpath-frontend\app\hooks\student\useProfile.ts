"use client";

import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import { toast } from "react-toastify";
import {
  EducationInfo,
  PersonalInfo,
  ProfileState,
} from "@/app/types/student/profile";

// Create a store for managing student profile state
export const useProfile = create<ProfileState>((set, get) => ({
  loading: false,
  error: null,
  currentStep: null,
  userInfo: null,
  personalInfo: null,
  educationInfo: null,
  servicesInfo: null,
  setCurrentStep: (step) => set({ currentStep: step }),

  fetchUserInfo: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get(
        `/user/me?timezone=${Intl.DateTimeFormat().resolvedOptions().timeZone}`
      );
      set({ userInfo: response.data, loading: false });
    } catch (error) {
      set({ error: "Failed to fetch user info", loading: false });
    }
  },

  fetchPersonalInfo: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get("/student/profile/personal-info");
      set({ personalInfo: response.data, loading: false });
    } catch (error: any) {
      set({
        error: error.response?.data?.detail || "Failed to fetch personal info",
        loading: false,
      });
    }
  },

  fetchEducationalBackground: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get(
        "/student/profile/educational-background"
      );
      set({ educationInfo: response.data.items, loading: false });
    } catch (error: any) {
      set({
        error:
          error.response?.data?.detail ||
          "Failed to fetch educational background",
        loading: false,
      });
    }
  },

  fetchServicesInfo: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get("/student/profile/services-info");
      set({ servicesInfo: response.data, loading: false });
    } catch (error: any) {
      set({
        error: error.response?.data?.detail || "Failed to fetch Services info",
        loading: false,
      });
    }
  },

  figureCurrentStep: async () => {
    try {
      set({ loading: true, error: null });

      const {
        fetchPersonalInfo,
        fetchEducationalBackground,
        // fetchServicesInfo,
      } = get();

      await Promise.all([
        fetchPersonalInfo(),
        fetchEducationalBackground(),
        // fetchServicesInfo(),
      ]);

      // const { personalInfo, educationInfo, servicesInfo } = get();
      const { personalInfo, educationInfo } = get();

      let currentStep = 1;
      if (personalInfo) {
        currentStep = 2;
        if (educationInfo && educationInfo.length > 0) {
          currentStep = 3;
          // if (servicesInfo) {
          //   currentStep = 4;
          // }
        }
      }

      set({ currentStep, loading: false });
    } catch (error: any) {
      set({
        error:
          error.response?.data?.detail || "Failed to determine current step",
        loading: false,
      });
    }
  },

  submitPersonalInfo: async (data: PersonalInfo) => {
    try {
      set({ loading: true, error: null });
      const { personalInfo } = get();
      // If data exists, use PUT, else use POST
      const method = personalInfo ? "put" : "post";
      await apiClient[method]("/student/profile/personal-info", data);
      set({ personalInfo: data, currentStep: 2 });
    } catch (error: any) {
      set({ error: error.response?.data?.detail || "Something went wrong" });
      toast.error(error.response?.data?.detail || "Something went wrong");
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  submitEducationInfo: async (data: EducationInfo[]) => {
    set({ loading: true, error: null });
    try {
      const results = await Promise.all(
        data.map(async (item) => {
          const payload = {
            ...item,
            end_date: item.is_current ? undefined : item.end_date,
          };

          if (item.id) {
            // Update existing entry
            const response = await apiClient.put(
              `/student/profile/educational-background/${item.id}`,
              payload
            );
            return response.data;
          } else {
            // Create new entry
            const response = await apiClient.post(
              "/student/profile/educational-background",
              payload
            );
            return response.data;
          }
        })
      );

      set({
        educationInfo: results,
        currentStep: Math.max(get().currentStep || 0, 3),
        loading: false,
      });

      return results;
    } catch (error: any) {
      set({
        error:
          error.response?.data?.detail || "Failed to submit education info",
        loading: false,
      });
      throw error;
    }
  },

  updateEducationInfo: async (id: number, data: Partial<EducationInfo>) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.put(
        `/student/profile/educational-background/${id}`,
        data
      );

      // Update the specific education item in the list
      set((state) => ({
        educationInfo:
          state.educationInfo?.map((item) =>
            item.id === id ? response.data : item
          ) || null,
      }));
      toast.success("Education info updated successfully");
    } catch (error: any) {
      set({ error: error.response?.data?.detail || "Something went wrong" });
      toast.error(error.response?.data?.detail || "Something went wrong");
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  deleteEducationInfo: async (id: number) => {
    try {
      set({ loading: true, error: null });
      await apiClient.delete(`/student/profile/educational-background/${id}`);

      // Remove the education item from the list
      set((state) => ({
        educationInfo:
          state.educationInfo?.filter((item) => item.id !== id) || null,
      }));
      toast.success("Education info deleted successfully");
    } catch (error: any) {
      set({ error: error.response?.data?.detail || "Something went wrong" });
      toast.error(error.response?.data?.detail || "Something went wrong");
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  submitExpectedServices: async (data) => {
    try {
      set({ loading: true, error: null });
      const { servicesInfo } = get();
      const method = servicesInfo ? "put" : "post";
      await apiClient[method]("/student/profile/expected-services", data);
      set({ servicesInfo: data, currentStep: 4 });
    } catch (error: any) {
      set({ error: error.response?.data?.detail || "Something went wrong" });
      toast.error(error.response?.data?.detail || "Something went wrong");
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  updateprofile_picture_url: (url: string | null) => {
    const currentUserInfo = get().userInfo;
    if (currentUserInfo) {
      set({ userInfo: { ...currentUserInfo, profile_picture_url: url } });
    }
  },

  clearError: () => set({ error: null }),
}));
