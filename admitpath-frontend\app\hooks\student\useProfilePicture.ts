"use client";

import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import { toast } from "react-toastify";
import { useProfile } from "./useProfile";

interface ProfilePictureState {
  loading: boolean;
  error: string | null;
  uploadProfilePicture: (file: File) => Promise<string>;
  deleteProfilePicture: () => Promise<void>;
  clearError: () => void;
}

export const useProfilePicture = create<ProfilePictureState>((set) => {
  // Get the updateprofile_picture_url function from useProfile
  const { updateprofile_picture_url } = useProfile.getState();

  return {
    loading: false,
    error: null,

    uploadProfilePicture: async (file: File) => {
      try {
        set({ loading: true, error: null });
        const formData = new FormData();
        formData.append("file", file);

        const response = await apiClient.post(
          "/user/profile-picture",
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        const profile_picture_url = response.data.profile_picture_url;
        updateprofile_picture_url(profile_picture_url);

        set({ loading: false });
        toast.success("Profile picture uploaded successfully");
        return profile_picture_url;
      } catch (error: any) {
        const errorMessage =
          error.response?.data?.detail || "Failed to upload profile picture";
        set({ error: errorMessage, loading: false });
        toast.error(errorMessage);
        throw error;
      }
    },

    deleteProfilePicture: async () => {
      try {
        set({ loading: true, error: null });
        await apiClient.delete("/user/profile-picture");
        updateprofile_picture_url(null);
        set({ loading: false });
        toast.success("Profile picture deleted successfully");
      } catch (error: any) {
        const errorMessage =
          error.response?.data?.detail || "Failed to delete profile picture";
        set({ error: errorMessage, loading: false });
        toast.error(errorMessage);
        throw error;
      }
    },

    clearError: () => set({ error: null }),
  };
});
