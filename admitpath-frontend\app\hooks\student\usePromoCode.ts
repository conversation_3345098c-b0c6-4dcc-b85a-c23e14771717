"use client";

import { create } from "zustand";
import apiClient from "@/lib/apiClient";

interface PromoCodeState {
    loading: boolean;
    error: string | null;
    promoDetails: {
        type: "percentage" | "amount" | "fixed_price";
        amount?: number;
        percentage?: number;
    } | null;
    verifyPromoCode: (
        code: string,
        counselorId: number,
        original_amont: number
    ) => Promise<void>;
    clearPromoCode: () => void;
}

export const usePromoCode = create<PromoCodeState>((set) => ({
    loading: false,
    error: null,
    promoDetails: null,

    verifyPromoCode: async (
        code: string,
        counselorId: number,
        original_amount: number
    ) => {
        if (!code.trim()) {
            set({ promoDetails: null, error: null, loading: false });
            return;
        }

        try {
            set({ loading: true, error: null });
            const response = await apiClient.get(
                `/promo-codes/verify?code=${code}&requested_counselor_id=${counselorId}&original_amount=${original_amount}`
            );
            set({ promoDetails: response.data, error: null });
        } catch (error: any) {
            set({
                error: error.response?.data?.detail || "Invalid promo code",
                promoDetails: null,
            });
        } finally {
            set({ loading: false });
        }
    },

    clearPromoCode: () => {
        set({ promoDetails: null, error: null });
    },
}));
