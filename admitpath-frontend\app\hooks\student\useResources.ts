"use client";

import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import { toast } from "react-toastify";

export interface Resource {
  id: number;
  title: string;
  description: string;
  file_url: string;
  file_type: string;
  image_url?: string;
  category: string;
  audience: string;
  uploaded_by: number;
  created_at: string;
  updated_at: string;
}

interface ResourcesState {
  resources: Resource[];
  loading: boolean;
  error: string | null;
  page: number;
  hasMore: boolean;
  total: number;
  searchTerm: string;
  category: string | null;
  audience: string | null;

  // Actions
  setSearchTerm: (term: string) => void;
  setCategory: (category: string | null) => void;
  setAudience: (audience: string | null) => void;
  fetchResources: () => Promise<void>;
  loadMore: () => void;
  clearError: () => void;
}

export const useResources = create<ResourcesState>((set, get) => ({
  resources: [],
  loading: false,
  error: null,
  page: 1,
  hasMore: true,
  total: 0,
  searchTerm: "",
  category: null,
  audience: null,

  setSearchTerm: (term) => {
    set({ searchTerm: term, page: 1, resources: [], hasMore: true });
    get().fetchResources();
  },

  setCategory: (category) => {
    set({ category, page: 1, resources: [], hasMore: true });
    get().fetchResources();
  },

  setAudience: (audience) => {
    set({ audience, page: 1, resources: [], hasMore: true });
    get().fetchResources();
  },

  fetchResources: async () => {
    try {
      const { page, category, searchTerm, audience } = get();
      set({ loading: true, error: null });

      const queryParams = new URLSearchParams();
      if (category) queryParams.append("category", category);
      if (searchTerm) queryParams.append("search", searchTerm);
      if (audience) queryParams.append("audience", audience);
      queryParams.append("page", page.toString());
      queryParams.append("page_size", "10"); // Fixed page size
      
      const response = await apiClient.get(`/resources?${queryParams.toString()}`);
      const data = response.data;

      set((state) => ({
        resources: page === 1 ? data.items : [...state.resources, ...data.items],
        total: data.total,
        hasMore: data.items.length > 0, // If no items returned, we've reached the end
        loading: false
      }));
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || "Failed to fetch resources";
      set({ error: errorMessage, loading: false });
      toast.error(errorMessage);
    }
  },

  loadMore: () => {
    const { loading, hasMore } = get();
    if (!loading && hasMore) {
      set((state) => ({ page: state.page + 1 }));
      get().fetchResources();
    }
  },

  clearError: () => set({ error: null })
}));