"use client";

import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import { NotesPayload, SessionsState } from "@/app/types/student/sessions";

export const useSessions = create<SessionsState>((set, get) => ({
    loading: false,
    error: null,
    sessions: { items: [], total: null },
    currentNotes: null,

    fetchSessions: async (timezone, params) => {
        try {
            set({ loading: true, error: null });
            const response = await apiClient.get(
                `/student/sessions/my-sessions?timezone=${timezone}`,
                {
                    params,
                }
            );
            set({
                sessions: {
                    items: response.data.items || [],
                    total: response.data.total || 0,
                },
                loading: false,
            });
            return response.data;
        } catch (error: any) {
            set({
                error:
                    error.response?.data?.detail || "Failed to fetch sessions",
                loading: false,
                sessions: { items: [], total: 0 },
            });
            throw error;
        }
    },

    fetchSessionNotes: async (sessionId: number) => {
        if (!sessionId) return null;

        try {
            set({ loading: true, error: null });
            const response = await apiClient.get(
                `/sessions/${sessionId}/notes`
            );
            set({ currentNotes: response.data, loading: false });
            return response.data;
        } catch (error: any) {
            set({
                error:
                    error.response?.data?.detail ||
                    "Failed to fetch session notes",
                loading: false,
                currentNotes: null,
            });
            throw error;
        }
    },

    createSessionNotes: async (sessionId: number, data: NotesPayload) => {
        try {
            set({ loading: true, error: null });
            const response = await apiClient.post(
                `/sessions/${sessionId}/notes`,
                data
            );
            set({ currentNotes: response.data, loading: false });
            return response.data;
        } catch (error: any) {
            set({
                error:
                    error.response?.data?.detail ||
                    "Failed to create session notes",
                loading: false,
                currentNotes: null,
            });
            throw error;
        }
    },

    updateSessionNotes: async (sessionId: number, data: NotesPayload) => {
        try {
            set({ loading: true, error: null });
            const response = await apiClient.put(
                `/sessions/${sessionId}/notes`,
                data
            );
            set({ currentNotes: response.data, loading: false });
            return response.data;
        } catch (error: any) {
            set({
                error:
                    error.response?.data?.detail ||
                    "Failed to update session notes",
                loading: false,
                currentNotes: null,
            });
            throw error;
        }
    },

    deleteSessionNotes: async (sessionId: number) => {
        try {
            set({ loading: true, error: null });
            await apiClient.delete(`/sessions/${sessionId}/notes`);
            set({ currentNotes: null, loading: false });
        } catch (error: any) {
            set({
                error:
                    error.response?.data?.detail ||
                    "Failed to delete session notes",
                loading: false,
                currentNotes: null,
            });
            throw error;
        }
    },

    clearError: () => {
        set({ error: null });
    },

    createSession: async (counselorId: number, data) => {
        try {
            set({ loading: true, error: null });
            const response = await apiClient.post(
                `/student/sessions/counselor/${counselorId}/sessions`,
                {
                    session: {
                        service_type: data.service_type,
                        description: data.description,
                        date: data.date,
                        start_time: data.start_time,
                        end_time: data.end_time,
                    },
                    payment: {
                        amount: data.amount,
                        promo_code: data.promo_code,
                    },
                }
            );
            set({ loading: false });
            return response.data;
        } catch (error: any) {
            set({
                error:
                    error.response?.data?.detail || "Failed to create session",
                loading: false,
            });
            throw error;
        }
    },
}));
