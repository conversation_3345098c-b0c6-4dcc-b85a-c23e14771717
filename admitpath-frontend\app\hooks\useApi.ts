import { useState, useEffect } from "react";
import axios from "axios";

interface ApiResponse<T> {
  data: T | null;
  error: string | null;
  loading: boolean;
}

const useApi = <T>(
  url: string,
  method: "GET" | "POST" | "PUT" | "DELETE",
  payload?: any
): ApiResponse<T> => {
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem("accessToken");

      const response = await axios({
        method,
        url: `${process.env.NEXT_PUBLIC_API_BASE_URL}${url}`,
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        data: payload,
      });

      setData(response.data);
    } catch (err: any) {
      setError(err.message || "Failed to fetch data");
    } finally {
      setLoading(false);
    }
  };

  // Trigger the API call when the hook is used
  useEffect(() => {
    fetchData();
  }, [method, payload]);

  return { data, error, loading };
};

export default useApi;
