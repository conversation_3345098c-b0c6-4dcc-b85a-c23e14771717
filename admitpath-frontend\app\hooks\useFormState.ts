import { useState } from "react";

function useFormState<T extends Record<string, any>>(initialState: T) {
  const [formData, setFormData] = useState(initialState);

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, type } = e.target;

    // Narrow down the type to HTMLInputElement for checkboxes and radio buttons
    if (
      e.target instanceof HTMLInputElement &&
      (type === "checkbox" || type === "radio")
    ) {
      const { checked } = e.target;
      setFormData((prev) => ({
        ...prev,
        [name]: checked, // Use `checked` for checkboxes and radio buttons
      }));
    } else {
      const { value } = e.target;
      setFormData((prev) => ({
        ...prev,
        [name]: value, // Use `value` for other input types
      }));
    }
  };

  return { formData, setFormData, handleChange };
}

export default useFormState;
