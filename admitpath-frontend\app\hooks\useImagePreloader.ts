"use client";

import { useEffect, useRef } from "react";

interface PreloadOptions {
  priority?: boolean;
  rootMargin?: string;
  threshold?: number;
}

export function useImagePreloader(
  images: string[],
  options: PreloadOptions = {}
) {
  const {
    priority = false,
    rootMargin = "200px",
    threshold = 0.1,
  } = options;

  const preloadedImages = useRef(new Set<string>());

  const preloadImage = (src: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (preloadedImages.current.has(src)) {
        resolve();
        return;
      }

      const img = new Image();
      img.onload = () => {
        preloadedImages.current.add(src);
        resolve();
      };
      img.onerror = reject;
      img.src = src;
    });
  };

  const preloadImages = async (imagesToPreload: string[]) => {
    const promises = imagesToPreload
      .filter(src => src && !preloadedImages.current.has(src))
      .map(src => preloadImage(src).catch(() => {
        // Silently handle errors for preloading
        console.warn(`Failed to preload image: ${src}`);
      }));

    await Promise.allSettled(promises);
  };

  useEffect(() => {
    if (priority) {
      // Immediately preload priority images
      preloadImages(images);
    } else {
      // Use requestIdleCallback for non-priority images
      const preloadWhenIdle = () => {
        if ('requestIdleCallback' in window) {
          window.requestIdleCallback(() => {
            preloadImages(images);
          });
        } else {
          // Fallback for browsers without requestIdleCallback
          setTimeout(() => {
            preloadImages(images);
          }, 100);
        }
      };

      preloadWhenIdle();
    }
  }, [images, priority]);

  return {
    preloadImage,
    preloadImages,
    isPreloaded: (src: string) => preloadedImages.current.has(src),
  };
}

// Hook for preloading images based on intersection observer
export function useIntersectionPreloader(
  targetRef: React.RefObject<Element>,
  images: string[],
  options: PreloadOptions = {}
) {
  const { rootMargin = "200px", threshold = 0.1 } = options;
  const { preloadImages } = useImagePreloader(images, { priority: false });
  const hasTriggered = useRef(false);

  useEffect(() => {
    const target = targetRef.current;
    if (!target || hasTriggered.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !hasTriggered.current) {
            hasTriggered.current = true;
            preloadImages(images);
            observer.disconnect();
          }
        });
      },
      {
        rootMargin,
        threshold,
      }
    );

    observer.observe(target);

    return () => {
      observer.disconnect();
    };
  }, [targetRef, images, rootMargin, threshold, preloadImages]);
}

// Utility function to extract image URLs from counselor data
export function extractCounselorImages(counselors: any[]): string[] {
  const images: string[] = [];
  
  counselors.forEach(counselor => {
    if (counselor.profile_picture_url) {
      images.push(counselor.profile_picture_url);
    }
  });

  return images.filter(Boolean);
}
