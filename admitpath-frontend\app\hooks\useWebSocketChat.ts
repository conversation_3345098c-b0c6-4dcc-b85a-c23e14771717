"use client";

import { create } from "zustand";
import apiClient from "@/lib/apiClient";
import {
  ChatState,
  Message,
  SendMessagePayload,
  InitialMessagePayload,
  WebSocketMessage,
  WebSocketConnectionStatus,
} from "@/app/types/chat";
import { dateNowWithTZOffset } from "../utils/time-helpers";

const LOCALSTORAGE_KEY = "chat_latest_messages";

interface LatestMessagesStorage {
  [channelId: string]: string;
}

class WebSocketManager {
  private socket: WebSocket | null = null;
  private channelId: string | null = null;
  private token: string | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000; // 3 seconds
  private reconnectTimeoutId: NodeJS.Timeout | null = null;
  private messageHandler: ((data: any) => void) | null = null;
  private statusChangeHandler: ((status: WebSocketConnectionStatus) => void) | null = null;
  private errorHandler: ((error: any) => void) | null = null;

  constructor() {
    this.connect = this.connect.bind(this);
    this.disconnect = this.disconnect.bind(this);
    this.sendMessage = this.sendMessage.bind(this);
    this.handleOpen = this.handleOpen.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
    this.handleClose = this.handleClose.bind(this);
    this.handleError = this.handleError.bind(this);
    this.reconnect = this.reconnect.bind(this);
  }

  public connect(channelId: string, token: string): void {
    this.channelId = channelId;
    this.token = token;

    if (this.socket) {
      this.socket.close();
    }

    try {
      const wsUrl = `${this.getWebSocketBaseUrl()}/ws/chat/${channelId}?token=${token}`;
      this.socket = new WebSocket(wsUrl);

      this.socket.onopen = this.handleOpen;
      this.socket.onmessage = this.handleMessage;
      this.socket.onclose = this.handleClose;
      this.socket.onerror = this.handleError;

      if (this.statusChangeHandler) {
        this.statusChangeHandler('connecting');
      }
    } catch (error) {
      console.error("WebSocket connection error:", error);
      if (this.errorHandler) {
        this.errorHandler(error);
      }
    }
  }

  public disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }

    this.channelId = null;
    this.token = null;
    this.reconnectAttempts = 0;

    if (this.statusChangeHandler) {
      this.statusChangeHandler('disconnected');
    }
  }

  public sendMessage(message: WebSocketMessage): boolean {
    if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
      console.error("WebSocket is not connected");
      return false;
    }

    try {
      this.socket.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error("Error sending message:", error);
      if (this.errorHandler) {
        this.errorHandler(error);
      }
      return false;
    }
  }

  public onMessage(handler: (data: any) => void): void {
    this.messageHandler = handler;
  }

  public onStatusChange(handler: (status: WebSocketConnectionStatus) => void): void {
    this.statusChangeHandler = handler;
  }

  public onError(handler: (error: any) => void): void {
    this.errorHandler = handler;
  }

  public isConnected(): boolean {
    return this.socket !== null && this.socket.readyState === WebSocket.OPEN;
  }

  private handleOpen(_event: Event): void {
    this.reconnectAttempts = 0;

    if (this.statusChangeHandler) {
      this.statusChangeHandler('connected');
    }
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);

      const now = new Date();
      data._receivedAt = now.toISOString();

      if (!this._recentMessages) {
        this._recentMessages = [];
      }

      const fingerprint = `${data.sender_id || ''}-${data.created_at || ''}-${(data.message || '').substring(0, 20)}`;

      // Check if we've seen this message recently (within 5 seconds)
      const isDuplicate = this._recentMessages.some(m => {
        // If fingerprints match and received within 5 seconds, it's likely a duplicate
        if (m.fingerprint === fingerprint) {
          const prevTime = new Date(m.receivedAt).getTime();
          const currentTime = now.getTime();
          return (currentTime - prevTime) < 5000; // 5 seconds
        }
        return false;
      });

      if (isDuplicate) {
        return;
      }

      this._recentMessages.push({
        fingerprint,
        receivedAt: now.toISOString()
      });

      if (this._recentMessages.length > 20) {
        this._recentMessages = this._recentMessages.slice(-20);
      }

      if (this.messageHandler) {
        this.messageHandler(data);
      }
    } catch (error) {
      console.error("Error parsing message:", error);
    }
  }

  private _recentMessages: Array<{fingerprint: string, receivedAt: string}> = [];

  private handleClose(event: CloseEvent): void {

    if (this.statusChangeHandler) {
      this.statusChangeHandler('disconnected');
    }

    if (!event.wasClean && this.channelId && this.token) {
      this.reconnect();
    }
  }

  private handleError(event: Event): void {
    console.error("WebSocket error:", event);
    if (this.errorHandler) {
      this.errorHandler(event);
    }
  }

  private reconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      return;
    }

    this.reconnectAttempts++;

    if (this.statusChangeHandler) {
      this.statusChangeHandler('reconnecting');
    }

    this.reconnectTimeoutId = setTimeout(() => {
      if (this.channelId && this.token) {
        this.connect(this.channelId, this.token);
      }
    }, this.reconnectInterval);
  }

  private getWebSocketBaseUrl(): string {
    const isSecure = window.location.protocol === 'https:';
    const protocol = isSecure ? 'wss:' : 'ws:';

    let apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || '';

    apiBaseUrl = apiBaseUrl.replace(/^https?:\/\//, '');

    return `${protocol}//${apiBaseUrl}`;
  }
}

const wsManager = new WebSocketManager();

let currentUserInfo: { user_id: number | string } | null = null;

export const setWebSocketChatUserInfo = (userInfo: { user_id: number | string }) => {
  currentUserInfo = userInfo;
};

export const useWebSocketChat = create<ChatState>((set, get) => ({
  channels: null,
  currentChannel: null,
  messages: null,
  messageIds: new Set<number>(),
  pendingMessages: new Set<number>(),
  unseenMessages: {},
  unseenChannelsCount: 0,
  loading: false,
  error: null,
  connectionStatus: 'disconnected',

  initWebSocket: () => {
    wsManager.onMessage((data) => {
      const message = data as Message;
      const state = get();


      if (currentUserInfo?.user_id && message.sender_id === currentUserInfo.user_id) {
        return;
      }

      if (state.currentChannel && message.channel_id !== state.currentChannel.channel_id) {
        return;
      }

      if (message.id && state.messageIds.has(message.id)) {
        return;
      }

      const messageFingerprint = `${message.sender_id}-${message.created_at}-${message.message.substring(0, 20)}`;

      const isDuplicate = state.messages?.some(m => {
        const existingFingerprint = `${m.sender_id}-${m.created_at}-${m.message.substring(0, 20)}`;
        return existingFingerprint === messageFingerprint;
      });

      if (isDuplicate) {
        return;
      }

      get().addMessage(message);

      if (state.currentChannel) {
        get().updateLatestSeenMessage(
          state.currentChannel.channel_id,
          message.created_at
        );
      }
    });

    wsManager.onStatusChange((status) => {
      set({ connectionStatus: status });
    });

    wsManager.onError((_error) => {
      set({ error: "WebSocket connection error" });
    });
  },

  fetchChannels: async () => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get("/messages/channels");
      set({ channels: response.data.channels, loading: false });
    } catch (error: any) {
      set({
        error: error.response?.data?.detail || "Failed to fetch channels",
        loading: false,
      });
    }
  },

  fetchMessages: async (channelId: string, skip = 0, limit = 15) => {
    try {
      set({ loading: true, error: null });
      const response = await apiClient.get(
        `/messages/channel/${channelId}?skip=${skip}&limit=${limit}`
      );
      const newMessages = response.data.messages;

      const messageIds = new Set(get().messageIds);
      newMessages.forEach((msg: Message) => messageIds.add(msg.id));

      set((state) => ({
        messages:
          skip === 0
            ? newMessages
            : [
                ...(state.messages || []),
                ...newMessages.filter(
                  (msg: Message) =>
                    !state.messages?.some(
                      (existingMsg: Message) => existingMsg.id === msg.id
                    )
                ),
              ],
        messageIds,
        loading: false,
      }));

      if (limit !== 1 && newMessages.length > 0) {
        get().updateLatestSeenMessage(channelId, newMessages[0].created_at);
      }
    } catch (error: any) {
      set({
        error: error.response?.data?.detail || "Failed to fetch messages",
        loading: false,
      });
    }
  },

  sendMessage: async (payload: SendMessagePayload) => {
    const currentChannel = get().currentChannel;
    if (!currentChannel) return;

    const state = get();
    const messageFingerprint = `${payload.sender_id}-${dateNowWithTZOffset()}-${payload.message.substring(0, 20)}`;

    const isDuplicate = state.messages?.some(m => {
      if (m.sender_id !== payload.sender_id) return false;

      if (m.message !== payload.message) return false;

      const msgTime = new Date(m.created_at).getTime();
      const now = new Date().getTime();
      return (now - msgTime) < 2000; // 2 seconds
    });

    if (isDuplicate) {
      return;
    }

    // Create optimistic message
    const tempId = Math.floor(Math.random() * 1000000);
    const optimisticMessage: Message = {
      id: tempId,
      channel_id: currentChannel.channel_id,
      message: payload.message,
      sender_id: payload.sender_id,
      sender_name: payload.sender_name,
      created_at: dateNowWithTZOffset(),
    };

    try {
      get().addMessage(optimisticMessage);
      set((state) => ({
        pendingMessages: new Set([...state.pendingMessages, tempId]),
      }));

      let serverMessage: Message | null = null;

      if (wsManager.isConnected()) {
        const wsMessage: WebSocketMessage = {
          type: "message",
          message: payload.message,
          recipient_id: payload.recipient_id,
          created_at: optimisticMessage.created_at,
        };

        const sentViaWebSocket = wsManager.sendMessage(wsMessage);

        if (sentViaWebSocket) {


          set((state) => ({
            pendingMessages: new Set([...state.pendingMessages].filter((id) => id !== tempId))
          }));

          return;
        }
      }

      const response = await apiClient.post(`/messages`, {
        recipient_id: payload.recipient_id,
        message: payload.message,
        created_at: optimisticMessage.created_at,
      });

      serverMessage = response.data;

      if (serverMessage) {
        get().updateLatestSeenMessage(
          currentChannel.channel_id,
          serverMessage.created_at
        );
      }

      set((state) => {
        const serverMessageExists = serverMessage && state.messages?.some(msg => msg.id === serverMessage.id);

        const serverMessageFingerprint = serverMessage ?
          `${serverMessage.sender_id}-${serverMessage.created_at}-${serverMessage.message.substring(0, 20)}` : '';

        const similarMessageExists = serverMessage && state.messages?.some(msg => {
          if (msg.id === tempId) return false; // Skip the optimistic message
          const msgFingerprint = `${msg.sender_id}-${msg.created_at}-${msg.message.substring(0, 20)}`;
          return msgFingerprint === serverMessageFingerprint;
        });

        if (serverMessageExists || similarMessageExists) {
          return {
            messages: (state.messages || []).filter(msg => msg.id !== tempId),
            messageIds: state.messageIds,
            pendingMessages: new Set([...state.pendingMessages].filter((id) => id !== tempId))
          };
        }

        if (!serverMessage) {
          return {
            messages: (state.messages || []).filter(msg => msg.id !== tempId),
            messageIds: state.messageIds,
            pendingMessages: new Set([...state.pendingMessages].filter((id) => id !== tempId))
          };
        }

        return {
          messages: (state.messages || []).map((msg) =>
            msg.id === tempId ? serverMessage : msg
          ),
          messageIds: new Set(
            [...(state.messageIds || [])]
              .filter((id) => id !== tempId)
              .concat(serverMessage.id)
          ),
          pendingMessages: new Set(
            [...state.pendingMessages].filter((id) => id !== tempId)
          ),
        };
      });
    } catch (error) {
      console.error("Failed to send message:", error);
      get().removeMessage(tempId);
      throw error;
    }
  },

  sendInitialMessage: async (payload: InitialMessagePayload) => {
    try {
      await apiClient.post(`/messages`, {
        ...payload,
        created_at: dateNowWithTZOffset(),
      });
    } catch (error) {
      throw error;
    }
  },

  setCurrentChannel: (channel) => {
    const currentChannelId = get().currentChannel?.channel_id;

    set({
      messages: null,
      messageIds: new Set<number>()
    });

    if (currentChannelId) {
      wsManager.disconnect();
    }

    set({ currentChannel: channel });

    if (channel) {

      const token = localStorage.getItem("access_token");

      if (token) {
        get().initWebSocket();

        wsManager.connect(channel.channel_id, token);

        get().fetchMessages(channel.channel_id);
      } else {
        set({ error: "Authentication token not found" });
      }
    }
  },

  addMessage: (message: Message) => {
    set((state) => {
      if (state.currentChannel && message.channel_id !== state.currentChannel.channel_id) {
        return state; // Return state unchanged
      }

      if (message.id && state.messageIds.has(message.id)) {
        return state; // Return state unchanged
      }

      const messageFingerprint = `${message.sender_id}-${message.created_at}-${message.message.substring(0, 20)}`;

      const messageExistsByFingerprint = state.messages?.some(m => {
        const existingFingerprint = `${m.sender_id}-${m.created_at}-${m.message.substring(0, 20)}`;
        return existingFingerprint === messageFingerprint;
      });

      if (messageExistsByFingerprint) {
        return state; // Return state unchanged
      }

      return {
        messages: [...(state.messages || []), message],
        messageIds: message.id ? new Set(state.messageIds).add(message.id) : state.messageIds,
      };
    });
  },

  removeMessage: (messageId: number) => {
    set((state) => ({
      messages: (state.messages || []).filter((msg) => msg.id !== messageId),
      messageIds: new Set(
        [...state.messageIds].filter((id) => id !== messageId)
      ),
      pendingMessages: new Set(
        [...state.pendingMessages].filter((id) => id !== messageId)
      ),
    }));
  },

  clearError: () => set({ error: null }),

  updateLatestSeenMessage: (channelId: string, timestamp: string) => {
    try {
      let latestMessages: LatestMessagesStorage = {};
      const storedData = localStorage.getItem(LOCALSTORAGE_KEY);

      if (storedData) {
        latestMessages = JSON.parse(storedData) as LatestMessagesStorage;
      }

      if (latestMessages[channelId] === timestamp) return;

      latestMessages[channelId] = timestamp;

      localStorage.setItem(LOCALSTORAGE_KEY, JSON.stringify(latestMessages));

      set((state) => ({
        unseenMessages: {
          ...state.unseenMessages,
          [channelId]: 0,
        },
        unseenChannelsCount: Math.max(0, state.unseenChannelsCount - 1),
      }));
    } catch (error) {
      console.error("Failed to update latest seen message:", error);
    }
  },

  getUnseenChannelsCount: async (): Promise<number> => {
    try {
      let latestSeenMessages: LatestMessagesStorage = {};
      const storedData = localStorage.getItem(LOCALSTORAGE_KEY);

      if (storedData) {
        latestSeenMessages = JSON.parse(storedData) as LatestMessagesStorage;
      }

      if (!get().channels) {
        await get().fetchChannels();
      }

      const channels = get().channels;
      if (!channels) return 0;

      let unseenMessages = {} as Record<string, number>;
      let unseenChannelsCount = 0;

      const promises = channels.map(async (channel) => {
        const response = await apiClient.get(
          `/messages/channel/${channel.channel_id}?skip=0&limit=1`
        );

        if (response.data.messages && response.data.messages.length > 0) {
          const latestMessage = response.data.messages[0];
          const latestSeenTimestamp = latestSeenMessages[channel.channel_id];

          if (
            !latestSeenTimestamp ||
            new Date(latestMessage.created_at) > new Date(latestSeenTimestamp)
          ) {
            unseenMessages[channel.channel_id] = 1;
            unseenChannelsCount++;
          }
        }
      });

      await Promise.all(promises);
      set({
        unseenMessages,
        unseenChannelsCount,
      });
      return unseenChannelsCount;
    } catch (error) {
      console.error("Failed to get unseen channels count:", error);
      return 0;
    }
  },

  reconnectWebSocket: () => {
    const currentChannel = get().currentChannel;
    if (currentChannel) {
      const token = localStorage.getItem("access_token");
      if (token) {
        wsManager.disconnect();
        wsManager.connect(currentChannel.channel_id, token);
      }
    } else {
      wsManager.disconnect();
    }
  },

  isWebSocketConnected: () => {
    return wsManager.isConnected();
  },
}));
