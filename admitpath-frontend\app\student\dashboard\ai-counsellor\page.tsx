"use client";

import { useState } from "react";
import { <PERSON><PERSON>, Send, Loader2, Play, AlertCircle } from "lucide-react";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card";
import { Alert, AlertDescription } from "@/app/components/ui/alert";
import { LogoLoader } from "@/app/components/ui/logo-loader";
import HeadWrapper from "@/app/components/student/head-wrapper";

interface VideoState {
  isGenerating: boolean;
  videoId: string | null;
  videoUrl: string | null;
  error: string | null;
  
}

export default function AICounsellorPage() {
  const [query, setQuery] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [aiResponse, setAiResponse] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [videoState, setVideoState] = useState<VideoState>({
    isGenerating: false,
    videoId: null,
    videoUrl: null,
    error: null,
    
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!query.trim()) {
      setError("Please enter a question or query");
      return;
    }

    setIsLoading(true);
    setError(null);
    setAiResponse(null);
    setVideoState({
      isGenerating: false,
      videoId: null,
      videoUrl: null,
      error: null,
      
    });

    try {
      const aiRes = await fetch("/api/ai-counsellor/generate-response", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ query }),
      });

      const aiData = await aiRes.json();

      if (!aiData.success) {
        throw new Error(aiData.error || "Failed to generate AI response");
      }

      setAiResponse(aiData.response);
      setIsLoading(false);

      setVideoState((prev) => ({ ...prev, isGenerating: true }));

      const videoRes = await fetch("/api/ai-counsellor/generate-video", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ text: aiData.response }),
      });

      const videoData = await videoRes.json();

      if (!videoData.success) {
        throw new Error(videoData.error || "Failed to generate video");
      }

      setVideoState((prev) => ({
        ...prev,
        videoId: videoData.video_id,
        progress: 10,
      }));

      pollVideoStatus(videoData.video_id);
    } catch (err: any) {
      setError(err.message || "An error occurred");
      setIsLoading(false);
      setVideoState((prev) => ({
        ...prev,
        isGenerating: false,
        error: err.message,
      }));
    }
  };

  const pollVideoStatus = async (videoId: string) => {
    const maxAttempts = 60; // 5 minutes max (5 second intervals)
    let attempts = 0;

    const poll = async () => {
      try {
        const response = await fetch(
          `/api/ai-counsellor/video-status?video_id=${videoId}`
        );
        const data = await response.json();

        if (!data.success) {
          throw new Error(data.error || "Failed to check video status");
        }

        const progress = Math.min(20 + attempts * 2, 90);
        setVideoState((prev) => ({ ...prev, progress }));

        if (data.status === "completed" && data.video_url) {
          setVideoState((prev) => ({
            ...prev,
            isGenerating: false,
            videoUrl: data.video_url,
            progress: 100,
          }));
          return;
        }

        if (data.status === "failed") {
          throw new Error("Video generation failed");
        }

        attempts++;
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          throw new Error("Video generation timed out");
        }
      } catch (err: any) {
        setVideoState((prev) => ({
          ...prev,
          isGenerating: false,
          error: err.message,
        }));
      }
    };

    poll();
  };

  const resetSession = () => {
    setQuery("");
    setAiResponse(null);
    setError(null);
    setVideoState({
      isGenerating: false,
      videoId: null,
      videoUrl: null,
      error: null,
      
    });
  };

  return (
    <>
      <HeadWrapper title="AI Counsellor" />
      <div className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Header */}
          <Card className="border-none shadow-md bg-white">
            <CardHeader className="text-center pb-4">
              <div className="flex items-center justify-center gap-3 mb-2">
                <div className="p-3 bg-blue-100 rounded-full">
                  <Bot className="w-8 h-8 text-blue-600" />
                </div>
                <CardTitle className="text-2xl md:text-3xl font-bold text-gray-800">
                  AI Counsellor
                </CardTitle>
              </div>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Get personalized guidance and advice from our AI counsellor. Ask
                any questions about university admissions, career planning, or
                academic guidance, and receive a comprehensive video response.
              </p>
            </CardHeader>
          </Card>

          {/* Input Form */}
          <Card className="border-none shadow-md bg-white">
            <CardContent className="p-6">
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <label
                    htmlFor="query"
                    className="text-sm font-medium text-gray-700"
                  >
                    What would you like to know?
                  </label>
                  <div className="flex gap-2">
                    <Input
                      id="query"
                      value={query}
                      onChange={(e) => setQuery(e.target.value)}
                      placeholder="Ask about university admissions, career guidance, study tips..."
                      className="flex-1 h-12 text-base"
                      disabled={isLoading || videoState.isGenerating}
                      maxLength={1000}
                    />
                    <Button
                      type="submit"
                      disabled={
                        isLoading || videoState.isGenerating || !query.trim()
                      }
                      className="h-12 px-6 bg-blue-600 hover:bg-blue-700"
                    >
                      {isLoading ? (
                        <Loader2 className="w-5 h-5 animate-spin" />
                      ) : (
                        <Send className="w-5 h-5" />
                      )}
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500">
                    {query.length}/1000 characters
                  </p>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Error Display */}
          {error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Loading State */}
          {(isLoading || videoState.isGenerating) && (
            <Card className="border-none shadow-md bg-white">
              <CardContent className="p-8 text-center">
                <LogoLoader
                  size={60}
                  text={
                    isLoading
                      ? "Generating Response..."
                      : "Generating..."
                  }
                />
                
              </CardContent>
            </Card>
          )}

          {/* Video Result */}
          {videoState.videoUrl && (
            <Card className="border-none shadow-md bg-white">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Play className="w-5 h-5 text-green-600" />
                  Your AI Counsellor Response
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="aspect-video bg-black rounded-lg overflow-hidden">
                  <video
                    controls
                    className="w-full h-full"
                    src={videoState.videoUrl}
                    poster="/images/video-placeholder.jpg"
                  >
                    Your browser does not support the video tag.
                  </video>
                </div>
                <div className="flex justify-center">
                  <Button
                    onClick={resetSession}
                    variant="outline"
                    className="px-6"
                  >
                    Ask Another Question
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Video Error */}
          {videoState.error && (
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                Video generation failed: {videoState.error}
              </AlertDescription>
            </Alert>
          )}
        </div>
      </div>
    </>
  );
}
