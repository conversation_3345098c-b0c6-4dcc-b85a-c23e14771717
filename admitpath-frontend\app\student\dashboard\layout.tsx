"use client";

import { ReactNode, useEffect, useState } from "react";
import Sidebar from "@components/student/sidebar";
import Navbar from "@components/student/navbar";
import { AppToastContainer } from "@/app/components/common/toastContainer";
import { useProfile } from "@/app/hooks/student/useProfile";
import { usePathname, useRouter } from "next/navigation";

interface LayoutProps {
  children: ReactNode;
}
export default function Layout({ children }: LayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { userInfo, fetchUserInfo } = useProfile();
  const [intialFetchDone, setIntialFetchDone] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  useEffect(() => {
    if (!localStorage.getItem("access_token")) {
      router.replace("/auth/login");
      return;
    }
    fetchUserInfo();
    setIntialFetchDone(true);
  }, [fetchUserInfo]);

  useEffect(() => {
    if (intialFetchDone && userInfo) {
      // If not a student, redirect to counselor dashboard
      if (userInfo.userType !== "student") {
        router.replace("/counselor/dashboard");
        return;
      }

      // Allowing dashboard access regardless of profile completion status
    }
  }, [userInfo, router, pathname]);

  return (
    <div className="min-h-screen bg-gray-100">
      <AppToastContainer />
      <Sidebar
        isOpen={isSidebarOpen}
        onMenuClick={() => setIsSidebarOpen(!isSidebarOpen)}
      />
      <div className="flex flex-col min-h-screen md:ml-20 xl:ml-64">
        <Navbar onMenuClick={() => setIsSidebarOpen(!isSidebarOpen)} />
        <main className="sm:p-6 p-2">{children}</main>
      </div>
    </div>
  );
}
