"use client";

import { <PERSON><PERSON>ooter } from "@components/ui/card";
import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { CheckCircle } from "lucide-react";

import { Button } from "@components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@components/ui/card";
import { Skeleton } from "@components/ui/skeleton";
import usePackages from "@/app/hooks/student/usePackages";
import { useSearchParams } from "next/navigation";

export default function PackageSuccessPage() {
  const searchParams = useSearchParams();
  const subscriptionId = searchParams.get("subscription_id"); // subscription_id from query params
  const { packages: myPackages, fetchPackages, loading } = usePackages();
  const [packageSubscription, setPackageSubscription] = useState<any>(null);
  const [loadingPackage, setLoadingPackage] = useState(true);

  useEffect(() => {
    if (!loading && myPackages) {
      // Only set loading to false when we've found the package or confirmed it doesn't exist
      const foundPackage = myPackages.find(
        (p) => p.id === Number(subscriptionId)
      );
      setPackageSubscription(foundPackage);
      setLoadingPackage(false);
    }
  }, [loading, myPackages, subscriptionId]);

  useEffect(() => {
    fetchPackages();
  }, [fetchPackages]);

  if (loadingPackage) {
    return <LoadingState />;
  }

  if (!packageSubscription) {
    return <PackageNotFound />;
  }

  return (
    <div className="flex items-center justify-center min-h-[80vh] px-4">
      <Card className="w-full max-w-md border-none shadow-xl bg-white dark:bg-slate-900">
        <CardHeader className="text-center pb-2">
          <div className="mx-auto mb-4 bg-green-50 dark:bg-green-900/20 p-4 rounded-full">
            <Image
              src="/images/success.png"
              alt="Success"
              width={80}
              height={80}
              className="mx-auto"
            />
          </div>
          <CardTitle className="text-2xl md:text-3xl text-green-600 dark:text-green-400 font-bold">
            Purchase Successful!
          </CardTitle>
          <CardDescription className="text-base mt-2">
            Your package has been successfully purchased
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6 px-6">
          <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-6 space-y-4 shadow-sm">
            <h3 className="font-semibold text-xl text-slate-800 dark:text-slate-200">
              {packageSubscription.package.title}
            </h3>

            <div className="grid grid-cols-1 gap-4 pt-2">
              <div className="bg-white dark:bg-slate-700 p-4 rounded-md shadow-sm space-y-2">
                <h4 className="font-medium text-slate-700 dark:text-slate-300 mb-2">
                  Services Included:
                </h4>
                {Object.entries(packageSubscription.service_hours).map(
                  ([service, hours]: [string, any], index: number) => (
                    <div key={index} className="flex items-center gap-3">
                      <CheckCircle className="h-5 w-5 flex-shrink-0 text-green-600 dark:text-green-400" />
                      <span className="font-medium">
                        {service}:{" "}
                        <span className="text-green-600 dark:text-green-400">
                          {hours.total} hours
                        </span>
                      </span>
                    </div>
                  )
                )}
              </div>
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800 rounded-lg p-4 text-center">
            <p className="text-green-700 dark:text-green-400">
              You will receive a confirmation email with all the details. Your
              counselor will schedule sessions for you.
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center pb-6 pt-2">
          <Button
            size="lg"
            className="bg-slate-800 hover:bg-slate-700 dark:bg-green-600 dark:hover:bg-green-700 text-white font-medium px-8 py-2 h-12 rounded-md transition-colors"
            asChild
          >
            <Link href="/student/dashboard/packages">Go to your packages</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

function LoadingState() {
  return (
    <div className="flex items-center justify-center min-h-[80vh] px-4">
      <Card className="w-full max-w-md border-none shadow-xl bg-white dark:bg-slate-900">
        <CardHeader className="text-center pb-2">
          <div className="mx-auto mb-4 bg-green-50 dark:bg-green-900/20 p-4 rounded-full animate-pulse">
            <div className="h-20 w-20 rounded-full mx-auto bg-green-100 dark:bg-green-800/30"></div>
          </div>
          <Skeleton className="h-8 w-64 mx-auto" />
          <Skeleton className="h-4 w-48 mx-auto mt-2" />
        </CardHeader>
        <CardContent className="space-y-6 px-6">
          <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-6 space-y-4 shadow-sm">
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5 rounded-full" />
              <Skeleton className="h-5 w-3/4" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5 rounded-full" />
              <Skeleton className="h-5 w-2/3" />
            </div>
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-5 rounded-full" />
              <Skeleton className="h-5 w-3/4" />
            </div>
          </div>

          <div className="pt-2 space-y-2">
            <Skeleton className="h-5 w-1/2" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </CardContent>
        <CardFooter className="flex justify-center pb-6 pt-2">
          <Skeleton className="h-10 w-48 rounded-md" />
        </CardFooter>
      </Card>
    </div>
  );
}

function PackageNotFound() {
  return (
    <div className="flex items-center justify-center min-h-[80vh] px-4">
      <Card className="w-full max-w-md border-none shadow-xl bg-white dark:bg-slate-900">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 bg-blue-50 dark:bg-blue-900/20 p-4 rounded-full">
            <div className="text-blue-500 dark:text-blue-400 text-4xl font-bold">
              i
            </div>
          </div>
          <CardTitle className="text-2xl text-blue-600 dark:text-blue-400 font-bold">
            Verifying Your Purchase
          </CardTitle>
          <CardDescription className="mt-2">
            We're confirming your package purchase
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center px-6">
          <p className="text-slate-600 dark:text-slate-400">
            Your payment has been received and we're finalizing your package
            details. This usually takes just a moment. If you've completed
            payment, your package will appear shortly.
          </p>
        </CardContent>
        <CardFooter className="flex justify-center pb-6">
          <Button
            className="bg-slate-800 hover:bg-slate-700 dark:bg-slate-700 dark:hover:bg-slate-600 text-white font-medium px-6"
            asChild
          >
            <Link href="/student/dashboard/packages">View all packages</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
