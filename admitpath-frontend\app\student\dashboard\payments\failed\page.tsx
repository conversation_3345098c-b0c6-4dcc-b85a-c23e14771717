"use client";

import Link from "next/link";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowLeft, CreditCard } from "lucide-react";

import { <PERSON><PERSON> } from "@components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from "@components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@components/ui/alert";

export default function PaymentFailedPage() {
    return (
        <div className="flex items-center justify-center min-h-[80vh] px-4">
            <Card className="w-full max-w-md border-none shadow-xl bg-white dark:bg-slate-900">
                <CardHeader className="text-center pb-2">
                    <div className="mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full bg-red-50 dark:bg-red-900/20">
                        <AlertCircle className="h-10 w-10 text-red-600 dark:text-red-400" />
                    </div>
                    <CardTitle className="text-2xl md:text-3xl text-red-600 dark:text-red-400 font-bold">
                        Payment Failed
                    </CardTitle>
                    <CardDescription className="text-base mt-2">
                        We couldn't process your payment for the session
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6 px-6">
                    <Alert
                        variant="destructive"
                        className="border-red-200 bg-red-50 dark:bg-red-950/20 dark:border-red-900 shadow-sm"
                    >
                        <AlertTitle className="font-semibold">
                            Payment Unsuccessful
                        </AlertTitle>
                        <AlertDescription className="mt-2">
                            Your stripe payment was not processed successfully.
                        </AlertDescription>
                    </Alert>

                    <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-6 shadow-sm">
                        <h3 className="font-medium mb-3 text-slate-800 dark:text-slate-200">
                            What happens next?
                        </h3>
                        <ul className="space-y-2.5 text-slate-600 dark:text-slate-400">
                            {/* <li className="flex items-start gap-2">
                                <span className="h-5 w-5 rounded-full bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-xs flex items-center justify-center mt-0.5">
                                    1
                                </span>
                                <span>
                                    Your session is temporarily reserved for the
                                    next 30 minutes
                                </span>
                            </li>
                            <li className="flex items-start gap-2">
                                <span className="h-5 w-5 rounded-full bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-xs flex items-center justify-center mt-0.5">
                                    2
                                </span>
                                <span>You can try another payment method</span>
                            </li>
                            <li className="flex items-start gap-2">
                                <span className="h-5 w-5 rounded-full bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-xs flex items-center justify-center mt-0.5">
                                    3
                                </span>
                                <span>
                                    If you don't complete the payment, the
                                    session will be released
                                </span>
                            </li> */}
                            <li className="flex items-start gap-2">
                                <span className="h-5 w-5 rounded-full bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-xs flex items-center justify-center mt-0.5">
                                    1
                                </span>
                                <span>
                                    Try again to pay for the session from your
                                    payments page.
                                </span>
                            </li>
                            <li className="flex items-start gap-2">
                                <span className="h-5 w-5 rounded-full bg-red-100 dark:bg-red-900/20 text-red-600 dark:text-red-400 text-xs flex items-center justify-center mt-0.5">
                                    2
                                </span>
                                <span>Contact with our support team</span>
                            </li>
                        </ul>
                    </div>
                </CardContent>
                <CardFooter className="flex flex-col gap-3 justify-center pb-6 pt-2">
                    <Button
                        size="lg"
                        className="w-full bg-slate-800 hover:bg-slate-700 dark:bg-red-600 dark:hover:bg-red-700 text-white font-medium h-12 rounded-md transition-colors"
                        asChild
                    >
                        <Link
                            href="/student/dashboard/payments"
                            className="flex items-center justify-center gap-2"
                        >
                            <CreditCard className="h-4 w-4" />
                            Go to payments
                        </Link>
                    </Button>
                    <Button
                        size="lg"
                        variant="outline"
                        className="w-full border-slate-200 dark:border-slate-700 text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 font-medium h-12 rounded-md transition-colors"
                        asChild
                    >
                        <Link
                            href="/student/dashboard/sessions"
                            className="flex items-center justify-center gap-2"
                        >
                            <ArrowLeft className="h-4 w-4" />
                            Back to sessions
                        </Link>
                    </Button>
                </CardFooter>
            </Card>
        </div>
    );
}
