"use client";

import { <PERSON><PERSON>ooter } from "@components/ui/card";

import { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { format } from "date-fns";
import { <PERSON><PERSON>he<PERSON>, Clock } from "lucide-react";

import { But<PERSON> } from "@components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@components/ui/card";
import { Skeleton } from "@components/ui/skeleton";
import { useSessions } from "@/app/hooks/student/useSessions";
import { useSearchParams } from "next/navigation";

export default function BookingConfirmationPage() {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get("session_id"); // session_id from query params
  const { sessions, fetchSessions, loading } = useSessions();
  const [session, setSession] = useState<any>(null);
  const [loadingSingleSession, setLoadingSingleSession] = useState(true);

  const formatTime = (date: Date) => {
    return date
      .toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      })
      .toLowerCase();
  };

  useEffect(() => {
    if (!loading && sessions?.total !== null) {
      if (sessions?.total === 0) {
        setLoadingSingleSession(false);
        return;
      }
      const foundSession = sessions?.items.find(
        (s) => s.id === Number(sessionId)
      );
      setSession(foundSession);
      setLoadingSingleSession(false);
    }
  }, [loading, sessions, sessionId]);

  useEffect(() => {
    fetchSessions(Intl.DateTimeFormat().resolvedOptions().timeZone);
  }, [fetchSessions]); // Added fetchSessions to dependencies

  if (loadingSingleSession) {
    return <LoadingState />;
  }

  if (!session) {
    return <SessionNotFound />;
  }

  return (
    <div className="flex items-center justify-center min-h-[80vh] px-4">
      <Card className="w-full max-w-md border-none shadow-xl bg-white dark:bg-slate-900">
        <CardHeader className="text-center pb-2">
          <div className="mx-auto mb-4 bg-green-50 dark:bg-green-900/20 p-4 rounded-full">
            <Image
              src="/images/success.png"
              alt="Success"
              width={80}
              height={80}
              className="mx-auto"
            />
          </div>
          <CardTitle className="text-2xl md:text-3xl text-green-600 dark:text-green-400 font-bold">
            Booking Successful!
          </CardTitle>
          <CardDescription className="text-base mt-2">
            Your session has been successfully booked
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6 px-6">
          <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-6 space-y-4 shadow-sm">
            <h3 className="font-semibold text-xl text-slate-800 dark:text-slate-200">
              {session.title}
            </h3>
            <p className="text-slate-600 dark:text-slate-400">
              with {session.counselor_name}
            </p>

            <div className="grid grid-cols-1 gap-4 pt-2">
              <div className="flex items-center gap-3 bg-white dark:bg-slate-700 p-3 rounded-md shadow-sm">
                <CalendarCheck className="h-5 w-5 text-green-600 dark:text-green-400" />
                <span className="font-medium">
                  {format(new Date(session.date), "EEEE, MMMM do, yyyy")}
                </span>
              </div>
              <div className="flex items-center gap-3 bg-white dark:bg-slate-700 p-3 rounded-md shadow-sm">
                <Clock className="h-5 w-5 text-green-600 dark:text-green-400" />
                <span className="font-medium">
                  {formatTime(new Date(session.start_time))} -{" "}
                  {formatTime(new Date(session.end_time))}
                </span>
              </div>
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 border border-green-100 dark:border-green-800 rounded-lg p-4 text-center">
            <p className="text-green-700 dark:text-green-400">
              You will receive a confirmation email with all the details.
            </p>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center pb-6 pt-2">
          <Button
            size="lg"
            className="bg-slate-800 hover:bg-slate-700 dark:bg-green-600 dark:hover:bg-green-700 text-white font-medium px-8 py-2 h-12 rounded-md transition-colors"
            asChild
          >
            <Link href="/student/dashboard/sessions">Go to your sessions</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

function LoadingState() {
  return (
    <div className="flex items-center justify-center min-h-[80vh] px-4">
      <Card className="w-full max-w-md border-none shadow-xl bg-white dark:bg-slate-900">
        <CardHeader className="text-center pb-2">
          <div className="mx-auto mb-4 h-20 w-20 bg-slate-100 dark:bg-slate-800 p-4 rounded-full">
            <Skeleton className="h-full w-full rounded-full" />
          </div>
          <Skeleton className="h-8 w-64 mx-auto" />
          <Skeleton className="h-4 w-48 mx-auto mt-2" />
        </CardHeader>
        <CardContent className="space-y-6 px-6">
          <div className="bg-slate-50 dark:bg-slate-800 rounded-lg p-6 space-y-4 shadow-sm">
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-1/2" />

            <div className="grid grid-cols-1 gap-4 pt-2">
              <Skeleton className="h-12 w-full rounded-md" />
              <Skeleton className="h-12 w-full rounded-md" />
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-center pb-6 pt-2">
          <Skeleton className="h-12 w-48 rounded-md" />
        </CardFooter>
      </Card>
    </div>
  );
}

function SessionNotFound() {
  return (
    <div className="flex items-center justify-center min-h-[80vh] px-4">
      <Card className="w-full max-w-md border-none shadow-xl bg-white dark:bg-slate-900">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-full">
            <div className="text-yellow-500 dark:text-yellow-400 text-4xl font-bold">
              !
            </div>
          </div>
          <CardTitle className="text-2xl text-yellow-600 dark:text-yellow-400 font-bold">
            Session Not Found
          </CardTitle>
          <CardDescription className="mt-2">
            We couldn't find the session you're looking for.
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center px-6">
          <p className="text-slate-600 dark:text-slate-400">
            The session may have been cancelled or not paid for.{" "}
          </p>
        </CardContent>
        <CardFooter className="flex justify-center pb-6">
          <Button
            className="bg-slate-800 hover:bg-slate-700 dark:bg-slate-700 dark:hover:bg-slate-600 text-white font-medium px-6"
            asChild
          >
            <Link href="/student/dashboard/sessions">View all sessions</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
