"use client";

import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useProfile } from "@/app/hooks/student/useProfile";

export default function StudentLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { userInfo, fetchUserInfo } = useProfile();

  useEffect(() => {
    if (!localStorage.getItem("access_token")) {
      router.replace("/auth/login");
      return;
    }
    fetchUserInfo();
  }, [fetchUserInfo]);

  useEffect(() => {
    if (userInfo) {
      // If not a student, redirect to counselor dashboard
      if (userInfo.userType !== "student") {
        router.replace("/counselor/dashboard");
        return;
      }

      // Allowing access to all student pages regardless of profile completion status
    }
  }, [userInfo, router, pathname]);

  return children;
}
