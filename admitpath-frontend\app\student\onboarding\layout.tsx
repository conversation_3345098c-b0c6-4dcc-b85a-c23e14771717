"use client";

import { useProfile } from "@/app/hooks/student/useProfile";
import { usePathname, useRouter } from "next/navigation";
import React, { ReactNode, useEffect } from "react";

interface LayoutProps {
  children: ReactNode;
}
export default function Layout({ children }: LayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { userInfo, fetchUserInfo } = useProfile();

  useEffect(() => {
    if (!localStorage.getItem("access_token")) {
      router.replace("/auth/login");
      return;
    }
    fetchUserInfo();
  }, [fetchUserInfo]);

  useEffect(() => {
    if (userInfo) {
      // If not a student, redirect to counselor dashboard
      if (userInfo.userType !== "student") {
        router.replace("/counselor/dashboard");
        return;
      }

    }
  }, [userInfo, router, pathname]);

  return (
    <div className="min-h-screen bg-gray-50">
      <div
        className="fixed top-0 left-0 right-0 h-[400px] z-0"
        style={{
          backgroundImage: 'url("/images/onboarding-banner.png")',
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        {/* <div className="absolute inset-0 bg-blue-600/90" /> */}
      </div>

      <div className="relative z-10">
        {/* <Navbar /> */}
        <main className="container mx-auto px-4 sm:px-8 md:mt-16 mt-8 pt-10 md:pt-20 xl:pt-32">
          {children}
        </main>
      </div>
    </div>
  );
}
