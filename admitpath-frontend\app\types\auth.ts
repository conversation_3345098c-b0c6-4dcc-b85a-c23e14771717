export interface FormProps {
  handleSubmit: (data: any) => Promise<void>;
  isLoading: boolean;
  isLogin: boolean;
}

export interface SignupFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
}

export interface LoginFormData {
  email: string;
  password: string;
}

export interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  password?: string;
}

export type SocialProvider = "google" | "linkedin" | "apple";

export interface PasswordData {
  password: string;
  confirmPassword: string;
}
