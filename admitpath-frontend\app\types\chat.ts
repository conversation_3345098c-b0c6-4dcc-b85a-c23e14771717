export interface Message {
  id: number;
  channel_id: string;
  message: string;
  sender_id: number;
  sender_name: string;
  sender_profile_picture_url?: string;
  created_at: string;
}

export interface Participant {
  user_id: number;
  first_name: string;
  last_name: string;
  user_type: string;
  profile_picture_url?: string;
}

export interface Channel {
  channel_id: string;
  other_participant: Participant;
  last_message_at: string;
}

export interface ChannelResponse {
  channels: Channel[];
}

export interface ChannelMessagesResponse {
  total: number;
  channel_id: string;
  messages: Message[];
}

export interface SendMessagePayload {
  channel_id: string;
  recipient_id: number;
  message: string;
  sender_id: number;
  sender_name: string;
}

export interface InitialMessagePayload {
  message: string;
  recipient_id: number;
}

// WebSocket connection status
export type WebSocketConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'reconnecting';

// WebSocket message format (client to server)
export interface WebSocketMessage {
  type: 'message';
  message: string;
  recipient_id: number;
  created_at: string;
}

export interface ChatState {
  channels: Channel[] | null;
  currentChannel: Channel | null;
  messages: Message[] | null;
  messageIds: Set<number>;
  pendingMessages: Set<number>;
  unseenMessages: Record<string, number>; // channelId -> count
  unseenChannelsCount: number;
  loading: boolean;
  error: string | null;
  connectionStatus?: WebSocketConnectionStatus;

  // WebSocket specific methods
  initWebSocket: () => void;
  reconnectWebSocket: () => void;
  isWebSocketConnected: () => boolean;

  fetchChannels: () => Promise<void>;
  fetchMessages: (
    channelId: string,
    skip?: number,
    limit?: number
  ) => Promise<void>;
  sendMessage: (payload: SendMessagePayload) => Promise<void>;
  sendInitialMessage: (payload: InitialMessagePayload) => Promise<void>;
  setCurrentChannel: (channel: Channel | null) => void;
  addMessage: (message: Message) => void;
  removeMessage: (messageId: number) => void;
  updateLatestSeenMessage: (channelId: string, timestamp: string) => void;
  getUnseenChannelsCount: () => Promise<number>;
  clearError: () => void;
}

// Pusher related types
export interface PusherMessageEvent extends Message {
  // Extends Message to ensure all message fields are included
}

export interface PusherErrorEvent {
  messageId: number;
  error: string;
}

export interface MessageResponse {
  message: Message;
  success: boolean;
}

export interface ApiError {
  detail: string;
  status: number;
}

export interface LatestMessagesStorage {
  [channelId: string]: string;
}
