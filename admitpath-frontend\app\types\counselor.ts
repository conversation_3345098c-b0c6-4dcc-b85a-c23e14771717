export interface CounselorPublicProfile {
    counselor_id: number;
    user_id: number;
    first_name: string;
    last_name: string;
    profile_picture_url: string;
    nationality: string;
    country_of_residence: string;
    gender: string;
    date_of_birth: string;
    bio?: string;
    tagline?: string;
    education: {
        university_name: string;
        degree: string;
        major: string;
        start_date: string;
        end_date: string;
        grade: string;
        accomplishments: string;
    }[];
    professional_experience: {
        role: string;
        company_name: string;
        start_date: string;
        end_date: string;
        experience_description: string;
    }[];
    has_mentored_before: boolean;
    experience_description: string;
    services: {
        id: number;
        service_type: string;
        custom_type: string;
        description: string;
        price: number;
        offers_intro_call: boolean;
        created_at: string;
        updated_at: string;
    }[];
    hours_per_week: number;
    hourly_rate: number;
    linkedin_url: string;
    portfolio_url: string;
    website_url: string;
}

export interface CounselorListResponse {
    items: CounselorPublicProfile[];
    total: number;
    page: number;
    pages: number;
    has_next: boolean;
    has_previous: boolean;
}

export interface CounselorFilters {
    page?: number;
    limit?: number;
    country?: string;
    university?: string;
    min_hourly_rate?: number;
    max_hourly_rate?: number;
    service_type?: string;
}

export interface TimeSlot {
    start_time: string;
    end_time: string;
    is_available: boolean;
}
