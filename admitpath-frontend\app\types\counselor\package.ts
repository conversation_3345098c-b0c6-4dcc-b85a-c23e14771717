export interface PackageItem {
  id?: number;
  package_id?: number;
  service_name: string;
  hours: number;
  created_at?: string;
  updated_at?: string;
}

export interface PackageData {
  id?: number;
  counselor_id: number;
  title: string;
  description: string;
  total_price: number;
  is_active: boolean;
  package_items: PackageItem[];
  created_at?: string;
  updated_at?: string;
}

export interface PackagePayload {
  id?: number;
  title: string;
  description: string;
  total_price: number;
  items: {
    id?: number;
    service_name: string;
    hours: number;
  }[];
}

export interface PackageSubscription {
  id: number;
  package: {
    id: number;
    title: string;
  };
  student: {
    id: number;
    name: string;
    profile_picture: string;
  };
  start_date: string;
  end_date: string;
  status: 'pending' | 'active' | 'completed' | 'cancelled' | 'expired';
  service_hours: {
    [service_name: string]: {
      total: number;
      used: number;
    };
  };
  sessions: PackageSession[];
}

export interface PackageSession {
  id: number;
  date: string;
  start_time: string;
  end_time: string;
  status: 'pending' | 'upcoming' | 'completed' | 'cancelled';
  service_name: string;
}

export interface PackageSessionPayload {
  service_name: string;
  date: string;
  start_time: string;
  end_time: string;
}

export interface PackagesState {
  packages: { total: number; items: PackageData[] };
  selectedPackage: PackageData | null;
  subscriptions: PackageSubscription[];
  activeSubscriptions: PackageSubscription[];
  completedSubscriptions: PackageSubscription[];
  loading: boolean;
  error: string | null;
  fetchPackages: () => Promise<void>;
  fetchSinglePackage: (packageId: number) => Promise<PackageData>;
  fetchSubscriptions: (status?: string) => Promise<void>;
  addPackage: (packageData: PackagePayload) => Promise<PackageData>;
  updatePackage: (
    packageId: number | undefined,
    packageData: PackagePayload
  ) => Promise<PackageData>;
  deletePackage: (packageId: number) => Promise<void>;
  createPackageSession: (subscriptionId: number, sessionData: PackageSessionPayload) => Promise<PackageSession>;
  clearError: () => void;
}
