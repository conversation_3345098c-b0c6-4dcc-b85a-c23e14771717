// Define types for Bank Details
export interface BankDetail {
  id?: number;
  counselor_id?: number;
  bank_name: string;
  account_title: string;
  account_number: string;
  swift_code: string;
  is_primary: boolean;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface BankDetailResponse extends BankDetail {
  id: number;
  counselor_id: number;
  created_at: string;
  updated_at: string;
}

export interface Paymentstate {
  loading: boolean;
  error: string | null;
  bankAccounts: BankDetail[] | null;
  fetchBankAccounts: () => Promise<void>;
  createBankAccount: (data: BankDetail) => Promise<void>;
  updateBankAccount: (id: number, data: Partial<BankDetail>) => Promise<void>;
  deleteBankAccount: (id: number) => Promise<void>;
  clearError: () => void;
}
