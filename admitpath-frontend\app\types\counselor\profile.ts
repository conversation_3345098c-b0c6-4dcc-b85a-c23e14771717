import { Service } from "./service";

export interface userInfo {
  user_id: number;
  student_id: number;
  counselor_id: number;
  firstName: string;
  lastName: string;
  email: string;
  userType: string;
  isProfileComplete: boolean;
  is_verified: boolean;
  profile_picture_url: string;
}

export interface PersonalInfo {
  id: number;
  counselor_id: number;
  nationality: string;
  country_of_residence: string;
  gender: string;
  date_of_birth: string;
  first_name: string;
  last_name: string;
  bio?: string;
  tagline?: string;
}

export interface EducationInfo {
  major: string;
  counselor_id: number;
  university_name: string;
  id: number;
  end_date: string;
  accomplishments: string;
  degree: string;
  start_date: string;
  grade: string;
}

export interface ExperienceInfo {
  start_date: string;
  id: number;
  experience_description: string;
  end_date: string;
  counselor_id: number;
  company_name: string;
  role: string;
}

export interface DocumentInfo {
  id: number;
  document_name: string;
  document_url: string;
  counselor_id: number;
}

export interface ProfileState {
  loading: boolean;
  error: string | null;
  userInfo: userInfo | null;
  personalInfo: PersonalInfo | null;
  educationInfo: EducationInfo[] | null;
  experienceInfo: ExperienceInfo[] | null;
  documentInfo: DocumentInfo[] | null;
  profilePicture: string | null;
  getUserInfo: () => Promise<void>;
  getPersonalInfo: () => Promise<void>;
  getEducationInfo: () => Promise<void>;
  getExperienceInfo: () => Promise<void>;
  getDocumentInfo: () => Promise<void>;
  uploadProfilePicture: (
    e: React.ChangeEvent<HTMLInputElement>
  ) => Promise<void>;
  deleteProfilePicture: () => Promise<void>;
  clearError: () => void;
}

export interface PackageData {
  id: number;
  title: string;
  description: string;
  total_price: number;
  package_items: { service_name: string; hours: number }[];
}

export interface PopupProps {
  isPopupOpen: boolean;
  setIsPopupOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setSuccessPopup?: React.Dispatch<React.SetStateAction<string>>;
  serviceData?: Service | null;
  onSave?: () => Promise<void>;
  packageData?: PackageData | null;
  services?: { value: number; label: string }[];
}

export interface ProfileCreateProps {
  selectedMenuIndex?: number;
  onMenuSelect?: (index: number) => void;
  edit?: boolean;
}
