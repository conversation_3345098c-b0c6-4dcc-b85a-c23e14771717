export interface Service {
  id: number;
  service_type: string;
  description: string;
  price: number | string;
  offers_intro_call?: boolean;
  custom_type?: string | null;
}

export interface ServicesState {
  services: Service[];
  selectedService: Service | null;
  loading: boolean;
  error: string | null;
  fetchServices: (id: number | undefined) => Promise<void>;
  fetchSingleService: (serviceId: number) => Promise<void>;
  addService: (serviceData: Omit<Service, "id">) => Promise<void>;
  updateService: (
    serviceId: number,
    serviceData: Partial<Service>
  ) => Promise<void>;
  deleteService: (serviceId: number) => Promise<void>;
  clearError: () => void;
}
