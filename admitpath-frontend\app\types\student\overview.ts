

export interface DashboardStats {
  total_sessions: number;
  upcoming_sessions: number;
  total_counsellors: number;
}

export interface PendingFeedbackSession {
  session_id: number;
  event_name: string;
  start_time: string;
  end_time: string;
  instructor_name: string;
}

export interface ReviewData {
  rating: number;
  received_expected_service: boolean;
  comment: string;
  additional_feedback: string;
}

export interface OverviewState {
  loading: boolean;
  error: string | null;
  stats: DashboardStats | null;
  pendingFeedbacks: PendingFeedbackSession[];
  totalPendingFeedbacks: number;
  fetchStats: () => Promise<void>;
  fetchPendingFeedbacks: () => Promise<void>;
  submitFeedback: (sessionId: number, data: ReviewData) => Promise<void>;
  clearError: () => void;
}
