// Student package types
import { StudentPackage } from "@/app/hooks/student/usePackages";

export interface PackageSubscription extends StudentPackage {
  // This extends the StudentPackage interface from usePackages
  // to maintain compatibility with the SessionsListDialog component
}

export interface PackageSession {
  id: number;
  date: string;
  start_time: string;
  end_time: string;
  status: 'pending' | 'upcoming' | 'completed' | 'cancelled';
  service_name: string;
}

export interface FirstSessionData {
  event_name: string;
  date: string;
  start_time: string;
  end_time: string;
  student_email: string;
}

export interface PackageSubscriptionPayload {
  package_id: number;
  selected_service: string;
  first_session: FirstSessionData;
}

export interface SessionFromPackagePayload {
  selected_service: string;
  date: string;
  start_time: string;
  end_time: string;
}

export interface PackageCheckoutPayload {
  package_id: number;
  subscription_id: number;
  first_session?: FirstSessionData;
  selected_service: string;
}

export interface PackageCheckoutResponse {
  checkout_url: string;
  session_id: string;
  payment_id: number;
}
