export interface PaymentStats {
  total_amount_spent: number;
  total_sessions: number;
  total_hours_spent: number;
}

export interface PaymentSession {
  id: number;
  amount: number;
  original_amount?: number;
  discount_amount?: number;
  is_discounted?: boolean;
  status: "pending" | "completed" | "rejected";
  created_at: string;
  session_id?: number;
  package_id?: number;
  service_id?: number;
  service_type?: string;
  counselor_name: string;
  counselor_id?: number;
  payment_date: string;
  service_name: string;
}

export interface PaymentSessionsResponse {
  items: PaymentSession[];
  total: number;
  page: number;
  size: number;
  has_more: boolean;
}

export interface PaymentFilters {
  status?: "pending" | "completed" | "rejected";
  service_type?: "session" | "package" | "service";
  page?: number;
  page_size?: number;
}

export interface PaymentState {
  loading: boolean;
  error: string | null;
  stats: PaymentStats | null;
  sessions: PaymentSession[];
  pagination: {
    total: number;
    page: number;
    size: number;
    hasMore: boolean;
  };
  filters: PaymentFilters;
  fetchStats: () => Promise<void>;
  fetchSessions: (filters?: PaymentFilters) => Promise<void>;
  updateFilters: (newFilters: Partial<PaymentFilters>) => void;
  createCheckoutSession: (sessionId: number) => Promise<{
    checkout_url: string;
    session_id: string;
    payment_id: number;
  }>;
  clearError: () => void;
}
