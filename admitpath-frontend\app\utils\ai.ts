import Groq from "groq-sdk";

const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY,
});

// Word limits for different environments
const WORD_LIMITS = {
  development: 150, // ~1 minute of speech
  production: 750, // ~5 minutes of speech
};

// Get current word limit based on environment
const getWordLimit = (): number => {
  return process.env.NODE_ENV === "production"
    ? WORD_LIMITS.production
    : WORD_LIMITS.development;
};

// Count words in a text
const countWords = (text: string): number => {
  return text
    .trim()
    .split(/\s+/)
    .filter((word) => word.length > 0).length;
};

// Truncate text to the last full sentence before the word limit
const truncateToWordLimit = (text: string, limit: number): string => {
  const words = text.trim().split(/\s+/);
  if (words.length <= limit) {
    return text;
  }

  // Find the last period within the word limit
  const truncatedText = words.slice(0, limit).join(" ");
  const lastPeriodIndex = truncatedText.lastIndexOf(".");

  if (lastPeriodIndex !== -1) {
    return truncatedText.substring(0, lastPeriodIndex + 1);
  }

  // If no period is found, just truncate at the word limit
  return truncatedText + "...";
};

export interface AIResponse {
  success: boolean;
  response?: string;
  error?: string;
  wordCount?: number;
  truncated?: boolean;
}

/**
 * Generate AI counsellor response using Groq
 * @param userQuery - The user's question or query
 * @returns Promise<AIResponse>
 */
export async function generateCounsellorResponse(
  userQuery: string
): Promise<AIResponse> {
  try {
    if (!process.env.GROQ_API_KEY) {
      throw new Error("GROQ_API_KEY is not configured");
    }

    if (!userQuery.trim()) {
      throw new Error("User query cannot be empty");
    }

    const wordLimit = getWordLimit();

    // Create a comprehensive prompt for the AI counsellor
    const systemPrompt = `You are an expert educational counsellor and consultant specializing in university admissions, career guidance, and academic planning. Your role is to provide comprehensive, helpful, and encouraging advice to students.

Key guidelines:
- Provide detailed, actionable advice
- Be encouraging and supportive
- Use specific examples when helpful
- Address the student's concerns directly
- Offer practical next steps
- Keep response under ${wordLimit} words
- Maintain a professional yet friendly tone
- Focus on university admissions, career planning, academic guidance, and personal development

Remember: You are helping students navigate their educational journey and make informed decisions about their future.`;

    const userPrompt = `Student Question: ${userQuery}

Please provide a comprehensive response that addresses their question with practical advice and guidance.`;

    // Make the API call to Groq
    const completion = await groq.chat.completions.create({
      messages: [
        {
          role: "system",
          content: systemPrompt,
        },
        {
          role: "user",
          content: userPrompt,
        },
      ],
      model: "meta-llama/llama-4-maverick-17b-128e-instruct", // Using a powerful Llama model
      temperature: 0.7, // Balanced creativity and consistency
      max_tokens: Math.min(wordLimit * 2, 2000), // Rough estimate: 1 word ≈ 1.3 tokens
      top_p: 0.9,
    });

    const aiResponse = completion.choices[0]?.message?.content;

    if (!aiResponse) {
      throw new Error("No response generated from AI model");
    }

    // Check word count and truncate if necessary
    console.log("OG AI Response:", aiResponse);
    const wordCount = countWords(aiResponse);
    let finalResponse = aiResponse;
    let truncated = false;

    if (wordCount > wordLimit) {
      finalResponse = truncateToWordLimit(aiResponse, wordLimit);
      truncated = true;
    }

    return {
      success: true,
      response: finalResponse,
      wordCount: countWords(finalResponse),
      truncated,
    };
  } catch (error: any) {
    console.error("Error generating AI response:", error);

    return {
      success: false,
      error: error.message || "Failed to generate AI response",
    };
  }
}

/**
 * Validate user input before processing
 * @param userQuery - The user's input
 * @returns Object with validation result
 */
export function validateUserInput(userQuery: string): {
  valid: boolean;
  error?: string;
} {
  if (!userQuery || typeof userQuery !== "string") {
    return { valid: false, error: "Query must be a non-empty string" };
  }

  const trimmedQuery = userQuery.trim();

  if (trimmedQuery.length === 0) {
    return { valid: false, error: "Query cannot be empty" };
  }

  if (trimmedQuery.length < 3) {
    return { valid: false, error: "Query must be at least 3 characters long" };
  }

  if (trimmedQuery.length > 2000) {
    return {
      valid: false,
      error: "Query is too long (maximum 2000 characters)",
    };
  }

  return { valid: true };
}

/**
 * Get current configuration info
 */
export function getAIConfig() {
  return {
    wordLimit: getWordLimit(),
    environment: process.env.NODE_ENV || "development",
    model: "meta-llama/llama-4-maverick-17b-128e-instruct",
  };
}
