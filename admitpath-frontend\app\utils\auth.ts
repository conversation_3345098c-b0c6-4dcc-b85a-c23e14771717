import { toast } from 'react-toastify';
import apiClient from '@lib/apiClient';

const API_URL = process.env.NEXT_PUBLIC_API_BASE_URL;

interface AuthError extends Error {
  isAuthError?: boolean;
}

export const handleGoogleLogin = async (token: string, userType?: string) => {
  try {
    const response = await apiClient.post('/auth/google', 
      userType 
        ? { token, user_type: userType }  // Signup case
        : { token }  // Login case
    );

    return response.data;
  } catch (error: any) {
    if (!error.isAuthError) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Google auth error:', error);
      }
    }
    throw error;
  }
};

export const handleLinkedInLogin = async (code: string, userType?: string) => {
  try {
    const response = await apiClient.post('/auth/linkedin',
      userType 
        ? { code, user_type: userType }  // Signup case
        : { code }  // Login case
    );

    return response.data;
  } catch (error: any) {
    if (!error.isAuthError) {
      if (process.env.NODE_ENV === 'development') {
        console.error('LinkedIn auth error:', error);
      }
    }
    throw error;
  }
};

export const initiateLinkedInLogin = (userType?: string) => {
  const LINKEDIN_CLIENT_ID = process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID;
  const LINKEDIN_REDIRECT_URI = process.env.NEXT_PUBLIC_LINKEDIN_REDIRECT_URI;
  const scope = 'openid profile email';
  
  // Only include state (userType) for signup flow
  const stateParam = userType ? `&state=${userType}` : '';
  const url = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=${LINKEDIN_CLIENT_ID}&redirect_uri=${LINKEDIN_REDIRECT_URI}${stateParam}&scope=${scope}`;
  
  window.location.href = url;
};
