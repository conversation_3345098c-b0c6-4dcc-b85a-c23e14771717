export function fuzzySearch(needle: string, haystack: string): boolean {
  const hLen = haystack.length;
  const nLen = needle.length;
  if (nLen > hLen) {
    return false;
  }
  if (nLen === hLen) {
    return needle.toLowerCase() === haystack.toLowerCase();
  }

  needle = needle.toLowerCase();
  haystack = haystack.toLowerCase();

  let nIdx = 0;
  let hIdx = 0;

  while (nIdx < nLen && hIdx < hLen) {
    if (needle[nIdx] === haystack[hIdx]) {
      nIdx++;
    }
    hIdx++;
  }
  return nIdx === nLen;
}

export function findClosestMatch(
  input: string,
  options: string[]
): string | null {
  const matches = options.filter((option) => fuzzySearch(input, option));
  return matches.length > 0 ? matches[0] : null;
}
