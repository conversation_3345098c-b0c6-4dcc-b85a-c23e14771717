const backgroundColors: Record<string, string> = {
  A: "#fef3c7",
  B: "#fde68a",
  C: "#fcd34d",
  D: "#fbbf24",
  E: "#f9a8d4",
  F: "#f472b6",
  G: "#ec4899",
  H: "#e879f9",
  I: "#d8b4fe",
  J: "#c4b5fd",
  K: "#a78bfa",
  L: "#818cf8",
  M: "#60a5fa",
  N: "#38bdf8",
  O: "#22d3ee",
  P: "#2dd4bf",
  Q: "#34d399",
  R: "#4ade80",
  S: "#a3e635",
  T: "#facc15",
  U: "#f97316",
  V: "#fb923c",
  W: "#f87171",
  X: "#ef4444",
  Y: "#dc2626",
  Z: "#b91c1c",
};

export function generatePlaceholder(
  firstName: string | null | undefined,
  lastName: string | null | undefined
): string {
  if (
    !firstName ||
    typeof firstName !== "string" ||
    !firstName.trim() ||
    !lastName
  ) {
    return "/images/person.png";
  }

  const firstLetter = firstName.trim()[0].toUpperCase();
  const secondLetter =
    lastName && typeof lastName === "string" && lastName.trim()
      ? lastName.trim()[0].toUpperCase()
      : "";
  const initials = firstLetter + secondLetter;

  const bgColor = backgroundColors[firstLetter] || "#adb5bd"; // Default grey shade
  
  return `https://placehold.co/100x100/${bgColor.slice(
    1
  )}/000000?text=${initials}`;
}
