// Convert date to ISO format
export const formatDateISO = (date: string) => {
  // Create a date object from the date string
  const dateObj = new Date(date);

  // Set the time to midnight (00:00:00) to get just the date
  dateObj.setHours(0, 0, 0, 0);

  // Return the ISO string
  return dateObj.toISOString();
};

// Convert to long date string
export function formatToLongDateString(dateString: string) {
  const date = new Date(dateString);

  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

// Convert date to 'yyyy-mm-dd' format in LOCAL TIMEZONE
export const formatNormalDate = (date: string) => {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, "0"); // Months are 0-based
  const day = String(d.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

// Convert ISO time to "HH:mm" in LOCAL TIMEZONE
export function formatTimeForInput(isoTime: string) {
  const date = new Date(isoTime);
  const hours = String(date.getHours()).padStart(2, "0"); // Local hours
  const minutes = String(date.getMinutes()).padStart(2, "0"); // Local minutes
  return `${hours}:${minutes}`;
}

// Convert "HH:mm" from time input back to ISO format
export function formatTimeToISO(date: string, time: string) {
  // Create a date object from the date and time strings
  const [hours, minutes] = time.split(':').map(Number);
  const dateObj = new Date(date);

  // Set the hours and minutes
  dateObj.setHours(hours, minutes, 0, 0);

  // Return the ISO string
  return dateObj.toISOString();
}

export function dateNowWithTZOffset() {
  const now = new Date();

  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");

  const microseconds = String(Math.floor(Math.random() * 1000000)).padStart(
    6,
    "0"
  );

  const tzOffset = now.getTimezoneOffset();
  const tzOffsetHours = Math.abs(Math.floor(tzOffset / 60));
  const tzOffsetMinutes = Math.abs(tzOffset % 60);
  const tzOffsetSign = tzOffset <= 0 ? "+" : "-";

  const tzFormatted = `${tzOffsetSign}${String(tzOffsetHours).padStart(
    2,
    "0"
  )}:${String(tzOffsetMinutes).padStart(2, "0")}`;

  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${microseconds}${tzFormatted}`;
}
