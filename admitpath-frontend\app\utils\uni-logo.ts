import axios from "axios";

const CLIENT_ID = "1idDNCRTv8QuIQpUJ7P";

// In-memory cache for university logos
const logoCache = new Map<string, string>();
const domainCache = new Map<string, string>();

// Cache expiry time (24 hours)
const CACHE_EXPIRY = 24 * 60 * 60 * 1000;
const cacheTimestamps = new Map<string, number>();

// Fallback logo for when university logo cannot be found
const FALLBACK_LOGO = "/images/university-placeholder.svg";

function isCacheValid(key: string): boolean {
  const timestamp = cacheTimestamps.get(key);
  if (!timestamp) return false;
  return Date.now() - timestamp < CACHE_EXPIRY;
}

function setCacheWithTimestamp(key: string, value: string): void {
  logoCache.set(key, value);
  cacheTimestamps.set(key, Date.now());
}

async function getUniversityDomain(universityName: string): Promise<string> {
  const cacheKey = `domain_${universityName.toLowerCase()}`;

  // Check domain cache first
  if (domainCache.has(cacheKey) && isCacheValid(cacheKey)) {
    return domainCache.get(cacheKey)!;
  }

  try {
    const encodedName = encodeURIComponent(universityName);
    const response = await axios.get(
      `https://universities.hipolabs.com/search?name=${encodedName}`,
      { timeout: 5000 } // 5 second timeout
    );

    if (response.data.length === 0) {
      throw new Error("University not found!");
    }

    const domain = response.data[0].domains[0];
    domainCache.set(cacheKey, domain);
    cacheTimestamps.set(cacheKey, Date.now());

    return domain;
  } catch (error) {
    console.warn(`Failed to fetch domain for ${universityName}:`, error);
    throw error;
  }
}

export async function getUniversityLogoUrl(
  universityName: string
): Promise<string> {
  if (!universityName || typeof universityName !== "string") {
    return FALLBACK_LOGO;
  }

  const cacheKey = universityName.toLowerCase().trim();

  // Check cache first
  if (logoCache.has(cacheKey) && isCacheValid(cacheKey)) {
    return logoCache.get(cacheKey)!;
  }

  try {
    const domain = await getUniversityDomain(universityName);
    const brandfetchUrl = `https://cdn.brandfetch.io/${domain}/w/200/h/200?c=${CLIENT_ID}`;

    // Verify the logo URL is accessible
    try {
      await axios.head(brandfetchUrl, { timeout: 3000 });
      setCacheWithTimestamp(cacheKey, brandfetchUrl);
      return brandfetchUrl;
    } catch (logoError) {
      // If brandfetch fails, cache the fallback
      setCacheWithTimestamp(cacheKey, FALLBACK_LOGO);
      return FALLBACK_LOGO;
    }
  } catch (error) {
    console.warn(
      `Error fetching university logo for ${universityName}:`,
      error
    );
    // Cache the fallback to prevent repeated failed requests
    setCacheWithTimestamp(cacheKey, FALLBACK_LOGO);
    return FALLBACK_LOGO;
  }
}

// Preload university logos for better performance
export function preloadUniversityLogos(universityNames: string[]): void {
  universityNames.forEach((name) => {
    if (name && !logoCache.has(name.toLowerCase().trim())) {
      // Fire and forget - don't await
      getUniversityLogoUrl(name).catch(() => {
        // Silently handle errors for preloading
      });
    }
  });
}

// Clear expired cache entries
export function clearExpiredCache(): void {
  const now = Date.now();
  for (const [key, timestamp] of cacheTimestamps.entries()) {
    if (now - timestamp > CACHE_EXPIRY) {
      logoCache.delete(key);
      domainCache.delete(key);
      cacheTimestamps.delete(key);
    }
  }
}
