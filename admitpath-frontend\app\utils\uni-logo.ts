import axios from "axios";

const CLIENT_ID = "1idDNCRTv8QuIQpUJ7P";

export async function getUniversityLogoUrl(
  universityName: string
): Promise<string> {
  try {
    const encodedName = encodeURIComponent(universityName);

    const response = await axios.get(
      `http://universities.hipolabs.com/search?name=${encodedName}`
    );

    if (response.data.length === 0) {
      throw new Error("University not found!");
    }

    const domain = response.data[0].domains[0];

    const brandfetchUrl = `https://cdn.brandfetch.io/${domain}/w/400/h/400?c=${CLIENT_ID}`;

    return brandfetchUrl;
  } catch (error) {
    throw new Error(`<PERSON>rror fetching university logo: ${error}`);
  }
}
