"use client";

import axios from "axios";

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000",
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
  // withCredentials: true,
});

// Request interceptor to handle dynamic headers or logging
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("access_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors globally
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle specific error responses (e.g., 401 Unauthorized)
    if (error.response) {
      const { status } = error.response;

      if (status === 401) {
        if (
          window.location.pathname.startsWith("/student") ||
          window.location.pathname.startsWith("/counselor") ||
          localStorage.getItem("access_token")
        ) {
          console.error("Unauthorized. Redirecting to login...");
          localStorage.removeItem("access_token");
          window.location.href = "/auth/login";
        }
      } else if (status >= 500) {
        console.error(
          "Server error:",
          error.response.data.message || "Internal Server Error"
        );
      }
    } else {
      console.error("Network error:", error.message);
    }

    return Promise.reject(error);
  }
);

export default apiClient;
